{"_attachments": {}, "_id": "yallist", "_rev": "444-61f14491963ca28f5ee374a8", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "description": "Yet Another Linked List", "dist-tags": {"latest": "5.0.0"}, "license": "BlueOak-1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "yallist", "readme": "# yallist\n\nYet Another Linked List\n\nThere are many doubly-linked list implementations like it, but this\none is mine.\n\nFor when an array would be too big, and a Map can't be iterated in\nreverse order.\n\n## basic usage\n\n```js\nimport { Yallist } from 'yallist'\nvar myList = new Yallist([1, 2, 3])\nmyList.push('foo')\nmyList.unshift('bar')\n// of course pop() and shift() are there, too\nconsole.log(myList.toArray()) // ['bar', 1, 2, 3, 'foo']\nmyList.forEach(function (k) {\n  // walk the list head to tail\n})\nmyList.forEachReverse(function (k, index, list) {\n  // walk the list tail to head\n})\nvar myDoubledList = myList.map(function (k) {\n  return k + k\n})\n// now myDoubledList contains ['barbar', 2, 4, 6, 'foofoo']\n// mapReverse is also a thing\nvar myDoubledListReverse = myList.mapReverse(function (k) {\n  return k + k\n}) // ['foofoo', 6, 4, 2, 'barbar']\n\nvar reduced = myList.reduce(function (set, entry) {\n  set += entry\n  return set\n}, 'start')\nconsole.log(reduced) // 'startfoo123bar'\n```\n\n## api\n\nThe whole API is considered \"public\".\n\nFunctions with the same name as an Array method work more or less the\nsame way.\n\nThere's reverse versions of most things because that's the point.\n\n### Yallist\n\nDefault export, the class that holds and manages a list.\n\nCall it with either a forEach-able (like an array) or a set of\narguments, to initialize the list.\n\nThe Array-ish methods all act like you'd expect.  No magic length,\nthough, so if you change that it won't automatically prune or add\nempty spots.\n\n### Yallist.create(..)\n\nAlias for Yallist function.  Some people like factories.\n\n#### yallist.head\n\nThe first node in the list\n\n#### yallist.tail\n\nThe last node in the list\n\n#### yallist.length\n\nThe number of nodes in the list.  (Change this at your peril.  It is\nnot magic like Array length.)\n\n#### yallist.toArray()\n\nConvert the list to an array.\n\n#### yallist.forEach(fn, [thisp])\n\nCall a function on each item in the list.\n\n#### yallist.forEachReverse(fn, [thisp])\n\nCall a function on each item in the list, in reverse order.\n\n#### yallist.get(n)\n\nGet the data at position `n` in the list.  If you use this a lot,\nprobably better off just using an Array.\n\n#### yallist.getReverse(n)\n\nGet the data at position `n`, counting from the tail.\n\n#### yallist.map(fn, thisp)\n\nCreate a new Yallist with the result of calling the function on each\nitem.\n\n#### yallist.mapReverse(fn, thisp)\n\nSame as `map`, but in reverse.\n\n#### yallist.pop()\n\nGet the data from the list tail, and remove the tail from the list.\n\n#### yallist.push(item, ...)\n\nInsert one or more items to the tail of the list.\n\n#### yallist.reduce(fn, initialValue)\n\nLike Array.reduce.\n\n#### yallist.reduceReverse\n\nLike Array.reduce, but in reverse.\n\n#### yallist.reverse\n\nReverse the list in place.\n\n#### yallist.shift()\n\nGet the data from the list head, and remove the head from the list.\n\n#### yallist.slice([from], [to])\n\nJust like Array.slice, but returns a new Yallist.\n\n#### yallist.sliceReverse([from], [to])\n\nJust like yallist.slice, but the result is returned in reverse.\n\n#### yallist.splice(start, deleteCount, ...)\n\nLike Array.splice.\n\n#### yallist.toArray()\n\nCreate an array representation of the list.\n\n#### yallist.toArrayReverse()\n\nCreate a reversed array representation of the list.\n\n#### yallist.unshift(item, ...)\n\nInsert one or more items to the head of the list.\n\n#### yallist.unshiftNode(node)\n\nMove a Node object to the front of the list.  (That is, pull it out of\nwherever it lives, and make it the new head.)\n\nIf the node belongs to a different list, then that list will remove it\nfirst.\n\n#### yallist.pushNode(node)\n\nMove a Node object to the end of the list.  (That is, pull it out of\nwherever it lives, and make it the new tail.)\n\nIf the node belongs to a list already, then that list will remove it\nfirst.\n\n#### yallist.removeNode(node)\n\nRemove a node from the list, preserving referential integrity of head\nand tail and other nodes.\n\nWill throw an error if you try to have a list remove a node that\ndoesn't belong to it.\n\n### Yallist.Node\n\nThe class that holds the data and is actually the list.\n\nCall with `const n = new Node(value, previousNode, nextNode)`\n\nNote that if you do direct operations on Nodes themselves, it's very\neasy to get into weird states where the list is broken.  Be careful :)\n\n#### node.next\n\nThe next node in the list.\n\n#### node.prev\n\nThe previous node in the list.\n\n#### node.value\n\nThe data the node contains.\n\n#### node.list\n\nThe list to which this node belongs.  (Null if it does not belong to\nany list.)\n", "time": {"created": "2022-01-26T12:54:41.922Z", "modified": "2024-04-10T15:11:27.179Z", "4.0.0": "2019-09-30T20:08:08.970Z", "3.1.1": "2019-09-30T20:07:18.343Z", "3.1.0": "2019-09-26T17:14:01.323Z", "3.0.3": "2018-11-21T23:22:36.619Z", "3.0.2": "2017-04-25T19:59:38.913Z", "2.1.2": "2017-03-13T22:16:05.251Z", "3.0.1": "2017-03-13T22:12:34.074Z", "3.0.0": "2017-03-13T16:56:52.633Z", "2.1.1": "2017-03-13T16:55:34.896Z", "2.1.0": "2017-03-13T02:03:46.998Z", "2.0.1": "2017-03-11T20:09:18.123Z", "2.0.0": "2015-12-19T19:55:29.661Z", "1.1.0": "2015-12-19T00:32:18.820Z", "1.0.2": "2015-12-18T23:20:23.258Z", "1.0.1": "2015-12-18T09:29:58.326Z", "1.0.0": "2015-12-18T08:02:25.789Z", "5.0.0": "2024-04-09T19:21:44.844Z"}, "versions": {"4.0.0": {"name": "yallist", "version": "4.0.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^12.1.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "1649cc57394b5affeca2c573943ebe3ed7d39119", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@4.0.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "9bb92790d9c0effec63be73519e11a35019a3a72", "size": 4494, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_4.0.0_1569874088837_0.19971227214997667"}, "_hasShrinkwrap": false, "publish_time": 1569874088970, "_cnpm_publish_time": 1569874088970, "_cnpmcore_publish_time": "2021-12-13T10:31:22.988Z"}, "3.1.1": {"name": "yallist", "version": "3.1.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^12.1.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ab350a79eb94c80f7146662748a12b6fcd63729c", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.1.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd", "size": 4496, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_3.1.1_1569874038188_0.3036372642366818"}, "_hasShrinkwrap": false, "publish_time": 1569874038343, "_cnpm_publish_time": 1569874038343, "_cnpmcore_publish_time": "2021-12-13T10:31:23.286Z"}, "3.1.0": {"name": "yallist", "version": "3.1.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^12.1.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ec6f8c1551f730a593bd658413e68ba779976602", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.1.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "906cc2100972dc2625ae78f566a2577230a1d6f7", "size": 4492, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-3.1.0.tgz", "integrity": "sha512-6gpP93MR+VOOehKbCPchro3wFZNSNmek8A2kbkOAZLIZAYx1KP/zAqwO0sOHi3xJEb+UBz8NaYt/17UNit1Q9w=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_3.1.0_1569518041046_0.5437509782914414"}, "_hasShrinkwrap": false, "publish_time": 1569518041323, "_cnpm_publish_time": 1569518041323, "_cnpmcore_publish_time": "2021-12-13T10:31:23.567Z"}, "3.0.3": {"name": "yallist", "version": "3.0.3", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^12.1.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "47ab1ce288032985c90ee568681b35ad8978be41", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b4b049e314be545e3ce802236d6cd22cd91c3de9", "size": 4265, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-3.0.3.tgz", "integrity": "sha512-S+Zk8DEWE6oKpV+vI3qWkaK+jSbIK86pCwe2IF/xwIpQ8jEuxpw9NyaGjmp9+BoJv5FV2piqCDcoCtStppiq2A=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_3.0.3_1542842556417_0.767908228373708"}, "_hasShrinkwrap": false, "publish_time": 1542842556619, "_cnpm_publish_time": 1542842556619, "_cnpmcore_publish_time": "2021-12-13T10:31:23.854Z"}, "3.0.2": {"name": "yallist", "version": "3.0.2", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "d692d0f974934858b76c3b1f7f0973d0450c5c87", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.0.2", "_shasum": "8452b4bb7e83c7c188d8041c1a837c773d6d8bb9", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "8452b4bb7e83c7c188d8041c1a837c773d6d8bb9", "size": 4287, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-3.0.2.tgz", "integrity": "sha512-U+iKQ8rDYMRmvEpvDUIWZ3CtM9/imlAc+c1yJ7YV0vu+HNtP82sAkXzuDXPLkIPoLZohnXFSs9wf2E17xk5yZA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/yallist-3.0.2.tgz_1493150377027_0.22160566318780184"}, "publish_time": 1493150378913, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493150378913, "_cnpmcore_publish_time": "2021-12-13T10:31:24.180Z"}, "2.1.2": {"name": "yallist", "version": "2.1.2", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "566cd4cd1e2ce57ffa84e295981cd9aa72319391", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.1.2", "_shasum": "1c11f9218f076089a47dd512f93c6699a6a81d52", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "1c11f9218f076089a47dd512f93c6699a6a81d52", "size": 4239, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-2.1.2.tgz", "integrity": "sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-2.1.2.tgz_1489443365033_0.47744474792853"}, "publish_time": 1489443365251, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489443365251, "_cnpmcore_publish_time": "2021-12-13T10:31:24.483Z"}, "3.0.1": {"name": "yallist", "version": "3.0.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "a0c17647ac41f86adb875af76d2f522462785ffb", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.0.1", "_shasum": "4affa89763ea5c2aeb9e2ed98387ceada34f4c4d", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "4affa89763ea5c2aeb9e2ed98387ceada34f4c4d", "size": 4284, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-3.0.1.tgz", "integrity": "sha512-XZPrO9OnL1B6IOPVEgcH6Ex3pOd6KMqtGlXjUSr/VR5nSWOYujm5z5y1iVxoVt9hrusLrKiWzGyqpQFILhtGIw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/yallist-3.0.1.tgz_1489443153380_0.10298502212390304"}, "publish_time": 1489443154074, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489443154074, "_cnpmcore_publish_time": "2021-12-13T10:31:24.824Z"}, "3.0.0": {"name": "yallist", "version": "3.0.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "68fb6bd20e99f78dcd8cc299ebe8ba98fc726ff0", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.0.0", "_shasum": "717369f9df727e5a03cc69c9ca94d7d94579f58c", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "717369f9df727e5a03cc69c9ca94d7d94579f58c", "size": 4272, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-3.0.0.tgz", "integrity": "sha512-e8<PERSON>sK5rgly0muU1LeKbPMpiJoLWDEfe5+hanR9wljH7NJqmQdtKIYFQkxdLENYpl59mqg5g42bYS2tX59Gu4w=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-3.0.0.tgz_1489424212365_0.014271438587456942"}, "publish_time": 1489424212633, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489424212633, "_cnpmcore_publish_time": "2021-12-13T10:31:25.182Z"}, "2.1.1": {"name": "yallist", "version": "2.1.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "9493a5b7460dd38dc3d3d488c92eae8a395efce3", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.1.1", "_shasum": "08309c7044b1761d5e1591dc12c67629271b6ac3", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "08309c7044b1761d5e1591dc12c67629271b6ac3", "size": 4230, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-2.1.1.tgz", "integrity": "sha512-wJXW2iOHgovkGFfG0dBkFufZ+QbrPRe4H7f2p4CSIOgmGB+32iuMV+5mjQUi+KOYEvukoQTxz+wYvraUdCNgfw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-2.1.1.tgz_1489424134677_0.7357181606348604"}, "publish_time": 1489424134896, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489424134896, "_cnpmcore_publish_time": "2021-12-13T10:31:25.541Z"}, "2.1.0": {"name": "yallist", "version": "2.1.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ce5fe86dc1e550835941d33ce134e0632e7b6287", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.1.0", "_shasum": "3a0f3b45f42cb60f822c92f69ade2bb88beb1ae0", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "3a0f3b45f42cb60f822c92f69ade2bb88beb1ae0", "size": 4272, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-2.1.0.tgz", "integrity": "sha512-bw4zKj62wAeQuEpEdtr5a8EWAhVdmCqzogKTo/3ZtdPCeu1yKOM+/DzzSdcs4BqOcovXxff8W/CAaNzp5qSzXw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-2.1.0.tgz_1489370626750_0.8869737309869379"}, "publish_time": 1489370626998, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489370626998, "_cnpmcore_publish_time": "2021-12-13T10:31:25.915Z"}, "2.0.1": {"name": "yallist", "version": "2.0.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"standard": "^5.4.1", "tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov", "posttest": "standard -F *.js test/*.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "64016921aae7ec4bd6d2d3e8c9f49d02529d9c3c", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.0.1", "_shasum": "6c11279640abd6ed0a86dd32ed56b03caa9df4f1", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "6c11279640abd6ed0a86dd32ed56b03caa9df4f1", "size": 5700, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-2.0.1.tgz", "integrity": "sha512-nvYoTNlZ+T0MBdHpdrJMyI1AY/s2NeSqukeXuwiaTPZjbkzsg2M9f0OmmPwyIr1DFu88Xd7jzUj2+SEotaEuog=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-2.0.1.tgz_1489262957872_0.8242232573684305"}, "publish_time": 1489262958123, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489262958123, "_cnpmcore_publish_time": "2021-12-13T10:31:26.363Z"}, "2.0.0": {"name": "yallist", "version": "2.0.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "702eaba87deefa9f8fc2f8e36cb225bc2141fdc3", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.0.0", "_shasum": "306c543835f09ee1a4cb23b7bce9ab341c91cdd4", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "306c543835f09ee1a4cb23b7bce9ab341c91cdd4", "size": 5553, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-2.0.0.tgz", "integrity": "sha512-l446SSVnGyIspyBekF2U4g2cUMLwBJLu3IvXvqDSwmJFWtAgQ9B8CtHArNluFgI7nPm5+EEbH12fwPIwSXPQyg=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1450554929661, "_hasShrinkwrap": false, "_cnpm_publish_time": 1450554929661, "_cnpmcore_publish_time": "2021-12-13T10:31:26.837Z"}, "1.1.0": {"name": "yallist", "version": "1.1.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "2f324037e84a1ffb140d485e3b18f7e915937132", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@1.1.0", "_shasum": "f1878f30e2955a6da83ac6f179782344cd6ea4fe", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f1878f30e2955a6da83ac6f179782344cd6ea4fe", "size": 5186, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-1.1.0.tgz", "integrity": "sha512-2pmuDf+Bn8FjaPPavD52OKuG+8rsSYkgDTHNXI6+YwRIUGNz7//deuhhy/3ON1jKvc7KExp0dQ8iNlHc6DhKog=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1450485138820, "_hasShrinkwrap": false, "_cnpm_publish_time": 1450485138820, "_cnpmcore_publish_time": "2021-12-13T10:31:27.337Z"}, "1.0.2": {"name": "yallist", "version": "1.0.2", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "56757373ff05e416992e323184946019db198759", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@1.0.2", "_shasum": "2805e1ba9e78bac506a19c4e028d7da6d031adef", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "2805e1ba9e78bac506a19c4e028d7da6d031adef", "size": 4647, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-1.0.2.tgz", "integrity": "sha512-m81cHiLhQWWHr9ewXLJ+zvFnk41LRiDC2+raYPdYRW7ohwC1tWz6xmfdFOxz3AaXQrIjk1aFSsSygfn7V0nCXg=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1450480823258, "_hasShrinkwrap": false, "_cnpm_publish_time": 1450480823258, "_cnpmcore_publish_time": "2021-12-13T10:31:27.804Z"}, "1.0.1": {"name": "yallist", "version": "1.0.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "071c8f329cb87dd996b360955a1a1a5199d1381a", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@1.0.1", "_shasum": "daa8df3166c72bc78c3d307ea8195c1c14a1430c", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "daa8df3166c72bc78c3d307ea8195c1c14a1430c", "size": 4511, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-1.0.1.tgz", "integrity": "sha512-34n0AanmymrvNSUcdwQAQ1RmvRoPkSKHmXR70ln4+2L3JZaKQBXyhxXUY7bRIqPKqb5jgncxFRbdCtTJqCYWDg=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1450430998326, "_hasShrinkwrap": false, "_cnpm_publish_time": 1450430998326, "_cnpmcore_publish_time": "2021-12-13T10:31:28.305Z"}, "1.0.0": {"name": "yallist", "version": "1.0.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "8d387d3d66afcd9045a98c86f320016c4e1fee9c", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@1.0.0", "_shasum": "478c1272847a661519938e3a43b98d00faa12f8a", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "478c1272847a661519938e3a43b98d00faa12f8a", "size": 3876, "noattachment": false, "tarball": "https://registry.npmmirror.com/yallist/-/yallist-1.0.0.tgz", "integrity": "sha512-KNi+WzRCAoXcBT3z+UFoYSubPzBDvm/Jh5GlLaaTdvo/NcJn/0DokB8Oci5xc6N5bVnZoWjS9hsfxJ5Wn35fXA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1450425745789, "_hasShrinkwrap": false, "_cnpm_publish_time": 1450425745789, "_cnpmcore_publish_time": "2021-12-13T10:31:28.841Z"}, "5.0.0": {"name": "yallist", "version": "5.0.0", "description": "Yet Another Linked List", "devDependencies": {"prettier": "^3.2.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn --ignore-path ../../.prettierignore --cache", "typedoc": "typedoc"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BlueOak-1.0.0", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "engines": {"node": ">=18"}, "_id": "yallist@5.0.0", "gitHead": "a082c2dd872bb3cd8fb92359ed66605064957cd4", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==", "shasum": "00e2de443639ed0d78fd87de0d27469fbcffb533", "tarball": "https://registry.npmmirror.com/yallist/-/yallist-5.0.0.tgz", "fileCount": 13, "unpackedSize": 80297, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6e4G4ytvhoms+0w0K9/h69xTaFaNhKrrQtsadvn51egIgFDoo7A3fA8NgnwexxWK9dLURqTrJhW0gluarpuWEJhI="}], "size": 9821}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_5.0.0_1712690504676_0.029932400139861093"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-09T19:21:44.844Z", "publish_time": 1712690504844, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "_source_registry_name": "default"}