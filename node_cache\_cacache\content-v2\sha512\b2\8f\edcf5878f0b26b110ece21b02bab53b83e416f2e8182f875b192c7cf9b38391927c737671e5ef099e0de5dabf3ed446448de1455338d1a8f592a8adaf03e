{"_attachments": {}, "_id": "node-gyp-build", "_rev": "1309-61f1463a4ce7cf8f5825e647", "author": {"name": "<PERSON>", "url": "@mafintosh"}, "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "dist-tags": {"latest": "4.8.4"}, "license": "MIT", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}], "name": "node-gyp-build", "readme": "# node-gyp-build\n\n> Build tool and bindings loader for [`node-gyp`][node-gyp] that supports prebuilds.\n\n```\nnpm install node-gyp-build\n```\n\n[![Test](https://github.com/prebuild/node-gyp-build/actions/workflows/test.yml/badge.svg)](https://github.com/prebuild/node-gyp-build/actions/workflows/test.yml)\n\nUse together with [`prebuildify`][prebuildify] to easily support prebuilds for your native modules.\n\n## Usage\n\n> **Note.** Prebuild names have changed in [`prebuildify@3`][prebuildify] and `node-gyp-build@4`. Please see the documentation below.\n\n`node-gyp-build` works similar to [`node-gyp build`][node-gyp] except that it will check if a build or prebuild is present before rebuilding your project.\n\nIt's main intended use is as an npm install script and bindings loader for native modules that bundle prebuilds using [`prebuildify`][prebuildify].\n\nFirst add `node-gyp-build` as an install script to your native project\n\n``` js\n{\n  ...\n  \"scripts\": {\n    \"install\": \"node-gyp-build\"\n  }\n}\n```\n\nThen in your `index.js`, instead of using the [`bindings`](https://www.npmjs.com/package/bindings) module use `node-gyp-build` to load your binding.\n\n``` js\nvar binding = require('node-gyp-build')(__dirname)\n```\n\nIf you do these two things and bundle prebuilds with [`prebuildify`][prebuildify] your native module will work for most platforms\nwithout having to compile on install time AND will work in both node and electron without the need to recompile between usage.\n\nUsers can override `node-gyp-build` and force compiling by doing `npm install --build-from-source`.\n\nPrebuilds will be attempted loaded from `MODULE_PATH/prebuilds/...` and then next `EXEC_PATH/prebuilds/...` (the latter allowing use with `zeit/pkg`)\n\n## Supported prebuild names\n\nIf so desired you can bundle more specific flavors, for example `musl` builds to support Alpine, or targeting a numbered ARM architecture version.\n\nThese prebuilds can be bundled in addition to generic prebuilds; `node-gyp-build` will try to find the most specific flavor first. Prebuild filenames are composed of _tags_. The runtime tag takes precedence, as does an `abi` tag over `napi`. For more details on tags, please see [`prebuildify`][prebuildify].\n\nValues for the `libc` and `armv` tags are auto-detected but can be overridden through the `LIBC` and `ARM_VERSION` environment variables, respectively.\n\n## License\n\nMIT\n\n[prebuildify]: https://github.com/prebuild/prebuildify\n[node-gyp]: https://www.npmjs.com/package/node-gyp\n", "time": {"created": "2022-01-26T13:01:46.328Z", "modified": "2024-11-19T14:49:52.292Z", "4.3.0": "2021-09-12T08:50:10.962Z", "4.2.3": "2020-07-20T10:30:30.072Z", "4.2.2": "2020-04-25T07:25:48.732Z", "4.2.1": "2020-03-04T12:48:27.664Z", "4.2.0": "2019-11-03T21:02:58.291Z", "4.1.1": "2019-09-06T13:13:56.376Z", "4.1.0": "2019-04-26T14:45:16.253Z", "3.9.0": "2019-04-26T14:44:02.194Z", "4.0.0": "2019-04-26T14:09:40.056Z", "3.8.0": "2019-01-21T11:43:49.629Z", "3.7.0": "2018-12-22T20:08:19.758Z", "3.6.0": "2018-12-10T12:39:13.743Z", "3.5.1": "2018-12-05T09:43:08.079Z", "3.5.0": "2018-10-11T16:11:00.254Z", "3.4.0": "2018-06-24T05:45:56.407Z", "3.3.0": "2018-03-02T17:38:14.266Z", "3.2.2": "2017-07-23T16:30:01.920Z", "3.2.1": "2017-07-23T16:24:53.735Z", "3.2.0": "2017-04-17T19:39:35.213Z", "3.1.0": "2017-02-23T07:17:55.590Z", "3.0.1": "2017-02-22T19:57:25.781Z", "3.0.0": "2017-02-04T02:03:30.125Z", "2.0.2": "2017-02-04T00:43:27.523Z", "2.0.1": "2017-02-04T00:42:22.128Z", "2.0.0": "2017-02-04T00:38:57.266Z", "1.1.1": "2017-02-03T23:01:54.558Z", "1.1.0": "2017-02-02T07:27:16.179Z", "1.0.1": "2017-01-26T16:42:38.327Z", "1.0.0": "2017-01-25T15:20:39.888Z", "4.4.0": "2022-04-01T08:37:39.585Z", "4.5.0": "2022-07-01T09:38:12.390Z", "4.6.0": "2023-01-05T20:34:14.136Z", "4.6.1": "2023-08-25T20:54:47.504Z", "4.7.0": "2023-11-17T10:45:51.439Z", "4.7.1": "2023-11-23T18:54:54.284Z", "4.8.0": "2024-01-07T17:29:08.237Z", "4.8.1": "2024-05-02T14:17:32.333Z", "4.8.2": "2024-08-28T13:07:28.470Z", "4.8.3": "2024-11-12T13:00:25.453Z", "4.8.4": "2024-11-19T14:43:46.572Z"}, "versions": {"4.3.0": {"name": "node-gyp-build", "version": "4.3.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "a443aead7dcc8ec613549956b780e31077e6f4c7", "_id": "node-gyp-build@4.3.0", "_nodeVersion": "12.22.0", "_npmVersion": "6.14.11", "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9f256b03e5826150be39c764bf51e993946d71a3", "size": 5083, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.3.0.tgz", "integrity": "sha512-iWjXZvmboq0ja1pUGULQBexmxq8CV4xBhX7VDOTbL7ZR4FOowwY/VOtRxBN/yKxmdGoIp4j5ysNT4u3S2pDQ3Q=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.3.0_**********806_0.15998875130206236"}, "_hasShrinkwrap": false, "publish_time": **********962, "_cnpm_publish_time": **********962, "_cnpmcore_publish_time": "2021-12-13T12:07:23.491Z"}, "4.2.3": {"name": "node-gyp-build", "version": "4.2.3", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "274bb4a2f345b229deda629a4498267fbbb543c2", "_id": "node-gyp-build@4.2.3", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.5", "dist": {"shasum": "ce6277f853835f718829efb47db20f3e4d9c4739", "size": 4822, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.2.3.tgz", "integrity": "sha512-MN6ZpzmfNCRM+3t57PTJHgHyw/h4OWnZ6mR8P5j/uZtqQr46RRuDE/P+g3n0YR/AiYXeWixZZzaip77gdICfRg=="}, "maintainers": [{"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "vweevers"}], "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.2.3_1595241029916_0.6790816049228807"}, "_hasShrinkwrap": false, "publish_time": 1595241030072, "_cnpm_publish_time": 1595241030072, "_cnpmcore_publish_time": "2021-12-13T12:07:23.827Z"}, "4.2.2": {"name": "node-gyp-build", "version": "4.2.2", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "7e1f184ea5c540d9c69e96362f3ba32ecb7a2e4e", "_id": "node-gyp-build@4.2.2", "_nodeVersion": "12.11.1", "_npmVersion": "6.11.3", "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "maintainers": [{"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "vweevers"}], "dist": {"shasum": "3f44b65adaafd42fb6c3d81afd630e45c847eb66", "size": 4765, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.2.2.tgz", "integrity": "sha512-Lqh7mrByWCM8Cf9UPqpeoVBBo5Ugx+RKu885GAzmLBVYjeywScxHXPGLa4JfYNZmcNGwzR0Glu5/9GaQZMFqyA=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.2.2_1587799548559_0.37843987300060156"}, "_hasShrinkwrap": false, "publish_time": 1587799548732, "_cnpm_publish_time": 1587799548732, "_cnpmcore_publish_time": "2021-12-13T12:07:24.106Z"}, "4.2.1": {"name": "node-gyp-build", "version": "4.2.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^4.10.1"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "f5f31028918bffb872908d066209bf3ef0fcc7b3", "_id": "node-gyp-build@4.2.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"shasum": "f28f0d3d3ab268d48ab76c6f446f19bc3d0db9dc", "size": 4654, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.2.1.tgz", "integrity": "sha512-XyCKXsqZfLqHep1hhsMncoXuUNt/cXCjg1+8CLbu69V1TKuPiOeSGbL9n+k/ByKH8UT0p4rdIX8XkTRZV0i7Sw=="}, "maintainers": [{"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "vweevers"}], "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.2.1_1583326107516_0.9478537115379864"}, "_hasShrinkwrap": false, "publish_time": 1583326107664, "_cnpm_publish_time": 1583326107664, "_cnpmcore_publish_time": "2021-12-13T12:07:24.413Z"}, "4.2.0": {"name": "node-gyp-build", "version": "4.2.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^4.10.1"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "abd0b6411cc7c2c82c8493030d031b14e6cb9a3b", "_id": "node-gyp-build@4.2.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "2c2b05f461f4178641a6ce2d7159f04094e9376d", "size": 4636, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.2.0.tgz", "integrity": "sha512-4oiumOLhCDU9Rronz8PZ5S4IvT39H5+JEv/hps9V8s7RSLhsac0TCP78ulnHXOo8X1wdpPiTayGlM1jr4IbnaQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "vweevers"}], "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.2.0_1572814978150_0.9965618535074023"}, "_hasShrinkwrap": false, "publish_time": 1572814978291, "_cnpm_publish_time": 1572814978291, "_cnpmcore_publish_time": "2021-12-13T12:07:24.783Z"}, "4.1.1": {"name": "node-gyp-build", "version": "4.1.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^4.10.1"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "e18b05e82c47be2c53ad0d362dd4b63233613662", "_id": "node-gyp-build@4.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.1", "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "maintainers": [{"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "vweevers"}], "dist": {"shasum": "d7270b5d86717068d114cc57fff352f96d745feb", "size": 4499, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.1.1.tgz", "integrity": "sha512-dSq1xmcPDKPZ2EED2S6zw/b9NKsqzXRE6dVr8TVQnI3FJOTteUMuqF3Qqs6LZg+mLGYJWqQzMbIjMtJqTv87nQ=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.1.1_1567775636267_0.24241488477198092"}, "_hasShrinkwrap": false, "publish_time": 1567775636376, "_cnpm_publish_time": 1567775636376, "_cnpmcore_publish_time": "2021-12-13T12:07:25.167Z"}, "4.1.0": {"name": "node-gyp-build", "version": "4.1.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^8.6.0", "tape": "^4.10.1"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "0df4855fc7a2564865f90a7e239a32fd94742caa", "_id": "node-gyp-build@4.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3bc3dd7dd4aafecaf64a2e3729e785bc3cdea565", "size": 4507, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.1.0.tgz", "integrity": "sha512-rGLv++nK20BG8gc0MzzcYe1Nl3p3mtwJ74Q2QD0HTEDKZ6NvOFSelY6s2QBPWIHRR8h7hpad0LiwajfClBJfNg=="}, "maintainers": [{"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "vweevers"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.1.0_1556289916134_0.06607094799376423"}, "_hasShrinkwrap": false, "publish_time": 1556289916253, "_cnpm_publish_time": 1556289916253, "_cnpmcore_publish_time": "2021-12-13T12:07:25.582Z"}, "3.9.0": {"name": "node-gyp-build", "version": "3.9.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "801e65dc6bf38e21495ce9c97351ec1e8de5cb68", "_id": "node-gyp-build@3.9.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "53a350187dd4d5276750da21605d1cb681d09e25", "size": 3764, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.9.0.tgz", "integrity": "sha512-zLcTg6P4AbcHPq465ZMFNXx7XpKKJh+7kkN699NiQWisR2uWYOWNWqRHAmbnmKiL4e9aLSlmy5U7rEMUXV59+A=="}, "maintainers": [{"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "vweevers"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_3.9.0_1556289842062_0.7630002835968355"}, "_hasShrinkwrap": false, "publish_time": 1556289842194, "_cnpm_publish_time": 1556289842194, "_cnpmcore_publish_time": "2021-12-13T12:07:25.906Z"}, "4.0.0": {"name": "node-gyp-build", "version": "4.0.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^8.6.0", "tape": "^4.10.1"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "f300deccabcc588b46082b8b3a7b6e9098393758", "_id": "node-gyp-build@4.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.14.0", "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "maintainers": [{"email": "<EMAIL>", "name": "ma<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "vweevers"}], "dist": {"shasum": "6c4ad2d0714860c4ff539bedad568794034f99a0", "size": 4473, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.0.0.tgz", "integrity": "sha512-HDXFunPktjT+Ixuso0gseGq56GKgYwPYJIIVx6pgKIE9NOGJTJd2K0Gwuv8gevg3uKnea5r8vWik79HFs37VmA=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.0.0_1556287779887_0.3098332246125741"}, "_hasShrinkwrap": false, "publish_time": 1556287780056, "_cnpm_publish_time": 1556287780056, "_cnpmcore_publish_time": "2021-12-13T12:07:26.329Z"}, "3.8.0": {"name": "node-gyp-build", "version": "3.8.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "b35200a91a0d701f558c5ac9e4836a1172edf709", "_id": "node-gyp-build@3.8.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0f57efeb1971f404dfcbfab975c284de7c70f14a", "size": 3741, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.8.0.tgz", "integrity": "sha512-bYbpIHyRqZ7sVWXxGpz8QIRug5JZc/hzZH4GbdT9HTZi6WmKCZ8GLvP8OZ9TTiIBvwPFKgtGrlWQSXDAvYdsPw=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_3.8.0_1548071029534_0.341346607232192"}, "_hasShrinkwrap": false, "publish_time": 1548071029629, "_cnpm_publish_time": 1548071029629, "_cnpmcore_publish_time": "2021-12-13T12:07:26.736Z"}, "3.7.0": {"name": "node-gyp-build", "version": "3.7.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "06f368cbe598462d92e072f59d250ce1671988db", "_id": "node-gyp-build@3.7.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "daa77a4f547b9aed3e2aac779eaf151afd60ec8d", "size": 3214, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.7.0.tgz", "integrity": "sha512-L/Eg02Epx6Si2NXmedx+Okg+4UHqmaf3TNcxd50SF9NQGcJaON3AtU++kax69XV7YWz4tUspqZSAsVofhFKG2w=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_3.7.0_1545509299528_0.5562737612352462"}, "_hasShrinkwrap": false, "publish_time": 1545509299758, "_cnpm_publish_time": 1545509299758, "_cnpmcore_publish_time": "2021-12-13T12:07:27.108Z"}, "3.6.0": {"name": "node-gyp-build", "version": "3.6.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "0aa1f356305eacd4a925fe52e470ee29ada5bc42", "_id": "node-gyp-build@3.6.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "484b229d92e3268bcc3bff6ca333ca15883f23f0", "size": 3070, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.6.0.tgz", "integrity": "sha512-4yfIUBKGAjjsgRI50D1U5RF8zgOn+xfV8qmP9zQ078erdxIX6dOPCRb37Vj0nm1yaONuWAJJcWwSZqrt+Fq/MA=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_3.6.0_1544445553563_0.5486025063149882"}, "_hasShrinkwrap": false, "publish_time": 1544445553743, "_cnpm_publish_time": 1544445553743, "_cnpmcore_publish_time": "2021-12-13T12:07:27.479Z"}, "3.5.1": {"name": "node-gyp-build", "version": "3.5.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "dd1730c3b4d33a340c58625bbde9c44334e4ca4d", "_id": "node-gyp-build@3.5.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "15abc0b483f1f031e48a9e2eb3976baa1d0c17f8", "size": 2990, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.5.1.tgz", "integrity": "sha512-AKJ4SyHiYvqwy5P9GaAnxi5IG3HSEPHV/1YDMlBA0vEEmi7qxeeSfKlCAau3XFvAPFR9EV6gvD9p2b0s8ghyww=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_3.5.1_1544002987969_0.9729078865191336"}, "_hasShrinkwrap": false, "publish_time": 1544002988079, "_cnpm_publish_time": 1544002988079, "_cnpmcore_publish_time": "2021-12-13T12:07:27.853Z"}, "3.5.0": {"name": "node-gyp-build", "version": "3.5.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "de43633f7c9ce17b81e3dce2eee66add44ede1e2", "_id": "node-gyp-build@3.5.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "92f7c6517d1f90e2795a1cd49a59c6487b4f1cdb", "size": 2899, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.5.0.tgz", "integrity": "sha512-qjEE8eIWVyqZhkAFUzytGpOGvLHeX5kXBB6MYyTOCPZBrBlsLyXAAzTsp/hWMbVlg8kVpzDJCZZowIrnKpwmqQ=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_3.5.0_1539274260133_0.41293786594149373"}, "_hasShrinkwrap": false, "publish_time": 1539274260254, "_cnpm_publish_time": 1539274260254, "_cnpmcore_publish_time": "2021-12-13T12:07:28.282Z"}, "3.4.0": {"name": "node-gyp-build", "version": "3.4.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "73ba58a3c400f190193f1c63ad50062426b1410c", "_id": "node-gyp-build@3.4.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f8f62507e65f152488b28aac25d04b9d79748cf7", "size": 2875, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.4.0.tgz", "integrity": "sha512-YoviGBJYGrPdLOKDIQB0sKxuKy/EEsxzooNkOZak4vSTKT/qH0Pa6dj3t1MJjEQGsefih61IyHDmO1WW7xOFfw=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_3.4.0_1529819155851_0.9973496763226808"}, "_hasShrinkwrap": false, "publish_time": 1529819156407, "_cnpm_publish_time": 1529819156407, "_cnpmcore_publish_time": "2021-12-13T12:07:28.736Z"}, "3.3.0": {"name": "node-gyp-build", "version": "3.3.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "5d4e43b4bdc637b1e4856c3ffb96a8991215ea6a", "_id": "node-gyp-build@3.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.6.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "df755c8dba1120c7c5e3179bbfd4203aa495e9a3", "size": 2798, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.3.0.tgz", "integrity": "sha512-SNtBzznpPggc7mY8XTfnYBywd9OGN99bwnxGKFqud9erYJMbwnJn6B8HXER2dy8iOYr6Nf2SzBQoJjV8gdM4Nw=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_3.3.0_1520012294210_0.8094816385913077"}, "_hasShrinkwrap": false, "publish_time": 1520012294266, "_cnpm_publish_time": 1520012294266, "_cnpmcore_publish_time": "2021-12-13T12:07:29.235Z"}, "3.2.2": {"name": "node-gyp-build", "version": "3.2.2", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "8d69247eca6f6c00a1437e5093c8d281d3a4ed36", "_id": "node-gyp-build@3.2.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f78a9f84834b1dbd293b0719fee48635d2789c27", "size": 2830, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.2.2.tgz", "integrity": "sha512-t8W/0UqFGl1c+5ORA3NoT3npU+PxWBL9iPhY7ZySSTszodj3RWexmu8niayWBE0v+0DLARvOXsjaAvfmSEQOyQ=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build-3.2.2.tgz_1500827400959_0.8974791367072612"}, "directories": {}, "publish_time": 1500827401920, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500827401920, "_cnpmcore_publish_time": "2021-12-13T12:07:29.681Z"}, "3.2.1": {"name": "node-gyp-build", "version": "3.2.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "86e376c1edb7abbd252d524b67f2930baf2106eb", "_id": "node-gyp-build@3.2.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "70d5eaec1bbb7f852dd5b4b4fcd63c0380cfc94a", "size": 2829, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.2.1.tgz", "integrity": "sha512-q1ecMjkluDz09RBcyc8OHVAUvvULh7Uf5BqUqwGN6J0s2/BEmIYxCqNe48b+tSnXZamMDFvtbdcRGsGAeO/Gmw=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build-3.2.1.tgz_1500827092755_0.6268511624075472"}, "directories": {}, "publish_time": 1500827093735, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500827093735, "_cnpmcore_publish_time": "2021-12-13T12:07:30.119Z"}, "3.2.0": {"name": "node-gyp-build", "version": "3.2.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "f5cd1b754db19c86b3629499a3df00645b5be0a6", "_id": "node-gyp-build@3.2.0", "_shasum": "283be074924aabb9240ce3f880c7ca0c8e7a00dc", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "283be074924aabb9240ce3f880c7ca0c8e7a00dc", "size": 2804, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.2.0.tgz", "integrity": "sha512-5cn9BeZ4zaasdtWQDwsy4cBRkUOsalWSqnaI1acsxuJQgU5iNxDufw4l34T2M5d8IeIjkwnKlbmd7jCUCzt6dg=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-gyp-build-3.2.0.tgz_1492457973156_0.18517498392611742"}, "directories": {}, "publish_time": 1492457975213, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492457975213, "_cnpmcore_publish_time": "2021-12-13T12:07:30.566Z"}, "3.1.0": {"name": "node-gyp-build", "version": "3.1.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "36e6da07e5873a1384bbfa334eb0b077f6bbb5c2", "_id": "node-gyp-build@3.1.0", "_shasum": "e57ade9bc18f04f3afa3581a1dd217385d7e8eae", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e57ade9bc18f04f3afa3581a1dd217385d7e8eae", "size": 2718, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.1.0.tgz", "integrity": "sha512-UTO+47iyf6w/4X8fpA9jGXiWwpoRLHYmz7IYLVvVfo80dCG/+imFPgE5gZk6yVCp8Y55dygjkwHSExzLQ6nrDg=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-gyp-build-3.1.0.tgz_1487834275015_0.49072388745844364"}, "directories": {}, "publish_time": 1487834275590, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487834275590, "_cnpmcore_publish_time": "2021-12-13T12:07:31.051Z"}, "3.0.1": {"name": "node-gyp-build", "version": "3.0.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/node-gyp-build/issues"}, "homepage": "https://github.com/mafintosh/node-gyp-build", "gitHead": "11979c823f2533c0badf8ba1d8f93cfa2e4f1f6c", "_id": "node-gyp-build@3.0.1", "_shasum": "0d9c6046e2f04c539b5a7b5be04e44bf40973b53", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0d9c6046e2f04c539b5a7b5be04e44bf40973b53", "size": 2692, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.0.1.tgz", "integrity": "sha512-ciJ7vHkNqu6csxIbCkEtnROcRCKly8mjDve43fVwpEqd0G2HgP6EdyQAmmZ4egj2eoMOROQVt/8UPxUq6wFzVQ=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-gyp-build-3.0.1.tgz_1487793443752_0.9462499427609146"}, "directories": {}, "publish_time": 1487793445781, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487793445781, "_cnpmcore_publish_time": "2021-12-13T12:07:31.627Z"}, "3.0.0": {"name": "node-gyp-build", "version": "3.0.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/sodium-native.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sodium-native/issues"}, "homepage": "https://github.com/mafintosh/sodium-native", "gitHead": "282b044d2ae115b5f094c3a6417f4425da804442", "_id": "node-gyp-build@3.0.0", "_shasum": "504d55fbec07bffe46035131472ff5e8e4efd860", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "504d55fbec07bffe46035131472ff5e8e4efd860", "size": 2680, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-3.0.0.tgz", "integrity": "sha512-ZAU0ARTF7ekoQ0dZqbrR35a9rOee8JA8DupLB37zJlcP9dUQgnpYXJZuknUV6IcaM9uNwnAbsNqJQmrH5PJaqA=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-gyp-build-3.0.0.tgz_1486173809449_0.20638897991739213"}, "directories": {}, "publish_time": 1486173810125, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486173810125, "_cnpmcore_publish_time": "2021-12-13T12:07:32.126Z"}, "2.0.2": {"name": "node-gyp-build", "version": "2.0.2", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/sodium-native.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sodium-native/issues"}, "homepage": "https://github.com/mafintosh/sodium-native", "gitHead": "a279c1965f134e4e69cbabbd211f58ccf102c261", "_id": "node-gyp-build@2.0.2", "_shasum": "6261a54c5cfc18da77174abd0b690c6f76d1051b", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6261a54c5cfc18da77174abd0b690c6f76d1051b", "size": 2685, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-2.0.2.tgz", "integrity": "sha512-ox8f82kP6o2jNTOdwaDhOyP3Lw7JeV3udHZt0j9GMkoCirleEKoAKVgU1VkpdKMs4jDINeVUTGYl94kKewuAcg=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-gyp-build-2.0.2.tgz_1486169005567_0.1839692727662623"}, "directories": {}, "publish_time": 1486169007523, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486169007523, "_cnpmcore_publish_time": "2021-12-13T12:07:32.694Z"}, "2.0.1": {"name": "node-gyp-build", "version": "2.0.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/sodium-native.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sodium-native/issues"}, "homepage": "https://github.com/mafintosh/sodium-native", "gitHead": "2d66c96c3c3f3e9c3f5a440c7a81e8a40ffd7ddf", "_id": "node-gyp-build@2.0.1", "_shasum": "f1f50bff59285b3c5b71cb2cd485bccc5c577550", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f1f50bff59285b3c5b71cb2cd485bccc5c577550", "size": 2690, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-2.0.1.tgz", "integrity": "sha512-LCqcNjr5BkA5r12/tY8Fg7FL5JtU++lE/yEigucIalAG7PKC9Ul8i0h0gSwft22yx5Y128+StV8cL1rdHX061A=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-gyp-build-2.0.1.tgz_1486168941903_0.42359478888101876"}, "directories": {}, "publish_time": 1486168942128, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486168942128, "_cnpmcore_publish_time": "2021-12-13T12:07:33.211Z"}, "2.0.0": {"name": "node-gyp-build", "version": "2.0.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/sodium-native.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sodium-native/issues"}, "homepage": "https://github.com/mafintosh/sodium-native", "gitHead": "8f21e7b9e81a21ab27b490a017d5e3a3907e432c", "_id": "node-gyp-build@2.0.0", "_shasum": "89bf721ad4120390877190bb1300bccfd7c7a56d", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "89bf721ad4120390877190bb1300bccfd7c7a56d", "size": 2674, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-2.0.0.tgz", "integrity": "sha512-9o25V7VelbspduubDnSwGc+LAnOa1lH4L5DQX0jBzTR/stIPijVJp8hazb3BLNHhEyT1s+7wstfiSVpo2FcSiA=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-gyp-build-2.0.0.tgz_1486168735250_0.1513099770527333"}, "directories": {}, "publish_time": 1486168737266, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486168737266, "_cnpmcore_publish_time": "2021-12-13T12:07:33.700Z"}, "1.1.1": {"name": "node-gyp-build", "version": "1.1.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/sodium-native.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sodium-native/issues"}, "homepage": "https://github.com/mafintosh/sodium-native", "gitHead": "c4a1017bb3bfa6a7dce5cf67a0a9e39b4e6d1e4e", "_id": "node-gyp-build@1.1.1", "_shasum": "0ff2e1a2516630370f259e9590dbcbbc73271c0f", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0ff2e1a2516630370f259e9590dbcbbc73271c0f", "size": 2745, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-1.1.1.tgz", "integrity": "sha512-d9jaC8XQpJ89n44QLCQ7Vlm2lY8xlOClAlZiB2d+nMOt3y2V3wdSNj1VnHhTBOfXigP33H+v2oXtf8nvXybD+g=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-gyp-build-1.1.1.tgz_1486162912746_0.5804845588281751"}, "directories": {}, "publish_time": 1486162914558, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486162914558, "_cnpmcore_publish_time": "2021-12-13T12:07:34.273Z"}, "1.1.0": {"name": "node-gyp-build", "version": "1.1.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/sodium-native.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sodium-native/issues"}, "homepage": "https://github.com/mafintosh/sodium-native", "gitHead": "c11d4e72e9e9a31d9605482fbbcd7cd41dbff0c3", "_id": "node-gyp-build@1.1.0", "_shasum": "e34304f7f086c0ca333eb060266d84e1cdc3130e", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e34304f7f086c0ca333eb060266d84e1cdc3130e", "size": 2734, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-1.1.0.tgz", "integrity": "sha512-Yal2QnJXRTc5Fw1PgI+sTcUu7JgaNUgxc79zR2Tcsptw9oFPueLXco/PY1nEiOegwFaie9qzNrAY/56rz3wZKA=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-gyp-build-1.1.0.tgz_1486020434499_0.6230307039804757"}, "directories": {}, "publish_time": 1486020436179, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486020436179, "_cnpmcore_publish_time": "2021-12-13T12:07:34.819Z"}, "1.0.1": {"name": "node-gyp-build", "version": "1.0.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/sodium-native.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sodium-native/issues"}, "homepage": "https://github.com/mafintosh/sodium-native", "gitHead": "96d9fc530869f906011545120bb205cec94b402d", "_id": "node-gyp-build@1.0.1", "_shasum": "00389de4c41b7055de91a28558ffd488b875de57", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.2", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "00389de4c41b7055de91a28558ffd488b875de57", "size": 2653, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-1.0.1.tgz", "integrity": "sha512-9FVf0j/OTG5JSJj8ikqqUVJ0yBVZlIoMZSEyQW5uPxDSGisCxUYuC3P94aL8UyOXbKRCIW8A3wpkahzpxE85IQ=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-gyp-build-1.0.1.tgz_1485448956506_0.36969083850272"}, "directories": {}, "publish_time": 1485448958327, "_hasShrinkwrap": false, "_cnpm_publish_time": 1485448958327, "_cnpmcore_publish_time": "2021-12-13T12:07:35.460Z"}, "1.0.0": {"name": "node-gyp-build", "version": "1.0.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/sodium-native.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sodium-native/issues"}, "homepage": "https://github.com/mafintosh/sodium-native", "gitHead": "aa325c5af207baccf8ba4acda66115cb1ee5453c", "_id": "node-gyp-build@1.0.0", "_shasum": "5e202323fb01121a179afb0bf6a0c9d182acda44", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.2", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5e202323fb01121a179afb0bf6a0c9d182acda44", "size": 2629, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-1.0.0.tgz", "integrity": "sha512-dgSIbwA52xukNcW+jicojaWOoCd0D7MdAhJ3KzkLXVawfrSxYDVr68IyL7yTJDnT5q71Ewk4oMkeA580lFLVKA=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-gyp-build-1.0.0.tgz_1485357639264_0.6245098535437137"}, "directories": {}, "publish_time": 1485357639888, "_hasShrinkwrap": false, "_cnpm_publish_time": 1485357639888, "_cnpmcore_publish_time": "2021-12-13T12:07:36.256Z"}, "4.4.0": {"name": "node-gyp-build", "version": "4.4.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "2e982977240368f8baed3975a0f3b048999af40e", "_id": "node-gyp-build@4.4.0", "_nodeVersion": "16.13.2", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-amJnQCcgtRVw9SvoebO3BKGESClrfXGCUTX9hSn1OuGQTQBOZmVd0Z0OlecpuRksKvbsUqALE8jls/ErClAPuQ==", "shasum": "42e99687ce87ddeaf3a10b99dc06abc11021f3f4", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.4.0.tgz", "fileCount": 7, "unpackedSize": 12820, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIES9vOsrZLh67+2SSO6X94lSHxKEVqF48si5btTaBO4FAiEAgb8/FJU4QW2Vmcp+KWKfrvOhd6fBRUONCorGdgyiuro="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRrnTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrx+g/+IVur2poHMEmXXqVY1aI8gAGdPZrYaEh6yjmiXLZkUU9cflXC\r\nZ9+AWT8syFISknnv+aS59to670QUz4wyPe7Sn+rE+GVoXZS5RziwY06AHkvJ\r\nYxV6rQDLIwXIK6eLTdm/OrJDJp4vLPgVbMIDRKRhY2x+U/8tiY2zDJr2JT1/\r\nEjZZJYOiGc7T/0diLeZRd2BrIWU5FjhFoI1qut4eaY6ng7cMklvzxdDpY3TA\r\nz/JQatVAlqFhh1gmZCg6sqmqWir6OLYkvumWAMgaXTAKiEYysyp8Ko2YkrCx\r\nvrJesPnuhOB4y7cVS8Y0C7DUW1KCcMMRAxY/C1pgksFodfjTZxIy4dYCii+X\r\nZB+cDPWSWMLBopd1WeNJ53fhs0CfnfPQm4CorykbKZKPwAJOydnyz0Vxwtv+\r\nzY/E3Xje035p5klgnVcb+2k85tPAIthQ91W8xSTXhkECvJOayAmbJGGUahcy\r\nVSa4DXSTKCkcGxMmDfZhiA2ipkOr9+4kVOjMWZtTXUVndQ/FOoZsFNkIIm+8\r\n4d9EunAAspEjBWRA8nFQyMQiK+6PJGhlH3o8FHKXor24ShsUwJ6hrn8O6GqR\r\nCDISKkA5zYx2qaCnDiAwbkpr4Ob6T6+H5b013GOGJnHU63aBHcljJSbnaOj+\r\ngkhIsCeQlIm878pFbeVzPQPzcrRUcDj9xto=\r\n=fOrr\r\n-----END PGP SIGNATURE-----\r\n", "size": 5120}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.4.0_1648802259410_0.5298043825925338"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-01T08:51:09.895Z"}, "4.5.0": {"name": "node-gyp-build", "version": "4.5.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "0078d7884dfeb0e708477b3e20bfc4f520c1e2f8", "_id": "node-gyp-build@4.5.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-2iGbaQBV+ITgCz76ZEjmhUKAKVf7xfY1sRl4UiKQspfZMH2h06SyhNsnSVy50cwkFQDGLyif6m/6uFXHkOZ6rg==", "shasum": "7a64eefa0b21112f89f58379da128ac177f20e40", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.5.0.tgz", "fileCount": 7, "unpackedSize": 12886, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7Rul/W+Wwr/pUsTGr+Dw+/exCG79WPgK/WBVrPG8TwAIgKoF6yAd96wXztbNpyh1tIQpQ7a4IWUyVkIG/738U/bs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivsCEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzYhAAnWGjGByQsNbNoosRrV4/INvGuSRRoZuwigv5+jvVDANgt7zm\r\n5xYNsONcta9KjQ6CeviF2nIAvVHsK1SBwXYhkeGppT7G+2zBxDEpS37xX6bY\r\n17IJaH7m/nnCl8xqneFOwM/TC7JOaWqbdeiTy7YYS+n1OC4ZbihaXITFXqj7\r\n9LqEkb2tHP92NjXeoe3sKazcJ480p9dkFVhnGLb1J4ESg60IBvLMJI56pd4z\r\nSSfFFlh1QKGl8aLKurS/6txr+mBwefEmZ0dpbCqkmS/DATIyG2eykaiNLHD3\r\n3Qhy81Pp3wo3gRjFKXPNryI/4wfwxtG/FUZd4v8LbRD8HK46eKiBSnTf0nkw\r\ndmVm6Ab8TMvd/llDfHTUq7XO2K9AK/y6EjhQ7v91/FK+ewEejKZpvUKExjxm\r\neCNGr64XanbBf1J1DWUEVNZo527TLFm7fTVGDc39dXN8j9hM4W8TbKX7w/bG\r\nnVEZlOn/9FqFrVP1zAPb7AK9ded27+tt5pleww1dDsCmmlccTo52tlNEkW/c\r\n9b1s2Eb5+V1ofGuM8RQJwn6t+FnQNcJg4dPfToMwZHnVKTTDQn7aKMSzinSX\r\njseIDy91dgk7eFc0Et18bgbX6tNsUkzT6wIeJddWizcavUHVOfDE8BmLP+uC\r\nktMbKs4Q1DqcITI6DcedM0LCTni0KjaLG6k=\r\n=rZnI\r\n-----END PGP SIGNATURE-----\r\n", "size": 5108}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.5.0_1656668292201_0.35804918465033464"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-02T01:48:00.029Z"}, "4.6.0": {"name": "node-gyp-build", "version": "4.6.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "da9976b5d0c874ee384461ce48a8243cc646e429", "_id": "node-gyp-build@4.6.0", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-NTZVKn9IylLwUzaKjkas1e4u2DLNcV4rdYagA4PWdPwW87Bi7z+BznyKSRwS/761tV/lzCGXplWsiaMjLqP2zQ==", "shasum": "0c52e4cbf54bbd28b709820ef7b6a3c2d6209055", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.6.0.tgz", "fileCount": 8, "unpackedSize": 13149, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8+gcENjNxwpKxvCmcYy1bhhbu5L86P6pfLco8QG5l/QIhAJSRuEjwEwI7/AjjUeGHxYuce5zQO+V3sYwRhyDYAuaq"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtzRGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdrA//UMkLlQEeaAzsdo0tnxvuhxE+ziCeYd8b4CBWNyzMWDa2onKm\r\nLddPfGnoe6hozHxHCK0YnD+iP1PwQ3F68zxC1tPIds1lVXxXfh3E8kvYPQNh\r\nAGcBzs3Ct/djVbAoOS+da6qpWMIztJ3uc+VTh5mJEQXm1+LmXYNCyOOiqaIr\r\n6B++lPNxqhIQrXMQHeAJuCBhQ55/67NLNP6d8hrCA2PnjBsEUw1X6oDXblrV\r\nJfKE5K83FXfEvHxoWdKpRYTk6SdZ2FqTTZxAMEByu963PFqmN2zjZLIFVJPW\r\n3SWxOOvG9mtc/4fwp8952qEkNTAQ+dj3MqdrEH3HkmNBWHF5NJC2AXcNzPNC\r\nZyxdMtvBefAW26i5yt8B6FoGr15Y62ksKiDlk2CNCMB2w1O9VwymtU3N4yj/\r\nUHcZ4jNiLc7DNEFG8hN7sx9R7R7OVjqZ86pXpOpRSDSiNoGDbkDwlP+ywwEh\r\n/Ga9e45fy0CMEWXS47oukUz+UehAXqcXT0STO5LB8Hxqc+vDqhdYnp9cet6c\r\nQqwfj75uRyJxCjSuRSvVuoQ5eAFN5JPkEZzsJdUiiCZmASOqiTgBPTfe7b3K\r\ns0fhh0UyCfiwJRJWEkO3wgAKPS8vnVDs7bJtz0KbB1iss3G+86JW+jm0nDaq\r\nVZmhjHmZnZ10K7JRxfMBbzzjiJL/zCx+dVo=\r\n=CGdQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 5217}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.6.0_1672950853962_0.36679191388435295"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-05T20:34:27.196Z"}, "4.6.1": {"name": "node-gyp-build", "version": "4.6.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "gitHead": "8419abba399ec01f28cfb02b207b659153052a69", "_id": "node-gyp-build@4.6.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-24vnklJmyRS8ViBNI8KbtK/r/DmXQMRiOMXTNz2nrTnAYUwjmEEbnnpB/+kt+yWRv73bPsSPRFddrcIbAxSiMQ==", "shasum": "24b6d075e5e391b8d5539d98c7fc5c210cac8a3e", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.6.1.tgz", "fileCount": 8, "unpackedSize": 13207, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+uObiUGK+vu3iOgrczZfDd1tMmj5ScAj2M7opoaAgTAiEAyhuY2Oy2XYo58G4m8gAuoF4rLjF6dM20JiQ6LLaxoUg="}], "size": 5229}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.6.1_1692996887272_0.08343152112087737"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-25T20:54:47.504Z", "publish_time": 1692996887504, "_source_registry_name": "default"}, "4.7.0": {"name": "node-gyp-build", "version": "4.7.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "_id": "node-gyp-build@4.7.0", "gitHead": "2f0695e156e584046872ad778460aceb7d272d49", "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-PbZERfeFdrHQOOXiAKOY0VPbykZy90ndPKk0d+CFDegTKmWp1VgOTz2xACVbr1BjCWxrQp68CXtvNsveFhqDJg==", "shasum": "749f0033590b2a89ac8edb5e0775f95f5ae86d15", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.7.0.tgz", "fileCount": 8, "unpackedSize": 13207, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCm8JcnR2ivE6iU8w9YBrC7MIHdvR3yas4c4QLTN9l5vwIhAKpYmK3VVbDa8COwX/I8rrNL2QlwGbOgSs8fTTAMrQlB"}], "size": 5234}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.7.0_1700217951226_0.7337036739802743"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-17T10:45:51.439Z", "publish_time": 1700217951439, "_source_registry_name": "default"}, "4.7.1": {"name": "node-gyp-build", "version": "4.7.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "_id": "node-gyp-build@4.7.1", "gitHead": "da611757e8b0e99e61d725e29805ed9ca8ec2f4b", "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-wTSrZ+8lsRRa3I3H8Xr65dLWSgCvY2l4AOnaeKdPA9TB/WYMPaTcrzf3rXvFoVvjKNVnu0CcWSx54qq9GKRUYg==", "shasum": "cd7d2eb48e594874053150a9418ac85af83ca8f7", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.7.1.tgz", "fileCount": 8, "unpackedSize": 13352, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/fq7F5Rb8/rd2AMlGSVYbCQvyt5ueBQAUxSic4R+gbAiB4+iiG9GcTh09ZjKsC8LyDxNKag3SQJQeLTnxKGpSTJw=="}], "size": 5242}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.7.1_1700765694081_0.7450088928942473"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-11-23T18:54:54.284Z", "publish_time": 1700765694284, "_source_registry_name": "default"}, "4.8.0": {"name": "node-gyp-build", "version": "4.8.0", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "_id": "node-gyp-build@4.8.0", "gitHead": "f891aa5f1033d1a3f043398157802098898a86b0", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-u6fs2AEUljNho3EYTJNBfImO5QTo/J/1Etd+NVdCj7qWKUSN/bSLkZwhDv7I+w/MSC6qJ4cknepkAYykDdK8og==", "shasum": "3fee9c1731df4581a3f9ead74664369ff00d26dd", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.8.0.tgz", "fileCount": 8, "unpackedSize": 13380, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8c5Ncvfxj8NJbfWWlBc3MfGi6IzN8PPwW8mZ0FeMQVAIhAJG/GCIO9iqevomsmbHYURWazkvbotf6XLNlaBt2N9g2"}], "size": 5266}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.8.0_1704648548068_0.354618887666708"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-07T17:29:08.237Z", "publish_time": 1704648548237, "_source_registry_name": "default"}, "4.8.1": {"name": "node-gyp-build", "version": "4.8.1", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "_id": "node-gyp-build@4.8.1", "gitHead": "86e2e0e440632eb259ea4eec23cc00b364cc001c", "_nodeVersion": "20.11.1", "_npmVersion": "10.5.2", "dist": {"integrity": "sha512-OSs33Z9yWr148JZcbZd5WiAXhh/n9z8TxQcdMhIOlpN9AhWpLfvVFO73+m77bBABQMaY9XSvIa+qk0jlI7Gcaw==", "shasum": "976d3ad905e71b76086f4f0b0d3637fe79b6cda5", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.8.1.tgz", "fileCount": 8, "unpackedSize": 13408, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDE2hdYjnIBeA4YoKPUIgdKg7e/1W8AtoC0kAR8QLfI/wIhAOVV7WLnHpT9gsW2glBUZQJTDZ+CNyZoDye1i1q6QyWx"}], "size": 5252}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.8.1_1714659452180_0.06646828157662976"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-02T14:17:32.333Z", "publish_time": 1714659452333, "_source_registry_name": "default"}, "4.8.2": {"name": "node-gyp-build", "version": "4.8.2", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "_id": "node-gyp-build@4.8.2", "gitHead": "2c8bd26db01a6397a68ba29201b165042c51e1c0", "_nodeVersion": "20.11.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-IRUxE4BVsHWXkV/SFOut4qTlagw2aM8T5/vnTsmrHJvVoKueJHRc/JaFND7QDDc61kLYUJ6qlZM3sqTSyx2dTw==", "shasum": "4f802b71c1ab2ca16af830e6c1ea7dd1ad9496fa", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.8.2.tgz", "fileCount": 9, "unpackedSize": 13632, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4ldOP0tmoVsHCji7gI6prJ6i7Agn1K3ec+KzTtLDgcgIhAMY7pJgCTpGaHaKU0KTOH2QNaZreVv4tCgaoGPq/zeT0"}], "size": 5389}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.8.2_1724850448308_0.1279931827479166"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-28T13:07:28.470Z", "publish_time": 1724850448470, "_source_registry_name": "default"}, "4.8.3": {"name": "node-gyp-build", "version": "4.8.3", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "exports": {".": {"bare": "./bare.js", "default": "./index.js"}, "./package": "./package.json"}, "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "_id": "node-gyp-build@4.8.3", "gitHead": "8f2180d036e9f1015b1b92029f5ea632a6b827db", "_nodeVersion": "20.11.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-EMS95CMJzdoSKoIiXo8pxKoL8DYxwIZXYlLmgPb8KUv794abpnLK6ynsCAWNliOjREKruYKdzbh76HHYUHX7nw==", "shasum": "9187216d24dbee29e44eb20d2ebf62a296bbea1a", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.8.3.tgz", "fileCount": 10, "unpackedSize": 13806, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEFh/AI3NlRuhqOKy+8Y3DhYw7UHdB/syr6FLmIh/R2sAiBS3GD9Lud23mnDF/DOBlz17n4FbFoFr168kmW2SgJn2Q=="}], "size": 5464}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.8.3_1731416425253_0.21864101244385536"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-12T13:00:25.453Z", "publish_time": 1731416425453, "_source_registry_name": "default"}, "4.8.4": {"name": "node-gyp-build", "version": "4.8.4", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "imports": {"fs": {"bare": "builtin:fs", "default": "fs"}, "path": {"bare": "builtin:path", "default": "path"}, "os": {"bare": "builtin:os", "default": "os"}}, "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "_id": "node-gyp-build@4.8.4", "gitHead": "6822ec52423a2b3ed48ef8960a9fe05902e9e1a3", "_nodeVersion": "20.11.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-LA4ZjwlnUblHVgq0oBF3Jl/6h/Nvs5fzBLwdEF4nuxnFdsfajde4WfxtJr3CaiH+F6ewcIB/q4jQ4UzPyid+CQ==", "shasum": "8a70ee85464ae52327772a90d66c6077a900cfc8", "tarball": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.8.4.tgz", "fileCount": 9, "unpackedSize": 13864, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBiK/mZ8tfA7fOlZaoUetv0lk8ECOjQn9wFP+1AMSayAAiBiD485HHqIt2sfZz2UWFhgRS5aOO/voFCmFOgQ4dl3IQ=="}], "size": 5449}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-gyp-build_4.8.4_1732027426407_0.2015201210117592"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-19T14:43:46.572Z", "publish_time": 1732027426572, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build", "repository": {"type": "git", "url": "git+https://github.com/prebuild/node-gyp-build.git"}, "_source_registry_name": "default"}