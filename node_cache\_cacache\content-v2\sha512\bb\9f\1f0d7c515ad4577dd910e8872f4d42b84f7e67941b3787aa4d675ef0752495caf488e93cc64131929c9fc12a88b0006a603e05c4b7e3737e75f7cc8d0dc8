{"_attachments": {}, "_id": "mkdirp", "_rev": "247-61f14433830fd08f52a1cd60", "description": "Recursively mkdir, like `mkdir -p`", "dist-tags": {"latest": "3.0.1", "legacy": "0.5.6"}, "license": "MIT", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "mkdirp", "readme": "# mkdirp\n\nLike `mkdir -p`, but in Node.js!\n\nNow with a modern API and no\\* bugs!\n\n<small>\\* may contain some bugs</small>\n\n# example\n\n## pow.js\n\n```js\n// hybrid module, import or require() both work\nimport { mkdirp } from 'mkdirp'\n// or:\nconst { mkdirp } = require('mkdirp')\n\n// return value is a Promise resolving to the first directory created\nmkdirp('/tmp/foo/bar/baz').then(made =>\n  console.log(`made directories, starting with ${made}`)\n)\n```\n\nOutput (where `/tmp/foo` already exists)\n\n```\nmade directories, starting with /tmp/foo/bar\n```\n\nOr, if you don't have time to wait around for promises:\n\n```js\nimport { mkdirp } from 'mkdirp'\n\n// return value is the first directory created\nconst made = mkdirp.sync('/tmp/foo/bar/baz')\nconsole.log(`made directories, starting with ${made}`)\n```\n\nAnd now /tmp/foo/bar/baz exists, huzzah!\n\n# methods\n\n```js\nimport { mkdirp } from 'mkdirp'\n```\n\n## `mkdirp(dir: string, opts?: MkdirpOptions) => Promise<string | undefined>`\n\nCreate a new directory and any necessary subdirectories at `dir`\nwith octal permission string `opts.mode`. If `opts` is a string\nor number, it will be treated as the `opts.mode`.\n\nIf `opts.mode` isn't specified, it defaults to `0o777`.\n\nPromise resolves to first directory `made` that had to be\ncreated, or `undefined` if everything already exists. Promise\nrejects if any errors are encountered. Note that, in the case of\npromise rejection, some directories _may_ have been created, as\nrecursive directory creation is not an atomic operation.\n\nYou can optionally pass in an alternate `fs` implementation by\npassing in `opts.fs`. Your implementation should have\n`opts.fs.mkdir(path, opts, cb)` and `opts.fs.stat(path, cb)`.\n\nYou can also override just one or the other of `mkdir` and `stat`\nby passing in `opts.stat` or `opts.mkdir`, or providing an `fs`\noption that only overrides one of these.\n\n## `mkdirp.sync(dir: string, opts: MkdirpOptions) => string|undefined`\n\nSynchronously create a new directory and any necessary\nsubdirectories at `dir` with octal permission string `opts.mode`.\nIf `opts` is a string or number, it will be treated as the\n`opts.mode`.\n\nIf `opts.mode` isn't specified, it defaults to `0o777`.\n\nReturns the first directory that had to be created, or undefined\nif everything already exists.\n\nYou can optionally pass in an alternate `fs` implementation by\npassing in `opts.fs`. Your implementation should have\n`opts.fs.mkdirSync(path, mode)` and `opts.fs.statSync(path)`.\n\nYou can also override just one or the other of `mkdirSync` and\n`statSync` by passing in `opts.statSync` or `opts.mkdirSync`, or\nproviding an `fs` option that only overrides one of these.\n\n## `mkdirp.manual`, `mkdirp.manualSync`\n\nUse the manual implementation (not the native one). This is the\ndefault when the native implementation is not available or the\nstat/mkdir implementation is overridden.\n\n## `mkdirp.native`, `mkdirp.nativeSync`\n\nUse the native implementation (not the manual one). This is the\ndefault when the native implementation is available and\nstat/mkdir are not overridden.\n\n# implementation\n\nOn Node.js v10.12.0 and above, use the native `fs.mkdir(p,\n{recursive:true})` option, unless `fs.mkdir`/`fs.mkdirSync` has\nbeen overridden by an option.\n\n## native implementation\n\n- If the path is a root directory, then pass it to the underlying\n  implementation and return the result/error. (In this case,\n  it'll either succeed or fail, but we aren't actually creating\n  any dirs.)\n- Walk up the path statting each directory, to find the first\n  path that will be created, `made`.\n- Call `fs.mkdir(path, { recursive: true })` (or `fs.mkdirSync`)\n- If error, raise it to the caller.\n- Return `made`.\n\n## manual implementation\n\n- Call underlying `fs.mkdir` implementation, with `recursive:\nfalse`\n- If error:\n  - If path is a root directory, raise to the caller and do not\n    handle it\n  - If ENOENT, mkdirp parent dir, store result as `made`\n  - stat(path)\n    - If error, raise original `mkdir` error\n    - If directory, return `made`\n    - Else, raise original `mkdir` error\n- else\n  - return `undefined` if a root dir, or `made` if set, or `path`\n\n## windows vs unix caveat\n\nOn Windows file systems, attempts to create a root directory (ie,\na drive letter or root UNC path) will fail. If the root\ndirectory exists, then it will fail with `EPERM`. If the root\ndirectory does not exist, then it will fail with `ENOENT`.\n\nOn posix file systems, attempts to create a root directory (in\nrecursive mode) will succeed silently, as it is treated like just\nanother directory that already exists. (In non-recursive mode,\nof course, it fails with `EEXIST`.)\n\nIn order to preserve this system-specific behavior (and because\nit's not as if we can create the parent of a root directory\nanyway), attempts to create a root directory are passed directly\nto the `fs` implementation, and any errors encountered are not\nhandled.\n\n## native error caveat\n\nThe native implementation (as of at least Node.js v13.4.0) does\nnot provide appropriate errors in some cases (see\n[nodejs/node#31481](https://github.com/nodejs/node/issues/31481)\nand\n[nodejs/node#28015](https://github.com/nodejs/node/issues/28015)).\n\nIn order to work around this issue, the native implementation\nwill fall back to the manual implementation if an `ENOENT` error\nis encountered.\n\n# choosing a recursive mkdir implementation\n\nThere are a few to choose from! Use the one that suits your\nneeds best :D\n\n## use `fs.mkdir(path, {recursive: true}, cb)` if:\n\n- You wish to optimize performance even at the expense of other\n  factors.\n- You don't need to know the first dir created.\n- You are ok with getting `ENOENT` as the error when some other\n  problem is the actual cause.\n- You can limit your platforms to Node.js v10.12 and above.\n- You're ok with using callbacks instead of promises.\n- You don't need/want a CLI.\n- You don't need to override the `fs` methods in use.\n\n## use this module (mkdirp 1.x or 2.x) if:\n\n- You need to know the first directory that was created.\n- You wish to use the native implementation if available, but\n  fall back when it's not.\n- You prefer promise-returning APIs to callback-taking APIs.\n- You want more useful error messages than the native recursive\n  mkdir provides (at least as of Node.js v13.4), and are ok with\n  re-trying on `ENOENT` to achieve this.\n- You need (or at least, are ok with) a CLI.\n- You need to override the `fs` methods in use.\n\n## use [`make-dir`](http://npm.im/make-dir) if:\n\n- You do not need to know the first dir created (and wish to save\n  a few `stat` calls when using the native implementation for\n  this reason).\n- You wish to use the native implementation if available, but\n  fall back when it's not.\n- You prefer promise-returning APIs to callback-taking APIs.\n- You are ok with occasionally getting `ENOENT` errors for\n  failures that are actually related to something other than a\n  missing file system entry.\n- You don't need/want a CLI.\n- You need to override the `fs` methods in use.\n\n## use mkdirp 0.x if:\n\n- You need to know the first directory that was created.\n- You need (or at least, are ok with) a CLI.\n- You need to override the `fs` methods in use.\n- You're ok with using callbacks instead of promises.\n- You are not running on Windows, where the root-level ENOENT\n  errors can lead to infinite regress.\n- You think vinyl just sounds warmer and richer for some weird\n  reason.\n- You are supporting truly ancient Node.js versions, before even\n  the advent of a `Promise` language primitive. (Please don't.\n  You deserve better.)\n\n# cli\n\nThis package also ships with a `mkdirp` command.\n\n```\n$ mkdirp -h\n\nusage: mkdirp [DIR1,DIR2..] {OPTIONS}\n\n  Create each supplied directory including any necessary parent directories\n  that don't yet exist.\n\n  If the directory already exists, do nothing.\n\nOPTIONS are:\n\n  -m<mode>       If a directory needs to be created, set the mode as an octal\n  --mode=<mode>  permission string.\n\n  -v --version   Print the mkdirp version number\n\n  -h --help      Print this helpful banner\n\n  -p --print     Print the first directories created for each path provided\n\n  --manual       Use manual implementation, even if native is available\n```\n\n# install\n\nWith [npm](http://npmjs.org) do:\n\n```\nnpm install mkdirp\n```\n\nto get the library locally, or\n\n```\nnpm install -g mkdirp\n```\n\nto get the command everywhere, or\n\n```\nnpx mkdirp ...\n```\n\nto run the command without installing it globally.\n\n# platform support\n\nThis module works on node v8, but only v10 and above are officially\nsupported, as Node v8 reached its LTS end of life 2020-01-01, which is in\nthe past, as of this writing.\n\n# license\n\nMIT\n", "time": {"created": "2022-01-26T12:53:07.695Z", "modified": "2024-05-25T08:37:32.598Z", "0.5.5": "2020-04-03T17:12:26.436Z", "1.0.4": "2020-04-03T17:03:08.825Z", "0.5.4": "2020-03-23T17:53:03.257Z", "0.5.3": "2020-03-17T16:28:33.897Z", "0.5.2": "2020-03-17T16:27:28.746Z", "1.0.3": "2020-01-24T21:00:54.556Z", "1.0.2": "2020-01-24T20:22:50.327Z", "1.0.1": "2020-01-24T20:20:30.453Z", "1.0.0": "2020-01-24T20:18:42.630Z", "0.5.1": "2015-05-14T02:27:01.553Z", "0.5.0": "2014-05-06T02:28:23.769Z", "0.4.2": "2014-05-06T01:40:35.608Z", "0.4.1": "2014-05-04T02:20:59.368Z", "0.4.0": "2014-04-22T22:21:21.058Z", "0.3.5": "2013-02-22T11:44:25.486Z", "0.3.4": "2012-08-20T12:27:24.983Z", "0.3.3": "2012-06-05T15:54:31.889Z", "0.3.2": "2012-04-30T08:52:12.424Z", "0.3.1": "2012-03-31T19:51:38.912Z", "0.3.0": "2012-01-20T02:20:42.042Z", "0.2.2": "2012-01-08T05:02:35.484Z", "0.2.1": "2011-11-16T09:26:51.089Z", "0.2.0": "2011-11-16T05:32:17.036Z", "0.1.0": "2011-11-06T06:32:23.379Z", "0.0.7": "2011-09-10T22:50:08.879Z", "0.0.6": "2011-08-20T21:37:10.730Z", "0.0.5": "2011-06-29T18:22:05.839Z", "0.0.4": "2011-06-29T00:28:32.272Z", "0.0.3": "2011-06-20T04:02:44.361Z", "0.0.2": "2011-02-14T20:11:44.988Z", "0.0.1": "2011-01-06T02:54:36.496Z", "0.5.6": "2022-03-22T23:48:15.108Z", "2.0.0": "2023-01-16T04:21:36.027Z", "2.1.0": "2023-01-17T00:03:01.053Z", "2.1.1": "2023-01-17T00:10:26.292Z", "2.1.2": "2023-01-17T14:42:19.058Z", "2.1.3": "2023-01-17T22:18:31.249Z", "2.1.4": "2023-03-01T20:14:21.010Z", "2.1.5": "2023-03-05T05:41:12.836Z", "2.1.6": "2023-03-22T18:50:34.833Z", "3.0.0": "2023-04-09T22:02:16.078Z", "3.0.1": "2023-04-24T17:27:38.604Z"}, "versions": {"0.5.5": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.5.5", "publishConfig": {"tag": "legacy"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "index.js", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git+https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"mock-fs": "^3.7.0", "tap": "^5.4.2"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "readmeFilename": "readme.markdown", "gitHead": "049cf185c9e91727bc505b796a2d16a4fe70d64d", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp#readme", "_id": "mkdirp@0.5.5", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "d91cefd62d1436ca0f41620e251288d420099def", "size": 3018, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.5.tgz", "integrity": "sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_0.5.5_1585933946291_0.6504650567992927"}, "_hasShrinkwrap": false, "publish_time": 1585933946436, "_cnpm_publish_time": 1585933946436, "_cnpmcore_publish_time": "2021-12-13T06:49:19.888Z"}, "1.0.4": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "1.0.4", "main": "index.js", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.7"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "b694079b54a0a59ef150c54b29c7c24e90d642f5", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@1.0.4", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "3eb5ed62622756d79a5f0e2a221dfebad75c2f7e", "size": 6665, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw=="}, "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_1.0.4_1585933388669_0.38878898110354276"}, "_hasShrinkwrap": false, "publish_time": 1585933388825, "_cnpm_publish_time": 1585933388825, "_cnpmcore_publish_time": "2021-12-13T06:49:20.112Z"}, "0.5.4": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.5.4", "publishConfig": {"tag": "legacy"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "index.js", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git+https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"mock-fs": "^3.7.0", "tap": "^5.4.2"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "readmeFilename": "readme.markdown", "gitHead": "42a012cc6dbd4648790f380df88190bb697dbb9c", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp#readme", "_id": "mkdirp@0.5.4", "_nodeVersion": "13.10.1", "_npmVersion": "6.13.7", "dist": {"shasum": "fd01504a6797ec5c9be81ff43d204961ed64a512", "size": 3033, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.4.tgz", "integrity": "sha512-iG9AK/dJLtJ0XNgTuDbSyNS3zECqDlAhnQW4CsNxBG3LQJBbHmRX1egw39DmtOdCAqY+dKXV+sgPgilNWUKMVw=="}, "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_0.5.4_1584985983142_0.3659078906393478"}, "_hasShrinkwrap": false, "publish_time": 1584985983257, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1584985983257, "_cnpmcore_publish_time": "2021-12-13T06:49:20.402Z"}, "0.5.3": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.5.3", "publishConfig": {"tag": "legacy"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "index.js", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git+https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"mock-fs": "^3.7.0", "tap": "^5.4.2"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "readmeFilename": "readme.markdown", "gitHead": "d784e70d1eb3fc73bcda52f22f57ec55c00c2525", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp#readme", "_id": "mkdirp@0.5.3", "_nodeVersion": "13.10.1", "_npmVersion": "6.13.7", "dist": {"shasum": "5a514b7179259287952881e94410ec5465659f8c", "size": 3021, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.3.tgz", "integrity": "sha512-P+2gwrFqx8lhew375MQHHeTlY8AuOJSrGf0R5ddkEndUkmwpgUob/vQuBD1V22/Cw1/lJr4x+EjllSezBThzBg=="}, "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_0.5.3_1584462513748_0.0257106127685085"}, "_hasShrinkwrap": false, "publish_time": 1584462513897, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1584462513897, "_cnpmcore_publish_time": "2021-12-13T06:49:20.689Z"}, "0.5.2": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.5.2", "publishConfig": {"tag": "legacy"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "index.js", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git+https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"mock-fs": "^3.7.0", "tap": "^5.4.2"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "readmeFilename": "readme.markdown", "gitHead": "b2e7ba0dd8ac7029735969c5a6062d49e839b30d", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp#readme", "_id": "mkdirp@0.5.2", "_nodeVersion": "13.10.1", "_npmVersion": "6.13.7", "dist": {"shasum": "2e7138d794dfbd097d74c84c410c3edd9eec479f", "size": 32354, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.2.tgz", "integrity": "sha512-jczt4BrifxW743wRHJ05AnqIF52sDrHCAjTO66cFQStG1/jHMLFSGdAa3Rec21jdEObaPugcXfbh6Ammt2VQsw=="}, "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_0.5.2_1584462448629_0.04027690349544977"}, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "publish_time": 1584462448746, "_cnpm_publish_time": 1584462448746, "_cnpmcore_publish_time": "2021-12-13T06:49:21.069Z"}, "1.0.3": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "1.0.3", "main": "index.js", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "9f29fc8c09b806cdb88e1f6699b31d7969b510dc", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@1.0.3", "_nodeVersion": "13.4.0", "_npmVersion": "6.13.6", "dist": {"shasum": "4cf2e30ad45959dddea53ad97d518b6c8205e1ea", "size": 6672, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.3.tgz", "integrity": "sha512-6uCP4Qc0sWsgMLy1EOqqS/3rjDHOEnsStVr/4vtAIK2Y5i2kA7lFFejYrpIyiN9w0pYf4ckeCYT9f1r1P9KX5g=="}, "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_1.0.3_1579899654395_0.05416104013537226"}, "_hasShrinkwrap": false, "publish_time": 1579899654556, "_cnpm_publish_time": 1579899654556, "_cnpmcore_publish_time": "2021-12-13T06:49:21.390Z"}, "1.0.2": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "1.0.2", "main": "index.js", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "6a061db3ded59fb867557116e6c5070e6916e557", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@1.0.2", "_nodeVersion": "13.4.0", "_npmVersion": "6.13.6", "dist": {"shasum": "5ccd93437619ca7050b538573fc918327eba98fb", "size": 6667, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.2.tgz", "integrity": "sha512-N2REVrJ/X/jGPfit2d7zea2J1pf7EAR5chIUcfHffAZ7gmlam5U65sAm76+o4ntQbSRdTjYf7qZz3chuHlwXEA=="}, "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_1.0.2_1579897370197_0.3887508695089861"}, "_hasShrinkwrap": false, "publish_time": 1579897370327, "_cnpm_publish_time": 1579897370327, "_cnpmcore_publish_time": "2021-12-13T06:49:21.748Z"}, "1.0.1": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "1.0.1", "main": "index.js", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "ab8b7548c444aab9b883f83f23806a6ff641ed72", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@1.0.1", "_nodeVersion": "13.4.0", "_npmVersion": "6.13.6", "dist": {"shasum": "57828231711628a8a303f455bb7d79b32d9c9d4f", "size": 6662, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.1.tgz", "integrity": "sha512-epSaAwmT1sXxKNHFtzxrNEepE0qneey1uVReE15tN3Zvba3ifzx01eStvvvyb8dQeI50CVuGZ5qnSk2wMtsysg=="}, "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_1.0.1_1579897230300_0.6147991442490592"}, "_hasShrinkwrap": false, "publish_time": 1579897230453, "_cnpm_publish_time": 1579897230453, "_cnpmcore_publish_time": "2021-12-13T06:49:22.126Z"}, "1.0.0": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "1.0.0", "main": "index.js", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "1b64c7bdb6eb4d28ac4c019e19f9a93a8338c14d", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@1.0.0", "_nodeVersion": "13.4.0", "_npmVersion": "6.13.6", "dist": {"shasum": "8487b07699b70c9b06fce47b3ce28d8176c13c75", "size": 12945, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.0.tgz", "integrity": "sha512-4Pb+8NJ5DdvaWD797hKOM28wMXsObb4HppQdIwKUHFiB69ICZ4wktOE+qsGGBy7GtwgYNizp0R9KEy4zKYBLMg=="}, "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_1.0.0_1579897122471_0.9445186710087152"}, "_hasShrinkwrap": false, "publish_time": 1579897122630, "_cnpm_publish_time": 1579897122630, "_cnpmcore_publish_time": "2021-12-13T06:49:22.602Z"}, "0.5.1": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.5.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "index.js", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git+https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "0.0.8"}, "devDependencies": {"tap": "1", "mock-fs": "2 >=2.7.0"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "gitHead": "d4eff0f06093aed4f387e88e9fc301cb76beedc7", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp#readme", "_id": "mkdirp@0.5.1", "_shasum": "30057438eac6cf7f8c4767f38648d6697d75c903", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "30057438eac6cf7f8c4767f38648d6697d75c903", "size": 4991, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha512-SknJC52obPfGQPnjIkXbmA6+5H15E+fR+E4iR2oQ3zzCLbd7/ONua69R/Gw7AgkTLsRG+r5fzksYwWe1AgTyWA=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431570421553, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1431570421553, "_cnpmcore_publish_time": "2021-12-13T06:49:22.949Z"}, "0.5.0": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "0.0.8"}, "devDependencies": {"tap": "~0.4.0", "mock-fs": "~2.2.0"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp", "_id": "mkdirp@0.5.0", "dist": {"shasum": "1d73076a6df986cd9344e15e71fcc05a4c9abf12", "size": 5090, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.0.tgz", "integrity": "sha512-xjjNGy+ry1lhtIKcr2PT6ok3aszhQfgrUDp4OZLHacgRgFmF6XR9XCOJVcXlVGQonIqXcK1DvqgKKQOPWYGSfw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1399343303769, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1399343303769, "_cnpmcore_publish_time": "2021-12-13T06:49:23.346Z"}, "0.4.2": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.4.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "0.0.8"}, "devDependencies": {"tap": "~0.4.0"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp", "_id": "mkdirp@0.4.2", "dist": {"shasum": "427c8c18ece398b932f6f666f4e1e5b7740e78c8", "size": 4714, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.4.2.tgz", "integrity": "sha512-aiwEvGvWUEFscNrK7RnxM++ltHpm/dzoVZicG8jqFK0yr3FgSUJ174D3b9kackdgHLuRb9cWapcUCoXVELD0Pg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1399340435608, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1399340435608, "_cnpmcore_publish_time": "2021-12-13T06:49:23.863Z"}, "0.4.1": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "http://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "0.0.8"}, "devDependencies": {"tap": "~0.4.0"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp", "_id": "mkdirp@0.4.1", "dist": {"shasum": "4d467afabfdf8ae460c2da656eae8f7b21af4558", "size": 4737, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.4.1.tgz", "integrity": "sha512-SaDX797Hg7C+3kHofgmPnqppj4Rp609u18iYeaWK7JXPSpqfYKLr1TjNEKb/kUFbs2dgS65t0PUcc6i01dF7Wg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1399170059368, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1399170059368, "_cnpmcore_publish_time": "2021-12-13T06:49:24.286Z"}, "0.4.0": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "http://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.4.0"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp", "_id": "mkdirp@0.4.0", "dist": {"shasum": "291ac2a2d43a19c478662577b5be846fe83b5923", "size": 4715, "noattachment": false, "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.4.0.tgz", "integrity": "sha512-l4/GdhkYnvcQxztcZecGWmF2TYbk6R52LS75hV0bzpZA+pvEJfeVtJrOU1hUFFZT9GihgEcFc65zmP2ZtNRtSg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398205281058, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1398205281058, "_cnpmcore_publish_time": "2021-12-13T06:49:24.760Z"}, "0.3.5": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.3.5", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "http://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "readmeFilename": "readme.markdown", "_id": "mkdirp@0.3.5", "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.3.5.tgz", "shasum": "de3e5f8961c88c787ee1368df849ac4413eca8d7", "size": 4162, "noattachment": false, "integrity": "sha512-8OCq0De/h9ZxseqzCH8Kw/Filf5pF/vMI6+BH7Lu0jXz2pqYCjTAQRolSxRIi+Ax+oCCjlxoJMP0YQ4XlrQNHg=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1361533465486, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1361533465486, "_cnpmcore_publish_time": "2021-12-13T06:49:25.281Z"}, "0.3.4": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.3.4", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "http://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.2.4"}, "license": "MIT/X11", "engines": {"node": "*"}, "_id": "mkdirp@0.3.4", "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.3.4.tgz", "shasum": "f8c81d213b7299a031f193a57d752a17d2f6c7d8", "size": 4202, "noattachment": false, "integrity": "sha512-sZObLj65ImOahHTaycVJF559muyAvv1hYyBQSfVfZq9ajpgY9Da+cRQzbXDfsKJTwUMUABRjBMDHieYqbHKx0g=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1345465644983, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1345465644983, "_cnpmcore_publish_time": "2021-12-13T06:49:25.709Z"}, "0.3.3": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.3.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.2.4"}, "license": "MIT/X11", "engines": {"node": "*"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "mkdirp@0.3.3", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.19", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.3.3.tgz", "shasum": "595e251c1370c3a68bab2136d0e348b8105adf13", "size": 4655, "noattachment": false, "integrity": "sha512-Oamd41MnZw/yuxtarGf3MFbHzFqQY4S17DcN+rATh2t5MKuCtG7vVVRG+RUT6g9+hr47DIVucIHGOUlwmJRvDA=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1338911671889, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1338911671889, "_cnpmcore_publish_time": "2021-12-13T06:49:26.216Z"}, "0.3.2": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.3.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.2.4"}, "license": "MIT/X11", "engines": {"node": "*"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "mkdirp@0.3.2", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.3.2.tgz", "shasum": "4bfb891e9c48b93d6b567f2c3cf2dd3f56bcdef8", "size": 4445, "noattachment": false, "integrity": "sha512-pc+27TvK2K/zCoLgoqHbCDndezId7Gbb00HjFACfXlFqOmbi+JyghsLmjiAGfDYrbGxQIlAWPM2UId3nx6JHOQ=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1335775932424, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1335775932424, "_cnpmcore_publish_time": "2021-12-13T06:49:26.672Z"}, "0.3.1": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.2.4"}, "license": "MIT/X11", "engines": {"node": "*"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "mkdirp@0.3.1", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.3.1.tgz", "shasum": "bee3db22a2aa1c81d4b4c0db39c7da9888799593", "size": 4331, "noattachment": false, "integrity": "sha512-wKuQTWc9aojLmcUo248LuWtbHZE26ftrcwYIdeNzASRdimIG56C9KCHJ+BxNmI/38NepT+eM6DNnNyZtMHKa4Q=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1333223498912, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1333223498912, "_cnpmcore_publish_time": "2021-12-13T06:49:27.157Z"}, "0.3.0": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "0.0.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "mkdirp@0.3.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.3.0.tgz", "shasum": "1bbf5ab1ba827af23575143490426455f481fe1e", "size": 3760, "noattachment": false, "integrity": "sha512-OHsdUcVAQ6pOtg5JYWpCBo9W/GySVuwvP9hueRMW7UqshC0tbfzLv8wjySTPm3tfUZ/21CE9E1pJagOA91Pxew=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1327026042042, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1327026042042, "_cnpmcore_publish_time": "2021-12-13T06:49:27.617Z"}, "0.2.2": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "0.0.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "mkdirp@0.2.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.2.2.tgz", "shasum": "7235f2a2062aaf3619189b9f4772114c30944498", "size": 3837, "noattachment": false, "integrity": "sha512-Sw4mJdYA5ryJGV3fnaafloGUh0qKdOicht1HCc58mCD+OD1iVrh+AppTmiveJFhrtb3+Ji+7sVLHK8wchTmV2g=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1325998955484, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1325998955484, "_cnpmcore_publish_time": "2021-12-13T06:49:28.170Z"}, "0.2.1": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "0.0.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "mkdirp@0.2.1", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.101", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.2.1.tgz", "shasum": "2ef920435c8511e135137a33f18a9e40cf9dd166", "size": 3624, "noattachment": false, "integrity": "sha512-WaFtVpAohqQ9xUaEkJ5LBpy9JFxYhsOyyh5QOyWiKZFfaufioXB65Tb38SklJ91RjvoEhH96PQXG1wcO7kM0lQ=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1321435611089, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1321435611089, "_cnpmcore_publish_time": "2021-12-13T06:49:28.682Z"}, "0.2.0": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "0.0.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "mkdirp@0.2.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.101", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.2.0.tgz", "shasum": "29dd87f198880b568d1efce0980e7231b048f3aa", "size": 3301, "noattachment": false, "integrity": "sha512-qXdycG9bh+1WgmITVghj8nQX89g2OKEI1QK7LnfhVf1WG3aQqCA0DEU8TszfCoQ+65/qXiav26oXQsTWyXXT5w=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1321421537036, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1321421537036, "_cnpmcore_publish_time": "2021-12-13T06:49:29.165Z"}, "0.1.0": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "0.0.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "mkdirp@0.1.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.101", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.1.0.tgz", "shasum": "53212930f7bd75f187b6c8688eb0a5fd69b7d118", "size": 3004, "noattachment": false, "integrity": "sha512-ow1qN/eaqDjNrPB8XGt2mdTkSDNFOW1ron/H3A3qXGkb+mYuhiDLvk3eH0VlMaPGcz/gNEqOgkuxdqEXqwZN7g=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1320561143379, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1320561143379, "_cnpmcore_publish_time": "2021-12-13T06:49:29.686Z"}, "0.0.7": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.0.7", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "node node_modules/tap/bin/tap.js test/*.js"}, "devDependencies": {"tap": "0.0.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_id": "mkdirp@0.0.7", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.0.7.tgz", "shasum": "d89b4f0e4c3e5e5ca54235931675e094fe1a5072", "size": 2471, "noattachment": false, "integrity": "sha512-r4Ml2CH2Kl4B0+Lwq1SVru0vjMxdtR+UEb938WTQcsnU+EJz8dUV/HY0LTZh466nUSljia9cvqW3LZLWs3l8LQ=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1315695008879, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1315695008879, "_cnpmcore_publish_time": "2021-12-13T06:49:30.269Z"}, "0.0.6": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.0.6", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "expresso"}, "devDependencies": {"expresso": "0.7.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_id": "mkdirp@0.0.6", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.0.6.tgz", "shasum": "0965de71060cf5e237ffa795243cb5d9a78d335b", "size": 2161, "noattachment": false, "integrity": "sha512-7ZUkNRk48VSNMMXGW8orFDLUKdRcTTfEIq9rcVBpLfgFT8PFzD2AgIqYXBj+uBA0LhJIcTG3WgzjOyoKXaN77g=="}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1313876230730, "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1313876230730, "_cnpmcore_publish_time": "2021-12-13T06:49:30.827Z"}, "0.0.5": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.0.5", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "expresso"}, "devDependencies": {"expresso": "0.7.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_id": "mkdirp@0.0.5", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.0.5.tgz", "shasum": "375facfa634b17dcdf734c56f59ddae5102811c8", "size": 2134, "noattachment": false, "integrity": "sha512-nu8UF+SW9iIgy3/keZzKtsanSVIP7ntI8A61nFWbTokT0bZXAaVjXzQ/RRtX8DVr+3CAdsFV/KnevwYgPAAjpg=="}, "directories": {}, "publish_time": 1309371725839, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1309371725839, "_cnpmcore_publish_time": "2021-12-13T06:49:31.457Z"}, "0.0.4": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "expresso"}, "devDependencies": {"expresso": "0.7.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_id": "mkdirp@0.0.4", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.0.4.tgz", "shasum": "fbb491deec0b9b00869f52582e5f431b3681d2f5", "size": 2080, "noattachment": false, "integrity": "sha512-<PERSON>z34RG1e4w1e+htz7wlxyl3FwlRqu1Eone957NT/PgEzTUDVe/7h3j32zri8QQi9Hw/ed7VDtq8ZmSphaDJw=="}, "directories": {}, "publish_time": 1309307312272, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1309307312272, "_cnpmcore_publish_time": "2021-12-13T06:49:32.039Z"}, "0.0.3": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "expresso"}, "devDependencies": {"expresso": "0.7.x"}, "license": "MIT/X11", "engines": {"node": "*"}, "_id": "mkdirp@0.0.3", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.0.3.tgz", "shasum": "5a7d88a26857023759ffee7fe4c0b28b0f0066b9", "size": 1881, "noattachment": false, "integrity": "sha512-y4EOEX7XAmN0n8CDL5wytcj9Z5x3FsvaFQH0VCy3KChSNvUb598ZTEFOs6JnhCb7Gshp1gDyAlqp75mABSAaHQ=="}, "directories": {}, "publish_time": 1308542564361, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1308542564361, "_cnpmcore_publish_time": "2021-12-13T06:49:32.603Z"}, "0.0.2": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "http://github.com/substack/node-mkdirp.git"}, "license": "MIT/X11", "engines": {"node": "*"}, "_id": "mkdirp@0.0.2", "_engineSupported": true, "_npmVersion": "0.2.18", "_nodeVersion": "v0.3.8-pre", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.0.2.tgz", "shasum": "d9438082daac12691c71d64076706c8a5c3511b6", "size": 1955, "noattachment": false, "integrity": "sha512-rNey95QC2ZVqHABQxdx1rnOtSu+eq2DQlSu172/xvnAYXxhihtuJnnZ4+bFgXMJAGWo2+qqejTipwfPYsceikA=="}, "publish_time": 1297714304988, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1297714304988, "_cnpmcore_publish_time": "2021-12-13T06:49:33.169Z"}, "0.0.1": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "modules": {"index": "./index"}, "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "http://github.com/substack/node-mkdirp.git"}, "engines": {"node": "*"}, "_id": "mkdirp@0.0.1", "_nodeSupported": true, "_npmVersion": "0.2.12-1", "_nodeVersion": "v0.2.5", "dist": {"tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.0.1.tgz", "shasum": "3fbd9f4711a5234233dc6c9d7a052d4b9f83b416", "size": 946, "noattachment": false, "integrity": "sha512-HQy0wosZ9vTZbcDvhhkHErQlJE5Nn1YJbgtOUzmBQlZyXP043eySHBGXk0mE4o5MSB+yz+Emwj/MUTy2uQXImA=="}, "directories": {}, "publish_time": 1294282476496, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "_cnpm_publish_time": 1294282476496, "_cnpmcore_publish_time": "2021-12-13T06:49:33.833Z"}, "0.5.6": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.5.6", "publishConfig": {"tag": "legacy"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "index.js", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "git+https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "^1.2.6"}, "devDependencies": {"tap": "^16.0.1"}, "bin": {"mkdirp": "bin/cmd.js"}, "license": "MIT", "readmeFilename": "readme.markdown", "gitHead": "92f086d2e28c6848951776fbe8ecadcf54c80c29", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "homepage": "https://github.com/substack/node-mkdirp#readme", "_id": "mkdirp@0.5.6", "_nodeVersion": "17.6.0", "_npmVersion": "8.5.3", "dist": {"integrity": "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==", "shasum": "7def03d2432dcae4ba1d611445c48396062255f6", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz", "fileCount": 6, "unpackedSize": 7688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOmA/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmkBAAo8qxsdtWDWH93Zld3CvA9oCCnBG8dUjrmESZJ55xUvtGfgG/\r\nIBfnNB1vhJslO6RvGQtb4yOUJ9B05dm6C+UXgIBaLpan+hXfPlnGEHOBYd/p\r\nLUaAUsvuMkae82291zZ64n940YfEVUEWDCGF0sCvyxn8PSz/P0E0MCUmgskk\r\n6v+zvG97veWGPPH41CW9SUe6lzw4bunaRIJAr2nktLQjK391NlctJKbklhPy\r\nLVfzJsnHVP0rWjrGZBExywIm4Uzp6eb5yMx4TNEPa9HeXusuwBzEew7CNSEB\r\ngvSf+w6TsoQIgeprQqWklSwHeDWeOgOaIMCaX1UFgzEzC+3YrHMBcwz14dH9\r\n5p3jTq+QC1+PRJa/0ohmdu9vGTFvO9fhrdzpHfsQxTCcskaphslBDf11v9BI\r\nju37bOXztCMC4ZUpLGLTqJil8OCo9aRfuY6LoT7KxHSIqBRQsLahrRT8+SIO\r\n4ecjLd5EczUPh1ugp3eQtyMf3SXFtIU24E/Du/xF745+M4fADE8uZC162aFh\r\nGFCkqSScw8nNLjwVvjwLH2E0N7d+lLs1QliShE925EH8U9bA2P4svSL/6thu\r\nQs8pZTqH0fQ//GsasuKJQBM5f+EzFCZlywprAfpIOiOGIbOULzVRs+Fdk4AM\r\nLpkGPdicU5FWBsk+nybYXOW1SuM0ATZGJ8U=\r\n=dgxS\r\n-----END PGP SIGNATURE-----\r\n", "size": 3018}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_0.5.6_1647992894912_0.4921061677907226"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-22T23:48:24.173Z"}, "2.0.0": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "2.0.0", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.d.ts", "bin": {"mkdirp": "dist/cjs/bin.js"}, "exports": {".": {"import": {"default": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts"}, "require": {"default": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "6e4e301b93a761bafe3bc1e2b231f84730f41082", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@2.0.0", "_nodeVersion": "19.4.0", "_npmVersion": "9.3.0", "dist": {"integrity": "sha512-M9ecBPkCu6jZ+H19zruhjw/JB97qqVhyi1H2Lxxo2XAoIMdpHKQ8MfQiMzXk9SH/oJXIbM3oSAfLB8qSWJdCLA==", "shasum": "1460f186644abf3b751e74f5ee82f47d191b586d", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.0.0.tgz", "fileCount": 53, "unpackedSize": 70901, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0vInk9Dp4uEPkwYHcwQf7qITdjFh0pJu5SoS7K+jmsQIgBXL0puK6VR7WwCKWKg/yVebnAwaDIqBT00SDtTyKk4U="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxNDQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0YQ/8D8kSJ+njFH2AofqUYstg4WZA/VxQDeW67bT3o+fjvTZx4U3z\r\npOUzVgDCtJYHhMYKLtNLGpT+K3TltAWJ8GSNABzXtEBYM+6eceXR4qfNnRkQ\r\nefjY1wX1KBd9kEa8epeqnStwVFqbEjI4WErqZSgDR9vlLpMZ3N8D84dOfFK2\r\nw6a178PJ4wZgUjHKckD99yA5wMoXq/mI6qqpAPYIfiLBBGdTbiBfj1h+YPs7\r\nUJppt5J/GW9tt1lr/FqYdTpMPQS19lJQSmH77/28JgpRSCDh7g6nuHp0+6Yk\r\nMNjsiqiAi8rHy11eRpB5mKUB257SWRPsvG6x3hT0tmTvSvDnMWE6n6dVOFdo\r\nx2mL+2PjmOwQqZC9CTV/F2cHWXDVY+zli7CURH2NyG7c82LqLdoEUF1VsTMn\r\nPo6MNDKLXqUmEU2LNbuFxJXrYT7b1rFQd0jhGYO9ucFng73JqeQdm/Wcb/sn\r\nOioLygv9CcesIHy9bLIdV+Sd5IkneF8cOFtWnvxfSzjdnc5c5mL/g8SqmXnG\r\n2XG/lX7Y+qgoKSgUq1yA9AxT2K9ILlwz21K4f8U8ZN5KLnF4rjy1/5OsEmmN\r\nWX+ln+l62jDp7ObqjTLSCipFEzfKFct5t/80ajjqx4UjiqRiTINvgisb2cWn\r\naLWDkSSQG5/93r3k4PzIlwJ7Y+TXVeW7DcQ=\r\n=NUQZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 13165}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_2.0.0_1673842895858_0.2293567806747685"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-16T04:21:36.027Z", "publish_time": 1673842896027}, "2.1.0": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "2.1.0", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "main": "./dist/cjs/src/index-cjs.js", "module": "./dist/mjs/index.js", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "exports": {".": {"import": {"default": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts"}, "require": {"default": "./dist/cjs/src/index-cjs.js", "types": "./dist/cjs/src/index-cjs.d.ts"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "types": "./dist/cjs/src/index-cjs.d.ts", "gitHead": "b2008f3607d3ffac1fabf982961d2bf4099b7c70", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@2.1.0", "_nodeVersion": "19.4.0", "_npmVersion": "9.3.0", "dist": {"integrity": "sha512-R0iLNgRMhNIIHmcsuFUnbKcWVo2kMxbIg0bVy/03zZ9lWbIpPq1YbW1SGfjYgEbr6kp+ILH6CzU418NNxXg0Rw==", "shasum": "4bf06e9e4f5871bf35a29f593cce3aba75c2781d", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.1.0.tgz", "fileCount": 53, "unpackedSize": 74594, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCt/BLbtRcIWnk0ZSrXCjA8+DH+mTKWueeBobZQnG/uoAIhAOVACpGyUlSFIrw6SYSic3/4K7GAhlANDL+wx59r2p+D"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxeW1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo37RAAg0vrvzJOiW0RsdtdH25BQxYAW4bfDFsPdt/6s5qSo5umXjLx\r\n3FGJGM68TaXlDA9lmxj3QMZ6/zgpa4YD3xI3LUotaNjxDD7kK9syDi08vrMo\r\nUbfD5FHE3qGRnh9p0jW8uWgtd729BF53DoHlCD+9Hv+deOoZ28qCXBiEbamP\r\n02AOtMiJNuAAoXI4ERukBImI6/8R+Kx8dhnThaDHl0iZ1+SHVA5DalqPB0SI\r\nUyD9wT9Ru/P9l77lWofz2mrCRfP4SS3OY5aVcQ26iFRNOKBK7ktG37CwFnfZ\r\nY8B6RpoluAG3b9dXbiJROHNxAYkyl8LbXtP1IVHXYlZ0fJKS9wZaVURKszR9\r\n6dQBI4J7aOa1cn0xWmNd+/ndHIfzKmWdAPCTCa7626AozJSCQnOW1Gc1lqhu\r\nv4JT+zGRElijO/Rr6u6XsljW2l/ZteGkXKTGQ+TZe+nXS+1jWFBnsupi6JRC\r\nFaExFeM+ApOvqWEE2Zn4VDiyt3VpV1oQh3jiKGFG6FAPr36mkcoWdrqjJMk4\r\nJ84yXTix/eDmhaoazzcVvbZmS4kIgWuxEweS+0bBSICx8/aqxSxz6U0e0g4O\r\n+/j1caLRjH8zCNiWQxB6Gmkr3PZ7BmC+WCBMO4eLbpU9Ips/ffhgEUmCLP2q\r\nHIaSizHV8+77D68bw3LfYzkizSsTCuWTP2I=\r\n=pStq\r\n-----END PGP SIGNATURE-----\r\n", "size": 13769}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_2.1.0_1673913780842_0.589216434819217"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-17T00:03:01.053Z", "publish_time": 1673913781053}, "2.1.1": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "2.1.1", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "main": "./dist/cjs/src/index-cjs.js", "module": "./dist/mjs/index.js", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "exports": {".": {"import": {"default": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts"}, "require": {"default": "./dist/cjs/src/index-cjs.js", "types": "./dist/cjs/src/index-cjs.d.ts"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "types": "./dist/cjs/src/index-cjs.d.ts", "gitHead": "bb88e784b4689becab74c7bf76ffe014628a3d74", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@2.1.1", "_nodeVersion": "19.4.0", "_npmVersion": "9.3.0", "dist": {"integrity": "sha512-tXNRfEiv5Z5R1jrXKfSNwhzDjMPdSZqYC1co5ZC7yjNwClJ0Hqt5NGRAayuK6urzM5MWX2IGFLAT4Gs1saeYQw==", "shasum": "76acc768901766072c9883378a8545a598419752", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.1.1.tgz", "fileCount": 53, "unpackedSize": 75194, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQteLAJN0s6bbnVJKPyN+T6sFbN8UC9HFlEEKAhyC5UAIhANAQMdJXBv5eGMWW7DGbCdjziVpGVh+dydEh+hD8YzpQ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxedyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqI2g//aCysxst+ycUOF+O/LoTrkPewogEcTzjxxM6B+1gTfoQGcl4T\r\nNjYV9iFz0zvt2OKszd/gvVKBEHuYuxeUjO0j8Y6VyEelHpsqHP67V6iE/9u2\r\nly5GBXdw9lqq2tzSdlM/D7oGnN1dsgNlscrFrMUFqgo1iUCQ8mWE25BQalqB\r\nF25ZalIKYP96i0qapJWvmFgr5rq8rsFYMM9l4ouMUPtOtXqjYE1JTjpeAWJT\r\nZJdzOoaGh1h7N7a9JP1oMlVJDSMVl576Jjmdys3kupdnFe/aOHxKZSIF7sMv\r\nBF+3JWv2jsbiT8Ufka3Nc47AvaSa/ruxmyqsH3KSMtH3fxsGbNIs1svPgBp/\r\nL8FTZRAzXfajDUIt+Q1mxZBHwji/uPJNO6AdBHGTLGi+uSZ6RJwJ+coLfgon\r\nvA+7UhedaBsv81FVlauA8ZgX9wv4dlEx4tIPgwZUnmBSLqTxdPx56bzJMeFH\r\nAeVrdd3HysYWUGThlkJrzglRBfcv6L7qLm6tLD7s/oKrL2N4CB8cik34nUbd\r\nEqDfTEf6UGms/PTNSBwxI6NFS7cUthNHLgIWh5ugUCSic5+XpWmOGXjE2w7Z\r\nZqrOOqTqiYzz1Um76eQa/8TbjrhiOnrsSL034qaNe2rTaAo9p5WwQ9EihHr+\r\n6wKQMxFCK75H2uLBg4I2T4TRJeZpjFnqyFg=\r\n=oMFo\r\n-----END PGP SIGNATURE-----\r\n", "size": 13796}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_2.1.1_1673914226139_0.411332082621396"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-17T00:10:26.292Z", "publish_time": 1673914226292}, "2.1.2": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "2.1.2", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "main": "./dist/cjs/src/index-cjs.js", "module": "./dist/mjs/index.js", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "exports": {".": {"import": {"default": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts"}, "require": {"default": "./dist/cjs/src/index-cjs.js", "types": "./dist/cjs/src/index-cjs.d.ts"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "types": "./dist/cjs/src/index-cjs.d.ts", "gitHead": "c40528a7e88d2b9fb33716ff6db9fed1f810e072", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@2.1.2", "_nodeVersion": "19.4.0", "_npmVersion": "9.3.0", "dist": {"integrity": "sha512-12UORspdR5q37RhJO1XasFrqNX3FbIbdtvNP2uHJJx6mhEXdNDaaCiXLiLGrqaKX1n6WvafTIIndZV2pJayNsg==", "shasum": "51524af79237f3cd1345d3b9eec2e0458dcf520c", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.1.2.tgz", "fileCount": 53, "unpackedSize": 77700, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGrRQAFVFXsO0tbOQWmZwIIcsuj3GmgfnlRvSmV2BVPLAiBYeyt2+bMsBJvdA6F0271CClBv3TBRoPB342ocXzntCw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxrPLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPEg//UHCrKWf1lu6Caka25GJdYGX66A1lxusiknTaBbcyqubNY2hv\r\ngWH/41+o/bp6RbtIPDTgTxdOhU4euuZo8GbOGZ0e8MBhHrgRgoJldj1UrcCN\r\ngDu4+orAx+SY7QOYpc6/j6Gkq5PbmJV45YfoeCgtP1FeSt+Ws58v4bC7VTAN\r\nMhJNh/Cn6Dl4mro2xXOI7FjDT9teGuAkG1rx/j8aBh5qosTXiHTI0PLHR7/E\r\n8Dk+5jZ82zN/JUu7Mhc8VnJvxBclCuvGEsD63Sjm8ZxkVjkRR0CFMamtS1vX\r\nO0kMq+ckWPt921h5wzP8woqqZxQEKlHKotUMe0tffvJ7vBzj56s1p1LAHQEk\r\nn4TS7do7xCpLwL46Y0LWg+DfkeKb3l5I9UKGnJ7PaoPML1iHfSPjERzU5LoX\r\nLlTsdBZE1sXIZ4rlsQdcwW6aaCKvqxln8R6jtPvbMXLlB0I9YQtuL8Za9Zzc\r\n0PlY7eP9zsbIbzgAkPshVAW1lk86Ud0ge2rjJ8gXuE0UwW+uWJW2KggI6fYQ\r\nDOsBWtBWCj6eo7hMCJKAjAw/h39aK6/0z73Y4Tujgv9ZAQFDc5o8N8WUC/eF\r\nnkziExB92LI5vS+HAf3vrZGe1tgm4RB+j5outDB8yY0HMJcSDsdPeCxgF5GZ\r\nRfBsJtjyJQs7qGklry3eWPqseReBWJ1h+PQ=\r\n=OPE+\r\n-----END PGP SIGNATURE-----\r\n", "size": 13832}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_2.1.2_1673966538890_0.9556925697796965"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-17T14:42:19.058Z", "publish_time": 1673966539058}, "2.1.3": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "2.1.3", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "main": "./dist/cjs/src/index-cjs.js", "module": "./dist/mjs/index.js", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index-cjs.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "types": "./dist/cjs/src/index-cjs.d.ts", "gitHead": "f0b1f8bfa503b26c4874748ff15b852e7698a840", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@2.1.3", "_nodeVersion": "19.4.0", "_npmVersion": "9.3.0", "dist": {"integrity": "sha512-sjAkg21peAG9HS+Dkx7hlG9Ztx7HLeKnvB3NQRcu/mltCVmvkF0pisbiTSfDVYTT86XEfZrTUosLdZLStquZUw==", "shasum": "b083ff37be046fd3d6552468c1f0ff44c1545d1f", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.1.3.tgz", "fileCount": 53, "unpackedSize": 77700, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICCtzbqG5HvsaFNvwdiU/xNPa8QgiH+k1EJ9NDYXLCzQAiEAxJhV8HgeZvbhwrtIDKq1y2NNkJKiDnD2UFEsUDHPSdE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxx63ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqP3A/+I3coUUm5o0xvdpTht/eXyKN9r8edcAJES5H1FkSCLTHKq4rB\r\nZwNXrRWSyjE5lljI/QFzoeQWmf1FWJK0Uqv+N7KrBeAVBoabFfP6YBOzRTpo\r\neP4FGW8CT8Ujjhj1UMIOfOsrGswmzRMBbbHksoLHWLLG3/D1c5igWQnjm5NJ\r\nM48sdSmG7OluuWQ4cVXbUQJXOZ0sWy5Cq2oOsv3N6PRi98Dv3h4MhTqnrzoj\r\ngMuYq53yFUZ8hUJtOMy/poeCUawj70WP8AXumuzGr9f7+HuL5yycaasnr5sY\r\nKpIQ+gc4/ixnDIjWcB7bwQQkYKVIxX0DsXheaHlfcEDMZVVNVaxWc/LewmF3\r\nwT73LaFw1ZTVZFT2WbMdWyVU99J/K8a0xJ43SKO/oWr/AporDVEYm8z46VE6\r\n9GK0ihEku7PldhAbPYnENdiZOYs5K27F5QY63T5TxbBtKYcIxmKLMPfKjeM+\r\nbwR22PGHs54ai1lZk/R91ayPcL1jPfvIjpiFoJmxVf+y4j0EQyAi1gXvdZvH\r\nKVx4k3/Jp9EpHYEcfKMrsyogT5/aFVkpnx6Kvdcia2rhkMHUU4qwc5U3zsLn\r\nd449fpOf5lV1KVPbJwT4OCYG+xjyiR4z9wZBRbSTa/w/YljFmvUfvB+ody95\r\nMR/aidPxQFOhOBM67HnomJlWPJ31IVc4/e4=\r\n=MGnN\r\n-----END PGP SIGNATURE-----\r\n", "size": 13834}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_2.1.3_1673993911111_0.79142783165591"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-17T22:18:31.249Z", "publish_time": 1673993911249}, "2.1.4": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "2.1.4", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "main": "./dist/cjs/src/index-cjs.js", "module": "./dist/mjs/index.js", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index-cjs.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "types": "./dist/cjs/src/index-cjs.d.ts", "gitHead": "a7886fc822809d051432c6a3570386fc69eede03", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@2.1.4", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-Cy9cV4pRSl1o10i1dURTuRt4T04l0DkS1WZrT+Jir886OqOVkSv4FbOA7pgjhS8kEUrmm4kCRvv5var2iOCxpA==", "shasum": "34faf1f2a1b4cc9a65a84e2979087211da608d11", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.1.4.tgz", "fileCount": 69, "unpackedSize": 87187, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3fjUvWvIPUCtiqNGj64avmsdpBKtSbhHZt93S76ZvDgIhALD2JXV+y6NQHmMGWzLSOQh/m7FYW2jGSKuvWF5EyXri"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/7IdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKrg//dV4+ZtwTM+p/OB9+LSMYu6jB5vEglnpbTMjTBw771W2S/Tf6\r\nlI3R3I9rN5fBO3Uvxkl6S91CXseEgy3gkGiPzTWUHqdnN0W2YB9r6FiqxeDp\r\nyKqBfjIPu4f1MEX3XzNM6BNJWe/zCIzVPXGojAsbIuzWmIYG0gw1YZFIGI74\r\nHYqNH6E2eXM0s9Hz3VfOjuT7yCQHSVycuIqOtwhGeT6er5CT85oecR/9bGpO\r\nRbwweiGnEn+BUSIhSSrhMmjteBXZRkTb66QJiRfskaJi1Nf/HprVpXooNY0I\r\nIqm2jQuNRHyQRPHFUlpYSAYR6UBHdsYXGxjc+Ncb+sdtzxL82RcwKl30yfg/\r\nNRRfq7/3O1vVJnNK22L3+Q74gaHMQpLA8oN1RfGLsZRUm3T+aBOew1WFeiOK\r\nvfFIJb8F1xF3aq8ObKT0hvWpmhfILn7R7wDxBVQzTv9/LwYvb2R5Jd2Qyblc\r\nP8AZ6lLbSpk9pKzWHuQ5ikB9l6uq3sPlEQUv7Kzyj8XmAGvpcAbIoA6/Jpoo\r\na4KLCy2YIvdMCOwOfEpYx2Z3Nsjl8seyzOaVr2UDZbyIhYD7Zb40khUeFBq9\r\n2bWZv/ro2QqgHcgdzFS/Tmlm8WhAvN1cjno5Lq6i9599N4JVGsZh7Ez2IWPt\r\nCazC3P7Xb7HbIiltZ6tECcrDNYLS7L+s5bs=\r\n=hWMB\r\n-----END PGP SIGNATURE-----\r\n", "size": 15116}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_2.1.4_1677701660819_0.7071004138394228"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-01T20:14:21.010Z", "publish_time": 1677701661010}, "2.1.5": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "2.1.5", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "main": "./dist/cjs/src/index-cjs.js", "module": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "a2d14cfc56eaca96722057c2fed96b84868b3a24", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@2.1.5", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-jbjfql+shJtAPrFoKxHOXip4xS+kul9W3OzfzzrqueWK2QMGon2bFH2opl6W9EagBThjEz+iysyi/swOoVfB/w==", "shasum": "78d7eaf15e069ba7b6b47d76dd94cfadf7a4062f", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.1.5.tgz", "fileCount": 69, "unpackedSize": 87253, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHzWKpgGkEO1muPdtNUPWgww/ks2NFTJlQY1HmaQe1HMAiAXO61DolahBWoEW7PJErkyt9CMEHIEZJPYOVLf9whX/A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBCt4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtBg/6Aqlz9g9szx7oRrLjeqs+I2r9ZVxnBIOEJAza2s3ayPokbaZB\r\nh1E1ekV8591YZH+dxmWdJveuTv8Egja3jNUjbjXp76k4Inmu8HiT6Y6qOl/U\r\n8zN/TYv91svWDgG36y3Hta1bB5Zkx7+qNW//Fvv4oDwd+HGWeoRLZe0NkTbQ\r\nY4cLCLPhkNucdh1okbtKvTblgXE0YlrF1K2ZTB7GeUTKcv4A5ISi5qzGDB5U\r\nr8ncNbp6nFBTihdUh++w3Zc+g10w5+a0oRNE+ABbYA8GMIaIymYsaa45QqgR\r\n87iPQgZxkMTGXV2t44FIGWzRcbHGATXv/IvnEI4EyLCCQKRkKYHvUeKDjQfs\r\ncREdPfZmpZosD7Ayj6XXN/MUixUz9l/ToNa3CXQhQV2VxPB+IiBjOhojeLBk\r\nKSsDSAX73B5nxwJkV9qJ6xLMfSPR0nWWdtHqsTfHwMKlt192GT+93d5aHN5y\r\nzrjNoO4KGSaRFggQzGbVK9QSzA24E3IWZdzGEgqNnlET4rNHsd17advZFTSH\r\n7ZbIwzbvnX48GE77faM1E+p7UP44mJR1wAKXGfYTlrXONp8cZwUXqEjEWHlW\r\na6SmMJFzuyX+gZA+wy5SsgN82+bdj+83t/WtPviNHSnFXCr8l4K93Xuyi5Kl\r\ntIupQi87Kcoa/1dURgLTNyzWuA4qsznsJsA=\r\n=+769\r\n-----END PGP SIGNATURE-----\r\n", "size": 15122}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_2.1.5_1677994872640_0.6663274750106163"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-05T05:41:12.836Z", "publish_time": 1677994872836}, "2.1.6": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "2.1.6", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "main": "./dist/cjs/src/index-cjs.js", "module": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "adabb30699eddad11b57c23aad93a8793fd254df", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@2.1.6", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-+hEnITedc8LAtIP9u3HJDFIdcLV2vXP33sqLLIzkv1Db1zO/1OxbvYf0Y1OC/S/Qo5dxHXepofhmxL02PsKe+A==", "shasum": "964fbcb12b2d8c5d6fbc62a963ac95a273e2cc19", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.1.6.tgz", "fileCount": 69, "unpackedSize": 111810, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHa4oOEYaff3s4uIQqTGotzuWsllLB8vC5uz1biUDDtJAiB2IAhIGgTVGkl5JNxbdrVly2YHYvx2VgM3J651w+3G7A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG036ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiyRAAgLP2PVXrf36Tt/ob42sZSq5haqY39CfSfm8QI8EiybaLCoRb\r\n4yzGBw0IpFSjlr/NAB4cGRD0W8wUOCQdE7dcdR/7GaeqqYyK78zxTrY5X2sa\r\nbyNWsX0xalK1YJOtvnTVwXn6VnOcQeFv4q5x8OkfGiJq+9DiUIsetWX6K8hV\r\nVBybfOazE2Hf0z8XzPpqCfBpbQRbGTqYMiEuPg2sDEaxA74LgSOmA3Ipf3Sl\r\nLJ6jYchUGBsvQ850rVg6LPDGKANXZwDSUP2OV4ChbEPRxXKQanku4dx+5mAz\r\nR589wmRJF5vbCCTXA6IkEm+WWSO9Yb+8bzAT1bN3NNjhUXgQYJReZAkgej38\r\nxqGIZxvdHOPAJ8cXCO3ixQMshWSFTHsrKiV6i/6OnEgfW2EuWL4zYIaY8DnI\r\nxW3Rj0DDMOLph+0podP++3YoT7bV9L6g2mu4QlmR5ZXJ1BXVarrj7nItYrL+\r\npMEy5M+LTMqzKt0EHFVtT8LGGkg235z/SXY37Zx9Sxrm1jezWNSEemGsvYvp\r\nDFNkj7EmFh5xJXCPHDbFQpa2uEM89Q95SLjDnWagZON6zlAQph/tI2ovM/Xx\r\n+IgOWX7bYekLd6ioi2Qku9ZeZKC9tcuk6c+6cLZjek/PBANZbzW5T6PwwfjH\r\nejrq8xhCj6dDKNxO79YG8fS41+Btotl+NWQ=\r\n=pn+L\r\n-----END PGP SIGNATURE-----\r\n", "size": 18522}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_2.1.6_1679511034653_0.21087704519367545"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-22T18:50:34.833Z", "publish_time": 1679511034833}, "3.0.0": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "3.0.0", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "main": "./dist/cjs/src/index.js", "module": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "06dfe5555af2dd834e8311b4f6abe3a9fcc95e07", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@3.0.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.3", "dist": {"integrity": "sha512-7+JDnNsyCvZXoUJdkMR0oUE2AmAdsNXGTmRbiOjYIwQ6q+bL6NwrozGQdPcmYaNcrhH37F50HHBUzoaBV6FITQ==", "shasum": "758101231418bda24435c0888a91d9bd91f1372d", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-3.0.0.tgz", "fileCount": 65, "unpackedSize": 103291, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAI9f6X+AZmSsRU8uewk0ZDWjqH+dLtb/QBtJyckJ61DAiEA0Mu0Y9lqSV0AwRv9EsjuJ/ZtmYCULjD71sp7yn1rGdU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMzXoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0hA//XAwLAgqyAFC9kt8P0WX7bIBLYWeGdjTX8Ixv+9AsE9It0a7g\r\nuItMVyDjnZEbaL7IiJpxKJMPjQWHDqF5JBc6sktIJJUsxacRd+PDmSEHZ8RK\r\nxzCjAgSKQApONWV6FjzBaoMt/dqBgb6GFLtzOw2lRsEsECDeXMWlmCk1Om5N\r\noIKoFjgchOgIx31FQ38pUSMeU4xy7iUKfGDaUbQKNVDPdJhds1bASDoPm4aa\r\nRJMFjc5SYqtwrs5gODrhPee7IVqsEEBivyMh9hnvFDvUEojhdMD2x9jR7/Bv\r\n+ajxwg/Y7/e0cgvCNz+wbUB3ZLNadXlKJ+tIpS9l66bkTILOMvxlY9xuxznP\r\n2QYElHE4M1t9/Geq2TCz3EQ2W+2LgFTcpW6/L6So8+i3GB2MZLDhanzz6BEY\r\ncQlMtEG1YRog8l/M7jIYTeYHdMooD97tv2Jufl15Zw9ez/3Dz81aOkYDTs6j\r\n85mFyouFKwy+4TIR8ikdMl6YWVbvhOmhF2aqd3X9i/TMV32jRSn2MMTQfvjx\r\nRsU/ybF8euK6+649pDct5+cEu7panI6egXQupghHxvG1/5jLWwwn9658RmKf\r\nRG9s+ypEVZinHA/g3w9DLJWg8WmIgusC4RzCZSsTufUf65MhjJDCZ87q8wtt\r\nqtQAwT1b7JRKwp20IyvYMTcEYKDRFeVvnEA=\r\n=Jf4L\r\n-----END PGP SIGNATURE-----\r\n", "size": 17975}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_3.0.0_1681077735922_0.9373742539136427"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-09T22:02:16.078Z", "publish_time": 1681077736078}, "3.0.1": {"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "3.0.1", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "main": "./dist/cjs/src/index.js", "module": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.0", "@types/node": "^18.11.9", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.2", "tap": "^16.3.3", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "typescript": "^4.9.3"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "license": "MIT", "engines": {"node": ">=10"}, "gitHead": "6fc29774a008f41d96b34523d6aae543ecb46cd1", "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "_id": "mkdirp@3.0.1", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==", "shasum": "e44e4c5607fb279c168241713cc6e0fea9adcb50", "tarball": "https://registry.npmmirror.com/mkdirp/-/mkdirp-3.0.1.tgz", "fileCount": 65, "unpackedSize": 106656, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCYu+EHDAX6Ji0g08ZsP4oQpIpqreEC35IB5Y2XapD0AIhANL2ZQS0wtPLFsED359gP4m65SJ41xQbx5R9tlZ/TNrP"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRrwKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmotyg//b9p44jEeclwRLu3cm/2H8EkEgJKcW73cpOc3GbqquJICFvkB\r\nKlMssGiom8/QUcuIPS4boa0l3BN2ICtr5oK5cd3VTyYG62o8AM9gFgGzvmXF\r\nYYoRK+wDzxopXl/ZbF4Wj1vOHiyu3bfkUhU88VhptAd2AZHEYjgmZcjMa5DJ\r\nZ7KI1ApHkQwhQrocTdWb7/H3atqWCNKMYKxO2I5m46zPhjVWjh6vmhdzoWqo\r\nEZ+G8OGmYcPDRt1c2PcVVd4MFBBLwgXYhrdpqXytAT52Xvfcq63yCVYuX0Hx\r\nhL4vXr+O2MuqlDp8SX9WMNCf6ZWMyzStrEtlRBqKNK4EQoLX1nkGsYhcqVW4\r\n9OhufJdjjJlCHxS8njYNyb/CIIQX38TdOSl5v/asCTTWKA2Vl82lDb3RVV25\r\ns/gEr8tXsgk5WrWaQ5wXhkMKXUrW6Vo9/YqWfJrtx+4aVODXSdbv45ntbDFY\r\nzogBqQxkOB1zz44U75cZcryVlmyqDS7undO/JD472Rul/PEZ1t6Tr3EW4a9B\r\nKvkgnm5m4qPKIXeBPPuBAiad5XJdsZ7N/v5S2FJeQNTFGn7dFe52TmiqPVYy\r\n7h1/ygda1psQkuXV8ahVyTyVrbkzM5NjyJEU5k0Ycno5SHNVV2grvtWxT3LY\r\nYwmYNlMT6b/imbCGR6mnBFgIs2oUrYOzkCQ=\r\n=oKXF\r\n-----END PGP SIGNATURE-----\r\n", "size": 18260}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp_3.0.1_1682357258427_0.11225899358209257"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-24T17:27:38.604Z", "publish_time": 1682357258604}}, "bugs": {"url": "https://github.com/isaacs/node-mkdirp/issues"}, "homepage": "https://github.com/isaacs/node-mkdirp#readme", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-mkdirp.git"}, "_source_registry_name": "default"}