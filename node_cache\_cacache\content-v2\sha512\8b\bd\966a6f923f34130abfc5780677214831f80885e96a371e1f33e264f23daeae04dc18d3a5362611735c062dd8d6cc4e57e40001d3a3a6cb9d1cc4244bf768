{"_attachments": {}, "_id": "generate-function", "_rev": "3385-61f14bf1830fd08f52a2ff12", "author": {"name": "<PERSON>"}, "description": "Module that helps you write generated functions in Node", "dist-tags": {"latest": "2.3.1"}, "license": "MIT", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "generate-function", "readme": "# generate-function\n\nModule that helps you write generated functions in Node\n\n```\nnpm install generate-function\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/generate-function.svg?style=flat)](http://travis-ci.org/mafintosh/generate-function)\n\n## Disclamer\n\nWriting code that generates code is hard.\nYou should only use this if you really, really, really need this for performance reasons (like schema validators / parsers etc).\n\n## Usage\n\n``` js\nconst genfun = require('generate-function')\nconst { d } = genfun.formats\n\nfunction addNumber (val) {\n  const gen = genfun()\n\n  gen(`\n    function add (n) {')\n      return n + ${d(val)}) // supports format strings to insert values\n    }\n  `)\n\n  return gen.toFunction() // will compile the function\n}\n\nconst add2 = addNumber(2)\n\nconsole.log('1 + 2 =', add2(1))\nconsole.log(add2.toString()) // prints the generated function\n```\n\nIf you need to close over variables in your generated function pass them to `toFunction(scope)`\n\n``` js\nfunction multiply (a, b) {\n  return a * b\n}\n\nfunction addAndMultiplyNumber (val) {\n  const gen = genfun()\n  \n  gen(`\n    function (n) {\n      if (typeof n !== 'number') {\n        throw new Error('argument should be a number')\n      }\n      const result = multiply(${d(val)}, n + ${d(val)})\n      return result\n    }\n  `)\n\n  // use gen.toString() if you want to see the generated source\n\n  return gen.toFunction({multiply})\n}\n\nconst addAndMultiply2 = addAndMultiplyNumber(2)\n\nconsole.log(addAndMultiply2.toString())\nconsole.log('(3 + 2) * 2 =', addAndMultiply2(3))\n```\n\nYou can call `gen(src)` as many times as you want to append more source code to the function.\n\n## Variables\n\nIf you need a unique safe identifier for the scope of the generated function call `str = gen.sym('friendlyName')`.\nThese are safe to use for variable names etc.\n\n## Object properties\n\nIf you need to access an object property use the `str = gen.property('objectName', 'propertyName')`.\n\nThis returns `'objectName.propertyName'` if `propertyName` is safe to use as a variable. Otherwise\nit returns `objectName[propertyNameAsString]`.\n\nIf you only pass `gen.property('propertyName')` it will only return the `propertyName` part safely\n\n## License\n\nMIT\n", "time": {"created": "2022-01-26T13:26:09.391Z", "modified": "2022-01-26T13:26:09.391Z", "2.3.1": "2018-08-31T14:46:21.330Z", "2.3.0": "2018-08-31T14:24:59.646Z", "2.2.1": "2018-08-31T14:11:10.513Z", "2.2.0": "2018-08-27T02:19:39.872Z", "2.0.0": "2014-08-21T16:53:53.661Z", "1.1.0": "2014-08-01T09:50:30.055Z", "1.0.3": "2014-08-01T09:04:48.912Z", "1.0.2": "2014-07-30T21:39:22.512Z", "1.0.1": "2014-07-30T18:22:50.528Z", "1.0.0": "2014-07-30T17:25:00.747Z", "0.0.0": "2014-07-30T17:17:07.759Z"}, "versions": {"2.3.1": {"name": "generate-function", "version": "2.3.1", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/generate-function.git"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^4.9.1"}, "dependencies": {"is-property": "^1.0.2"}, "gitHead": "240c6ef0f243fa8e37d2656f880c7145c9f6d12d", "_id": "generate-function@2.3.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f069617690c10c868e73b8465746764f97c3479f", "size": 3632, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-2.3.1.tgz", "integrity": "sha512-eeB5GfMNeevm/GRYq20ShmsaGcmI81kIX2K9XQx5miC8KdHaC6Jm0qQ8ZNeGOi7wYB8OsdxKs+Y2oVuTFuVwKQ=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/generate-function_2.3.1_1535726781175_0.7685665533040811"}, "_hasShrinkwrap": false, "publish_time": 1535726781330, "_cnpm_publish_time": 1535726781330, "_cnpmcore_publish_time": "2021-12-16T12:03:58.809Z"}, "2.3.0": {"name": "generate-function", "version": "2.3.0", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/generate-function.git"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^4.9.1"}, "dependencies": {"is-property": "^1.0.2"}, "gitHead": "1107e1e0c4e2490a6fc0c6e0a94c30ffafca31a1", "_id": "generate-function@2.3.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b3aac2a706a902ddc8954f7ee4c450facb21efa0", "size": 3123, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-2.3.0.tgz", "integrity": "sha512-l9f57Gv0++DaxzEjIDTnBkvI+9j41AAm+ORz609Z4mf5jEQ30R7tOlzR8beIcaKhM8pos9zBMSnpoPIgOSkm7g=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/generate-function_2.3.0_1535725499510_0.761206463485137"}, "_hasShrinkwrap": false, "publish_time": 1535725499646, "_cnpm_publish_time": 1535725499646, "_cnpmcore_publish_time": "2021-12-16T12:03:59.097Z"}, "2.2.1": {"name": "generate-function", "version": "2.2.1", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/generate-function.git"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^4.9.1"}, "dependencies": {"is-property": "^1.0.2"}, "gitHead": "bce8178e466f1609b388d3601d73fe6c201738bf", "_id": "generate-function@2.2.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6e25c0a48fb47cd0b5d4e8815ca286171acc8e27", "size": 3051, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-2.2.1.tgz", "integrity": "sha512-Qiaeib/wcNIj0igwKAAiWyvPpCcZAAchlrVLg7Q7GSj9ctvZeUXEb+IHu8THPYEz9+LlU01MRMUINA6Do0DEjg=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/generate-function_2.2.1_1535724670315_0.06041108490401381"}, "_hasShrinkwrap": false, "publish_time": 1535724670513, "_cnpm_publish_time": 1535724670513, "_cnpmcore_publish_time": "2021-12-16T12:03:59.323Z"}, "2.2.0": {"name": "generate-function", "version": "2.2.0", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/generate-function.git"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^2.13.4"}, "gitHead": "1bf54dde865a62f782658f4721e58165778f08d7", "_id": "generate-function@2.2.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1aeac896147293d27bce65eb295ce5f3f094a292", "size": 3019, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-2.2.0.tgz", "integrity": "sha512-EYWRyUEUdNSsmfMZ2udk1AaxEmJQBaCNgfh+FJo0lcUvP42nyR/Xe30kCyxZs7e6t47bpZw0HftWF+KFjD/Lzg=="}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/generate-function_2.2.0_1535336379774_0.7369054827033938"}, "_hasShrinkwrap": false, "publish_time": 1535336379872, "_cnpm_publish_time": 1535336379872, "_cnpmcore_publish_time": "2021-12-16T12:03:59.549Z"}, "2.0.0": {"name": "generate-function", "version": "2.0.0", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/generate-function"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^2.13.4"}, "gitHead": "3d5fc8de5859be95f58e3af9bfb5f663edd95149", "_id": "generate-function@2.0.0", "_shasum": "6858fe7c0969b7d4e9093337647ac79f60dfbe74", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6858fe7c0969b7d4e9093337647ac79f60dfbe74", "size": 2026, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-2.0.0.tgz", "integrity": "sha512-X46lB9wLCsgkyagCmX2Dev5od5j6niCr3UeMbXVDBVO4tlpXp3o4OFh+0gPTlkD3ZMixU8PCKxf0IMGQvPo8HQ=="}, "directories": {}, "publish_time": 1408640033661, "_hasShrinkwrap": false, "_cnpm_publish_time": 1408640033661, "_cnpmcore_publish_time": "2021-12-16T12:03:59.783Z"}, "1.1.0": {"name": "generate-function", "version": "1.1.0", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/generate-function"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^2.13.4"}, "gitHead": "99be057fb14f2e7f87afa116e5ca4d40a379c080", "_id": "generate-function@1.1.0", "_shasum": "54c21b080192b16d9877779c5bb81666e772365f", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "54c21b080192b16d9877779c5bb81666e772365f", "size": 2153, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-1.1.0.tgz", "integrity": "sha512-Wv4qgYgt2m9QH7K+jklCX/o4gn1ijnS4nT+nxPYBbhdqZLDLtvNh2o26KP/nxN42Tk6AnrGftCLzjiMuckZeQw=="}, "directories": {}, "publish_time": 1406886630055, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406886630055, "_cnpmcore_publish_time": "2021-12-16T12:03:59.960Z"}, "1.0.3": {"name": "generate-function", "version": "1.0.3", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/generate-function"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^2.13.4"}, "gitHead": "ed65d50801cf4a6ccd9f2272caa262d84fd300ee", "_id": "generate-function@1.0.3", "_shasum": "2083919b224afe5cb884b91693744efd2bf9bb03", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2083919b224afe5cb884b91693744efd2bf9bb03", "size": 2022, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-1.0.3.tgz", "integrity": "sha512-feMJy5S9HXM9/6s5jVNzuCnxKz8sq5KajlGP56JLdpM94Eq3Oiek7FQ20Uv59dC9DnhZ1zdmSjisFf2f0VKLFw=="}, "directories": {}, "publish_time": 1406883888912, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406883888912, "_cnpmcore_publish_time": "2021-12-16T12:04:00.166Z"}, "1.0.2": {"name": "generate-function", "version": "1.0.2", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/generate-function"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^2.13.4"}, "gitHead": "f74a9edbbaced7cbb481bbadd18b36b1ae233754", "_id": "generate-function@1.0.2", "_shasum": "b6d5748ff5f35567d82908e84e4a921fad23c6d6", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b6d5748ff5f35567d82908e84e4a921fad23c6d6", "size": 2007, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-1.0.2.tgz", "integrity": "sha512-LTPwPP4OnEN89O2zR7rV+K31AlMB+7pMTJ44mlE6qrhGG+VJvEDYcLjEx9q14wyLl20QqiSiSGcvsVrA+/RNvA=="}, "directories": {}, "publish_time": 1406756362512, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406756362512, "_cnpmcore_publish_time": "2021-12-16T12:04:00.377Z"}, "1.0.1": {"name": "generate-function", "version": "1.0.1", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/generate-function"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^2.13.4"}, "gitHead": "17c6c09d5ea81fc783481d736388201644962842", "_id": "generate-function@1.0.1", "_shasum": "3c53e1a681619063c3bac744af8cd134d9dc6efa", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3c53e1a681619063c3bac744af8cd134d9dc6efa", "size": 2011, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-1.0.1.tgz", "integrity": "sha512-NRHkiLvL9EfCeuj7FjYsYYK/G5MCUSuAtcGOVgbHJOI2JWeA5qks7+386fDNHLP+RS+3A3amkPexJsOpLtfFXw=="}, "directories": {}, "publish_time": 1406744570528, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406744570528, "_cnpmcore_publish_time": "2021-12-16T12:04:00.601Z"}, "1.0.0": {"name": "generate-function", "version": "1.0.0", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/generate-function"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^2.13.4"}, "gitHead": "5a757a46bb7121e203a25ea301ca3ef6a2091411", "_id": "generate-function@1.0.0", "_shasum": "1e6af3d0ad50f35050b1a9a1c056dad4a2c7f5c5", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1e6af3d0ad50f35050b1a9a1c056dad4a2c7f5c5", "size": 1950, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-1.0.0.tgz", "integrity": "sha512-O65y7LrTTl6XUQu+7RbmTxtdokZVrE/z4VERUh8zoiebVtFyPogA/uHFed6ahRMg970lCVKc9k1SmW6aGurdNg=="}, "directories": {}, "publish_time": 1406741100747, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406741100747, "_cnpmcore_publish_time": "2021-12-16T12:04:00.818Z"}, "0.0.0": {"name": "generate-function", "version": "0.0.0", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/generate-function"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^2.13.4"}, "gitHead": "f4dc06374d18af82d530b3aecc7ba3cf4803188d", "_id": "generate-function@0.0.0", "_shasum": "fd561eece1948ea5ee9138d50ac479d18f7f7822", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fd561eece1948ea5ee9138d50ac479d18f7f7822", "size": 1827, "noattachment": false, "tarball": "https://registry.npmmirror.com/generate-function/-/generate-function-0.0.0.tgz", "integrity": "sha512-+s+bzKZsAa6ucNldJd4mQWYS/t+xFsMyDx+rS1C9B4Qd/n8M7iayCK15m51ViO8iwNtrigrUErzgZKebQOdSOw=="}, "directories": {}, "publish_time": 1406740627759, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406740627759, "_cnpmcore_publish_time": "2021-12-16T12:04:01.025Z"}}, "_source_registry_name": "default"}