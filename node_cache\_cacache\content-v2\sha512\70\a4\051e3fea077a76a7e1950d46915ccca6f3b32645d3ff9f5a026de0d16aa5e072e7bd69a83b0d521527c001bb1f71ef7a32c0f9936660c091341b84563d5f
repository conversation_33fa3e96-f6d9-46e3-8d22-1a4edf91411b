{"_attachments": {}, "_id": "sqlstring", "_rev": "88219-61f18904efbf788ede86c2ac", "description": "Simple SQL escape and format for MySQL", "dist-tags": {"latest": "2.3.3"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sidor<PERSON>", "email": "<EMAIL>"}], "name": "sqlstring", "readme": "# sqlstring\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][github-actions-ci-image]][github-actions-ci-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nSimple SQL escape and format for MySQL\n\n## Install\n\n```sh\n$ npm install sqlstring\n```\n\n## Usage\n\n\n```js\nvar SqlString = require('sqlstring');\n```\n\n### Escaping query values\n\n**Caution** These methods of escaping values only works when the\n[NO_BACKSLASH_ESCAPES](https://dev.mysql.com/doc/refman/5.7/en/sql-mode.html#sqlmode_no_backslash_escapes)\nSQL mode is disabled (which is the default state for MySQL servers).\n\nIn order to avoid SQL Injection attacks, you should always escape any user\nprovided data before using it inside a SQL query. You can do so using the\n`SqlString.escape()` method:\n\n```js\nvar userId = 'some user provided value';\nvar sql    = 'SELECT * FROM users WHERE id = ' + SqlString.escape(userId);\nconsole.log(sql); // SELECT * FROM users WHERE id = 'some user provided value'\n```\n\nAlternatively, you can use `?` characters as placeholders for values you would\nlike to have escaped like this:\n\n```js\nvar userId = 1;\nvar sql    = SqlString.format('SELECT * FROM users WHERE id = ?', [userId]);\nconsole.log(sql); // SELECT * FROM users WHERE id = 1\n```\n\nMultiple placeholders are mapped to values in the same order as passed. For example,\nin the following query `foo` equals `a`, `bar` equals `b`, `baz` equals `c`, and\n`id` will be `userId`:\n\n```js\nvar userId = 1;\nvar sql    = SqlString.format('UPDATE users SET foo = ?, bar = ?, baz = ? WHERE id = ?',\n  ['a', 'b', 'c', userId]);\nconsole.log(sql); // UPDATE users SET foo = 'a', bar = 'b', baz = 'c' WHERE id = 1\n```\n\nThis looks similar to prepared statements in MySQL, however it really just uses\nthe same `SqlString.escape()` method internally.\n\n**Caution** This also differs from prepared statements in that all `?` are\nreplaced, even those contained in comments and strings.\n\nDifferent value types are escaped differently, here is how:\n\n* Numbers are left untouched\n* Booleans are converted to `true` / `false`\n* Date objects are converted to `'YYYY-mm-dd HH:ii:ss'` strings\n* Buffers are converted to hex strings, e.g. `X'0fa5'`\n* Strings are safely escaped\n* Arrays are turned into list, e.g. `['a', 'b']` turns into `'a', 'b'`\n* Nested arrays are turned into grouped lists (for bulk inserts), e.g. `[['a',\n  'b'], ['c', 'd']]` turns into `('a', 'b'), ('c', 'd')`\n* Objects that have a `toSqlString` method will have `.toSqlString()` called\n  and the returned value is used as the raw SQL.\n* Objects are turned into `key = 'val'` pairs for each enumerable property on\n  the object. If the property's value is a function, it is skipped; if the\n  property's value is an object, toString() is called on it and the returned\n  value is used.\n* `undefined` / `null` are converted to `NULL`\n* `NaN` / `Infinity` are left as-is. MySQL does not support these, and trying\n  to insert them as values will trigger MySQL errors until they implement\n  support.\n\nYou may have noticed that this escaping allows you to do neat things like this:\n\n```js\nvar post  = {id: 1, title: 'Hello MySQL'};\nvar sql = SqlString.format('INSERT INTO posts SET ?', post);\nconsole.log(sql); // INSERT INTO posts SET `id` = 1, `title` = 'Hello MySQL'\n```\n\nAnd the `toSqlString` method allows you to form complex queries with functions:\n\n```js\nvar CURRENT_TIMESTAMP = { toSqlString: function() { return 'CURRENT_TIMESTAMP()'; } };\nvar sql = SqlString.format('UPDATE posts SET modified = ? WHERE id = ?', [CURRENT_TIMESTAMP, 42]);\nconsole.log(sql); // UPDATE posts SET modified = CURRENT_TIMESTAMP() WHERE id = 42\n```\n\nTo generate objects with a `toSqlString` method, the `SqlString.raw()` method can\nbe used. This creates an object that will be left un-touched when using in a `?`\nplaceholder, useful for using functions as dynamic values:\n\n**Caution** The string provided to `SqlString.raw()` will skip all escaping\nfunctions when used, so be careful when passing in unvalidated input.\n\n```js\nvar CURRENT_TIMESTAMP = SqlString.raw('CURRENT_TIMESTAMP()');\nvar sql = SqlString.format('UPDATE posts SET modified = ? WHERE id = ?', [CURRENT_TIMESTAMP, 42]);\nconsole.log(sql); // UPDATE posts SET modified = CURRENT_TIMESTAMP() WHERE id = 42\n```\n\nIf you feel the need to escape queries by yourself, you can also use the escaping\nfunction directly:\n\n```js\nvar sql = 'SELECT * FROM posts WHERE title=' + SqlString.escape('Hello MySQL');\nconsole.log(sql); // SELECT * FROM posts WHERE title='Hello MySQL'\n```\n\n### Escaping query identifiers\n\nIf you can't trust an SQL identifier (database / table / column name) because it is\nprovided by a user, you should escape it with `SqlString.escapeId(identifier)` like this:\n\n```js\nvar sorter = 'date';\nvar sql    = 'SELECT * FROM posts ORDER BY ' + SqlString.escapeId(sorter);\nconsole.log(sql); // SELECT * FROM posts ORDER BY `date`\n```\n\nIt also supports adding qualified identifiers. It will escape both parts.\n\n```js\nvar sorter = 'date';\nvar sql    = 'SELECT * FROM posts ORDER BY ' + SqlString.escapeId('posts.' + sorter);\nconsole.log(sql); // SELECT * FROM posts ORDER BY `posts`.`date`\n```\n\nIf you do not want to treat `.` as qualified identifiers, you can set the second\nargument to `true` in order to keep the string as a literal identifier:\n\n```js\nvar sorter = 'date.2';\nvar sql    = 'SELECT * FROM posts ORDER BY ' + SqlString.escapeId(sorter, true);\nconsole.log(sql); // SELECT * FROM posts ORDER BY `date.2`\n```\n\nAlternatively, you can use `??` characters as placeholders for identifiers you would\nlike to have escaped like this:\n\n```js\nvar userId = 1;\nvar columns = ['username', 'email'];\nvar sql     = SqlString.format('SELECT ?? FROM ?? WHERE id = ?', [columns, 'users', userId]);\nconsole.log(sql); // SELECT `username`, `email` FROM `users` WHERE id = 1\n```\n**Please note that this last character sequence is experimental and syntax might change**\n\nWhen you pass an Object to `.escape()` or `.format()`, `.escapeId()` is used to avoid SQL injection in object keys.\n\n### Formatting queries\n\nYou can use `SqlString.format` to prepare a query with multiple insertion points,\nutilizing the proper escaping for ids and values. A simple example of this follows:\n\n```js\nvar userId  = 1;\nvar inserts = ['users', 'id', userId];\nvar sql     = SqlString.format('SELECT * FROM ?? WHERE ?? = ?', inserts);\nconsole.log(sql); // SELECT * FROM `users` WHERE `id` = 1\n```\n\nFollowing this you then have a valid, escaped query that you can then send to the database safely.\nThis is useful if you are looking to prepare the query before actually sending it to the database.\nYou also have the option (but are not required) to pass in `stringifyObject` and `timeZone`,\nallowing you provide a custom means of turning objects into strings, as well as a\nlocation-specific/timezone-aware `Date`.\n\nThis can be further combined with the `SqlString.raw()` helper to generate SQL\nthat includes MySQL functions as dynamic vales:\n\n```js\nvar userId = 1;\nvar data   = { email: '<EMAIL>', modified: SqlString.raw('NOW()') };\nvar sql    = SqlString.format('UPDATE ?? SET ? WHERE `id` = ?', ['users', data, userId]);\nconsole.log(sql); // UPDATE `users` SET `email` = '<EMAIL>', `modified` = NOW() WHERE `id` = 1\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-version-image]: https://img.shields.io/npm/v/sqlstring.svg\n[npm-downloads-image]: https://img.shields.io/npm/dm/sqlstring.svg\n[npm-url]: https://npmjs.org/package/sqlstring\n[coveralls-image]: https://img.shields.io/coveralls/mysqljs/sqlstring/master.svg\n[coveralls-url]: https://coveralls.io/r/mysqljs/sqlstring?branch=master\n[github-actions-ci-image]: https://img.shields.io/github/workflow/status/mysqljs/sqlstring/ci/master?label=build\n[github-actions-ci-url]: https://github.com/mysqljs/sqlstring/actions/workflows/ci.yml\n[node-image]: https://img.shields.io/node/v/sqlstring.svg\n[node-url]: https://nodejs.org/en/download\n", "time": {"created": "2022-01-26T17:46:44.454Z", "modified": "2025-05-13T03:27:32.504Z", "2.3.2": "2020-04-16T02:25:43.546Z", "2.3.1": "2018-02-25T00:02:18.705Z", "2.3.0": "2017-10-02T03:27:42.183Z", "2.2.0": "2016-11-02T03:27:22.252Z", "2.1.0": "2016-09-27T02:28:34.117Z", "2.0.1": "2016-06-06T19:47:19.886Z", "2.0.0": "2016-06-06T19:44:19.336Z", "1.0.0": "2014-11-09T09:40:14.414Z", "0.0.1": "2014-02-25T03:44:04.681Z", "2.3.3": "2022-03-06T21:04:40.065Z"}, "versions": {"2.3.2": {"name": "sqlstring", "description": "Simple SQL escape and format for MySQL", "version": "2.3.2", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": {"type": "git", "url": "git+https://github.com/mysqljs/sqlstring.git"}, "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "5.16.0", "eslint-plugin-markdown": "1.0.2", "nyc": "15.0.0", "urun": "0.0.8", "utest": "0.0.8"}, "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "node test/run.js", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "b580866cdedfd748e110a87fbfb27b5154b99545", "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "homepage": "https://github.com/mysqljs/sqlstring#readme", "_id": "sqlstring@2.3.2", "_nodeVersion": "13.12.0", "_npmVersion": "6.14.4", "dist": {"shasum": "cdae7169389a1375b18e885f2e60b3e460809514", "size": 6349, "noattachment": false, "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-2.3.2.tgz", "integrity": "sha512-vF4ZbYdKS8OnoJAWBmMxCQDkiEBkGQYU7UZPtL8flbDRSNkhaXvRJ279ZtI6M+zDaQovVU4tuRgzK5fVhvFAhg=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sidor<PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sqlstring_2.3.2_1587003943332_0.8006529859541776"}, "_hasShrinkwrap": false, "publish_time": 1587003943546, "_cnpm_publish_time": 1587003943546, "_cnpmcore_publish_time": "2021-12-16T10:39:00.322Z"}, "2.3.1": {"name": "sqlstring", "description": "Simple SQL escape and format for MySQL", "version": "2.3.1", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": {"type": "git", "url": "git+https://github.com/mysqljs/sqlstring.git"}, "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "4.18.1", "eslint-plugin-markdown": "1.0.0-beta.6", "nyc": "10.3.2", "urun": "0.0.8", "utest": "0.0.8"}, "files": ["lib/", "HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "node test/run.js", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "8f193cae10a2208010102fd50f0b61e869e14dcb", "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "homepage": "https://github.com/mysqljs/sqlstring#readme", "_id": "sqlstring@2.3.1", "_shasum": "475393ff9e91479aea62dcaf0ca3d14983a7fb40", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "475393ff9e91479aea62dcaf0ca3d14983a7fb40", "size": 6330, "noattachment": false, "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-2.3.1.tgz", "integrity": "sha512-ooAzh/7dxIG5+uDik1z/Rd1vli0+38izZhGzSa34FwR7IbelPWCCKSNIl8jlL/F7ERvy8CB2jNeM1E9i9mXMAQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "sidor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sqlstring_2.3.1_1519516938645_0.9513261619591036"}, "_hasShrinkwrap": false, "publish_time": 1519516938705, "_cnpm_publish_time": 1519516938705, "_cnpmcore_publish_time": "2021-12-16T10:39:00.492Z"}, "2.3.0": {"name": "sqlstring", "description": "Simple SQL escape and format for MySQL", "version": "2.3.0", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": {"type": "git", "url": "git+https://github.com/mysqljs/sqlstring.git"}, "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "4.8.0", "eslint-plugin-markdown": "1.0.0-beta.6", "nyc": "10.3.2", "urun": "0.0.8", "utest": "0.0.8"}, "files": ["lib/", "HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "node test/run.js", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "8e919225b7ad8a126345e4903ff6e5acb6d42cf2", "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "homepage": "https://github.com/mysqljs/sqlstring#readme", "_id": "sqlstring@2.3.0", "_shasum": "525b8a4fd26d6f71aa61e822a6caf976d31ad2a8", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "525b8a4fd26d6f71aa61e822a6caf976d31ad2a8", "size": 6271, "noattachment": false, "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-2.3.0.tgz", "integrity": "sha512-6GGE+iGeAJqxpcSzZFOwJmr97jtTszR6VYCDZp4pxuGjHRl8zSdpZfrU1fTpQ53s4tU0eLlrjD9wZpzJdEuchg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "sidor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sqlstring-2.3.0.tgz_1506914861137_0.21478801174089313"}, "directories": {}, "publish_time": 1506914862183, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506914862183, "_cnpmcore_publish_time": "2021-12-16T10:39:00.708Z"}, "2.2.0": {"name": "sqlstring", "description": "Simple SQL escape and format for MySQL", "version": "2.2.0", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": {"type": "git", "url": "https://github.com/mysqljs/sqlstring"}, "devDependencies": {"eslint": "3.9.1", "istanbul": "0.4.5", "mkdirp": "0.5.1", "require-all": "2.0.0", "rimraf": "2.2.8", "urun": "0.0.8", "utest": "0.0.8"}, "files": ["lib/", "HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint lib/**/*.js index.js test/**/*.js", "test": "node test/run.js", "test-ci": "node test/run-cov.js lcovonly", "test-cov": "node test/run-cov.js"}, "gitHead": "0c89dc18177ca0817d704167acf1ab9437a3fa7e", "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "homepage": "https://github.com/mysqljs/sqlstring", "_id": "sqlstring@2.2.0", "_shasum": "c3135c4ea8abcd7e7ee741a4966a891d86a4f191", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c3135c4ea8abcd7e7ee741a4966a891d86a4f191", "size": 5392, "noattachment": false, "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-2.2.0.tgz", "integrity": "sha512-ykqw3eJjyUTlQo06Hku92yCqBwt84cNIW7JFGLHskK/J7h82KlNH6eX5jb7PSXmH2uwndMrUt2AfDvnd+2yQMw=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/sqlstring-2.2.0.tgz_1478057241750_0.010372670134529471"}, "directories": {}, "publish_time": 1478057242252, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478057242252, "_cnpmcore_publish_time": "2021-12-16T10:39:00.915Z"}, "2.1.0": {"name": "sqlstring", "description": "Simple SQL escape and format for MySQL", "version": "2.1.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": {"type": "git", "url": "https://github.com/mysqljs/sqlstring"}, "devDependencies": {"eslint": "2.11.1", "istanbul": "0.4.5", "mkdirp": "0.5.1", "require-all": "2.0.0", "rimraf": "2.2.8", "urun": "0.0.8", "utest": "0.0.8"}, "files": ["lib/", "HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint lib/**/*.js index.js test/**/*.js", "test": "node test/run.js", "test-ci": "node test/run-cov.js lcovonly", "test-cov": "node test/run-cov.js"}, "gitHead": "9c324e4135655ead77a05d355162a5a51c5eec5a", "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "homepage": "https://github.com/mysqljs/sqlstring", "_id": "sqlstring@2.1.0", "_shasum": "5a3d992dbb4e2d075245725bc8044b8b4388d645", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5a3d992dbb4e2d075245725bc8044b8b4388d645", "size": 5202, "noattachment": false, "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-2.1.0.tgz", "integrity": "sha512-SLMSDZoeS3Pym10xtOyNcrBxyZ7vpBA5Jqd3MimNQeqqvku7VwhbU/CwCvzbh2P8t6A493TWbdc5dLqSEb+C4A=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/sqlstring-2.1.0.tgz_1474943312149_0.7986461834516376"}, "directories": {}, "publish_time": 1474943314117, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474943314117, "_cnpmcore_publish_time": "2021-12-16T10:39:01.188Z"}, "2.0.1": {"name": "sqlstring", "description": "Simple SQL escape and format for MySQL", "version": "2.0.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": {"type": "git", "url": "https://github.com/mysqljs/sqlstring"}, "devDependencies": {"eslint": "2.11.1", "istanbul": "0.4.3", "mkdirp": "0.5.1", "require-all": "2.0.0", "rimraf": "2.2.8", "urun": "0.0.8", "utest": "0.0.8"}, "files": ["lib/", "HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint lib/**/*.js index.js test/**/*.js", "test": "node test/run.js", "test-ci": "node test/run-cov.js lcovonly", "test-cov": "node test/run-cov.js"}, "gitHead": "b7e11a5bfdfa1da198e32a046d6dcfaf04e141dc", "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "homepage": "https://github.com/mysqljs/sqlstring", "_id": "sqlstring@2.0.1", "_shasum": "bcd3c3931af1a5ab7d54a558eb3563ca3d378b98", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bcd3c3931af1a5ab7d54a558eb3563ca3d378b98", "size": 5147, "noattachment": false, "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-2.0.1.tgz", "integrity": "sha512-rCevKVuF40d9Z1nZPe8AjT2amDTxvI4dnsvmwAS7TUagoQ2OnKRHlZstIQpHYmUaaTqNInGGxhJrGNcJL2c8Eg=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/sqlstring-2.0.1.tgz_1465242438154_0.3652630455326289"}, "directories": {}, "publish_time": 1465242439886, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465242439886, "_cnpmcore_publish_time": "2021-12-16T10:39:01.442Z"}, "2.0.0": {"name": "sqlstring", "description": "Simple SQL escape and format for MySQL", "version": "2.0.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": {"type": "git", "url": "https://github.com/mysqljs/sqlstring"}, "devDependencies": {"eslint": "2.11.1", "istanbul": "0.4.3", "mkdirp": "0.5.1", "require-all": "2.0.0", "rimraf": "2.2.8", "urun": "0.0.8", "utest": "0.0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint lib/**/*.js index.js test/**/*.js", "test": "node test/run.js", "test-ci": "node test/run-cov.js lcovonly", "test-cov": "node test/run-cov.js"}, "gitHead": "d1dcb4a2f9a3d2f7db03396abb5e107bfa23cb04", "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "homepage": "https://github.com/mysqljs/sqlstring", "_id": "sqlstring@2.0.0", "_shasum": "49eab0454de451e385250b223aee8575d46721f8", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "49eab0454de451e385250b223aee8575d46721f8", "size": 3578, "noattachment": false, "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-2.0.0.tgz", "integrity": "sha512-sUtomy5q/9/mUQLLTvc9e2UTaIoMw87uAOg270EWuKAme6x7lwr3KspHOlCX7V4kMDpn8YMWID6LfY45sQVggg=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/sqlstring-2.0.0.tgz_1465242257476_0.07983834785409272"}, "directories": {}, "publish_time": 1465242259336, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465242259336, "_cnpmcore_publish_time": "2021-12-16T10:39:01.706Z"}, "1.0.0": {"name": "sqlstring", "version": "1.0.0", "description": "SQL escape and format from mysql/lib/protocol/SqlString.js", "main": "index.js", "scripts": {"test": "make test-all"}, "config": {"blanket": {"pattern": "sqlstring/lib", "data-cover-flags": {"debug": false}}, "travis-cov": {"threshold": 20}}, "dependencies": {}, "devDependencies": {"autod": "*", "blanket": "*", "contributors": "*", "cov": "*", "coveralls": "*", "mocha": "*", "mocha-lcov-reporter": "*", "should": "3.1.2", "travis-cov": "*"}, "homepage": "https://github.com/node-modules/sqlstring", "repository": {"type": "git", "url": "git://github.com/node-modules/sqlstring.git", "web": "https://github.com/node-modules/sqlstring"}, "bugs": {"url": "https://github.com/node-modules/sqlstring/issues", "email": "<EMAIL>"}, "keywords": ["sqlstring", "sql", "escape", "sql escape"], "engines": {"node": ">= 0.8.0"}, "author": {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, "license": "MIT", "contributors": [], "gitHead": "dc525e1fb3d2225a758749b8c41708647d50082b", "_id": "sqlstring@1.0.0", "_shasum": "077dd8b1cebd06d78f7eefd56b07ae5d7ccc897c", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "dist": {"shasum": "077dd8b1cebd06d78f7eefd56b07ae5d7ccc897c", "size": 3566, "noattachment": false, "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-1.0.0.tgz", "integrity": "sha512-7daDqfZnrvinEN0egORzlQ3xjq6dlo41kjOfsq9hDAJMAANf2UftmbBm6BQMk6d62vRLf0N2AC7nk1sD3Ru7wg=="}, "directories": {}, "publish_time": 1415526014414, "_hasShrinkwrap": false, "_cnpm_publish_time": 1415526014414, "_cnpmcore_publish_time": "2021-12-16T10:39:01.985Z"}, "0.0.1": {"name": "sqlstring", "version": "0.0.1", "description": "SQL escape and format from mysql/lib/protocol/SqlString.js", "main": "index.js", "scripts": {"test": "make test-all"}, "config": {"blanket": {"pattern": "sqlstring/lib", "data-cover-flags": {"debug": false}}, "travis-cov": {"threshold": 20}}, "dependencies": {}, "devDependencies": {"autod": "*", "blanket": "*", "contributors": "*", "cov": "*", "coveralls": "*", "mocha": "*", "mocha-lcov-reporter": "*", "should": "3.1.2", "travis-cov": "*"}, "homepage": "https://github.com/fengmk2/sqlstring", "repository": {"type": "git", "url": "git://github.com/fengmk2/sqlstring.git", "web": "https://github.com/fengmk2/sqlstring"}, "bugs": {"url": "https://github.com/fengmk2/sqlstring/issues", "email": "<EMAIL>"}, "keywords": ["sqlstring", "sql", "escape", "sql escape"], "engines": {"node": ">= 0.10.0"}, "author": {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, "license": "MIT", "contributors": [], "_id": "sqlstring@0.0.1", "dist": {"shasum": "0916399d7e7b80df3195c488aa66ea66c891f867", "size": 3348, "noattachment": false, "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-0.0.1.tgz", "integrity": "sha512-sCdm4AkA9f32gNVwlMPNhOCCpJ1Sy4lLcq6GiVTAeSBjS9w+wXfPHGzRKxkbDeXTudiTQiM38rc0DbgBRYuI9w=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1393299844681, "_hasShrinkwrap": false, "_cnpm_publish_time": 1393299844681, "_cnpmcore_publish_time": "2021-12-16T10:39:02.253Z"}, "2.3.3": {"name": "sqlstring", "description": "Simple SQL escape and format for MySQL", "version": "2.3.3", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": {"type": "git", "url": "git+https://github.com/mysqljs/sqlstring.git"}, "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "nyc": "15.1.0", "urun": "0.0.8", "utest": "0.0.8"}, "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "node test/run.js", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "5aa85a7ae8ee1c1ace84e4b5d099836712f54275", "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "homepage": "https://github.com/mysqljs/sqlstring#readme", "_id": "sqlstring@2.3.3", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-qC9iz2FlN7DQl3+wjwn3802RTyjCx7sDvfQEXchwa6CWOx07/WVfh91gBmQ9fahw8snwGEWU3xGzOt4tFyHLxg==", "shasum": "2ddc21f03bce2c387ed60680e739922c65751d0c", "tarball": "https://registry.npmmirror.com/sqlstring/-/sqlstring-2.3.3.tgz", "fileCount": 6, "unpackedSize": 17255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJSHoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqVQ/+LzElTtBicNsoEPZfGxWCWypcDbL76KhF217kOtVN7D8vcZ5a\r\nM4Z8//scrvj1fdbuxs0zToJ3LzuGuPztfn8PfAsWa7xYpqiR8Xk1m4ZsSs1h\r\n9smNZNyPlrM5EP2FasW+8OeFZ/e0j3O85xt3SYkZgi0l2+VvzCsv80tVS+qz\r\nsET0Lp3bAVBhU/PTZfUn/5JTU7NoAof+l4ZwAJtzeDG+84D71D67WXXAvvnG\r\n7ZGpXObnZVfRz9Nlx6IL/x6odnJSwyIC1UYoTMP587tva8TwECD3aH8TzO4T\r\n1x4QObtXGHhambn1fwReQro1V6pNhtXbXZ9JblcK1Zh3LN69siaJKL5Li+yv\r\n24L1haLe1Oajp80eUY4Ic68FyZQ9efXTumOTdq5wyFGXoCkCzZA46O9JXPpQ\r\ntTPnDYgts/xFpwnm/Z7A2Mx8xFm9uWRTUo0u8O1AM6HQ4hjOR3as3WytY7W3\r\nALgHARel+pZ+NoLtWLXN6w4rX2XEPTXZ+7w0+U1jFHCwBcPYjl7EgJdGSiY3\r\nUWjoNCcwOcf1iDGFWnwbzUqAjDtLhtPxyyMFPFx9n3piuaLPcFc7SQSt4egp\r\ngidg8CY2/XUj/MbqK8bPLXC5Qma7fB9KCGXcfjUR7U1mhvymDikR2S9796ft\r\nXzpwWu/DiiCt67qx8OjmzAc1nsOWjOJTWRs=\r\n=s87f\r\n-----END PGP SIGNATURE-----\r\n", "size": 6369}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sqlstring_2.3.3_1646600679896_0.8295277746063681"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-06T21:04:43.292Z"}}, "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mysqljs/sqlstring#readme", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "repository": {"type": "git", "url": "git+https://github.com/mysqljs/sqlstring.git"}, "_source_registry_name": "default"}