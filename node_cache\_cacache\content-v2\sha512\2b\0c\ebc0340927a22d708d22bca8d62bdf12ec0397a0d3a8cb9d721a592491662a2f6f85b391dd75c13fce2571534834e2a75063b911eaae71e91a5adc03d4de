{"_attachments": {}, "_id": "lodash.isplainobject", "_rev": "781-61f1453fb677e08f5113b7b0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "description": "The lodash method `_.isPlainObject` exported as a module.", "dist-tags": {"latest": "4.0.6"}, "license": "MIT", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "lodash.isplainobject", "readme": "# lodash.isplainobject v4.0.6\n\nThe [lodash](https://lodash.com/) method `_.isPlainObject` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isplainobject\n```\n\nIn Node.js:\n```js\nvar isPlainObject = require('lodash.isplainobject');\n```\n\nSee the [documentation](https://lodash.com/docs#isPlainObject) or [package source](https://github.com/lodash/lodash/blob/4.0.6-npm-packages/lodash.isplainobject) for more details.\n", "time": {"created": "2022-01-26T12:57:35.490Z", "modified": "2023-07-28T04:06:43.708Z", "4.0.6": "2016-08-13T17:41:07.730Z", "4.0.5": "2016-07-25T14:46:28.634Z", "4.0.4": "2016-04-03T03:50:15.384Z", "4.0.3": "2016-02-24T16:22:12.961Z", "4.0.2": "2016-02-16T09:36:36.279Z", "4.0.1": "2016-02-03T07:28:53.142Z", "4.0.0": "2016-01-13T11:06:06.941Z", "3.2.0": "2015-06-30T15:21:42.104Z", "3.1.0": "2015-05-19T19:52:23.266Z", "3.0.2": "2015-04-16T16:32:00.224Z", "3.0.1": "2015-03-25T23:36:02.824Z", "3.0.0": "2015-01-26T15:29:26.090Z", "2.4.1": "2013-12-03T17:14:43.989Z", "2.4.0": "2013-11-26T19:55:56.026Z", "2.3.0": "2013-11-11T16:47:50.892Z", "2.2.1": "2013-10-03T18:50:29.346Z", "2.2.0": "2013-09-29T22:09:54.093Z", "2.1.0": "2013-09-23T07:56:34.793Z", "2.0.0": "2013-09-23T07:38:13.320Z"}, "versions": {"4.0.6": {"name": "lodash.isplainobject", "version": "4.0.6", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.6", "_shasum": "7c526a52d89b45c45cc690b88163be0497f550cb", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7c526a52d89b45c45cc690b88163be0497f550cb", "size": 3031, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.6.tgz_1471110064885_0.12097060843370855"}, "directories": {}, "publish_time": 1471110067730, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471110067730, "_cnpmcore_publish_time": "2021-12-13T13:40:42.448Z"}, "4.0.5": {"name": "lodash.isplainobject", "version": "4.0.5", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.5", "_shasum": "0a7520765351ec3bc764ceb5fda07f5ed5f3560d", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0a7520765351ec3bc764ceb5fda07f5ed5f3560d", "size": 3086, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.5.tgz", "integrity": "sha512-PJGSj3BYFLNA3MUJAtASIoBfRTEbfVH1nh6o6qA1PHq7IE7iXuGwv4xaNAEZP63f6nHk9hHEon/GYlXwsecxIA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.5.tgz_1469457985171_0.5964741928037256"}, "directories": {}, "publish_time": 1469457988634, "_hasShrinkwrap": false, "_cnpm_publish_time": 1469457988634, "_cnpmcore_publish_time": "2021-12-13T13:40:42.769Z"}, "4.0.4": {"name": "lodash.isplainobject", "version": "4.0.4", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.4", "_shasum": "61c7b80ef01fbeea60cd4bf76957580188fa6b1c", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "61c7b80ef01fbeea60cd4bf76957580188fa6b1c", "size": 2963, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.4.tgz", "integrity": "sha512-XHHWBSw2MXzCqjKE7xiSOtNDzpmSR1+yCzto7HZ61dubRBkNawlJ7qpsOhpPAKW3/j6KLX5v6/PNleBG313CZA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.4.tgz_1459655414760_0.7456746795214713"}, "directories": {}, "publish_time": 1459655415384, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459655415384, "_cnpmcore_publish_time": "2021-12-13T13:40:43.046Z"}, "4.0.3": {"name": "lodash.isplainobject", "version": "4.0.3", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.3", "_shasum": "32d7a64edaf9da9ae48188cd7aa7cfd07515efc1", "_from": ".", "_npmVersion": "2.14.17", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "32d7a64edaf9da9ae48188cd7aa7cfd07515efc1", "size": 2527, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.3.tgz", "integrity": "sha512-SJTgqr7yyGfxaRHKN+Jk/IGiQsd2lmw+b4F+3ZyPwtPgFJuDVnbA7861vMVeHMM9CXXUa3GnSqxlN/Pep5qDQQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.3.tgz_1456330932268_0.40299616008996964"}, "directories": {}, "publish_time": 1456330932961, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456330932961, "_cnpmcore_publish_time": "2021-12-13T13:40:43.372Z"}, "4.0.2": {"name": "lodash.isplainobject", "version": "4.0.2", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.2", "_shasum": "72d737a3fa74c8c1b8ec0faf1e468d1e4ca02efb", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "72d737a3fa74c8c1b8ec0faf1e468d1e4ca02efb", "size": 2543, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.2.tgz", "integrity": "sha512-GOCDvdp0Ak/0shsQBspaZ+CiK3x5EElZ2u4VQ5OGjocS8kuDUtJJK0+mxJbYvhHwt/w9QnlODjvpl4qlkhXhpg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.2.tgz_1455615394325_0.4941383805125952"}, "directories": {}, "publish_time": 1455615396279, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455615396279, "_cnpmcore_publish_time": "2021-12-13T13:40:43.661Z"}, "4.0.1": {"name": "lodash.isplainobject", "version": "4.0.1", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.1", "_shasum": "12af3dcc3f36297869ccdb82c6fda8f01896abc5", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "12af3dcc3f36297869ccdb82c6fda8f01896abc5", "size": 2530, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.1.tgz", "integrity": "sha512-JJlFVyArqDQYdhAe1nC6JQ0io30/0eyDgP4yxNWQgrUZVvNZLIGiPM1iiJ/zd9KF45YmBOuHJJG3qf/bKp8Cfw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isplainobject-4.0.1.tgz_1454484532395_0.5940206786617637"}, "directories": {}, "publish_time": 1454484533142, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454484533142, "_cnpmcore_publish_time": "2021-12-13T13:40:43.976Z"}, "4.0.0": {"name": "lodash.isplainobject", "version": "4.0.0", "description": "The lodash method `_.isPlainObject` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "isplainobject"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@4.0.0", "_shasum": "70f6d1e860a12bc1a2977fd170b2a7f7327c14fa", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "70f6d1e860a12bc1a2977fd170b2a7f7327c14fa", "size": 2554, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.0.tgz", "integrity": "sha512-ZkePJ/JCgT8bN3e/4WwcFmINEUeoCgRHsLwyTtAYTJcrxJngpdR/pPZZyjNHONbxsKpRIU93Wg7Hpw5SDEDKTw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452683166941, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452683166941, "_cnpmcore_publish_time": "2021-12-13T13:40:44.335Z"}, "3.2.0": {"name": "lodash.isplainobject", "version": "3.2.0", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.2.0", "_shasum": "9a8238ae16b200432960cd7346512d0123fbf4c5", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "9a8238ae16b200432960cd7346512d0123fbf4c5", "size": 2804, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-3.2.0.tgz", "integrity": "sha512-P4wZnho5curNqeEq/x292Pb57e1v+woR7DJ84DURelKB46lby8aDEGVobSaYtzHdQBWQrJSdxcCwjlGOvvdIyg=="}, "directories": {}, "publish_time": 1435677702104, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435677702104, "_cnpmcore_publish_time": "2021-12-13T13:40:44.657Z"}, "3.1.0": {"name": "lodash.isplainobject", "version": "3.1.0", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash._getnative": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.1.0", "_shasum": "52c4231910e3eda5719c7d21d8bfdd74aad5376a", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "0.12.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "52c4231910e3eda5719c7d21d8bfdd74aad5376a", "size": 2965, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-3.1.0.tgz", "integrity": "sha512-DzehkwGgUBWBwGGL01+yQNH07kLIzONaEZ8bPSjXuwywbO4ctD3rV1aLTOn3GCczJ65PdMUFUvxuYgnw4jUKrg=="}, "directories": {}, "publish_time": 1432065143266, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432065143266, "_cnpmcore_publish_time": "2021-12-13T13:40:44.976Z"}, "3.0.2": {"name": "lodash.isplainobject", "version": "3.0.2", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash.isnative": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.0.2", "_shasum": "15d4dcf27f89986b71e25b995e1f916ffbf2d561", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "15d4dcf27f89986b71e25b995e1f916ffbf2d561", "size": 2967, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-3.0.2.tgz", "integrity": "sha512-+Rf3lIANaz3hYCbVwgqLF9fR7P1QKuU7iiY11y6mAKmM8tTbQQDe6cNHiZejMNWJVcywwHNa+fN/YfT5Tl18AA=="}, "directories": {}, "publish_time": 1429201920224, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429201920224, "_cnpmcore_publish_time": "2021-12-13T13:40:45.305Z"}, "3.0.1": {"name": "lodash.isplainobject", "version": "3.0.1", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash.isnative": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.0.1", "_shasum": "94b601b0c281651e17c988e8589162c1de3bb176", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "94b601b0c281651e17c988e8589162c1de3bb176", "size": 2969, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-3.0.1.tgz", "integrity": "sha512-jUKYvBBDQTd7yijiXaNDVD2YcRr56EhI2roK+GwyMdePQlhnrb+HJBla5DRoOO+ZhP2YLMfECoeJqOBjoK/k5g=="}, "directories": {}, "publish_time": 1427326562824, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427326562824, "_cnpmcore_publish_time": "2021-12-13T13:40:45.707Z"}, "3.0.0": {"name": "lodash.isplainobject", "version": "3.0.0", "description": "The modern build of lodash’s `_.isPlainObject` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._basefor": "^3.0.0", "lodash.isnative": "^3.0.0", "lodash.keysin": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isplainobject@3.0.0", "_shasum": "a0e0d00714e900bf33b26c3b8cef13b858042fa2", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "a0e0d00714e900bf33b26c3b8cef13b858042fa2", "size": 2985, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-3.0.0.tgz", "integrity": "sha512-16Y59ucRgfWr+1KTUeGXyN1QQWQhgTidvWx+FjhzsIoGtaJLI+k54Z8+MG6alT3QooKJme2cXPwfxkiu8DbJ7A=="}, "directories": {}, "publish_time": 1422286166090, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422286166090, "_cnpmcore_publish_time": "2021-12-13T13:40:46.110Z"}, "2.4.1": {"name": "lodash.isplainobject", "version": "2.4.1", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._isnative": "~2.4.1", "lodash._shimisplainobject": "~2.4.1"}, "readmeFilename": "README.md", "_id": "lodash.isplainobject@2.4.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-2.4.1.tgz", "shasum": "ac7385e2ea9ac0321f30dc3b8032a6d2231a8011", "size": 2400, "noattachment": false, "integrity": "sha512-2giZfjfnDTJbU/WOB8vzWvO6/8+6btWKwvbZZirewRf+SEGr8CiygksV1BhIv6XEmGYAoGXydq5Ekg9S21q0QQ=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386090883989, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386090883989, "_cnpmcore_publish_time": "2021-12-13T13:40:46.539Z"}, "2.4.0": {"name": "lodash.isplainobject", "version": "2.4.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.4.0", "lodash._shimisplainobject": "~2.4.0"}, "readmeFilename": "README.md", "_id": "lodash.isplainobject@2.4.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-2.4.0.tgz", "shasum": "a3539e240eb8f880495338373632d200878ee22b", "size": 2402, "noattachment": false, "integrity": "sha512-NlEbQkBN8wBhDJuUWT6z3Yssm2hydhbxzcmQ4FKVOOTdHCfj2yBD7KWrDZj2Fh6Urem5azjlYFiP7aCZkYN+CQ=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1385495756026, "_hasShrinkwrap": false, "_cnpm_publish_time": 1385495756026, "_cnpmcore_publish_time": "2021-12-13T13:40:46.989Z"}, "2.3.0": {"name": "lodash.isplainobject", "version": "2.3.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.3.0", "lodash._shimisplainobject": "~2.3.0"}, "readmeFilename": "README.md", "_id": "lodash.isplainobject@2.3.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-2.3.0.tgz", "shasum": "ccb0c70aa4882f3dbc73f9c3bd39e7984c7a27f7", "size": 2399, "noattachment": false, "integrity": "sha512-SUkmzY55hlsMDEFJGxdsq5GYz+7jLdenmRC+3fq7DmLqE5Elvux47oyUF6yh3OdOOVtNGWSZBfhaAeLhp7BBeA=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384188470892, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384188470892, "_cnpmcore_publish_time": "2021-12-13T13:40:47.393Z"}, "2.2.1": {"name": "lodash.isplainobject", "version": "2.2.1", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.2.1", "lodash._shimisplainobject": "~2.2.1"}, "readmeFilename": "README.md", "_id": "lodash.isplainobject@2.2.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-2.2.1.tgz", "shasum": "c08c1de9ceecf5109f84709ddd52ffcde1a76464", "size": 2403, "noattachment": false, "integrity": "sha512-JKcDB73B77QWlP6+19F68V3GR2m3Ln/B9xk2++a1E/5UALVKVFs10WNTw2sHIpHqZLufS1cxl6r/f+nFzkDADg=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380826229346, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380826229346, "_cnpmcore_publish_time": "2021-12-13T13:40:47.828Z"}, "2.2.0": {"name": "lodash.isplainobject", "version": "2.2.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.2.0", "lodash._shimisplainobject": "~2.2.0"}, "readmeFilename": "README.md", "_id": "lodash.isplainobject@2.2.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-2.2.0.tgz", "shasum": "f2938963137c9922cef607e38e68ecacbb9c5b6c", "size": 2445, "noattachment": false, "integrity": "sha512-UHQo1MTGHU9q7x6PO3VgoP4+3GGKxnwIzXQswr9Ls/KFiQA6iIW7JFsjsilVsLtnHIhzIerv1QiR12SGd+PIJw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380492594093, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380492594093, "_cnpmcore_publish_time": "2021-12-13T13:40:48.255Z"}, "2.1.0": {"name": "lodash.isplainobject", "version": "2.1.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.1.0", "lodash._shimisplainobject": "~2.1.0"}, "readmeFilename": "README.md", "_id": "lodash.isplainobject@2.1.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-2.1.0.tgz", "shasum": "e6867d54cc91105191ed6b8502b59d068f1391be", "size": 2458, "noattachment": false, "integrity": "sha512-C2IRbB95w4LvlIEWKLYhwKUnd3yX/AepboAWdnpZEafGAEbrVTHGMhEjFh0xCfvBMcz0gPr64TOu8edWomlYTQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379922994793, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379922994793, "_cnpmcore_publish_time": "2021-12-13T13:40:48.746Z"}, "2.0.0": {"name": "lodash.isplainobject", "version": "2.0.0", "description": "The Lo-Dash function `_.isPlainObject` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash._renative": "~2.0.0", "lodash._shimisplainobject": "~2.0.0"}, "readmeFilename": "README.md", "_id": "lodash.isplainobject@2.0.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-2.0.0.tgz", "shasum": "d2964a26b3c7afb78bd86bce6766cbca311c0129", "size": 2428, "noattachment": false, "integrity": "sha512-Kl8nhSRuxUjCtgu9CbLlcfNSbRgZaHD3co7+9WdWErr5CXKEqb2mn1hQYoGnufMcxTTTQaj67wgllE5n46FwPw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379921893320, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379921893320, "_cnpmcore_publish_time": "2021-12-13T13:40:49.184Z"}}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "isplainobject"], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "_source_registry_name": "default"}