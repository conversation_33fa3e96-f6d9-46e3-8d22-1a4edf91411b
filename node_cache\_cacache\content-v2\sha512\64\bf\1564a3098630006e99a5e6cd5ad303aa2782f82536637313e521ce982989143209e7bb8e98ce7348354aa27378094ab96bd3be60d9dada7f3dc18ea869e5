{"_attachments": {}, "_id": "bcrypt", "_rev": "86364-61f1889e06e2bc05a1d8ddf3", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "description": "A bcrypt library for NodeJS.", "dist-tags": {"latest": "6.0.0", "n-api": "1.1.0-napi", "napi": "3.0.4-napi"}, "license": "MIT", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}, {"name": "amitosh", "email": "<EMAIL>"}], "name": "bcrypt", "readme": "# node.bcrypt.js\n\n[![ci](https://github.com/kelektiv/node.bcrypt.js/actions/workflows/ci.yaml/badge.svg)](https://github.com/kelektiv/node.bcrypt.js/actions/workflows/ci.yaml)\n\n[![Build Status](https://ci.appveyor.com/api/projects/status/github/kelektiv/node.bcrypt.js)](https://ci.appveyor.com/project/defunctzombie/node-bcrypt-js-pgo26/branch/master)\n\nA library to help you hash passwords.\n\nYou can read about [bcrypt in Wikipedia][bcryptwiki] as well as in the following article:\n[How To Safely Store A Password][codahale]\n\n## If You Are Submitting Bugs or Issues\n\nPlease verify that the NodeJS version you are using is a _stable_ version; Unstable versions are currently not supported and issues created while using an unstable version will be closed.\n\nIf you are on a stable version of NodeJS, please provide a sufficient code snippet or log files for installation issues. The code snippet does not require you to include confidential information. However, it must provide enough information so the problem can be replicable, or it may be closed without an explanation.\n\n\n## Version Compatibility\n\n_Please upgrade to atleast v5.0.0 to avoid security issues mentioned below._\n\n| Node Version   |   Bcrypt Version  |\n| -------------- | ------------------|\n| 0.4            | <= 0.4            |\n| 0.6, 0.8, 0.10 | >= 0.5            |\n| 0.11           | >= 0.8            |\n| 4              | <= 2.1.0          |\n| 8              | >= 1.0.3 < 4.0.0  |\n| 10, 11         | >= 3              |\n| 12 onwards     | >= 3.0.6          |\n\n`node-gyp` only works with stable/released versions of node. Since the `bcrypt` module uses `node-gyp` to build and install, you'll need a stable version of node to use bcrypt. If you do not, you'll likely see an error that starts with:\n\n```\ngyp ERR! stack Error: \"pre\" versions of node cannot be installed, use the --nodedir flag instead\n```\n\n## Security Issues And Concerns\n\n> Per bcrypt implementation, only the first 72 bytes of a string are used. Any extra bytes are ignored when matching passwords. Note that this is not the first 72 *characters*. It is possible for a string to contain less than 72 characters, while taking up more than 72 bytes (e.g. a UTF-8 encoded string containing emojis). If a string is provided, it will be encoded using UTF-8.\n\nAs should be the case with any security tool, anyone using this library should scrutinise it. If you find or suspect an issue with the code, please bring it to the maintainers' attention. We will spend some time ensuring that this library is as secure as possible.\n\nHere is a list of BCrypt-related security issues/concerns that have come up over the years.\n\n* An [issue with passwords][jtr] was found with a version of the Blowfish algorithm developed for John the Ripper. This is not present in the OpenBSD version and is thus not a problem for this module. HT [zooko][zooko].\n* Versions `< 5.0.0` suffer from bcrypt wrap-around bug and _will truncate passwords >= 255 characters leading to severely weakened passwords_. Please upgrade at  earliest. See [this wiki page][wrap-around-bug] for more details.\n* Versions `< 5.0.0` _do not handle NUL characters inside passwords properly leading to all subsequent characters being dropped and thus resulting in severely weakened passwords_. Please upgrade at earliest. See [this wiki page][improper-nuls] for more details.\n\n## Compatibility Note\n\nThis library supports `$2a$` and `$2b$` prefix bcrypt hashes. `$2x$` and `$2y$` hashes are specific to bcrypt implementation developed for John the Ripper. In theory, they should be compatible with `$2b$` prefix.\n\nCompatibility with hashes generated by other languages is not 100% guaranteed due to difference in character encodings. However, it should not be an issue for most cases.\n\n### Migrating from v1.0.x\n\nHashes generated in earlier version of `bcrypt` remain 100% supported in `v2.x.x` and later versions. In most cases, the migration should be a bump in the `package.json`.\n\nHashes generated in `v2.x.x` using the defaults parameters will not work in earlier versions.\n\n## Dependencies\n\n* NodeJS\n* `node-gyp`\n * Please check the dependencies for this tool at: https://github.com/nodejs/node-gyp\n  * Windows users will need the options for c# and c++ installed with their visual studio instance.\n  * Python 2.x/3.x\n* `OpenSSL` - This is only required to build the `bcrypt` project if you are using versions <= 0.7.7. Otherwise, we're using the builtin node crypto bindings for seed data (which use the same OpenSSL code paths we were, but don't have the external dependency).\n\n## Install via NPM\n\n```\nnpm install bcrypt\n```\n***Note:*** OS X users using Xcode 4.3.1 or above may need to run the following command in their terminal prior to installing if errors occur regarding xcodebuild: ```sudo xcode-select -switch /Applications/Xcode.app/Contents/Developer```\n\n_Pre-built binaries for various NodeJS versions are made available on a best-effort basis._\n\nOnly the current stable and supported LTS releases are actively tested against.\n\n_There may be an interval between the release of the module and the availabilty of the compiled modules._\n\nCurrently, we have pre-built binaries that support the following platforms:\n\n1. Windows x32 and x64\n2. Linux x64 (GlibC and musl)\n3. macOS\n\nIf you face an error like this:\n\n```\nnode-pre-gyp ERR! Tried to download(404): https://github.com/kelektiv/node.bcrypt.js/releases/download/v1.0.2/bcrypt_lib-v1.0.2-node-v48-linux-x64.tar.gz\n```\n\nmake sure you have the appropriate dependencies installed and configured for your platform. You can find installation instructions for the dependencies for some common platforms [in this page][depsinstall].\n\n## Usage\n\n### async (recommended)\n\n```javascript\nconst bcrypt = require('bcrypt');\nconst saltRounds = 10;\nconst myPlaintextPassword = 's0/\\/\\P4$$w0rD';\nconst someOtherPlaintextPassword = 'not_bacon';\n```\n\n#### To hash a password:\n\nTechnique 1 (generate a salt and hash on separate function calls):\n\n```javascript\nbcrypt.genSalt(saltRounds, function(err, salt) {\n    bcrypt.hash(myPlaintextPassword, salt, function(err, hash) {\n        // Store hash in your password DB.\n    });\n});\n```\n\nTechnique 2 (auto-gen a salt and hash):\n\n```javascript\nbcrypt.hash(myPlaintextPassword, saltRounds, function(err, hash) {\n    // Store hash in your password DB.\n});\n```\n\nNote that both techniques achieve the same end-result.\n\n#### To check a password:\n\n```javascript\n// Load hash from your password DB.\nbcrypt.compare(myPlaintextPassword, hash, function(err, result) {\n    // result == true\n});\nbcrypt.compare(someOtherPlaintextPassword, hash, function(err, result) {\n    // result == false\n});\n```\n\n[A Note on Timing Attacks](#a-note-on-timing-attacks)\n\n### with promises\n\nbcrypt uses whatever `Promise` implementation is available in `global.Promise`. NodeJS >= 0.12 has a native `Promise` implementation built in. However, this should work in any Promises/A+ compliant implementation.\n\nAsync methods that accept a callback, return a `Promise` when callback is not specified if Promise support is available.\n\n```javascript\nbcrypt.hash(myPlaintextPassword, saltRounds).then(function(hash) {\n    // Store hash in your password DB.\n});\n```\n```javascript\n// Load hash from your password DB.\nbcrypt.compare(myPlaintextPassword, hash).then(function(result) {\n    // result == true\n});\nbcrypt.compare(someOtherPlaintextPassword, hash).then(function(result) {\n    // result == false\n});\n```\n\nThis is also compatible with `async/await`\n\n```javascript\nasync function checkUser(username, password) {\n    //... fetch user from a db etc.\n\n    const match = await bcrypt.compare(password, user.passwordHash);\n\n    if(match) {\n        //login\n    }\n\n    //...\n}\n```\n\n### ESM import\n```javascript\nimport bcrypt from \"bcrypt\";\n\n// later\nawait bcrypt.compare(password, hash);\n```\n\n### sync\n\n```javascript\nconst bcrypt = require('bcrypt');\nconst saltRounds = 10;\nconst myPlaintextPassword = 's0/\\/\\P4$$w0rD';\nconst someOtherPlaintextPassword = 'not_bacon';\n```\n\n#### To hash a password:\n\nTechnique 1 (generate a salt and hash on separate function calls):\n\n```javascript\nconst salt = bcrypt.genSaltSync(saltRounds);\nconst hash = bcrypt.hashSync(myPlaintextPassword, salt);\n// Store hash in your password DB.\n```\n\nTechnique 2 (auto-gen a salt and hash):\n\n```javascript\nconst hash = bcrypt.hashSync(myPlaintextPassword, saltRounds);\n// Store hash in your password DB.\n```\n\nAs with async, both techniques achieve the same end-result.\n\n#### To check a password:\n\n```javascript\n// Load hash from your password DB.\nbcrypt.compareSync(myPlaintextPassword, hash); // true\nbcrypt.compareSync(someOtherPlaintextPassword, hash); // false\n```\n\n[A Note on Timing Attacks](#a-note-on-timing-attacks)\n\n### Why is async mode recommended over sync mode?\nWe recommend using async API if you use `bcrypt` on a server. Bcrypt hashing is CPU intensive which will cause the sync APIs to block the event loop and prevent your application from servicing any inbound requests or events. The async version uses a thread pool which does not block the main event loop.\n\n## API\n\n`BCrypt.`\n\n  * `genSaltSync(rounds, minor)`\n    * `rounds` - [OPTIONAL] - the cost of processing the data. (default - 10)\n    * `minor` - [OPTIONAL] - minor version of bcrypt to use. (default - b)\n  * `genSalt(rounds, minor, cb)`\n    * `rounds` - [OPTIONAL] - the cost of processing the data. (default - 10)\n    * `minor` - [OPTIONAL] - minor version of bcrypt to use. (default - b)\n    * `cb` - [OPTIONAL] - a callback to be fired once the salt has been generated. uses eio making it asynchronous. If `cb` is not specified, a `Promise` is returned if Promise support is available.\n      * `err` - First parameter to the callback detailing any errors.\n      * `salt` - Second parameter to the callback providing the generated salt.\n  * `hashSync(data, salt)`\n    * `data` - [REQUIRED] - the data to be encrypted.\n    * `salt` - [REQUIRED] - the salt to be used to hash the password. if specified as a number then a salt will be generated with the specified number of rounds and used (see example under **Usage**).\n  * `hash(data, salt, cb)`\n    * `data` - [REQUIRED] - the data to be encrypted.\n    * `salt` - [REQUIRED] - the salt to be used to hash the password. if specified as a number then a salt will be generated with the specified number of rounds and used (see example under **Usage**).\n    * `cb` - [OPTIONAL] - a callback to be fired once the data has been encrypted. uses eio making it asynchronous. If `cb` is not specified, a `Promise` is returned if Promise support is available.\n      * `err` - First parameter to the callback detailing any errors.\n      * `encrypted` - Second parameter to the callback providing the encrypted form.\n  * `compareSync(data, encrypted)`\n    * `data` - [REQUIRED] - data to compare.\n    * `encrypted` - [REQUIRED] - data to be compared to.\n  * `compare(data, encrypted, cb)`\n    * `data` - [REQUIRED] - data to compare.\n    * `encrypted` - [REQUIRED] - data to be compared to.\n    * `cb` - [OPTIONAL] - a callback to be fired once the data has been compared. uses eio making it asynchronous. If `cb` is not specified, a `Promise` is returned if Promise support is available.\n      * `err` - First parameter to the callback detailing any errors.\n      * `same` - Second parameter to the callback providing whether the data and encrypted forms match [true | false].\n  * `getRounds(encrypted)` - return the number of rounds used to encrypt a given hash\n    * `encrypted` - [REQUIRED] - hash from which the number of rounds used should be extracted.\n\n## A Note on Rounds\n\nA note about the cost: when you are hashing your data, the module will go through a series of rounds to give you a secure hash. The value you submit is not just the number of rounds the module will go through to hash your data. The module will use the value you enter and go through `2^rounds` hashing iterations.\n\nFrom @garthk, on a 2GHz core you can roughly expect:\n\n    rounds=8 : ~40 hashes/sec\n    rounds=9 : ~20 hashes/sec\n    rounds=10: ~10 hashes/sec\n    rounds=11: ~5  hashes/sec\n    rounds=12: 2-3 hashes/sec\n    rounds=13: ~1 sec/hash\n    rounds=14: ~1.5 sec/hash\n    rounds=15: ~3 sec/hash\n    rounds=25: ~1 hour/hash\n    rounds=31: 2-3 days/hash\n\n\n## A Note on Timing Attacks\n\nBecause it's come up multiple times in this project and other bcrypt projects, it needs to be said. The `bcrypt` library is not susceptible to timing attacks. From codahale/bcrypt-ruby#42:\n\n> One of the desired properties of a cryptographic hash function is preimage attack resistance, which means there is no shortcut for generating a message which, when hashed, produces a specific digest.\n\nA great thread on this, in much more detail can be found @ codahale/bcrypt-ruby#43\n\nIf you're unfamiliar with timing attacks and want to learn more you can find a great writeup @ [A Lesson In Timing Attacks][timingatk]\n\nHowever, timing attacks are real. And the comparison function is _not_ time safe. That means that it may exit the function early in the comparison process. Timing attacks happen because of the above. We don't need to be careful that an attacker will learn anything, and our comparison function provides a comparison of hashes. It is a utility to the overall purpose of the library. If you end up using it for something else, we cannot guarantee the security of the comparator. Keep that in mind as you use the library.\n\n## Hash Info\n\nThe characters that comprise the resultant hash are `./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789$`.\n\nResultant hashes will be 60 characters long and they will include the salt among other parameters, as follows:\n\n`$[algorithm]$[cost]$[salt][hash]`\n\n- 2 chars hash algorithm identifier prefix. `\"$2a$\" or \"$2b$\"` indicates BCrypt\n- Cost-factor (n). Represents the exponent used to determine how many iterations 2^n\n- 16-byte (128-bit) salt, base64 encoded to 22 characters\n- 24-byte (192-bit) hash, base64 encoded to 31 characters\n\nExample:\n```\n$2b$10$nOUIs5kJ7naTuTFkBy1veuK0kSxUFXfuaOKdOKf9xYT0KKIGSJwFa\n |  |  |                     |\n |  |  |                     hash-value = K0kSxUFXfuaOKdOKf9xYT0KKIGSJwFa\n |  |  |\n |  |  salt = nOUIs5kJ7naTuTFkBy1veu\n |  |\n |  cost-factor => 10 = 2^10 rounds\n |\n hash-algorithm identifier => 2b = BCrypt\n```\n\n## Testing\n\nIf you create a pull request, tests better pass :)\n\n```\nnpm install\nnpm test\n```\n\n## Credits\n\nThe code for this comes from a few sources:\n\n* blowfish.cc - OpenBSD\n* bcrypt.cc - OpenBSD\n* bcrypt::gen_salt - [gen_salt inclusion to bcrypt][bcryptgs]\n* bcrypt_node.cc - me\n\n## Contributors\n\n* [Antonio Salazar Cardozo][shadowfiend] - Early MacOS X support (when we used libbsd)\n* [Ben Glow][pixelglow] - Fixes for thread safety with async calls\n* [Van Nguyen][thegoleffect] - Found a timing attack in the comparator\n* [NewITFarmer][newitfarmer] - Initial Cygwin support\n* [David Trejo][dtrejo] - packaging fixes\n* [Alfred Westerveld][alfredwesterveld] - packaging fixes\n* [Vincent Côté-Roy][vincentr] - Testing around concurrency issues\n* [Lloyd Hilaiel][lloyd] - Documentation fixes\n* [Roman Shtylman][shtylman] - Code refactoring, general rot reduction, compile options, better memory management with delete and new, and an upgrade to libuv over eio/ev.\n* [Vadim Graboys][vadimg] - Code changes to support 0.5.5+\n* [Ben Noordhuis][bnoordhuis] - Fixed a thread safety issue in nodejs that was perfectly mappable to this module.\n* [Nate Rajlich][tootallnate] - Bindings and build process.\n* [Sean McArthur][seanmonstar] - Windows Support\n* [Fanie Oosthuysen][weareu] - Windows Support\n* [Amitosh Swain Mahapatra][recrsn] - $2b$ hash support, ES6 Promise support\n* [Nicola Del Gobbo][NickNaso] - Initial implementation with N-API\n\n## License\nUnless stated elsewhere, file headers or otherwise, the license as stated in the LICENSE file.\n\n[bcryptwiki]: https://en.wikipedia.org/wiki/Bcrypt\n[bcryptgs]: http://mail-index.netbsd.org/tech-crypto/2002/05/24/msg000204.html\n[codahale]: http://codahale.com/how-to-safely-store-a-password/\n[gh13]: https://github.com/ncb000gt/node.bcrypt.js/issues/13\n[jtr]: http://www.openwall.com/lists/oss-security/2011/06/20/2\n[depsinstall]: https://github.com/kelektiv/node.bcrypt.js/wiki/Installation-Instructions\n[timingatk]: https://codahale.com/a-lesson-in-timing-attacks/\n[wrap-around-bug]: https://github.com/kelektiv/node.bcrypt.js/wiki/Security-Issues-and-Concerns#bcrypt-wrap-around-bug-medium-severity\n[improper-nuls]: https://github.com/kelektiv/node.bcrypt.js/wiki/Security-Issues-and-Concerns#improper-nul-handling-medium-severity\n\n[shadowfiend]:https://github.com/Shadowfiend\n[thegoleffect]:https://github.com/thegoleffect\n[pixelglow]:https://github.com/pixelglow\n[dtrejo]:https://github.com/dtrejo\n[alfredwesterveld]:https://github.com/alfredwesterveld\n[newitfarmer]:https://github.com/newitfarmer\n[zooko]:https://twitter.com/zooko\n[vincentr]:https://twitter.com/vincentcr\n[lloyd]:https://github.com/lloyd\n[shtylman]:https://github.com/shtylman\n[vadimg]:https://github.com/vadimg\n[bnoordhuis]:https://github.com/bnoordhuis\n[tootallnate]:https://github.com/tootallnate\n[seanmonstar]:https://github.com/seanmonstar\n[weareu]:https://github.com/weareu\n[recrsn]:https://github.com/recrsn\n[NickNaso]: https://github.com/NickNaso\n", "time": {"created": "2022-01-26T17:45:02.385Z", "modified": "2025-06-05T16:05:49.244Z", "5.0.1": "2021-02-26T04:59:28.540Z", "5.0.0": "2020-06-08T10:01:24.007Z", "4.0.1": "2020-03-02T16:23:41.761Z", "4.0.0": "2020-02-21T09:58:15.106Z", "3.0.8": "2020-02-05T18:31:55.544Z", "3.0.7": "2019-11-18T10:26:39.291Z", "3.0.6": "2019-04-14T09:25:57.465Z", "3.0.5": "2019-03-19T12:54:33.200Z", "3.0.4-napi": "2019-03-08T12:37:49.850Z", "3.0.4": "2019-02-06T20:11:20.709Z", "3.0.3": "2018-12-24T03:55:31.196Z", "3.0.2": "2018-10-18T02:58:28.960Z", "3.0.1": "2018-09-21T12:47:19.842Z", "3.0.0": "2018-07-06T09:55:34.022Z", "2.0.1": "2018-04-20T17:40:43.516Z", "2.0.0": "2018-04-07T15:36:38.221Z", "1.1.0-napi": "2018-01-21T07:35:27.113Z", "1.0.3": "2017-08-24T03:47:34.385Z", "1.0.2": "2016-12-31T22:40:45.765Z", "1.0.1": "2016-12-08T05:16:23.310Z", "1.0.0": "2016-12-05T01:01:21.014Z", "0.8.7": "2016-06-10T02:45:14.309Z", "0.8.6": "2016-04-21T01:03:00.575Z", "0.8.5": "2015-08-12T22:11:55.229Z", "0.8.4": "2015-07-24T16:48:25.677Z", "0.8.3": "2015-05-06T19:40:27.563Z", "0.8.2": "2015-03-29T02:02:28.434Z", "0.8.1": "2015-01-19T02:51:25.147Z", "0.8.0": "2014-08-03T23:03:45.186Z", "0.7.8": "2014-04-25T14:22:32.721Z", "0.7.7": "2013-09-05T22:46:19.086Z", "0.7.6": "2013-06-13T00:36:00.210Z", "0.7.5": "2013-03-12T03:21:50.426Z", "0.7.4": "2013-02-26T02:43:26.810Z", "0.7.3": "2012-11-04T04:16:14.570Z", "0.7.2": "2012-09-02T19:21:59.112Z", "0.7.1": "2012-08-04T04:42:55.593Z", "0.7.0": "2012-07-04T17:36:17.751Z", "0.6.0": "2012-06-26T17:23:19.824Z", "0.5.0": "2012-01-09T04:15:26.039Z", "0.4.1": "2011-11-18T13:57:42.489Z", "0.4.0": "2011-10-05T12:48:59.793Z", "0.3.2": "2011-09-26T23:54:54.796Z", "0.3.1": "2011-09-02T22:19:18.125Z", "0.3.0": "2011-09-02T03:39:02.622Z", "0.2.4": "2011-08-08T01:52:14.334Z", "0.2.3": "2011-04-24T19:32:38.008Z", "0.2.2": "2011-04-13T02:03:37.294Z", "0.1.3": "2011-04-12T05:25:27.185Z", "0.2.1": "2011-04-12T05:10:00.455Z", "0.2.0": "2011-03-28T05:57:12.566Z", "0.1.2": "2011-02-21T19:08:56.225Z", "5.1.0": "2022-10-06T17:44:39.162Z", "5.1.1": "2023-08-16T03:48:51.540Z", "6.0.0": "2025-05-11T18:06:24.247Z"}, "versions": {"5.0.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "5.0.1", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 10.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"@mapbox/node-pre-gyp": "^1.0.0", "node-addon-api": "^3.1.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/napi-v{napi_build_version}", "package_name": "{module_name}-v{version}-napi-v{napi_build_version}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com", "remote_path": "kelektiv/node.bcrypt.js/releases/download/v{version}", "napi_versions": [3]}, "gitHead": "2f124bd84181ae2166232566c6c3fa116f28f7a6", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@5.0.1", "_nodeVersion": "15.9.0", "_npmVersion": "7.5.3", "dist": {"shasum": "f1a2c20f208e2ccdceea4433df0c8b2c54ecdf71", "size": 33730, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-5.0.1.tgz", "integrity": "sha512-9BTgmrhZM2t1bNuDtrtIMVSmmxZBrJ71n8Wg+YgdjHuIWYF7SjjmCPZFB+/5i/o/PIeRpwVJR3P+NrpIItUjqw=="}, "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_5.0.1_1614315568344_0.13947425786305678"}, "_hasShrinkwrap": false, "publish_time": 1614315568540, "_cnpm_publish_time": 1614315568540, "_cnpmcore_publish_time": "2021-12-16T12:25:24.278Z", "hasInstallScript": true}, "5.0.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "5.0.0", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 10.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"node-pre-gyp": "0.15.0", "node-addon-api": "^3.0.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/napi-v{napi_build_version}", "package_name": "{module_name}-v{version}-napi-v{napi_build_version}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com", "remote_path": "kelektiv/node.bcrypt.js/releases/download/v{version}", "napi_versions": [3]}, "gitHead": "61139e6bbe6afe9c2c2a0dbdfe8f5e6a7b746a67", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@5.0.0", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.4", "dist": {"shasum": "051407c7cd5ffbfb773d541ca3760ea0754e37e2", "size": 67285, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-5.0.0.tgz", "integrity": "sha512-jB0yCBl4W/kVHM2whjfyqnxTmOHkCX4kHEa5nYKSoGeYe8YrjTYTc87/6bwt1g8cmV0QrbhKriETg9jWtcREhg=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_5.0.0_1591610483839_0.6836961686688388"}, "_hasShrinkwrap": false, "publish_time": 1591610484007, "_cnpm_publish_time": 1591610484007, "_cnpmcore_publish_time": "2021-12-16T12:25:24.728Z", "hasInstallScript": true}, "4.0.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "4.0.1", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 10.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"node-pre-gyp": "0.14.0", "node-addon-api": "^2.0.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/napi-v{napi_build_version}", "package_name": "{module_name}-v{version}-napi-v{napi_build_version}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}", "napi_versions": [3]}, "gitHead": "87c214f23bc4ae21b9064cf645b17179a6f40fed", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@4.0.1", "_nodeVersion": "13.5.0", "_npmVersion": "6.13.4", "dist": {"shasum": "06e21e749a061020e4ff1283c1faa93187ac57fe", "size": 66121, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-4.0.1.tgz", "integrity": "sha512-hSIZHkUxIDS5zA2o00Kf2O5RfVbQ888n54xQoF/eIaquU4uaLxK8vhhBdktd0B3n2MjkcAWzv4mnhogykBKOUQ=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_4.0.1_1583166221591_0.6666641293252842"}, "_hasShrinkwrap": false, "publish_time": 1583166221761, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1583166221761, "_cnpmcore_publish_time": "2021-12-16T12:25:25.021Z", "hasInstallScript": true}, "4.0.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "4.0.0", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 10.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"node-pre-gyp": "0.14.0", "node-addon-api": "^2.0.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/napi-v{napi_build_version}", "package_name": "{module_name}-v{version}-napi-v{napi_build_version}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}", "napi_versions": [3]}, "gitHead": "4adc3869a9a97f73aa3858a7ead575196b17a22f", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@4.0.0", "_nodeVersion": "13.5.0", "_npmVersion": "6.13.4", "dist": {"shasum": "c973832823c45d6d5cbcd943f5f07dde546df252", "size": 32063, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-4.0.0.tgz", "integrity": "sha512-UroxVJgmpeek3uxjY0IgtVtegM8EQqSLXnc5HE59m388MGZr0wPpRBqKJTaTraY3YEJOo1XIczExiEY9eeOCmg=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_4.0.0_1582279094986_0.0007513444453219886"}, "_hasShrinkwrap": false, "publish_time": 1582279095106, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1582279095106, "_cnpmcore_publish_time": "2021-12-16T12:25:25.354Z", "hasInstallScript": true}, "3.0.8": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.8", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.14.0", "node-pre-gyp": "0.14.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}"}, "gitHead": "5dd9f079c22a27dd6b94a24525c771796aef3496", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@3.0.8", "_nodeVersion": "13.5.0", "_npmVersion": "6.13.4", "dist": {"shasum": "fe437b7569faffc1105c3c3f6e7d2913e3d3bea5", "size": 31640, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.8.tgz", "integrity": "sha512-jKV6RvLhI36TQnPDvUFqBEnGX9c8dRRygKxCZu7E+MgLfKZbmmXL8a7/SFFOyHoPNX9nV81cKRC5tbQfvEQtpw=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.8_1580927515412_0.18315586598345646"}, "_hasShrinkwrap": false, "publish_time": 1580927515544, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1580927515544, "_cnpmcore_publish_time": "2021-12-16T12:25:25.657Z", "hasInstallScript": true}, "3.0.7": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.7", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.14.0", "node-pre-gyp": "0.13.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}"}, "gitHead": "4d3a977b361506d22123fa7df16caeb2b2699b3f", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@3.0.7", "_nodeVersion": "12.9.1", "_npmVersion": "6.10.3", "dist": {"shasum": "1187d29df2e1cde44268152b13e3d4a655a7c7de", "size": 63959, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.7.tgz", "integrity": "sha512-K5UglF9VQvBMHl/1elNyyFvAfOY9Bj+rpKrCSR9sFwcW8FywAYJSRwTURNej5TaAK2TEJkcJ6r6lh1YPmspx5Q=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.7_1574072799142_0.6799788794787169"}, "_hasShrinkwrap": false, "publish_time": 1574072799291, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1574072799291, "_cnpmcore_publish_time": "2021-12-16T12:25:26.025Z", "hasInstallScript": true}, "3.0.6": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.6", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.13.2", "node-pre-gyp": "0.12.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}"}, "gitHead": "5b9f3a6ff02352cbf1fc68f539c39b16cff9dbdd", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@3.0.6", "_npmVersion": "6.5.0", "_nodeVersion": "11.13.0", "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "dist": {"shasum": "f607846df62d27e60d5e795612c4f67d70206eb2", "size": 31464, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.6.tgz", "integrity": "sha512-taA5bCTfXe7FUjKroKky9EXpdhkVvhE5owfxfLYodbrAR1Ul3juLmIQmIQBK4L9a5BuUcE6cqmwT+Da20lF9tg=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.6_1555233957292_0.027760286358932573"}, "_hasShrinkwrap": false, "publish_time": 1555233957465, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1555233957465, "_cnpmcore_publish_time": "2021-12-16T12:25:26.367Z", "hasInstallScript": true}, "3.0.5": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.5", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.13.1", "node-pre-gyp": "0.12.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}"}, "gitHead": "5f5beb51800552129e1bc9d9a72829d66f1f6692", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@3.0.5", "_nodeVersion": "11.12.0", "_npmVersion": "6.7.0", "dist": {"shasum": "37a296c48ebf39fe6b28e4da3a221bf80da5aa26", "size": 31444, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.5.tgz", "integrity": "sha512-m4o91nB+Ce8696Ao4R3B/WtVWTc1Lszgd098/OIjU9D/URmdYwT3ooBs9uv1b97J5YhZweTq9lldPefTYZ0TwA=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.5_1553000072979_0.21201821675816412"}, "_hasShrinkwrap": false, "publish_time": 1553000073200, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1553000073200, "_cnpmcore_publish_time": "2021-12-16T12:25:26.617Z", "hasInstallScript": true}, "3.0.4-napi": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.4-napi", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"node-addon-api": "^1.6.2", "node-pre-gyp": "0.12.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/napi-v{napi_build_version}", "package_name": "{module_name}-v{version}-napi-v{napi_build_version}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}", "napi_versions": [3]}, "readmeFilename": "README.md", "gitHead": "cbfb03fc4cb27b68cb9e393f532c349c055d5055", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "bundleDependencies": ["node-pre-gyp"], "_id": "bcrypt@3.0.4-napi", "_nodeVersion": "11.11.0", "_npmVersion": "6.7.0", "dist": {"shasum": "c3e521720cf155c93fea7a6ffd734bacd68f2b2b", "size": 646936, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.4-napi.tgz", "integrity": "sha512-3bR2UZ32pMtEYilT///FyPse6E7RQQwxDJz4pHRtyzgfYdK5PXe/TvQ+HZjhcwck3613Gn0vYS5zSo3nBYC7mg=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.4-napi_1552048669632_0.40452863893782776"}, "_hasShrinkwrap": false, "publish_time": 1552048669850, "_cnpm_publish_time": 1552048669850, "_cnpmcore_publish_time": "2021-12-16T12:25:28.877Z", "hasInstallScript": true}, "3.0.4": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.4", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.12.1", "node-pre-gyp": "0.12.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}"}, "gitHead": "73b4227b048d219ecb605653fc8065e4518b366e", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "bundleDependencies": ["node-pre-gyp"], "_id": "bcrypt@3.0.4", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "dist": {"shasum": "1c881379ddf21bcade56e3172669d27152d90d50", "size": 1300694, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.4.tgz", "integrity": "sha512-XqmCym97kT6l+jFEKeFvGuNE9aVEFDGsLMv+tIBTXkJI1sHS0g8s7VQEPJagSMPwWiB5Vpr2kVzVKc/YfwWthA=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.4_1549483880512_0.5619516594844494"}, "_hasShrinkwrap": false, "publish_time": 1549483880709, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1549483880709, "_cnpmcore_publish_time": "2021-12-16T12:25:32.022Z", "hasInstallScript": true}, "3.0.3": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.3", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.12.1", "node-pre-gyp": "0.12.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}"}, "gitHead": "1316e26a84ea9eddef61fd463a98ec4a9e7e0673", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "bundleDependencies": ["node-pre-gyp"], "_id": "bcrypt@3.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "11.5.0", "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "dist": {"shasum": "7d1e9e5d533c5ea060e6ac8834942c004dbffe9c", "size": 649223, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.3.tgz", "integrity": "sha512-4EuzUo6K790QC3uq/ogzy9w2Hc7XDIBoEndU5y7l7YaEAwQF8vyFqv6tC30+gOBZvyxk3F632xzKBQoLNz2pjg=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.3_1545623731044_0.843945033414814"}, "_hasShrinkwrap": false, "publish_time": 1545623731196, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1545623731196, "_cnpmcore_publish_time": "2021-12-16T12:25:33.353Z", "hasInstallScript": true}, "3.0.2": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.2", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.11.1", "node-pre-gyp": "0.11.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}"}, "gitHead": "596ac3495cba62a2667c55684fa6d25b654da525", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "bundleDependencies": ["node-pre-gyp"], "_id": "bcrypt@3.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "dist": {"shasum": "3c575c49ccbfdf0875eb42aa1453f5654092a33d", "size": 648456, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.2.tgz", "integrity": "sha512-kE1IaaRchCgdrmzQX/eBQKcsuL4jRHZ+O11sMvEUrI/HgFTQYAGvxlj9z7kb3zfFuwljQ5y8/NrbnXtgx5oJLg=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.2_1539831508765_0.34844086042908873"}, "_hasShrinkwrap": false, "publish_time": 1539831508960, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1539831508960, "_cnpmcore_publish_time": "2021-12-16T12:25:34.250Z", "hasInstallScript": true}, "3.0.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.1", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.11.0", "node-pre-gyp": "0.11.0"}, "devDependencies": {"nodeunit": "^0.11.3"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com/kelektiv/node.bcrypt.js/releases/download/", "remote_path": "v{version}"}, "gitHead": "0da3dc35317c1b720c347d7edfde20ce0745e2a8", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "bundleDependencies": ["node-pre-gyp"], "_id": "bcrypt@3.0.1", "_npmVersion": "6.4.0", "_nodeVersion": "10.10.0", "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "dist": {"shasum": "2ea12883330bf5f81926f70475cf21bdd0c2f8ff", "size": 648436, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.1.tgz", "integrity": "sha512-DSTLQZdvzJ7znQ1WOqkN3X0Hutt6BTVaZNWyX8/B4P+s9SIxkYgtGKfgHokli1syPcWJUE63/kGVyV1ECA4d1A=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.1_1537534039620_0.5771597871337091"}, "_hasShrinkwrap": false, "publish_time": 1537534039842, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1537534039842, "_cnpmcore_publish_time": "2021-12-16T12:25:35.449Z", "hasInstallScript": true}, "3.0.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "3.0.0", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.10.0", "node-pre-gyp": "0.10.2"}, "devDependencies": {"nodeunit": "^0.11.2"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "host": "https://github.com", "remote_path": "/kelektiv/node.bcrypt.js/releases/download/v{version}/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz"}, "gitHead": "50bd6cb9867eb843c61131466448455cb11f37b9", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "bundleDependencies": ["node-pre-gyp"], "_id": "bcrypt@3.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "dist": {"shasum": "0cd38983f45143aa5a6122c9660d0b7ec3a33fb0", "size": 646595, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-3.0.0.tgz", "integrity": "sha512-gjicxsD4e5U3nH0EqiEb5y+fKpsZ7F52wcnmNfu45nxnolWVAYh7NgbdfilY+5x1v6cLspxmzz4hf+ju2pFxhA=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_3.0.0_1530870933886_0.24497604719151544"}, "_hasShrinkwrap": false, "publish_time": 1530870934022, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1530870934022, "_cnpmcore_publish_time": "2021-12-16T12:25:36.872Z", "hasInstallScript": true}, "2.0.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "2.0.1", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.10.0", "node-pre-gyp": "0.9.1"}, "devDependencies": {"nodeunit": "~0.11.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "host": "https://github.com", "remote_path": "/kelektiv/node.bcrypt.js/releases/download/v{version}/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz"}, "gitHead": "0a7e8ae191e8359a55f1c82c51096053a6f10a29", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "bundleDependencies": ["node-pre-gyp"], "_id": "bcrypt@2.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "dist": {"shasum": "229c5afe09379789f918efe86e5e5b682e509f85", "size": 626809, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-2.0.1.tgz", "integrity": "sha512-DwB7WgJPdskbR+9Y3OTJtwRq09Lmm7Na6b+4ewvXjkD0nfNRi1OozxljHm5ETlDCBq9DTy04lQz+rj+T2ztIJg=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_2.0.1_1524246043029_0.3759755547600323"}, "_hasShrinkwrap": false, "publish_time": 1524246043516, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1524246043516, "_cnpmcore_publish_time": "2021-12-16T12:25:38.406Z", "hasInstallScript": true}, "2.0.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "2.0.0", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.10.0", "node-pre-gyp": "0.9.0"}, "devDependencies": {"nodeunit": "~0.11.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "host": "https://github.com", "remote_path": "/kelektiv/node.bcrypt.js/releases/download/v{version}/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz"}, "gitHead": "ab026b202f3dfea2b3d301868a45c25a6a2b8348", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@2.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "dist": {"shasum": "7c0c68e7593854dd397e048c6e042dcc5e462592", "size": 30912, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-2.0.0.tgz", "integrity": "sha512-KL3nXU8H6QR/dgSUWHkjId5xIOJn8DTl4idFl720nsBwoq5ArAqIVmZ5BbD8LiCH+wjS7NX9hBvp30rGMmU0LA=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_2.0.0_1523115398116_0.24898807728838168"}, "_hasShrinkwrap": false, "publish_time": 1523115398221, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1523115398221, "_cnpmcore_publish_time": "2021-12-16T12:25:39.150Z", "hasInstallScript": true}, "1.1.0-napi": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "1.1.0-napi", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"bindings": "1.3.0", "node-addon-api": "1.1.0", "node-pre-gyp": "0.6.39"}, "devDependencies": {"nodeunit": "~0.9.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "host": "https://github.com", "remote_path": "/kelektiv/node.bcrypt.js/releases/download/v{version}/"}, "gitHead": "b1de58e392c009dd5e41c3ad238cac2a71c5d868", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@1.1.0-napi", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "dist": {"shasum": "064749bc130ffa1c988b635ba9604c3839b4b5a9", "size": 29409, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-1.1.0-napi.tgz", "integrity": "sha512-vZPwM70GsL8vZRnOLaNraQMw787HgJ/C7/0leRQSmhFxdSr0T+KYf+HsQrORhiNWWqsSTvdenbjdfCIFohg/tw=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt-1.1.0-napi.tgz_1516520125560_0.14250003988854587"}, "directories": {}, "publish_time": 1516520127113, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516520127113, "_cnpmcore_publish_time": "2021-12-16T12:25:39.417Z", "hasInstallScript": true}, "1.0.3": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "1.0.3", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"nan": "2.6.2", "node-pre-gyp": "0.6.36"}, "devDependencies": {"nodeunit": "~0.9.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "host": "https://github.com", "remote_path": "/kelektiv/node.bcrypt.js/releases/download/v{version}/"}, "gitHead": "054cf76ba6e2127560ed897d00b5b88d11dc5626", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@1.0.3", "_npmVersion": "5.3.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "ncb000gt", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "dist": {"shasum": "b02ddc6c0b52ea16b8d3cf375d5a32e780dab548", "size": 28613, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-1.0.3.tgz", "integrity": "sha512-pRyDdo73C8Nim3jwFJ7DWe3TZCgwDfWZ6nHS5LSdU77kWbj1frruvdndP02AOavtD4y8v6Fp2dolbHgp4SDrfg=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt-1.0.3.tgz_1503546453159_0.5182333381380886"}, "directories": {}, "publish_time": 1503546454385, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1503546454385, "_cnpmcore_publish_time": "2021-12-16T12:25:39.716Z", "hasInstallScript": true}, "1.0.2": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "1.0.2", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"bindings": "1.2.1", "nan": "2.5.0", "node-pre-gyp": "0.6.32"}, "devDependencies": {"nodeunit": "~0.9.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "host": "https://github.com", "remote_path": "/kelektiv/node.bcrypt.js/releases/download/v{version}/"}, "gitHead": "9036615a24c51f0d125ae39efbf9b943f16c8571", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@1.0.2", "_shasum": "d05fc5d223173e0e28ec381c0f00cc25ffaf2736", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "d05fc5d223173e0e28ec381c0f00cc25ffaf2736", "size": 28287, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-1.0.2.tgz", "integrity": "sha512-mChQbPrR6qQJtG7M8e/kx6Nk5bemJh8+8/uzSRNcLj9TuXzJYuuiGV3R4n2HtsNLLLMDeCDW74KZwxLXV8/U1Q=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/bcrypt-1.0.2.tgz_1483224043409_0.592600307893008"}, "directories": {}, "publish_time": 1483224045765, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1483224045765, "_cnpmcore_publish_time": "2021-12-16T12:25:39.943Z", "hasInstallScript": true}, "1.0.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "1.0.1", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"bindings": "1.2.1", "nan": "2.3.5", "node-pre-gyp": "0.6.30"}, "devDependencies": {"nodeunit": "~0.9.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "host": "https://github.com", "remote_path": "/kelektiv/node.bcrypt.js/releases/download/v{version}/"}, "gitHead": "941df17dbb3d1fa24fb1e71a99664c8fface2b08", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@1.0.1", "_shasum": "453b7aa1e05e03fcc48b8e368865123ae3fc313f", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "453b7aa1e05e03fcc48b8e368865123ae3fc313f", "size": 27981, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-1.0.1.tgz", "integrity": "sha512-yQLn+CWUewxY9BOdIMTKLONAYY/s2s0Xf/qL3f5N6txCjzHTxIHRXZiV06ZCszco6yuJpC7HpFZgfM4E2J5AHw=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/bcrypt-1.0.1.tgz_1481174181452_0.3468507663346827"}, "directories": {}, "publish_time": 1481174183310, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1481174183310, "_cnpmcore_publish_time": "2021-12-16T12:25:40.616Z", "hasInstallScript": true}, "1.0.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "1.0.0", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm install --build-from-source && nodeunit test", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"bindings": "1.2.1", "nan": "2.3.5", "node-pre-gyp": "0.6.30"}, "devDependencies": {"nodeunit": "~0.9.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/", "host": "https://github.com", "remote_path": "/kelektiv/node.bcrypt.js/releases/download/v{version}/"}, "gitHead": "4836633aab0af7bef43e1083a0e2e30eb342b07f", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@1.0.0", "_shasum": "e91170e6b8464e62200e66184cb55c5fa0161a21", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "e91170e6b8464e62200e66184cb55c5fa0161a21", "size": 27928, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-1.0.0.tgz", "integrity": "sha512-lFdLtnXW2kVzjaoApdYaskqv4hXrZMoqzXhf1BV/RYEMaJFm4Zjq6cZLmWJDIWuaZGzn/iQYSUHNpRdMzLVjKw=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/bcrypt-1.0.0.tgz_1480899678493_0.7228606131393462"}, "directories": {}, "publish_time": 1480899681014, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1480899681014, "_cnpmcore_publish_time": "2021-12-16T12:25:40.854Z", "hasInstallScript": true}, "0.8.7": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.8.7", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/ncb000gt/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.2.1", "nan": "2.3.5"}, "devDependencies": {"nodeunit": "~0.9.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "gitHead": "0dc9b78dab6980d129b77d1df723aa44daa7deb4", "homepage": "https://github.com/ncb000gt/node.bcrypt.js#readme", "_id": "bcrypt@0.8.7", "_shasum": "bc3875a9afd0a7b2cd231a6a7f218a5ce156b093", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.2.0", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "bc3875a9afd0a7b2cd231a6a7f218a5ce156b093", "size": 25464, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.8.7.tgz", "integrity": "sha512-0oOGm2Gs67b02E2xdjyuxu5rfqtWVKUnMeJmevQ3/W8pAgw2Yoh5yvSYC0z7ghvoy9sOB8GUePieCHB3Ic3qpQ=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/bcrypt-0.8.7.tgz_1465526713712_0.05265477881766856"}, "directories": {}, "publish_time": 1465526714309, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1465526714309, "_cnpmcore_publish_time": "2021-12-16T12:25:41.388Z", "hasInstallScript": true}, "0.8.6": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.8.6", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/ncb000gt/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.2.1", "nan": "2.2.1"}, "devDependencies": {"nodeunit": "~0.9.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "gitHead": "10f0f99232cea96446e95bf128f718b87aec3237", "homepage": "https://github.com/ncb000gt/node.bcrypt.js#readme", "_id": "bcrypt@0.8.6", "_shasum": "182164f7d5e1de94ddd797473efd48b57b1f04b4", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "182164f7d5e1de94ddd797473efd48b57b1f04b4", "size": 24983, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.8.6.tgz", "integrity": "sha512-mzuw6S94AxSYm/IpbMuqy6Zt1HWyM+mAlBRKLIzBMPTWxWGN/KJaPZPNkO3SjJhrVLYiMapvJxJyCbRIF0bW7w=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/bcrypt-0.8.6.tgz_1461200578042_0.6844706872943789"}, "directories": {}, "publish_time": 1461200580575, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1461200580575, "_cnpmcore_publish_time": "2021-12-16T12:25:41.702Z", "hasInstallScript": true}, "0.8.5": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.8.5", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/ncb000gt/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.2.1", "nan": "2.0.5"}, "devDependencies": {"nodeunit": "~0.9.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "gitHead": "5d97708f06754d7d7989c67ba867002d10013615", "homepage": "https://github.com/ncb000gt/node.bcrypt.js#readme", "_id": "bcrypt@0.8.5", "_shasum": "8e5b81b4db80e944f440005979ca8d58a961861d", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "8e5b81b4db80e944f440005979ca8d58a961861d", "size": 24859, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.8.5.tgz", "integrity": "sha512-az/W0hNqgGJ35JgNepkr2U/cXv3/zuMwmHHMXDBfZQYMBnBoy6RaXMI6/+M8FYX4vAxZ8XEoAXbpObbnsT5nnQ=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1439417515229, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1439417515229, "_cnpmcore_publish_time": "2021-12-16T12:25:42.008Z", "hasInstallScript": true}, "0.8.4": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.8.4", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/ncb000gt/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.2.1", "nan": "1.8.4"}, "devDependencies": {"nodeunit": "~0.9.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "gitHead": "5ca168581369b2d75823b3000803d8edf94ae459", "homepage": "https://github.com/ncb000gt/node.bcrypt.js#readme", "_id": "bcrypt@0.8.4", "_shasum": "fabad077c91c484591a4a34778317ef56478126f", "_from": ".", "_npmVersion": "2.11.1", "_nodeVersion": "0.10.38", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "fabad077c91c484591a4a34778317ef56478126f", "size": 24778, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.8.4.tgz", "integrity": "sha512-QAbwm+ITNbquskO5hvDVP89IPlRWSZAzPyxZGQy3S/kwoFYdusGhsUbgYslGdQhk6nxIToge8sFCYGkaQ0yCnw=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1437756505677, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1437756505677, "_cnpmcore_publish_time": "2021-12-16T12:25:43.212Z", "hasInstallScript": true}, "0.8.3": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.8.3", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "https://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.2.1", "nan": "1.8.4"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "gitHead": "59b9a1e4b3a0d0889812fdeda4e1c7bd0224ebd1", "homepage": "https://github.com/ncb000gt/node.bcrypt.js#readme", "_id": "bcrypt@0.8.3", "_shasum": "714f92307246a35350744312b0d6a11a4abf792e", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "dist": {"shasum": "714f92307246a35350744312b0d6a11a4abf792e", "size": 24714, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.8.3.tgz", "integrity": "sha512-xoTZ51fXxBcXSJMFRg3tgVwWF4dj+j0EQqF9afAxNQ1ll3Gk1xAMNpIkgnLGU8gh1S7rJXIXzLamBe8dPbF9ww=="}, "directories": {}, "publish_time": 1430941227563, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1430941227563, "_cnpmcore_publish_time": "2021-12-16T12:25:43.407Z", "hasInstallScript": true}, "0.8.2": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.8.2", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "https://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "https://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.2.0", "nan": "1.7.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "gitHead": "5f5f0e9fa8c0f18b6489d6a07551999de2eb40e5", "homepage": "https://github.com/ncb000gt/node.bcrypt.js", "_id": "bcrypt@0.8.2", "_shasum": "4732f0ddd1e0fb066dcd8ad9472d3d300fd96824", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.10.35", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "dist": {"shasum": "4732f0ddd1e0fb066dcd8ad9472d3d300fd96824", "size": 24549, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.8.2.tgz", "integrity": "sha512-UGCDQQ+J85cPBLcyomOa/bfvUjT2FiYyqahfVwP0lExTu593oo1sxXDXvkIew8vEfwI6WjXpIJQDl30pTjhtCg=="}, "directories": {}, "publish_time": 1427594548434, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1427594548434, "_cnpmcore_publish_time": "2021-12-16T12:25:43.696Z", "hasInstallScript": true}, "0.8.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.8.1", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "https://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "https://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0", "nan": "1.5.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "gitHead": "1c76a3000c4e3a89f6e59d3f66e6d7b9da51affd", "homepage": "https://github.com/ncb000gt/node.bcrypt.js", "_id": "bcrypt@0.8.1", "_shasum": "384fa4fbc106ec87ccf8ee3d1e7e12d0ef8eeaaf", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.33", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "dist": {"shasum": "384fa4fbc106ec87ccf8ee3d1e7e12d0ef8eeaaf", "size": 24256, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.8.1.tgz", "integrity": "sha512-zTKwOwBrXFF5ZCJdznvyCCl5j9YT70CVIYBailrRuDmu6oeq+axuL4KNTjd2nsaLr1Tp3JN/1NCptObnvYKG5g=="}, "directories": {}, "publish_time": 1421635885147, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1421635885147, "_cnpmcore_publish_time": "2021-12-16T12:25:43.932Z", "hasInstallScript": true}, "0.8.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.8.0", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "https://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "https://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0", "nan": "1.3.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "gitHead": "067b4e5fea672f1953632668acb40b88222eb737", "homepage": "https://github.com/ncb000gt/node.bcrypt.js", "_id": "bcrypt@0.8.0", "_shasum": "b8f226406e5b78c838833a8468a4a0402cbc93c9", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "dist": {"shasum": "b8f226406e5b78c838833a8468a4a0402cbc93c9", "size": 24416, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.8.0.tgz", "integrity": "sha512-OLjNCRPKnwcvza1qiyZ0Mefu4AgRoSnv/yms6gia3O4A6RFW1Hi+J0rxHn8cbXSiU6JRbRQlJTksnt3n9jG+sA=="}, "directories": {}, "publish_time": 1407107025186, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1407107025186, "_cnpmcore_publish_time": "2021-12-16T12:25:44.660Z", "hasInstallScript": true}, "0.7.8": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.8", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "https://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "https://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "homepage": "https://github.com/ncb000gt/node.bcrypt.js", "_id": "bcrypt@0.7.8", "dist": {"shasum": "42c99aac202918e947b5bd086110184f62745e3e", "size": 24105, "noattachment": false, "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.7.8.tgz", "integrity": "sha512-WYSfgf4MxhQH59Cr1u/3rJBJjgYA2igMVxrTGK1y8ROjps5XIRyitUww54yHlPJw9F+M7GhNM8KSfxzh17cmNA=="}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398435752721, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1398435752721, "_cnpmcore_publish_time": "2021-12-16T12:25:44.917Z", "hasInstallScript": true}, "0.7.7": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.7", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "http://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "readmeFilename": "README.md", "_id": "bcrypt@0.7.7", "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.7.7.tgz", "shasum": "966a2e709b8cf62c2e05408baf7c5ed663b3c868", "size": 25890, "noattachment": false, "integrity": "sha512-OXLBA2Wwy6LNcC37bitEZXpCs+7pouSpUBVRgZ4jkrjyGz3zfQrhmJb1rdCe6jyl3ptHQBd4qhwfDhX8C8ICLw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378421179086, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1378421179086, "_cnpmcore_publish_time": "2021-12-16T12:25:45.217Z", "hasInstallScript": true}, "0.7.6": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.6", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "http://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "readmeFilename": "README.md", "_id": "bcrypt@0.7.6", "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.7.6.tgz", "shasum": "97eae4472baf2352699f5fd1662e77e63d0cd0aa", "size": 25961, "noattachment": false, "integrity": "sha512-aR/RHnDxuGI4m4ATSiVUSGCazIhrz1jETX3kwxxMJzFx94n5XXImbnHrx7AdkwL1aMB7+DR4Q8oaHbjUR/uLYw=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371083760210, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1371083760210, "_cnpmcore_publish_time": "2021-12-16T12:25:45.483Z", "hasInstallScript": true}, "0.7.5": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.5", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "http://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "_id": "bcrypt@0.7.5", "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.7.5.tgz", "shasum": "edabf290e61761acb66047b9b43c6467a6241ce3", "size": 26071, "noattachment": false, "integrity": "sha512-8PAWwqPiwZW/JgtO/LFZFQW7+loFDPWOwv3cTp6FX0Lh3zV2ZwyTq4F/kGBbw4GBjQVNwX5vH0eJyK+up+MQTA=="}, "_npmVersion": "1.1.59", "_npmUser": {"name": "ncb000gt", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1363058510426, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1363058510426, "_cnpmcore_publish_time": "2021-12-16T12:25:45.669Z", "hasInstallScript": true}, "0.7.4": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.4", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "http://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "readmeFilename": "README.md", "_id": "bcrypt@0.7.4", "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.7.4.tgz", "shasum": "6742afa771eccf2e7a8d1f69386c21b1f3d106e1", "size": 25556, "noattachment": false, "integrity": "sha512-IwHcJNKw+b79dRDe213O/vnnyrZV+ABKDxSFNc1I6vGQ04LT6crzTtfPr9RPmPSaNIvJ9z9N0szQQu/NuMFtKg=="}, "_from": ".", "_npmVersion": "1.2.12", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1361846606810, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1361846606810, "_cnpmcore_publish_time": "2021-12-16T12:25:45.953Z", "hasInstallScript": true}, "0.7.3": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.3", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "http://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "readmeFilename": "README.md", "_id": "bcrypt@0.7.3", "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.7.3.tgz", "shasum": "7523db1fdd8b0bda337bade63b5b90a7ee9c448a", "size": 25637, "noattachment": false, "integrity": "sha512-mgU5xZYMYrk7bc+U0M/ssV344+GVzTZAQ3gjWiG1vESHky6IpWUI5N9AQGFSl63hR+3PE7D4svGrQ4vfROwCXA=="}, "_npmVersion": "1.1.65", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1352002574570, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1352002574570, "_cnpmcore_publish_time": "2021-12-16T12:25:46.220Z", "hasInstallScript": true}, "0.7.2": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.2", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "http://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "gypfile": true, "_id": "bcrypt@0.7.2", "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.7.2.tgz", "shasum": "e5cef20f2456630787bae59f5825f61c65508766", "size": 25675, "noattachment": false, "integrity": "sha512-spZZj2spJywhtIsOSSEHibdNZkTG3MtZ2p1gIPEYoFDWXlxIaSr5+PXSnrTOebEzbIWvRJ/AgwmsMLRBMiY6HQ=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1346613719112, "_hasShrinkwrap": false, "_cnpm_publish_time": 1346613719112, "_cnpmcore_publish_time": "2021-12-16T12:25:46.635Z", "hasInstallScript": true}, "0.7.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.1", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}], "_npmUser": {"name": "ncb000gt", "email": "<EMAIL>"}, "_id": "bcrypt@0.7.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.9", "_nodeVersion": "v0.6.13", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.7.1.tgz", "shasum": "923e2623331211adcab6ac84ec4fcd41713e1e58", "size": 26352, "noattachment": false, "integrity": "sha512-bufcBfz7UoUFBmgPVCGJY4OzvnelOdn+uL9Fvm3u//QMURieBWw5TvH7EV7DmG0Gyi7Ow3CuiZMo/qBPebiQYw=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1344055375593, "_hasShrinkwrap": false, "_cnpm_publish_time": 1344055375593, "_cnpmcore_publish_time": "2021-12-16T12:25:47.456Z", "hasInstallScript": true}, "0.7.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.0", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "http://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}], "gypfile": true, "_id": "bcrypt@0.7.0", "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.7.0.tgz", "shasum": "57b5b06290718aa34f080d2bfa289e351172e559", "size": 24729, "noattachment": false, "integrity": "sha512-fnLqbDymugyynLY5KEduA1Vh+hM9WZB+jL2QzCTSleOTcXyHgmhKW9VNLsvJ5B48Um8ZdHvw+0d3tuhKn1SzZg=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1341423377751, "_hasShrinkwrap": false, "_cnpm_publish_time": 1341423377751, "_cnpmcore_publish_time": "2021-12-16T12:25:47.754Z", "hasInstallScript": true}, "0.6.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.6.0", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "make test", "install": "node-gyp rebuild"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}], "_npmUser": {"name": "ncb000gt", "email": "<EMAIL>"}, "_id": "bcrypt@0.6.0", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.8.0", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.6.0.tgz", "shasum": "3f49d8c78c0887c1b420f07e4abe6e7a03874c81", "size": 24478, "noattachment": false, "integrity": "sha512-rUzai3XhwEvVl8ZagBLo2leY18mYGLsSGYTPoA5pu4GFwEjq6lMaooVSzWnEIwYf9v70teaA1F4zZ5aDmEOttw=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1340731399824, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1340731399824, "_cnpmcore_publish_time": "2021-12-16T12:25:47.979Z", "hasInstallScript": true}, "0.5.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.5.0", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "make build", "test": "make test"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}], "_npmUser": {"name": "ncb000gt", "email": "<EMAIL>"}, "_id": "bcrypt@0.5.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-10", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.5.0.tgz", "shasum": "612e01afa125c7332310f94e5186425c482b55f8", "size": 24369, "noattachment": false, "integrity": "sha512-i3GSBtDi0Mhp16PdqMewe2SFRMMoGVlO1YEHtwKc3KnVPXrLCf1mbBiEKKcuZ5Ku2cVGtl+KKIwpC/Lrfthw/g=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1326082526039, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1326082526039, "_cnpmcore_publish_time": "2021-12-16T12:25:48.298Z", "hasInstallScript": true}, "0.4.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.4.1", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.4.0"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "make build", "test": "make test"}, "devDependencies": {"nodeunit": ">=0.5.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}], "_npmUser": {"name": "ncb000gt", "email": "<EMAIL>"}, "_id": "bcrypt@0.4.1", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.4.1.tgz", "shasum": "0e0f84582ebd6ea0a4190739001b4af3d2c3d188", "size": 22987, "noattachment": false, "integrity": "sha512-UwKx76FlADd9AA8pEcgwCHfcSpYteuSQhZEjeuucOF2X46qX57RMYRkNX5sgB+NG8dHZmKPm7Gw9evwvwD7Tpw=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1321624662489, "_hasShrinkwrap": false, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "_cnpm_publish_time": 1321624662489, "_cnpmcore_publish_time": "2021-12-16T12:25:48.571Z", "hasInstallScript": true}, "0.4.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.4.0", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.4.0"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "make build", "test": "make test"}, "devDependencies": {"nodeunit": ">=0.5.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}], "_npmJsonOpts": {"file": "/Users/<USER>/.npm/bcrypt/0.4.0/package/package.json", "contributors": false, "serverjs": false, "wscript": true}, "_id": "bcrypt@0.4.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.4.0.tgz", "shasum": "646daf99707d584d7d814b47c421858af8e23247", "size": 22147, "noattachment": false, "integrity": "sha512-UjTXiaBmevpFBq2rCWA9ccuZHTEirKIje896lXVzR9/qn58EQLjmwyMtj98aA+lzGMsB4IDDQlERp8XhHtyxyw=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1317818939793, "_hasShrinkwrap": false, "_cnpm_publish_time": 1317818939793, "_cnpmcore_publish_time": "2021-12-16T12:25:48.866Z", "hasInstallScript": true}, "0.3.2": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.3.2", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.4.0"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "make build", "test": "make test"}, "devDependencies": {"nodeunit": ">=0.5.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}], "_npmJsonOpts": {"file": "/home/<USER>/.npm/bcrypt/0.3.2/package/package.json", "contributors": false, "serverjs": false, "wscript": true}, "_id": "bcrypt@0.3.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.27", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.3.2.tgz", "shasum": "e94b7f67aed1a6d95e9e9268b0c688c93540f3af", "size": 21732, "noattachment": false, "integrity": "sha512-RNKPfQiHu7Q+MJ0cdWOESG6aKmyumEzDVJgPbXQjKow8z23DKnSs57mLwgE3t/gwM+pzYe3BT6/J2NKg8aaO0Q=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1317081294796, "_hasShrinkwrap": false, "_cnpm_publish_time": 1317081294796, "_cnpmcore_publish_time": "2021-12-16T12:25:49.048Z", "hasInstallScript": true}, "0.3.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.3.1", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.4.0"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "make build", "test": "make test"}, "devDependencies": {"nodeunit": ">=0.5.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}], "_npmJsonOpts": {"file": "/Users/<USER>/.npm/bcrypt/0.3.1/package/package.json", "contributors": false, "serverjs": false, "wscript": true}, "_id": "bcrypt@0.3.1", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.5.3", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.3.1.tgz", "shasum": "35189eb1bc38f349727dabef66d397839d7b74e6", "size": 21777, "noattachment": false, "integrity": "sha512-i2l7jhqZjifpqkXHAnWLNqO6jZcutiOUfR3zlgS6FhG5Ng+5U0kZTQWHhnrdmdvlArAbFr6sG26X6WfkOnLMng=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1315001958125, "_hasShrinkwrap": false, "_cnpm_publish_time": 1315001958125, "_cnpmcore_publish_time": "2021-12-16T12:25:49.263Z", "hasInstallScript": true}, "0.3.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.3.0", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.4.0"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "make build", "test": "make test"}, "devDependencies": {"nodeunit": ">=0.5.4"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}], "_npmJsonOpts": {"file": "/home/<USER>/.npm/bcrypt/0.3.0/package/package.json", "contributors": false, "serverjs": false, "wscript": true}, "_id": "bcrypt@0.3.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.5.4", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.3.0.tgz", "shasum": "d84543bef358fd325d7acef53552f9589c1b534b", "size": 21684, "noattachment": false, "integrity": "sha512-iCdSauj2y4LT5Pc8OCy4b19zs1iJgxqrRR4SaK32ZdFoqRXL7NJMu/i6RMCTJppRRzYasoGosOR5bdUIiInLPA=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1314934742622, "_hasShrinkwrap": false, "_cnpm_publish_time": 1314934742622, "_cnpmcore_publish_time": "2021-12-16T12:25:49.969Z", "hasInstallScript": true}, "0.2.4": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "main": "./bcrypt", "version": "0.2.4", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.1.100"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "node-waf configure build", "test": "node-waf configure build; nodeunit test/"}, "devDependencies": {"nodeunit": ">=0.5.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}], "_npmJsonOpts": {"file": "/home/<USER>/.npm/bcrypt/0.2.4/package/package.json", "contributors": false, "serverjs": false, "wscript": true}, "_id": "bcrypt@0.2.4", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.2.4.tgz", "shasum": "07d977afab3da66b45f2f37996e4d6e4a828e333", "size": 21850, "noattachment": false, "integrity": "sha512-eXzg32GUu1V6C+32IWiHc4tHWcyAiC4oYpps6Enfmpz53CRs791mAkkO7iq8XHWShfTJiCj/6u5wEennyb+FSA=="}, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "directories": {}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1312768334334, "_hasShrinkwrap": false, "_cnpm_publish_time": 1312768334334, "_cnpmcore_publish_time": "2021-12-16T12:25:50.308Z", "hasInstallScript": true}, "0.2.3": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "main": "./bcrypt", "version": "0.2.3", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.1.100"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "node-waf configure build", "test": "node-waf configure build; nodeunit test/"}, "dependencies": {"nodeunit": ">=0.5.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}], "_id": "bcrypt@0.2.3", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.7", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.2.3.tgz", "shasum": "8eb612ffaffbffc0041141bb4fee2c748f0b18de", "size": 29984, "noattachment": false, "integrity": "sha512-ioZ+/KQ6T73ZRlU5qduEY1AF/z3eBXErtbBPjnJWZlBKqwwToy8WTuXmzz81IRvK/srjBoivd6XNmiON3z8D9g=="}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1303673558008, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1303673558008, "_cnpmcore_publish_time": "2021-12-16T12:25:50.633Z", "hasInstallScript": true}, "0.2.2": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "main": "./bcrypt", "version": "0.2.2", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.1.100"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "node-waf configure build", "test": "node-waf configure build; nodeunit test/"}, "dependencies": {"nodeunit": ">=0.5.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}], "_id": "bcrypt@0.2.2", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.5.0-pre", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.2.2.tgz", "shasum": "fa051d13aea3d7e7c7b28712f5b3a9746a6f273f", "size": 23676, "noattachment": false, "integrity": "sha512-YoNIObV/ejnbgHcpE5GoFA0fUafLGMZNbLhsGTD2A7QEXG3tpTfiFMZMxy2/XtrXbCq6z+I7gFvkmhyBU9jRyw=="}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1302660217294, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302660217294, "_cnpmcore_publish_time": "2021-12-16T12:25:50.900Z", "hasInstallScript": true}, "0.1.3": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "main": "./bcrypt", "version": "0.1.3", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.1.100"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "node-waf configure build", "test": "node-waf configure build; nodeunit test/"}, "dependencies": {"nodeunit": ">=0.3.1"}, "_id": "bcrypt@0.1.3", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.5.0-pre", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.1.3.tgz", "shasum": "ea5bdd817a828e40b87a41412cdaa8afa23c567e", "size": 19109, "noattachment": false, "integrity": "sha512-BXX1A8+A1RbLLVNcQz76RYGrZx3Xj2mun40SUUSqQAEvSvi+C+3p8eY3fRdVG9G/O54Cjeh8O/jq31ZLehaliA=="}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1302585927185, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302585927185, "_cnpmcore_publish_time": "2021-12-16T12:25:51.144Z", "hasInstallScript": true}, "0.2.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "main": "./bcrypt", "version": "0.2.1", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.1.100"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "node-waf configure build", "test": "node-waf configure build; nodeunit test/"}, "dependencies": {"nodeunit": ">=0.5.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}], "_id": "bcrypt@0.2.1", "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.5.0-pre", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.2.1.tgz", "shasum": "6f62f49984ebc8a85113a3002a7fa978ddb86241", "size": 24516, "noattachment": false, "integrity": "sha512-ccAAgSGJb/IIwj05zWRvIljuJlSSnxfxDIcitrnPx65UTSqSBdwZt0IkTp6eVWret16gNln6UW9dvNiqiUuHRg=="}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1302585000455, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1302585000455, "_cnpmcore_publish_time": "2021-12-16T12:25:51.368Z", "hasInstallScript": true}, "0.2.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "main": "./bcrypt", "version": "0.2.0", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.1.100"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "node-waf configure build", "test": "node-waf configure build; nodeunit test/"}, "dependencies": {"nodeunit": ">=0.5.1"}, "_id": "bcrypt@0.2.0", "_engineSupported": true, "_npmVersion": "0.3.0-8", "_nodeVersion": "v0.4.2", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.2.0.tgz", "shasum": "1c93e3048c07fb1341504e09c8f81cc1e6f2fcb1", "size": 20529, "noattachment": false, "integrity": "sha512-YwVacZM0jR0uGuxDI1hj/UL1/yT049yuCvdVPLVVmVCUalp9JgpNP9NRquBRD1ZbiqBSM9pH/D3HBOMLi8zGVw=="}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1301291832566, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1301291832566, "_cnpmcore_publish_time": "2021-12-16T12:25:51.612Z", "hasInstallScript": true}, "0.1.2": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "main": "./bcrypt", "version": "0.1.2", "author": {"name": "<PERSON>", "url": "http://github.com/ncb000gt"}, "engines": {"node": ">= 0.1.100"}, "repository": {"type": "git", "url": "git://github.com/ncb000gt/node.bcrypt.js.git"}, "directories": {"lib": "."}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"install": "node-waf configure build", "test": "node-waf configure build; nodeunit test/"}, "dependencies": {"nodeunit": ">=0.3.1"}, "_id": "bcrypt@0.1.2", "_engineSupported": true, "_npmVersion": "0.3.0-8", "_nodeVersion": "v0.4.0", "files": [""], "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-0.1.2.tgz", "shasum": "d00c887a9366912c3408475174da1a6ad0c5098b", "size": 17236, "noattachment": false, "integrity": "sha512-gUXFUuo92wIAEAEwrx8wwiHqMXC+QTu21Uy/Q4rV9Zwhd4A1fag1opRVc17GtxlHMnDtIOQUfRYVX11fmxZtlw=="}, "deprecated": "versions < v5.0.0 do not handle NUL in passwords properly", "publish_time": 1298315336225, "maintainers": [{"name": "amitosh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1298315336225, "_cnpmcore_publish_time": "2021-12-16T12:25:51.835Z", "hasInstallScript": true}, "5.1.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "5.1.0", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 10.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm ci --build-from-source && jest", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"@mapbox/node-pre-gyp": "^1.0.10", "node-addon-api": "^5.0.0"}, "devDependencies": {"jest": "^29.1.2"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/napi-v{napi_build_version}", "package_name": "{module_name}-v{version}-napi-v{napi_build_version}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com", "remote_path": "kelektiv/node.bcrypt.js/releases/download/v{version}", "napi_versions": [3]}, "gitHead": "fc225b11b2f6203b7d04665bc3e90d3d006bae7c", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_id": "bcrypt@5.1.0", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-RHBS7HI5N5tEnGTmtR/pppX0mmDSBpQ4aCBsj7CEQfYXDcO74A8sIBYcJMuCsis2E81zDxeENYhv66oZwLiA+Q==", "shasum": "bbb27665dbc400480a524d8991ac7434e8529e17", "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-5.1.0.tgz", "fileCount": 27, "unpackedSize": 142512, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+Fs2wh8bDzeCFVeGYIKAr7kJe/AEiSdlmpwxd2riodwIhAP2STMzdo7L0/sG/Kk8S6CU8utak6mrFm8+3BSAYYVOH"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPxQHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqU+w//ZSz5b8XlQYiUXzI1s7zAeDVmHJgJEzmWCYVUF3ctzwqkIjZK\r\nmMh0NL9hyFfWMVXV29g3A6XXqiyYn/uXddYAtD5C6f3lqEPeO3doYm1Y+bxL\r\n2LKQC7pLTA6zD9Sub3QP6eqLpeEoBEEurX+le+mZ+Y2/PJwARgXKAEz+DSDD\r\ncJvWrjCMiBrO1YmXp5zRWNplVMVKrmmbi1WFx37OtwiR3yfbyMeQHp5jfnRk\r\ngMSOcn5VONMr8XiSvUPRi2msmxUzbWkg90p1Oxj0R54guOQymNInb29RICSN\r\n/TeXnGSB4Feret8o+LZ7pf/qDTZsg/2n1vNTcTzLqdNF2Z7dxtd7UUK6nzHz\r\n6LMw3BOphNCrDntCpY5f92P+tdtnSDarsQEqgJRsPLxMbQcxe35HShuoFIbt\r\n72IfGxMkXm1QsTtPsO+f9DMef/RTpE5keByooausnKKDf6IPyIgLEMitgebW\r\n1nzCV2wgc1xHeUshibKSiKe/BvKHsKoFJTtjbxVBaRs5yi0VWfXEjsq8TotY\r\nryp6IUbxBbjidwSIMKAGdohg+aK0eTT8thO9U0+R17NyylbX53Abz/261zf5\r\nNdl9ccIh8cFOc5hkR9BAc6FmcvbXXhqEcUn7s3w6Ry7JL+xv3WNHtbeDuc/m\r\nMEgA1ZQNagEfLxc8dxRAJkBGh/aDOwN+9zs=\r\n=04C7\r\n-----END PGP SIGNATURE-----\r\n", "size": 67589}, "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "amitosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_5.1.0_1665078278977_0.4333938535945563"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-06T17:46:04.797Z", "hasInstallScript": true}, "5.1.1": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "5.1.1", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 10.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "npm ci --build-from-source && jest", "install": "node-pre-gyp install --fallback-to-build"}, "dependencies": {"@mapbox/node-pre-gyp": "^1.0.11", "node-addon-api": "^5.0.0"}, "devDependencies": {"jest": "^29.6.2"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "binary": {"module_name": "bcrypt_lib", "module_path": "./lib/binding/napi-v{napi_build_version}", "package_name": "{module_name}-v{version}-napi-v{napi_build_version}-{platform}-{arch}-{libc}.tar.gz", "host": "https://github.com", "remote_path": "kelektiv/node.bcrypt.js/releases/download/v{version}", "napi_versions": [3]}, "_id": "bcrypt@5.1.1", "gitHead": "a0a88a88a304a145f5bfcfda69ac6d58d3017001", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_nodeVersion": "20.5.0", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-AGBHOG5hPYZ5Xl9KXzU5iKq9516yEmvCKDg3ecP5kX2aB6UqTeXZxk2ELnDgDm6BQSMlLt9rDB4LoSMx0rYwww==", "shasum": "0f732c6dcb4e12e5b70a25e326a72965879ba6e2", "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-5.1.1.tgz", "fileCount": 26, "unpackedSize": 110858, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICY/g0difJmUX9BZ1FWU8OEbbuO9n4BbWfz6UBSTUCiyAiBgwbprJSHSlD1T6G8UZIEX/sYwOvjElFBaGqCwQE8g0Q=="}], "size": 33310}, "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "amitosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bcrypt_5.1.1_1692157731291_0.8863146871550696"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-16T03:48:51.540Z", "publish_time": 1692157731540, "_source_registry_name": "default", "hasInstallScript": true}, "6.0.0": {"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "6.0.0", "author": {"name": "<PERSON>", "url": "https://github.com/ncb000gt"}, "engines": {"node": ">= 18"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "jest", "install": "node-gyp-build", "build": "prebuildify --napi --tag-libc --strip"}, "dependencies": {"node-addon-api": "^8.3.0", "node-gyp-build": "^4.8.4"}, "devDependencies": {"jest": "^29.7.0", "prebuildify": "^6.0.1"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "binary": {"module_name": "bcrypt_lib"}, "_id": "bcrypt@6.0.0", "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "_integrity": "sha512-cU8v/EGSrnH+HnxV2z0J7/blxH8gq7Xh2JFT6Aroax7UohdmiJJlxApMxtKfuI7z68NvvVcmR78k2LbT6efhRg==", "_resolved": "/Users/<USER>/Workspace/recrsn/node.bcrypt.js/bcrypt-6.0.0.tgz", "_from": "file:bcrypt-6.0.0.tgz", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-cU8v/EGSrnH+HnxV2z0J7/blxH8gq7Xh2JFT6Aroax7UohdmiJJlxApMxtKfuI7z68NvvVcmR78k2LbT6efhRg==", "shasum": "86643fddde9bcd0ad91400b063003fa4b0312835", "tarball": "https://registry.npmmirror.com/bcrypt/-/bcrypt-6.0.0.tgz", "fileCount": 38, "unpackedSize": 1106749, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDzk+vHSvWjwrO1UX5+9HmNSkCoLTa5bdkKJTCT2OrCuAIhALS3o6upuKaJX2WQD+qZpBje9XFxdQ6fd2f6w0WAw4t6"}], "size": 483460}, "_npmUser": {"name": "amitosh", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "ncb000gt", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "amitosh", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/bcrypt_6.0.0_1746986783874_0.338364929800111"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-11T18:06:24.247Z", "publish_time": 1746986784247, "_source_registry_name": "default", "hasInstallScript": true}}, "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Shadowfiend"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/thegoleffect"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dtrejo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pixelglow"}, {"name": "NewITFarmer.com", "url": "https://github.com/newitfarmer"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/alfredwesterveld"}, {"name": "<PERSON>-<PERSON>", "email": "<EMAIL>", "url": "https://github.com/vincentcr"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/lloyd"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/shtylman"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/vadimg"}, {"name": "<PERSON>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tootallnate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/seanmonstar"}, {"name": "<PERSON><PERSON>", "email": "fanie.oosthu<PERSON><EMAIL>", "url": "https://github.com/weareu"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Agathver"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/crutchcorn"}, {"name": "<PERSON>", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/NickNaso"}], "homepage": "https://github.com/kelektiv/node.bcrypt.js#readme", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node.bcrypt.js.git"}, "_source_registry_name": "default"}