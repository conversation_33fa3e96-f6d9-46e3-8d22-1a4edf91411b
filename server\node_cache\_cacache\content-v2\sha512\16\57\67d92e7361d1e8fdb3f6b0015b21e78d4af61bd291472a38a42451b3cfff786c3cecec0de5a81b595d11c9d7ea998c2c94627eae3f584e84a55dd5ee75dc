{"_attachments": {}, "_id": "is-property", "_rev": "3461-61f14c314ce7cf8f5826bb0e", "author": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Tests if a JSON property can be accessed using . syntax", "dist-tags": {"latest": "1.0.2"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "name": "is-property", "readme": "is-property\n===========\nTests if a property of a JavaScript object can be accessed using the dot (.) notation or if it must be enclosed in brackets, (ie use x[\" ... \"])\n\nExample\n-------\n\n```javascript\nvar isProperty = require(\"is-property\")\n\nconsole.log(isProperty(\"foo\"))  //Prints true\nconsole.log(isProperty(\"0\"))    //Prints false\n```\n\nInstall\n-------\n\n    npm install is-property\n    \n### `require(\"is-property\")(str)`\nChecks if str is a property\n\n* `str` is a string which we will test if it is a property or not\n\n**Returns** true or false depending if str is a property\n\n## Credits\n(c) 2013 <PERSON><PERSON><PERSON>. MIT License", "time": {"created": "2022-01-26T13:27:13.883Z", "modified": "2022-01-26T13:27:13.883Z", "1.0.2": "2014-12-25T14:50:43.208Z", "1.0.1": "2014-12-25T14:48:06.931Z", "1.0.0": "2014-07-30T14:50:33.406Z", "0.0.0": "2013-07-18T14:13:30.325Z"}, "versions": {"1.0.2": {"name": "is-property", "version": "1.0.2", "description": "Tests if a JSON property can be accessed using . syntax", "main": "is-property.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "homepage": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property", "_id": "is-property@1.0.2", "_shasum": "57fe1c4e48474edd65b09911f26b1cd4095dda84", "_from": ".", "_npmVersion": "2.1.4", "_nodeVersion": "0.10.26", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "57fe1c4e48474edd65b09911f26b1cd4095dda84", "size": 4498, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-property/-/is-property-1.0.2.tgz", "integrity": "sha512-Ks/IoX00TtClbGQr4TWXemAnktAQvYB7HzcCxDGqEZU6oCmb2INHuOoKxbtR+HFkmYWBKv/dOZtGRiAjDhj92g=="}, "publish_time": 1419519043208, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419519043208, "_cnpmcore_publish_time": "2021-12-16T14:33:40.470Z"}, "1.0.1": {"name": "is-property", "version": "1.0.1", "description": "Tests if a JSON property can be accessed using . syntax", "main": "is-property.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "homepage": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property", "_id": "is-property@1.0.1", "_shasum": "1805f426a444a65aead0ef0c21bc8713690fa0ec", "_from": ".", "_npmVersion": "2.1.4", "_nodeVersion": "0.10.26", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "1805f426a444a65aead0ef0c21bc8713690fa0ec", "size": 1796, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-property/-/is-property-1.0.1.tgz", "integrity": "sha512-rR7yTZ+303q3LBEkgPXXwbm3lhAObvhlz3MzqmMC8LEi5mNmZFqMO7R5IY3TsCWGas/e7U+0CMXbxWe2jo+jig=="}, "publish_time": 1419518886931, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419518886931, "_cnpmcore_publish_time": "2021-12-16T14:33:40.719Z"}, "1.0.0": {"name": "is-property", "version": "1.0.0", "description": "Tests if a JSON property can be accessed using . syntax", "main": "is-property.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "homepage": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property", "_id": "is-property@1.0.0", "dist": {"shasum": "a638fd20fe4ff37a73c78fbb306f8639e18d3d46", "size": 1796, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-property/-/is-property-1.0.0.tgz", "integrity": "sha512-3IW5/0B0ltNAriS+0T7vC5lMITvn6GoaoRPmNIYTKO1IaIKsn8Fj37Vu5eqfzRjxoDs46IGNUDlmKGpB1HcK/w=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "publish_time": 1406731833406, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406731833406, "_cnpmcore_publish_time": "2021-12-16T14:33:41.000Z"}, "0.0.0": {"name": "is-property", "version": "0.0.0", "description": "Tests if a JSON property can be accessed using . syntax", "main": "is-property.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "readmeFilename": "README.md", "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "_id": "is-property@0.0.0", "dist": {"tarball": "https://registry.npmmirror.com/is-property/-/is-property-0.0.0.tgz", "shasum": "433eda4f3d8af5918b2d25b3760d30544fa3f5fc", "size": 1793, "noattachment": false, "integrity": "sha512-bLB7Hm24xzG0pBeehAIZ0WfhmLpFhwAYrBsLahO8VfgApkt2vSmO2e0hBAXlL6yuAoZHXKjPagm9cjxgd09sfg=="}, "_from": ".", "_npmVersion": "1.2.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "publish_time": 1374156810325, "_hasShrinkwrap": false, "_cnpm_publish_time": 1374156810325, "_cnpmcore_publish_time": "2021-12-16T14:33:41.212Z"}}, "_source_registry_name": "default"}