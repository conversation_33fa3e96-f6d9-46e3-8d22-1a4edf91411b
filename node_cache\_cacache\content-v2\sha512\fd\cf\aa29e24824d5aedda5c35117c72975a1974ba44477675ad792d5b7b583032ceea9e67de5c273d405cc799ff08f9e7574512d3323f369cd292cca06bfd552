{"_attachments": {}, "_id": "formdata-polyfill", "_rev": "484-61f144a2b77ea98a748f5615", "author": {"name": "<PERSON>"}, "description": "HTML5 `FormData` for Browsers and Node.", "dist-tags": {"latest": "4.0.10"}, "license": "MIT", "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "name": "formdata-polyfill", "readme": "### A `FormData` polyfill for the browser ...and a module for NodeJS (`New!`)\n\n```bash\nnpm install formdata-polyfill\n```\n\nThe browser polyfill will likely have done its part already, and i hope you stop supporting old browsers c\",)<br>\nBut NodeJS still laks a proper FormData<br>The good old form-data package is a very old and isn't spec compatible and dose some abnormal stuff to construct and read FormData instances that other http libraries are not happy about when it comes to follow the spec.\n\n### The NodeJS / ESM version\n- The modular (~2.3 KiB minified uncompressed) version of this package is independent of any browser stuff and don't patch anything\n- It's as pure/spec compatible as it possible gets the test are run by WPT.\n- It's compatible with [node-fetch](https://github.com/node-fetch/node-fetch).\n- It have higher platform dependencies as it uses classes, symbols, ESM & private fields\n- Only dependency it has is [fetch-blob](https://github.com/node-fetch/fetch-blob)\n\n```js\n// Node example\nimport fetch from 'node-fetch'\nimport File from 'fetch-blob/file.js'\nimport { fileFromSync } from 'fetch-blob/from.js'\nimport { FormData } from 'formdata-polyfill/esm.min.js'\n\nconst file = fileFromSync('./README.md')\nconst fd = new FormData()\n\nfd.append('file-upload', new File(['abc'], 'hello-world.txt'))\nfd.append('file-upload', file)\n\n// it's also possible to append file/blob look-a-like items\n// if you have streams coming from other destinations\nfd.append('file-upload', {\n  size: 123,\n  type: '',\n  name: 'cat-video.mp4',\n  stream() { return stream },\n  [Symbol.toStringTag]: 'File'\n})\n\nfetch('https://httpbin.org/post', { method: 'POST', body: fd })\n```\n\n----\n\nIt also comes with way to convert FormData into Blobs - it's not something that every developer should have to deal with.\nIt's mainly for [node-fetch](https://github.com/node-fetch/node-fetch) and other http library to ease the process of serializing a FormData into a blob and just wish to deal with Blobs instead (Both Deno and Undici adapted a version of this [formDataToBlob](https://github.com/jimmywarting/FormData/blob/5ddea9e0de2fc5e246ab1b2f9d404dee0c319c02/formdata-to-blob.js) to the core and passes all WPT tests run by the browser itself)\n```js\nimport { Readable } from 'node:stream'\nimport { FormData, formDataToBlob } from 'formdata-polyfill/esm.min.js'\n\nconst blob = formDataToBlob(new FormData())\nfetch('https://httpbin.org/post', { method: 'POST', body: blob })\n\n// node built in http and other similar http library have to do:\nconst stream = Readable.from(blob.stream())\nconst req = http.request('http://httpbin.org/post', {\n  method: 'post',\n  headers: {\n    'Content-Length': blob.size,\n    'Content-Type': blob.type\n  }\n})\nstream.pipe(req)\n```\n\nPS: blob & file that are appended to the FormData will not be read until any of the serialized blob read-methods gets called\n...so uploading very large files is no biggie\n\n### Browser polyfill\n\nusage:\n\n```js\nimport 'formdata-polyfill' // that's it\n```\n\nThe browser polyfill conditionally replaces the native implementation rather than fixing the missing functions,\nsince otherwise there is no way to get or delete existing values in the FormData object.\nTherefore this also patches `XMLHttpRequest.prototype.send` and `fetch` to send the `FormData` as a blob,\nand `navigator.sendBeacon` to send native `FormData`.\n\nI was unable to patch the Response/Request constructor\nso if you are constructing them with FormData then you need to call `fd._blob()` manually.\n\n```js\nnew Request(url, {\n  method: 'post',\n  body: fd._blob ? fd._blob() : fd\n})\n```\n\nDependencies\n---\n\nIf you need to support IE <= 9 then I recommend you to include eligrey's [blob.js]\n(which i hope you don't - since IE is now dead)\n\n<details>\n    <summary>Updating from 2.x to 3.x</summary>\n\nPreviously you had to import the polyfill and use that,\nsince it didn't replace the global (existing) FormData implementation.\nBut now it transparently calls `_blob()` for you when you are sending something with fetch or XHR,\nby way of monkey-patching the `XMLHttpRequest.prototype.send` and `fetch` functions.\n\nSo you maybe had something like this:\n\n```javascript\nvar FormData = require('formdata-polyfill')\nvar fd = new FormData(form)\nxhr.send(fd._blob())\n```\n\nThere is no longer anything exported from the module\n(though you of course still need to import it to install the polyfill),\nso you can now use the FormData object as normal:\n\n```javascript\nrequire('formdata-polyfill')\nvar fd = new FormData(form)\nxhr.send(fd)\n```\n\n</details>\n\n\n\nNative Browser compatibility (as of 2021-05-08)\n---\nBased on this you can decide for yourself if you need this polyfill.\n\n[![screenshot](https://user-images.githubusercontent.com/1148376/117550329-0993aa80-b040-11eb-976c-14e31f1a3ba4.png)](https://developer.mozilla.org/en-US/docs/Web/API/FormData#Browser_compatibility)\n\n\n\nThis normalizes support for the FormData API:\n\n - `append` with filename\n - `delete()`, `get()`, `getAll()`, `has()`, `set()`\n - `entries()`, `keys()`, `values()`, and support for `for...of`\n - Available in web workers (just include the polyfill)\n\n  [npm-image]: https://img.shields.io/npm/v/formdata-polyfill.svg\n  [npm-url]: https://www.npmjs.com/package/formdata-polyfill\n  [blob.js]: https://github.com/eligrey/Blob.js\n", "time": {"created": "2022-01-26T12:54:58.579Z", "modified": "2023-07-27T22:55:14.088Z", "4.0.10": "2021-09-30T13:07:58.251Z", "4.0.9": "2021-09-29T21:48:26.581Z", "4.0.8": "2021-09-26T13:10:14.583Z", "4.0.7": "2021-09-06T23:20:30.961Z", "4.0.6": "2021-06-21T07:04:22.915Z", "4.0.5": "2021-06-19T11:02:27.305Z", "4.0.4": "2021-06-17T17:18:42.080Z", "4.0.3": "2021-06-06T12:37:59.929Z", "4.0.2": "2021-06-06T12:35:15.567Z", "4.0.1": "2021-05-29T12:11:52.653Z", "4.0.0": "2021-05-08T19:37:24.643Z", "3.0.20": "2020-06-17T19:59:30.535Z", "3.0.19": "2019-08-06T09:05:57.645Z", "3.0.18": "2019-05-16T19:54:05.160Z", "3.0.17": "2019-03-03T09:07:22.195Z", "3.0.15": "2019-02-09T11:59:18.439Z", "3.0.14": "2019-02-09T11:52:39.989Z", "3.0.13": "2018-11-15T19:10:53.230Z", "3.0.12": "2018-09-03T14:41:37.055Z", "3.0.11": "2018-06-26T21:09:26.595Z", "3.0.10": "2018-04-16T13:00:55.799Z", "3.0.9": "2017-12-06T18:15:17.503Z", "3.0.8": "2017-12-04T13:16:27.191Z", "3.0.7": "2017-12-03T15:59:54.928Z", "3.0.6": "2017-12-03T15:42:04.645Z", "3.0.5": "2017-11-29T13:46:17.481Z", "3.0.4": "2017-11-28T10:41:14.220Z", "3.0.3": "2017-11-28T10:18:14.550Z", "3.0.2": "2017-11-28T10:10:08.196Z", "3.0.1": "2017-11-22T12:12:27.878Z", "3.0.0": "2017-11-22T11:52:11.979Z", "3.0.0-alpha.1": "2017-11-22T11:48:45.314Z", "2.0.4": "2017-08-22T06:54:45.605Z", "2.0.3": "2017-07-12T12:43:35.312Z", "2.0.2": "2017-06-17T21:47:41.273Z", "2.0.1": "2017-06-17T17:03:22.927Z", "2.0.0": "2017-06-17T14:18:54.755Z", "1.0.7": "2017-03-28T08:00:29.069Z", "1.0.6": "2017-03-03T12:24:56.593Z", "1.0.5": "2017-03-02T12:38:44.633Z", "1.0.4": "2017-02-05T13:32:54.569Z", "1.0.3": "2016-12-19T22:43:10.021Z", "1.0.2": "2016-11-25T22:06:07.344Z", "1.0.1": "2016-11-25T22:05:22.137Z"}, "versions": {"4.0.10": {"name": "formdata-polyfill", "version": "4.0.10", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build.js", "test": "node test/test-esm.js", "test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test-polyfill": "php -S localhost:4445 & open http://localhost:4445/test/test-polyfill.html"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "engines": {"node": ">=12.20.0"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.1.2"}, "devDependencies": {"@types/google-closure-compiler": "^0.0.19", "@types/node": "^16.7.10", "google-closure-compiler": "^20210808.0.0"}, "gitHead": "fbb503396bfda85e255a2adb3711c44ff1544c7b", "_id": "formdata-polyfill@4.0.10", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.1", "dist": {"shasum": "24807c31c9d402e002ab3d8c720144ceb8848423", "size": 11359, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.10_1633007278127_0.641382215737518"}, "_hasShrinkwrap": false, "publish_time": 1633007278251, "_cnpm_publish_time": 1633007278251, "_cnpmcore_publish_time": "2021-12-13T13:55:04.685Z"}, "4.0.9": {"name": "formdata-polyfill", "version": "4.0.9", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build.js", "test": "node test/test-esm.js", "test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test-polyfill": "php -S localhost:4445 & open http://localhost:4445/test/test-polyfill.html"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "engines": {"node": ">=12.20.0"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.1.2"}, "devDependencies": {"@types/google-closure-compiler": "^0.0.19", "@types/node": "^16.7.10", "google-closure-compiler": "^20210808.0.0"}, "gitHead": "36e2491b3354050e8e7ba50423fae5dc46ced039", "_id": "formdata-polyfill@4.0.9", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.1", "dist": {"shasum": "0609d8e6a5b5ba19d3eb8102c48be228aa1c8cdd", "size": 11361, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.9.tgz", "integrity": "sha512-SuhZ/i+uopeFEiLXOuaP9f3Jdy+fxzhplh4nI5g1io9IUl6CaNl/PXXKb3gqDYvr+cxZTBzxpqc2zP7oDZ+NjA=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.9_1632952106419_0.23415920996230066"}, "_hasShrinkwrap": false, "publish_time": 1632952106581, "_cnpm_publish_time": 1632952106581, "_cnpmcore_publish_time": "2021-12-13T13:55:04.981Z"}, "4.0.8": {"name": "formdata-polyfill", "version": "4.0.8", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build.js", "test": "node test/test-esm.js", "test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test-polyfill": "php -S localhost:4445 & open http://localhost:4445/test/test-polyfill.html"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "engines": {"node": ">=12.20.0"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.1.2"}, "devDependencies": {"@types/google-closure-compiler": "^0.0.19", "@types/node": "^16.7.10", "google-closure-compiler": "^20210808.0.0"}, "gitHead": "04ed0f98e3228c9d51c44fb2bc7ea20d26a24e92", "_id": "formdata-polyfill@4.0.8", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.1", "dist": {"shasum": "ce9fde6430914f798ede294a037c631176160e5a", "size": 11354, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.8.tgz", "integrity": "sha512-p0naEVqvCUELWr294iIyMwXH3mhlsg2AhTrFEAWbJx1i8FOrHuaoQQKEvQsK08oF+77KxFwuRVm5ltOY1Ac1rg=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.8_1632661814466_0.21954346950435366"}, "_hasShrinkwrap": false, "publish_time": 1632661814583, "_cnpm_publish_time": 1632661814583, "_cnpmcore_publish_time": "2021-12-13T13:55:05.324Z"}, "4.0.7": {"name": "formdata-polyfill", "version": "4.0.7", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build.js", "prepare": "npm run build && npm run test", "test": "node test/test-esm.js", "test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test-polyfill": "php -S localhost:4445 & open http://localhost:4445/test/test-polyfill.html"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.1.2"}, "devDependencies": {"@types/google-closure-compiler": "^0.0.19", "@types/node": "^16.7.10", "google-closure-compiler": "^20210808.0.0"}, "gitHead": "978ee337826c82b3fad777810f3366a3d813cdd9", "_id": "formdata-polyfill@4.0.7", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "83edbac83e66dcb4ce54be53c884999c2eefefba", "size": 11351, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.7.tgz", "integrity": "sha512-pTY5vaVDcdiEyFQfTkRoCPAtv1I9yLqsf8TICX3vqQmpywZaGfjIf9tW86yt3iTcbE5oEhhUNJQa/Nn7Uv/i2w=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.7_1630970430571_0.45618183455738515"}, "_hasShrinkwrap": false, "publish_time": 1630970430961, "_cnpm_publish_time": 1630970430961, "_cnpmcore_publish_time": "2021-12-13T13:55:05.671Z"}, "4.0.6": {"name": "formdata-polyfill", "version": "4.0.6", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "node test/test-esm.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "5ddea9e0de2fc5e246ab1b2f9d404dee0c319c02", "_id": "formdata-polyfill@4.0.6", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "ba89818da1dca550b72da38c84b6bd2a9cf76130", "size": 10620, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.6.tgz", "integrity": "sha512-oJeqWjhE6r9I4TiolW6sBYkLzupq3QZLNAYcR+b1tzh/9Uktuf4qRgJY8qqLo6an0uKckdHPpfsUYHqVf/tprA=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.6_1624259062546_0.8166713323417698"}, "_hasShrinkwrap": false, "publish_time": 1624259062915, "_cnpm_publish_time": 1624259062915, "_cnpmcore_publish_time": "2021-12-13T13:55:06.146Z"}, "4.0.5": {"name": "formdata-polyfill", "version": "4.0.5", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "node test/test-esm.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "76df0e14cd5e829992bb9025beff3b47209f96a1", "_id": "formdata-polyfill@4.0.5", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "74d3acf53c3db58d3b008b96d20efa200c78365b", "size": 8670, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.5.tgz", "integrity": "sha512-ww/gjDtWjnnpaX1eAC/nXCVn3oKrnobYy1VNWSiebstwwibgaZIZHYZa2fqPmK0IWpJhtcQLlhiSkJfzbalOZQ=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.5_1624100547155_0.9545483312718841"}, "_hasShrinkwrap": false, "publish_time": 1624100547305, "_cnpm_publish_time": 1624100547305, "_cnpmcore_publish_time": "2021-12-13T13:55:06.522Z"}, "4.0.4": {"name": "formdata-polyfill", "version": "4.0.4", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "node test/test-esm.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "d26bf54b873d446900c8d5884faff4c4832247de", "_id": "formdata-polyfill@4.0.4", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "55f523da373c98834bc4ed4d6431c5bed1322453", "size": 10491, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.4.tgz", "integrity": "sha512-1ueXll39gwEaDJHGkcySG6kISzCOstgySzvMG8g5nEtDOulFO9d6l37BJlNloVqt7SiVR26EYe4z6hlQKvv5kg=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.4_1623950321953_0.34666127640591116"}, "_hasShrinkwrap": false, "publish_time": 1623950322080, "_cnpm_publish_time": 1623950322080, "_cnpmcore_publish_time": "2021-12-13T13:55:06.887Z"}, "4.0.3": {"name": "formdata-polyfill", "version": "4.0.3", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "_prepare": "npm run build && npm run test", "_test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "c3f91d86e094e7cb112233fcaa6cbe9bb0af64ee", "_id": "formdata-polyfill@4.0.3", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "ddb2df6110b698e70fac5c374b9dd0a7ade1d93a", "size": 10387, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.3.tgz", "integrity": "sha512-blDq9bSQZVknQSGq2D8w49PucQ+/q1hVRtXlLfSRQZJ+istRXoISXNWHTzg0xPchhzbFe2VIUx0t3NGNWekExw=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.3_1622983079719_0.18849432098643004"}, "_hasShrinkwrap": false, "publish_time": 1622983079929, "_cnpm_publish_time": 1622983079929, "_cnpmcore_publish_time": "2021-12-13T13:55:07.263Z"}, "4.0.2": {"name": "formdata-polyfill", "version": "4.0.2", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "_prepare": "npm run build && npm run test", "_test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "c3f91d86e094e7cb112233fcaa6cbe9bb0af64ee", "_id": "formdata-polyfill@4.0.2", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "6b0d342a5aa2e2575692ce1d4d1325261c415679", "size": 10395, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.2.tgz", "integrity": "sha512-ANCp/Co1e+aYNnEMfC7daDl202nG+UfRB76/mKpcJ6CPntxm01hlcJdyXes04Bi+o/B1kwCKPdjzFTBwYOS02w=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.2_1622982915373_0.902325491822993"}, "_hasShrinkwrap": false, "publish_time": 1622982915567, "_cnpm_publish_time": 1622982915567, "_cnpmcore_publish_time": "2021-12-13T13:55:07.657Z"}, "4.0.1": {"name": "formdata-polyfill", "version": "4.0.1", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "_prepare": "npm run build && npm run test", "_test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "c3f91d86e094e7cb112233fcaa6cbe9bb0af64ee", "_id": "formdata-polyfill@4.0.1", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "4ac57214455b7c7e4e0849e1ff20f7c8c6315ff5", "size": 10384, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.1.tgz", "integrity": "sha512-zS1ntkqglv6ClvbiHGgLWdNHjZHyrvZzct3ufpbI5Y4S4f5/Ia11G6AWEcq+G6ANrPtOINhl1/4vfnqaqbnmdg=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.1_1622290312512_0.5809119995039427"}, "_hasShrinkwrap": false, "publish_time": 1622290312653, "_cnpm_publish_time": 1622290312653, "_cnpmcore_publish_time": "2021-12-13T13:55:08.084Z"}, "4.0.0": {"name": "formdata-polyfill", "version": "4.0.0", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "_prepare": "npm run build && npm run test", "_test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "mocha": "^8.0.1"}, "dependencies": {"fetch-blob": "*", "karma": "^6.3.2"}, "gitHead": "e6d62cb38a2d8676182bce8a0d26d2c9c4c19f0d", "_id": "formdata-polyfill@4.0.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "a4bad1033447c10b601224ccd24ab32fcb4d2464", "size": 10735, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-4.0.0.tgz", "integrity": "sha512-sBVj9czlZu7nOjbHDZa3IqNT/OCs5JR45G5FW4B7ZthDpcfqIl9CCFbLXYSEh/5YDIr0cZaFEBzHaGs1o2hCgA=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.0_1620502644473_0.8887595405354125"}, "_hasShrinkwrap": false, "publish_time": 1620502644643, "_cnpm_publish_time": 1620502644643, "_cnpmcore_publish_time": "2021-12-13T13:55:08.499Z"}, "3.0.20": {"name": "formdata-polyfill", "version": "3.0.20", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^5.1.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "mocha": "^8.0.1"}, "gitHead": "18479550bebb79ae76c39cf23c2694619463170f", "_id": "formdata-polyfill@3.0.20", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "d6319db8efc5cf4bb2da27856c2b902be63be1c6", "size": 8776, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.20.tgz", "integrity": "sha512-TAaxIEwTBdoH1TWndtUH1T0/GisUHwmOKcV5hjkR/iTatHBJSOHb563FP86Lra5nXo3iNdhK7HPwMl5Ihg71pg=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.20_1592423970384_0.4095490439811864"}, "_hasShrinkwrap": false, "publish_time": 1592423970535, "_cnpm_publish_time": 1592423970535, "_cnpmcore_publish_time": "2021-12-13T13:55:08.951Z"}, "3.0.19": {"name": "formdata-polyfill", "version": "3.0.19", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "standard ./FormData.js && karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.2.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.0.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0", "standard": "^13.1.0"}, "gitHead": "807557aa4213718305b39ecd9579ee3f7a670f22", "_id": "formdata-polyfill@3.0.19", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "dist": {"shasum": "72f517db3a646a5dd8c31af0edf111fd8f1e4cee", "size": 8247, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.19.tgz", "integrity": "sha512-nRSp8nniopIOCLZOUE2omwnUvmRH6VEdKm52rLTne8XBsW7hMMBUiOjuxUPoBsiK0CatKmxArh+Svt2s7R66JQ=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.19_1565082357545_0.8210880565852463"}, "_hasShrinkwrap": false, "publish_time": 1565082357645, "_cnpm_publish_time": 1565082357645, "_cnpmcore_publish_time": "2021-12-13T13:55:09.350Z"}, "3.0.18": {"name": "formdata-polyfill", "version": "3.0.18", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "standard ./FormData.js && karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.1.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.1.4", "standard": "^12.0.1"}, "gitHead": "f9d4b2028f36c9704d914480189379f37d7510b7", "_id": "formdata-polyfill@3.0.18", "_npmVersion": "6.5.0", "_nodeVersion": "11.7.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "e94916610328db1f6796860a0a78b2e7b937889e", "size": 8282, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.18.tgz", "integrity": "sha512-qydEiPA/DWm1reRCfBJyFHs/wQhjvjoQjN8P/FatoaZJ+Efc/1kVwrHPR7Ek+BuIGINr26mHXT9KpR5WYmWZww=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.18_1558036444877_0.27173946783646397"}, "_hasShrinkwrap": false, "publish_time": 1558036445160, "_cnpm_publish_time": 1558036445160, "_cnpmcore_publish_time": "2021-12-13T13:55:09.855Z"}, "3.0.17": {"name": "formdata-polyfill", "version": "3.0.17", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "standard ./FormData.js && karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.0.0", "standard": "^12.0.1"}, "gitHead": "f7b972f8a5bd83510ca2d773dea4ba0de403e583", "_id": "formdata-polyfill@3.0.17", "_npmVersion": "6.5.0", "_nodeVersion": "11.7.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "b82c30ee8755653927ce2b5231e9ee8a66dd448e", "size": 8050, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.17.tgz", "integrity": "sha512-YQDARbu++HLwfbhY5a5Doo1Sk7Ib/UFct1AejQyjYyrYxVcus+Oiybest+xgSXaTnf99jNQti/hHngZwA494/Q=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.17_1551604042093_0.07406782022906566"}, "_hasShrinkwrap": false, "publish_time": 1551604042195, "_cnpm_publish_time": 1551604042195, "_cnpmcore_publish_time": "2021-12-13T13:55:10.295Z"}, "3.0.15": {"name": "formdata-polyfill", "version": "3.0.15", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0"}, "gitHead": "f6906e9a893f049f8d4573f973e4c3d75de16c5d", "_id": "formdata-polyfill@3.0.15", "_npmVersion": "6.5.0", "_nodeVersion": "11.7.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "77b38e7fff59181a81215ecc22ed0072f55b8ea6", "size": 7901, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.15.tgz", "integrity": "sha512-nnsIjNta4jSkBhQpxeIL5u+4fasC091qS2k9+SQbQnITGmR1/PzbLI8gex+T/bJCqwHQTfh/gqfHsZi+L3p+UA=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.15_1549713558269_0.3837759140722732"}, "_hasShrinkwrap": false, "publish_time": 1549713558439, "_cnpm_publish_time": 1549713558439, "_cnpmcore_publish_time": "2021-12-13T13:55:10.761Z"}, "3.0.14": {"name": "formdata-polyfill", "version": "3.0.14", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0"}, "gitHead": "f6906e9a893f049f8d4573f973e4c3d75de16c5d", "_id": "formdata-polyfill@3.0.14", "_npmVersion": "6.5.0", "_nodeVersion": "11.7.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "66b2ad64a22b732d31aba0d5537d7c34c150b2b0", "size": 10863, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.14.tgz", "integrity": "sha512-nARSIzabuVb7bGoG1VusU95bQOA0QCRVGrLSaq7l6lCCEF6CAm1BrUQhXt3fCpuzfCz8aTSvx0zlHoLM0LFnMQ=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.14_1549713159855_0.644691324085958"}, "_hasShrinkwrap": false, "publish_time": 1549713159989, "_cnpm_publish_time": 1549713159989, "_cnpmcore_publish_time": "2021-12-13T13:55:11.295Z"}, "3.0.13": {"name": "formdata-polyfill", "version": "3.0.13", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^3.1.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0"}, "gitHead": "1695a7c732baf57260124983dd5db4f330ed7fa4", "_id": "formdata-polyfill@3.0.13", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "991321c83b7a760aaf3788f1b7bbbf77e85b09bb", "size": 10049, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.13.tgz", "integrity": "sha512-aYrFSz9wxtBc9E1yCfD5IFhTK9FDZVRM53kUQn1W6Et5+R9c4tpa5Mg9hQIBIc2er3fLx0ElbKujipu+2551vQ=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.13_1542309053065_0.25506122995683467"}, "_hasShrinkwrap": false, "publish_time": 1542309053230, "_cnpm_publish_time": 1542309053230, "_cnpmcore_publish_time": "2021-12-13T13:55:11.758Z"}, "3.0.12": {"name": "formdata-polyfill", "version": "3.0.12", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.1.0"}, "gitHead": "e84efe752a1452d8b8a69d94377d827ac173d6a5", "_id": "formdata-polyfill@3.0.12", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "43f8d9bad5408a57c4f801784526e8230b6e9f7a", "size": 9917, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.12.tgz", "integrity": "sha512-y5V1Y4e2VSJ29+CAHozJwN9BqvsigYicZp/MqeYP00X/UMah3cLb2ix7/58fLcaAjwr8HfztD7Ih/IQCW1BGfw=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.12_1535985696860_0.3201304824786584"}, "_hasShrinkwrap": false, "publish_time": 1535985697055, "_cnpm_publish_time": 1535985697055, "_cnpmcore_publish_time": "2021-12-13T13:55:12.304Z"}, "3.0.11": {"name": "formdata-polyfill", "version": "3.0.11", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^2.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.1.0"}, "gitHead": "871683163d359883851b0839c6d160482dfbcec8", "_id": "formdata-polyfill@3.0.11", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "c82b4b4bea3356c0a6752219e54ce1edb2a7fb5b", "size": 9836, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.11.tgz", "integrity": "sha512-lDyjdlptnGL1Fk7q+hketv31EN9rWaVC/SLz1tRaUktGrsCijyueIcjn7Tw3xKEdCjS5SeBrWp5aNLWUQq+QLg=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.11_1530047366478_0.4021415435359905"}, "_hasShrinkwrap": false, "publish_time": 1530047366595, "_cnpm_publish_time": 1530047366595, "_cnpmcore_publish_time": "2021-12-13T13:55:12.881Z"}, "3.0.10": {"name": "formdata-polyfill", "version": "3.0.10", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^2.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.1.0"}, "gitHead": "13705652b43a2d139b5f5e40f713f566ae48c766", "_id": "formdata-polyfill@3.0.10", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "4e1bfcc1e131f73b07856fc159ee103d0b37ec3c", "size": 9504, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.10.tgz", "integrity": "sha512-aAnr6/BW5pnzzeCvuYQBnyf6a2bovVeMwaFiMMkoH9RanBYAkBlTvB+RfNeNC4qLa5+m9ZtFGtrW+LzXqtXLbw=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.10_1523883655665_0.3556061276708973"}, "_hasShrinkwrap": false, "publish_time": 1523883655799, "_cnpm_publish_time": 1523883655799, "_cnpmcore_publish_time": "2021-12-13T13:55:13.492Z"}, "3.0.9": {"name": "formdata-polyfill", "version": "3.0.9", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "008b5c3169362f8548e0d7fcd1da1893a65bb7b1", "_id": "formdata-polyfill@3.0.9", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "9cdcbd90e31378f5a1e65bc5f68e0db28606a5ed", "size": 8951, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.9.tgz", "integrity": "sha512-KKTifs9ipLF+y6HUdL1l7njs1he2z8QhX7te+IlX2zm7ItNlaMqnXi2GfJNq8xHFbZ1ZOHMvSBWLYs8jR3XCkA=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.9.tgz_1512584116547_0.07209669169969857"}, "directories": {}, "publish_time": 1512584117503, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512584117503, "_cnpmcore_publish_time": "2021-12-13T13:55:14.043Z"}, "3.0.8": {"name": "formdata-polyfill", "version": "3.0.8", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "13284d6743303a648754be28b4caf9357158b278", "_id": "formdata-polyfill@3.0.8", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "eab9896f54aec79b0fc57f068e3f880bb80c6127", "size": 8956, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.8.tgz", "integrity": "sha512-JGkQgtGIpCNldkgTbSirI9uZUzmJZWuWszFNpDMgPmdbHz3j2gGuZ6rk9A6VqeeTOZR59PmEzUS8mN4AUFAYxw=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.8.tgz_1512393386264_0.2903132955543697"}, "directories": {}, "publish_time": 1512393387191, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512393387191, "_cnpmcore_publish_time": "2021-12-13T13:55:14.648Z"}, "3.0.7": {"name": "formdata-polyfill", "version": "3.0.7", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "af507b2391876f0b76db25335774e3f712d39ac1", "_id": "formdata-polyfill@3.0.7", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "8eeec0ca62421af79164d0691725f94eca0eb081", "size": 8779, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.7.tgz", "integrity": "sha512-7cnmgtyakIv0rZXKJECvFh+cTMn8P1THqIG0RE9fFVOrgG6y0bF3O4n1nilGizj4/ffiyY84kb1qF0/kef3WPw=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.7.tgz_1512316794027_0.29985491721890867"}, "directories": {}, "publish_time": 1512316794928, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512316794928, "_cnpmcore_publish_time": "2021-12-13T13:55:15.200Z"}, "3.0.6": {"name": "formdata-polyfill", "version": "3.0.6", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "03526b6db506663ded4afcadb26df5d892de5745", "_id": "formdata-polyfill@3.0.6", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "3a960d511294376f7e0085244b6ddb91727ca920", "size": 8783, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.6.tgz", "integrity": "sha512-8teKnVhuyOUyN5QXiMe1NvN3kZetX7buS7NpA66qMq+oPyUJ0ASe7kvmBFgWx+Xtv1MCHcBfwfEjQUSJALz5CQ=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.6.tgz_1512315723604_0.8781582294031978"}, "directories": {}, "publish_time": 1512315724645, "_hasShrinkwrap": false, "_cnpm_publish_time": 1512315724645, "_cnpmcore_publish_time": "2021-12-13T13:55:15.748Z"}, "3.0.5": {"name": "formdata-polyfill", "version": "3.0.5", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "95c9a168a3111bd34a72d308e792f2e940413194", "_id": "formdata-polyfill@3.0.5", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "b2800e437b9bb7b77c305886ba21b56c90319081", "size": 8581, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.5.tgz", "integrity": "sha512-QPaWGKQfp1FBRE+5RVfixxW94gs7QpCNivvC7U82CbZmpn5qn/S06kl4HrasUgbniJ3lt5a0Nq9AuzFOQSpczg=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.5.tgz_1511963176365_0.2223030086606741"}, "directories": {}, "publish_time": 1511963177481, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511963177481, "_cnpmcore_publish_time": "2021-12-13T13:55:16.335Z"}, "3.0.4": {"name": "formdata-polyfill", "version": "3.0.4", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "328c0cdc75dfdff24b603f664a7cef21e7fc3ff4", "_id": "formdata-polyfill@3.0.4", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "ae2877205ce5d4a6e413f9c19dc289654ada1a19", "size": 7697, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.4.tgz", "integrity": "sha512-L/k+T0OQvAE7aN0i0Vpdlp+qXwATJcvZFUPR58ked26iPo5HS4q8wVl1JPJoufhAp96gerDE69z+zYj+g5Vkjw=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.4.tgz_1511865673218_0.4973791416268796"}, "directories": {}, "publish_time": 1511865674220, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511865674220, "_cnpmcore_publish_time": "2021-12-13T13:55:16.935Z"}, "3.0.3": {"name": "formdata-polyfill", "version": "3.0.3", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "daab62a9d4ca014757e2d0beb2c34f8ffa065e46", "_id": "formdata-polyfill@3.0.3", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "dbca1bda194b1656ffd6aac3f4ba86d472592de2", "size": 7260, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.3.tgz", "integrity": "sha512-gEeQA4B9H2EyQCnjj8Jma3/bhrcmc61N+dTDOmXktYe06HKcp1i6BxHO3J/oQp+VQIF6jHXzeHI51wv4rjH+Wg=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.3.tgz_1511864293551_0.11417500744573772"}, "directories": {}, "publish_time": 1511864294550, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511864294550, "_cnpmcore_publish_time": "2021-12-13T13:55:17.612Z"}, "3.0.2": {"name": "formdata-polyfill", "version": "3.0.2", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "daab62a9d4ca014757e2d0beb2c34f8ffa065e46", "_id": "formdata-polyfill@3.0.2", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "cdd6ed3579b3915f696ea830d4f56d9be0520800", "size": 7247, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.2.tgz", "integrity": "sha512-oUGGwATfMU6oQj5jCvXNMpjPV42LJYFeVvzJ3D0xP/36CxpnAWlZHuMGru487mKLoZ6GEZsEbHuSG41y4SZL2Q=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.2.tgz_1511863807349_0.5209966427646577"}, "directories": {}, "publish_time": 1511863808196, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511863808196, "_cnpmcore_publish_time": "2021-12-13T13:55:18.372Z"}, "3.0.1": {"name": "formdata-polyfill", "version": "3.0.1", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "google-closure-compiler --compilation_level ADVANCED --js_output_file formdata.min.js FormData.js", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"google-closure-compiler": "^20171112.0.0"}, "gitHead": "e44bfd6ee3b1f08a8465893a8fe8011dd96baf32", "_id": "formdata-polyfill@3.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "3ba73d6ced4af6e124cb78eedcca38881b6abea1", "size": 7400, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.1.tgz", "integrity": "sha512-lwPJQPW9vSnS/475ZRBdwLl9q20axFzKn+oE2PHvvTdvOJn2+jg5IRnElqqU9FzTr6hrdyEcKVXANoop7HyDpg=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.1.tgz_1511352746948_0.20994951110333204"}, "directories": {}, "publish_time": 1511352747878, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511352747878, "_cnpmcore_publish_time": "2021-12-13T13:55:19.086Z"}, "3.0.0": {"name": "formdata-polyfill", "version": "3.0.0", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "google-closure-compiler --compilation_level ADVANCED --js_output_file formdata.min.js FormData.js", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"google-closure-compiler": "^20171112.0.0"}, "gitHead": "ccd2f7ca8a3e8b95c27b0c71ced952c02ea7a7e5", "_id": "formdata-polyfill@3.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "8a95a8e12caecde9217d908743b1e56fb6f68ca9", "size": 7388, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.0.tgz", "integrity": "sha512-PWxesKKgQs8n4ZpDCJScvS2QDZg979eFxqLUB9BjdzRedBRVOzt6DczI438XY+QFuQW31IrWATwgVbFEeDuoig=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.0.tgz_1511351531046_0.49980301898904145"}, "directories": {}, "publish_time": 1511351531979, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511351531979, "_cnpmcore_publish_time": "2021-12-13T13:55:19.778Z"}, "3.0.0-alpha.1": {"name": "formdata-polyfill", "version": "3.0.0-alpha.1", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "google-closure-compiler --compilation_level ADVANCED --js_output_file formdata.min.js FormData.js", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"google-closure-compiler": "^20171112.0.0"}, "gitHead": "ccd2f7ca8a3e8b95c27b0c71ced952c02ea7a7e5", "_id": "formdata-polyfill@3.0.0-alpha.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "13c16b1d6ff2c698a1863319390a6b43d3011a11", "size": 7392, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-3.0.0-alpha.1.tgz", "integrity": "sha512-Uw3YTOszlLzumpL/ATdG6+Tj6mX9miZXOdyzTgHk9sIY1j3zfEsBTkeMgoNMTqoyYh0AoKFpMtBYSAtu4CzsRA=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.0-alpha.1.tgz_1511351324433_0.6919542476534843"}, "directories": {}, "publish_time": 1511351325314, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511351325314, "_cnpmcore_publish_time": "2021-12-13T13:55:20.436Z"}, "2.0.4": {"name": "formdata-polyfill", "version": "2.0.4", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "f24b6e7c43813c8f1b86e112d82216f8175ec380", "_id": "formdata-polyfill@2.0.4", "_npmVersion": "5.1.0", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "2f5eae8f34b697cdc6b107ace7d617ed5f1fb7c4", "size": 6480, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-2.0.4.tgz", "integrity": "sha512-jsOLsQy0/dbO7BG3G9H1riXNuVxn5L2aBqdyzYUnxk1Jt4KztpF5N2HgHijzfs52temBpLYXz/jEwkbcQodfdQ=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.4.tgz_1503384884050_0.5999235606286675"}, "directories": {}, "publish_time": 1503384885605, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503384885605, "_cnpmcore_publish_time": "2021-12-13T13:55:21.156Z"}, "2.0.3": {"name": "formdata-polyfill", "version": "2.0.3", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "c6d4e234982b2b4359ffde6cbfb317ec41a90dc9", "_id": "formdata-polyfill@2.0.3", "_npmVersion": "5.1.0", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "d85146ec1fc37a37279221273ac5045078e79628", "size": 6469, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-2.0.3.tgz", "integrity": "sha512-Jlrp2WZ4oA513iPFSXwsmSTKU3+CEbwMtPdxUuyd4WaVaxorYai3V71T9n0/TOESb3oy0F/vyvsiybDtKzYQvg=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.3.tgz_1499863414138_0.9088069072458893"}, "directories": {}, "publish_time": 1499863415312, "_hasShrinkwrap": false, "_cnpm_publish_time": 1499863415312, "_cnpmcore_publish_time": "2021-12-13T13:55:21.982Z"}, "2.0.2": {"name": "formdata-polyfill", "version": "2.0.2", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "4b2f5b154068cd51a577ab20f36501698f22b9b0", "_id": "formdata-polyfill@2.0.2", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "c4384ddd1641908cb008d92324fe113ce2a5d0ad", "size": 6458, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-2.0.2.tgz", "integrity": "sha512-dvrrNWQE4tDUL6goS+QweI7lGi8b3WzU2aKOwpjekP91s9Mgtcfos1T7eKYr1/BvKA9kCKsoEqNqF1pBVZKxDA=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.2.tgz_1497736060307_0.817123741377145"}, "directories": {}, "publish_time": 1497736061273, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497736061273, "_cnpmcore_publish_time": "2021-12-13T13:55:22.689Z"}, "2.0.1": {"name": "formdata-polyfill", "version": "2.0.1", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "79d7643d58925f12d2cfadc984ae450956956198", "_id": "formdata-polyfill@2.0.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "0d584d2f101ecc277727ac79d6094b941bf8ec5e", "size": 6470, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-2.0.1.tgz", "integrity": "sha512-4XWvky9pZFyY+68aRcEcGVM4j0QdKgDw+5lma1iw9KAFhItksNJJkclNkstfp/IxMBWrFj7yILdJAdVxVd+YGg=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.1.tgz_1497719001967_0.32420781231485307"}, "directories": {}, "publish_time": 1497719002927, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497719002927, "_cnpmcore_publish_time": "2021-12-13T13:55:23.477Z"}, "2.0.0": {"name": "formdata-polyfill", "version": "2.0.0", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "79d7643d58925f12d2cfadc984ae450956956198", "_id": "formdata-polyfill@2.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "d3f9f88f575f96d10be3e9e48adb070d3a9fefe2", "size": 6053, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-2.0.0.tgz", "integrity": "sha512-TxL/8V5O5JgRrpujoIcZ7VqMLtLOwyh8W5Jb4+3sPdF4Bb8Oln7VQ6kGgeG96UFtWTFWlD0OvWBqSHN1kwPFMw=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.0.tgz_1497709133794_0.48105699475854635"}, "directories": {}, "publish_time": 1497709134755, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497709134755, "_cnpmcore_publish_time": "2021-12-13T13:55:24.299Z"}, "1.0.7": {"name": "formdata-polyfill", "version": "1.0.7", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "83d92125c3b267a06fb9c07bf1b5ce86be823472", "_id": "formdata-polyfill@1.0.7", "_shasum": "35236fbf6d1efd096cea115004901a97762d0acf", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "35236fbf6d1efd096cea115004901a97762d0acf", "size": 4381, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-1.0.7.tgz", "integrity": "sha512-AiRnP2e4tSa01n7Gulb00K5PmxfP0nwPB/U19eyAe69LfcFmKe/UQPpP28ojkYAhLE3DjVIxcGiDwm7G+p69kQ=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.7.tgz_1490688027036_0.5020065174903721"}, "directories": {}, "publish_time": 1490688029069, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490688029069, "_cnpmcore_publish_time": "2021-12-13T13:55:25.081Z"}, "1.0.6": {"name": "formdata-polyfill", "version": "1.0.6", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "f85488093b330ed18107df705a5e38ca1251a13b", "_id": "formdata-polyfill@1.0.6", "_shasum": "f6fea532bde6c206cdc17e2307583e547585c146", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "f6fea532bde6c206cdc17e2307583e547585c146", "size": 4247, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-1.0.6.tgz", "integrity": "sha512-U/hpTyQ0W9jmmYRjWuGYU0iRg2v/qAPh6rV3yP3b0OyGeHwcXjfJ1/DihwcuC2qKd+y0wwA6jg6BgKIYEkI4jQ=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.6.tgz_1488543894812_0.38432479999028146"}, "directories": {}, "publish_time": 1488543896593, "_hasShrinkwrap": false, "_cnpm_publish_time": 1488543896593, "_cnpmcore_publish_time": "2021-12-13T13:55:25.892Z"}, "1.0.5": {"name": "formdata-polyfill", "version": "1.0.5", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "77929d49347102a86fcd80639147928ae6b04e72", "_id": "formdata-polyfill@1.0.5", "_shasum": "dd024d970432ce7a46d4458f394ed9454c1eca69", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "dd024d970432ce7a46d4458f394ed9454c1eca69", "size": 4245, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-1.0.5.tgz", "integrity": "sha512-u2O+UUBYpcjmL3KsJtrQ3qpmu3TC4eUiyk9mlhUj/KYJYC7f6YWQB4wzrZUMslh0zr1vWoELY6pQIboo00xB0w=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.5.tgz_1488458323992_0.20464711193926632"}, "directories": {}, "publish_time": 1488458324633, "_hasShrinkwrap": false, "_cnpm_publish_time": 1488458324633, "_cnpmcore_publish_time": "2021-12-13T13:55:26.691Z"}, "1.0.4": {"name": "formdata-polyfill", "version": "1.0.4", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "b7c38dc379ced5131639f9b4a35932e3051347f4", "_id": "formdata-polyfill@1.0.4", "_shasum": "9ef5dd2d78db8d635351224c60e4a915bf1e29af", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "9ef5dd2d78db8d635351224c60e4a915bf1e29af", "size": 4261, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-1.0.4.tgz", "integrity": "sha512-3GLBR7SRx+1KvouQMLM+t8FZmvnYWCfWzdl/OjS72rL/4feYr+N45poIivrb2p05Rr2UbGgyP/+2dzkJrzPtUw=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.4.tgz_1486301572854_0.7544996605720371"}, "directories": {}, "publish_time": 1486301574569, "_hasShrinkwrap": false, "_cnpm_publish_time": 1486301574569, "_cnpmcore_publish_time": "2021-12-13T13:55:27.506Z"}, "1.0.3": {"name": "formdata-polyfill", "version": "1.0.3", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "59ca5ac59abb7a9a3f74dc5f84288d95d5ecdf52", "_id": "formdata-polyfill@1.0.3", "_shasum": "d89832b78b453d7caab05c1e79e47020d0262491", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "d89832b78b453d7caab05c1e79e47020d0262491", "size": 4271, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-1.0.3.tgz", "integrity": "sha512-G6wiigZPhRnlIkrAEzndPv4gZUULXekgVvVd6M0GzJ57OeUsJo/fZfIRFo1d4Sz00Q4Fnb+xmtnZSrLtZ5S4Zg=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.3.tgz_1482187389404_0.12283244170248508"}, "directories": {}, "publish_time": 1482187390021, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482187390021, "_cnpmcore_publish_time": "2021-12-13T13:55:28.348Z"}, "1.0.2": {"name": "formdata-polyfill", "version": "1.0.2", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "1ed0d82cd110b3ca047db5974c1d8450ff6ae2f6", "_id": "formdata-polyfill@1.0.2", "_shasum": "1f2aa19a2a593b5129b449ba7f42364b8e8e30b4", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "1f2aa19a2a593b5129b449ba7f42364b8e8e30b4", "size": 4064, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-1.0.2.tgz", "integrity": "sha512-IyMlp4FK0mHj1/t6Ws7mmgTnN80mNgkWauLQmWxPL8YFBH2lLK0/d3IxzcoxxWGTTQu6Z3sEsCMpB+bNcrD/1Q=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.2.tgz_1480111566754_0.6992857563309371"}, "directories": {}, "publish_time": 1480111567344, "_hasShrinkwrap": false, "_cnpm_publish_time": 1480111567344, "_cnpmcore_publish_time": "2021-12-13T13:55:29.166Z"}, "1.0.1": {"name": "formdata-polyfill", "version": "1.0.1", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "c9dff1a702133a525e7628ab7eb0c1251f5762bc", "_id": "formdata-polyfill@1.0.1", "_shasum": "a698239a46d0e71eea09a8119373bc9d9398ff5d", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "a698239a46d0e71eea09a8119373bc9d9398ff5d", "size": 4050, "noattachment": false, "tarball": "https://registry.npmmirror.com/formdata-polyfill/-/formdata-polyfill-1.0.1.tgz", "integrity": "sha512-M7WlN1lbIrlm0gUr3sv5H656raI0XEDK5hiSieM/2kA1eUNJndH09+DUyBPYjUP8H1PvJ+dbn27ewQzA/WMt+A=="}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.1.tgz_1480111520085_0.02396637643687427"}, "directories": {}, "publish_time": 1480111522137, "_hasShrinkwrap": false, "_cnpm_publish_time": 1480111522137, "_cnpmcore_publish_time": "2021-12-13T13:55:29.894Z"}}, "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "_source_registry_name": "default"}