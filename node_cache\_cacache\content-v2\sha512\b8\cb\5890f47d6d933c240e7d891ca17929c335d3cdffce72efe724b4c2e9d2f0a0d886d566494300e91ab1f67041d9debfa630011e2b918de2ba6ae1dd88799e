{"_attachments": {}, "_id": "iconv-lite", "_rev": "877-61f14566a920628a7b6e606e", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Convert character encodings in pure javascript.", "dist-tags": {"bleeding": "0.4.0-pre3", "latest": "0.6.3"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "iconv-lite", "readme": "## iconv-lite: Pure JS character encoding conversion\n\n * No need for native code compilation. Quick to install, works on Windows and in sandboxed environments like [Cloud9](http://c9.io).\n * Used in popular projects like [Express.js (body_parser)](https://github.com/expressjs/body-parser), \n   [<PERSON><PERSON><PERSON>](http://gruntjs.com/), [Nodemailer](http://www.nodemailer.com/), [Ye<PERSON>](http://yeoman.io/) and others.\n * Faster than [node-iconv](https://github.com/bnoordhuis/node-iconv) (see below for performance comparison).\n * Intuitive encode/decode API, including Streaming support.\n * In-browser usage via [browserify](https://github.com/substack/node-browserify) or [webpack](https://webpack.js.org/) (~180kb gzip compressed with <PERSON>uffer shim included).\n * Typescript [type definition file](https://github.com/ashtuchkin/iconv-lite/blob/master/lib/index.d.ts) included.\n * React Native is supported (need to install `stream` module to enable Streaming API).\n * License: MIT.\n\n[![NPM Stats](https://nodei.co/npm/iconv-lite.png)](https://npmjs.org/package/iconv-lite/)  \n[![Build Status](https://travis-ci.org/ashtuchkin/iconv-lite.svg?branch=master)](https://travis-ci.org/ashtuchkin/iconv-lite)\n[![npm](https://img.shields.io/npm/v/iconv-lite.svg)](https://npmjs.org/package/iconv-lite/)\n[![npm downloads](https://img.shields.io/npm/dm/iconv-lite.svg)](https://npmjs.org/package/iconv-lite/)\n[![npm bundle size](https://img.shields.io/bundlephobia/min/iconv-lite.svg)](https://npmjs.org/package/iconv-lite/)\n\n## Usage\n### Basic API\n```javascript\nvar iconv = require('iconv-lite');\n\n// Convert from an encoded buffer to a js string.\nstr = iconv.decode(Buffer.from([0x68, 0x65, 0x6c, 0x6c, 0x6f]), 'win1251');\n\n// Convert from a js string to an encoded buffer.\nbuf = iconv.encode(\"Sample input string\", 'win1251');\n\n// Check if encoding is supported\niconv.encodingExists(\"us-ascii\")\n```\n\n### Streaming API\n```javascript\n\n// Decode stream (from binary data stream to js strings)\nhttp.createServer(function(req, res) {\n    var converterStream = iconv.decodeStream('win1251');\n    req.pipe(converterStream);\n\n    converterStream.on('data', function(str) {\n        console.log(str); // Do something with decoded strings, chunk-by-chunk.\n    });\n});\n\n// Convert encoding streaming example\nfs.createReadStream('file-in-win1251.txt')\n    .pipe(iconv.decodeStream('win1251'))\n    .pipe(iconv.encodeStream('ucs2'))\n    .pipe(fs.createWriteStream('file-in-ucs2.txt'));\n\n// Sugar: all encode/decode streams have .collect(cb) method to accumulate data.\nhttp.createServer(function(req, res) {\n    req.pipe(iconv.decodeStream('win1251')).collect(function(err, body) {\n        assert(typeof body == 'string');\n        console.log(body); // full request body string\n    });\n});\n```\n\n## Supported encodings\n\n *  All node.js native encodings: utf8, ucs2 / utf16-le, ascii, binary, base64, hex.\n *  Additional unicode encodings: utf16, utf16-be, utf-7, utf-7-imap, utf32, utf32-le, and utf32-be.\n *  All widespread singlebyte encodings: Windows 125x family, ISO-8859 family, \n    IBM/DOS codepages, Macintosh family, KOI8 family, all others supported by iconv library. \n    Aliases like 'latin1', 'us-ascii' also supported.\n *  All widespread multibyte encodings: CP932, CP936, CP949, CP950, GB2312, GBK, GB18030, Big5, Shift_JIS, EUC-JP.\n\nSee [all supported encodings on wiki](https://github.com/ashtuchkin/iconv-lite/wiki/Supported-Encodings).\n\nMost singlebyte encodings are generated automatically from [node-iconv](https://github.com/bnoordhuis/node-iconv). Thank you Ben Noordhuis and libiconv authors!\n\nMultibyte encodings are generated from [Unicode.org mappings](http://www.unicode.org/Public/MAPPINGS/) and [WHATWG Encoding Standard mappings](http://encoding.spec.whatwg.org/). Thank you, respective authors!\n\n\n## Encoding/decoding speed\n\nComparison with node-iconv module (1000x256kb, on MacBook Pro, Core i5/2.6 GHz, Node v0.12.0). \nNote: your results may vary, so please always check on your hardware.\n\n    operation             iconv@2.1.4   iconv-lite@0.4.7\n    ----------------------------------------------------------\n    encode('win1251')     ~96 Mb/s      ~320 Mb/s\n    decode('win1251')     ~95 Mb/s      ~246 Mb/s\n\n## BOM handling\n\n * Decoding: BOM is stripped by default, unless overridden by passing `stripBOM: false` in options\n   (f.ex. `iconv.decode(buf, enc, {stripBOM: false})`).\n   A callback might also be given as a `stripBOM` parameter - it'll be called if BOM character was actually found.\n * If you want to detect UTF-8 BOM when decoding other encodings, use [node-autodetect-decoder-stream](https://github.com/danielgindi/node-autodetect-decoder-stream) module.\n * Encoding: No BOM added, unless overridden by `addBOM: true` option.\n\n## UTF-16 Encodings\n\nThis library supports UTF-16LE, UTF-16BE and UTF-16 encodings. First two are straightforward, but UTF-16 is trying to be\nsmart about endianness in the following ways:\n * Decoding: uses BOM and 'spaces heuristic' to determine input endianness. Default is UTF-16LE, but can be \n   overridden with `defaultEncoding: 'utf-16be'` option. Strips BOM unless `stripBOM: false`.\n * Encoding: uses UTF-16LE and writes BOM by default. Use `addBOM: false` to override.\n\n## UTF-32 Encodings\n\nThis library supports UTF-32LE, UTF-32BE and UTF-32 encodings. Like the UTF-16 encoding above, UTF-32 defaults to UTF-32LE, but uses BOM and 'spaces heuristics' to determine input endianness. \n * The default of UTF-32LE can be overridden with the `defaultEncoding: 'utf-32be'` option. Strips BOM unless `stripBOM: false`.\n * Encoding: uses UTF-32LE and writes BOM by default. Use `addBOM: false` to override. (`defaultEncoding: 'utf-32be'` can also be used here to change encoding.)\n\n## Other notes\n\nWhen decoding, be sure to supply a Buffer to decode() method, otherwise [bad things usually happen](https://github.com/ashtuchkin/iconv-lite/wiki/Use-Buffers-when-decoding).  \nUntranslatable characters are set to � or ?. No transliteration is currently supported.  \nNode versions 0.10.31 and 0.11.13 are buggy, don't use them (see #65, #77).  \n\n## Testing\n\n```bash\n$ <NAME_EMAIL>:ashtuchkin/iconv-lite.git\n$ cd iconv-lite\n$ npm install\n$ npm test\n    \n$ # To view performance:\n$ node test/performance.js\n\n$ # To view test coverage:\n$ npm run coverage\n$ open coverage/lcov-report/index.html\n```\n", "time": {"created": "2022-01-26T12:58:14.466Z", "modified": "2023-07-27T17:53:29.783Z", "0.6.3": "2021-05-24T03:00:17.928Z", "0.6.2": "2020-07-08T05:24:56.196Z", "0.6.1": "2020-06-28T06:09:17.204Z", "0.6.0": "2020-06-08T09:01:03.203Z", "0.5.2": "2020-06-08T08:29:58.934Z", "0.5.1": "2020-01-18T07:51:52.746Z", "0.5.0": "2019-06-26T22:29:48.857Z", "0.4.24": "2018-08-22T20:23:12.162Z", "0.4.23": "2018-05-07T22:22:24.294Z", "0.4.22": "2018-05-05T23:37:31.575Z", "0.4.21": "2018-04-07T04:31:13.009Z", "0.4.20": "2018-04-07T04:02:35.444Z", "0.4.19": "2017-09-10T03:56:41.914Z", "0.4.18": "2017-06-13T15:20:12.238Z", "0.4.17": "2017-05-01T05:10:12.243Z", "0.4.16": "2017-04-22T22:48:59.698Z", "0.4.15": "2016-11-21T19:02:59.113Z", "0.4.14": "2016-11-21T05:54:48.478Z", "0.4.13": "2015-10-02T04:10:55.543Z", "0.4.12": "2015-09-26T21:17:50.923Z", "0.4.11": "2015-07-03T21:29:49.475Z", "0.4.10": "2015-05-27T06:18:34.272Z", "0.4.9": "2015-05-24T12:47:29.122Z", "0.4.8": "2015-04-14T17:46:59.824Z", "0.4.7": "2015-02-06T07:41:29.590Z", "0.4.6": "2015-01-12T14:46:50.603Z", "0.4.5": "2014-11-20T10:38:59.501Z", "0.4.4": "2014-07-17T04:53:40.014Z", "0.4.3": "2014-06-15T04:57:36.477Z", "0.4.2": "2014-06-12T23:55:05.316Z", "0.4.1": "2014-06-12T04:58:44.366Z", "0.4.0": "2014-06-11T03:06:17.844Z", "0.4.0-pre3": "2014-04-28T10:27:26.683Z", "0.4.0-pre2": "2014-04-26T02:35:46.872Z", "0.4.0-pre": "2014-04-25T21:50:56.999Z", "0.2.11": "2013-07-15T01:23:17.021Z", "0.2.10": "2013-05-27T03:33:58.336Z", "0.2.9": "2013-05-19T23:16:51.894Z", "0.2.8": "2013-04-16T20:41:10.178Z", "0.2.7": "2012-12-05T06:40:33.383Z", "0.2.6": "2012-11-20T07:39:43.417Z", "0.2.5": "2012-08-26T02:55:50.688Z", "0.2.4": "2012-08-24T21:10:48.490Z", "0.2.3": "2012-07-13T14:07:06.630Z", "0.2.1": "2012-06-29T14:49:19.424Z", "0.2.0": "2012-05-07T17:43:01.137Z", "0.1.4": "2012-05-06T11:24:01.572Z", "0.1.3": "2012-05-06T10:06:54.542Z", "0.1.2": "2012-03-09T18:49:04.510Z", "0.1.1": "2011-11-23T12:55:22.201Z", "0.1.0": "2011-11-09T17:51:05.090Z"}, "versions": {"0.6.3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.6.3", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"stream": false}, "devDependencies": {"async": "^3.2.0", "c8": "^7.2.0", "errto": "^0.2.1", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "^2.88.2", "semver": "^6.3.0", "unorm": "^1.6.0"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "gitHead": "d13de386c07a1574425811f8f219c181c633e88d", "_id": "iconv-lite@0.6.3", "_nodeVersion": "15.6.0", "_npmVersion": "7.6.1", "dist": {"shasum": "a52f80bf38da1952eb5c681790719871a1a72501", "size": 190667, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.6.3_1621825217719_0.23976423925317714"}, "_hasShrinkwrap": false, "publish_time": 1621825217928, "_cnpm_publish_time": 1621825217928, "_cnpmcore_publish_time": "2021-12-13T08:15:45.466Z"}, "0.6.2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.6.2", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"stream": false}, "devDependencies": {"async": "^3.2.0", "c8": "^7.2.0", "errto": "^0.2.1", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "^2.88.2", "semver": "^6.3.0", "unorm": "^1.6.0"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "gitHead": "efbad0a92edf1b09c111278abb104d935c6c0482", "_id": "iconv-lite@0.6.2", "_nodeVersion": "12.18.1", "_npmVersion": "6.14.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ce13d1875b0c3a674bd6a04b7f76b01b1b6ded01", "size": 188946, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.2.tgz", "integrity": "sha512-2y91h5OpQlolefMPmUlivelittSWy0rP+oYVpn6A7GwVHNE8AWzoYOBNmlwks3LobaJxgHCYZAnyNo2GgpNRNQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.6.2_1594185895956_0.9242056530346248"}, "_hasShrinkwrap": false, "publish_time": 1594185896196, "_cnpm_publish_time": 1594185896196, "_cnpmcore_publish_time": "2021-12-13T08:15:45.774Z"}, "0.6.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.6.1", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"stream": false}, "devDependencies": {"async": "^3.2.0", "c8": "^7.2.0", "errto": "^0.2.1", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "^2.88.2", "semver": "^6.3.0", "unorm": "^1.6.0"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "gitHead": "724829e8fc39525fbeded0f837da53c13de179ae", "_id": "iconv-lite@0.6.1", "_nodeVersion": "12.18.1", "_npmVersion": "6.14.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "dcff79a060333879dd83ebc3eb6a217f5f0facc5", "size": 188633, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.1.tgz", "integrity": "sha512-Gjcihg3Bi6PI+5V7JlqWmXyVDyX5UQuwulJcbb3btuSoXIoGUy8zwJpRIOpRSzHz0IVnsT2FkceLlM8mm72d3w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.6.1_1593324557035_0.30138755547387275"}, "_hasShrinkwrap": false, "publish_time": 1593324557204, "_cnpm_publish_time": 1593324557204, "_cnpmcore_publish_time": "2021-12-13T08:15:46.142Z"}, "0.6.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.6.0", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"stream": false}, "devDependencies": {"async": "*", "c8": "*", "errto": "*", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "~2.87.0", "semver": "~6.1.2", "unorm": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "b106faaf15bb1bc66b20bdb81aa687415f54a7d4", "_id": "iconv-lite@0.6.0", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "66a93b80df0bd05d2a43a7426296b7f91073f125", "size": 188188, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.0.tgz", "integrity": "sha512-43ZpGYZ9QtuutX5l6WC1DSO8ane9N+Ct5qPLF2OV7vM9abM69gnAbVkh66ibaZd3aOGkoP1ZmringlKhLBkw2Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.6.0_1591606862922_0.8978731391649568"}, "_hasShrinkwrap": false, "publish_time": 1591606863203, "_cnpm_publish_time": 1591606863203, "_cnpmcore_publish_time": "2021-12-13T08:15:46.502Z"}, "0.5.2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.5.2", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "c8": "*", "semver": "6.1.2", "iconv": "2"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "5148f43abebd8dabe53710d6056558ba66e089f9", "_id": "iconv-lite@0.5.2", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "af6d628dccfb463b7364d97f715e4b74b8c8c2b8", "size": 189276, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.5.2.tgz", "integrity": "sha512-kERHXvpSaB4aU3eANwidg79K8FlrN77m8G9V+0vOR3HYaRifrlwMEpT7ZBJqLSEIHnEgJTHcWK82wwLwwKwtag=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.5.2_1591604998671_0.552415139324663"}, "_hasShrinkwrap": false, "publish_time": 1591604998934, "_cnpm_publish_time": 1591604998934, "_cnpmcore_publish_time": "2021-12-13T08:15:46.875Z"}, "0.5.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.5.1", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "c60e647d0d825ad3815d0865e871fabb68a531df", "_id": "iconv-lite@0.5.1", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b2425d3c7b18f7219f2ca663d103bddb91718d64", "size": 189065, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.5.1.tgz", "integrity": "sha512-ONHr16SQvKZNSqjQT9gy5z24Jw+uqfO02/ngBSBoqChZ+W8qXX7GPRa1RoUnzGADw8K63R1BXUMzarCVQBpY8Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.5.1_1579333912541_0.3489234031838713"}, "_hasShrinkwrap": false, "publish_time": 1579333912746, "_cnpm_publish_time": 1579333912746, "_cnpmcore_publish_time": "2021-12-13T08:15:47.293Z"}, "0.5.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.5.0", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "2b4125d11a733a40e45a755648389b2512a97a62", "_id": "iconv-lite@0.5.0", "_nodeVersion": "12.1.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "59cdde0a2a297cc2aeb0c6445a195ee89f127550", "size": 187393, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.5.0.tgz", "integrity": "sha512-NnEhI9hIEKHOzJ4f697DMz9IQEXr/MMJ5w64vN2/4Ai+wRnvV7SBrL0KLoRlwaKVghOc7LQ5YkPLuX146b6Ydw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.5.0_1561588188650_0.5881942713503567"}, "_hasShrinkwrap": false, "publish_time": 1561588188857, "_cnpm_publish_time": 1561588188857, "_cnpmcore_publish_time": "2021-12-13T08:15:47.745Z"}, "0.4.24": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.24", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.87.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "efbbb0937ca8dda1c14e0b69958b9d6f20771f7a", "_id": "iconv-lite@0.4.24", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2022b4b25fbddc21d2f524974a474aafe733908b", "size": 185241, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.24_1534969392040_0.7324868237182729"}, "_hasShrinkwrap": false, "publish_time": 1534969392162, "_cnpm_publish_time": 1534969392162, "_cnpmcore_publish_time": "2021-12-13T08:15:48.121Z"}, "0.4.23": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.23", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.81.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "d37d558b3caf5a158bf70accb788c36286fffca5", "_id": "iconv-lite@0.4.23", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "297871f63be507adcfbfca715d0cd0eed84e9a63", "size": 185457, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.23.tgz", "integrity": "sha512-neyTUVFtahjf0mB3dZT77u+8O0QB89jFdnBkd5P1JgYPbPaia3gXXOVL2fq8VyU2gMMD7SaN7QukTB/pmXYvDA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.23_1525731744169_0.30693622174256374"}, "_hasShrinkwrap": false, "publish_time": 1525731744294, "_cnpm_publish_time": 1525731744294, "_cnpmcore_publish_time": "2021-12-13T08:15:48.548Z"}, "0.4.22": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.22", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.81.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "gitHead": "6b9091873b12929a605c819a547ce73a916ccd01", "_id": "iconv-lite@0.4.22", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c6b16b9d05bc6c307dc9303a820412995d2eea95", "size": 185359, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.22.tgz", "integrity": "sha512-1AinFBeDTnsvVEP+V1QBlHpM1UZZl7gWB6fcz7B1Ho+LI1dUh2sSrxoCfVt2PinRHzXAziSniEV3P7JbTDHcXA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.22_1525563451449_0.750337618259016"}, "_hasShrinkwrap": false, "publish_time": 1525563451575, "_cnpm_publish_time": 1525563451575, "_cnpmcore_publish_time": "2021-12-13T08:15:49.007Z"}, "0.4.21": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.21", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.81.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": "^2.1.0"}, "gitHead": "c679ab26ad95a804ff95671d7258a505ccba36c2", "_id": "iconv-lite@0.4.21", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c47f8733d02171189ebc4a400f3218d348094798", "size": 185267, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.21.tgz", "integrity": "sha512-En5V9za5mBt2oUA03WGD3TwDv0MKAruqsuxstbMUZaj9W9k/m1CV/9py3l0L5kw9Bln8fdHQmzHSYtvpvTLpKw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.21_1523075472870_0.21793181179943022"}, "_hasShrinkwrap": false, "publish_time": 1523075473009, "_cnpm_publish_time": 1523075473009, "_cnpmcore_publish_time": "2021-12-13T08:15:49.393Z"}, "0.4.20": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.20", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "^3.1.0", "request": "~2.81.0", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "dependencies": {"safer-buffer": "^2.1.0"}, "gitHead": "9a6ad952f47639d16c1e7273d07a5660ab0634e1", "_id": "iconv-lite@0.4.20", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c1f7a1dbd98de51f275776575ebfa67433d01d22", "size": 185519, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.20.tgz", "integrity": "sha512-YyvWZ7Konl8yQCyGdFub5XmVqQonxkFjDoExIY22RA0NI0pskdU6plSyaUnVyEL+RsOcz+LhPDclXsc02indDQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite_0.4.20_1523073755298_0.9205402977665564"}, "_hasShrinkwrap": false, "publish_time": 1523073755444, "_cnpm_publish_time": 1523073755444, "_cnpmcore_publish_time": "2021-12-13T08:15:49.900Z"}, "0.4.19": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.19", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "gitHead": "5255c1b3c81a0f276619cce3151a1923cba90431", "_id": "iconv-lite@0.4.19", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f7468f60135f5e5dad3399c0a81be9a1603a082b", "size": 186571, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.19.tgz", "integrity": "sha512-oTZqweIP51xaGPI4uPa56/Pri/480R+mo7SeU+YETByQNhDG55ycFyNLIgta9vXhILrxXDmF7ZGhqZIcuN0gJQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite-0.4.19.tgz_1505015801484_0.10463660513050854"}, "directories": {}, "publish_time": 1505015801914, "_hasShrinkwrap": false, "_cnpm_publish_time": 1505015801914, "_cnpmcore_publish_time": "2021-12-13T08:15:50.602Z"}, "0.4.18": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.18", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "gitHead": "637fbc0172247da3b4bf57685dd945b786ca2bee", "_id": "iconv-lite@0.4.18", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "23d8656b16aae6742ac29732ea8f0336a4789cf2", "size": 186295, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.18.tgz", "integrity": "sha512-sr1ZQph3UwHTR0XftSbK85OvBbxe/abLGzEnPENCQwmHf7sck8Oyu4ob3LgBxWWxRoM+QszeUyl7jbqapu2TqA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/iconv-lite-0.4.18.tgz_1497367212038_0.6705294267740101"}, "directories": {}, "publish_time": 1497367212238, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497367212238, "_cnpmcore_publish_time": "2021-12-13T08:15:51.033Z"}, "0.4.17": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.17", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "gitHead": "64d1e3d7403bbb5414966de83d325419e7ea14e2", "_id": "iconv-lite@0.4.17", "_shasum": "4fdaa3b38acbc2c031b045d0edcdfe1ecab18c8d", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4fdaa3b38acbc2c031b045d0edcdfe1ecab18c8d", "size": 186253, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.17.tgz", "integrity": "sha512-vAmILHWeClQb9Qryg5j1EW5L3cuj2cqWGVL2ireWbRrUPtx7WVXHo4DsbFCN1luHXLGFJ34vt2aryk/TeYEV8Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/iconv-lite-0.4.17.tgz_1493615411939_0.8651245310902596"}, "directories": {}, "publish_time": 1493615412243, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493615412243, "_cnpmcore_publish_time": "2021-12-13T08:15:51.525Z"}, "0.4.16": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.16", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "semver": "*", "iconv": "*"}, "gitHead": "bf0acd95103de6fd624d10b65abaf6e91753c4c8", "_id": "iconv-lite@0.4.16", "_shasum": "65de3beeb39e2960d67f049f1634ffcbcde9014b", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "65de3beeb39e2960d67f049f1634ffcbcde9014b", "size": 186208, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.16.tgz", "integrity": "sha512-1OQ/A9QOJsAFxBvoN6Sz6I7aOghM6vVRcO6JuQ0O2YBOSTAGA2kBGtB11ejON7c6dRA4cvYhRftqpqf/LlAroA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/iconv-lite-0.4.16.tgz_1492901339391_0.7763116010464728"}, "directories": {}, "publish_time": 1492901339698, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492901339698, "_cnpmcore_publish_time": "2021-12-13T08:15:52.087Z"}, "0.4.15": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.15", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "*"}, "gitHead": "c3bcedcd6a5025c25e39ed1782347acaed1d290f", "_id": "iconv-lite@0.4.15", "_shasum": "fe265a218ac6a57cfe854927e9d04c19825eddeb", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "7.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fe265a218ac6a57cfe854927e9d04c19825eddeb", "size": 195402, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.15.tgz", "integrity": "sha512-RGR+c9Lm+tLsvU57FTJJtdbv2hQw42Yl2n26tVIBaYmZzLN+EGfroUugN/z9nJf9kOXd49hBmpoGr4FEm+A4pw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/iconv-lite-0.4.15.tgz_1479754977280_0.752664492232725"}, "directories": {}, "publish_time": 1479754979113, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479754979113, "_cnpmcore_publish_time": "2021-12-13T08:15:52.585Z"}, "0.4.14": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.14", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "*"}, "gitHead": "65beacd34d084bbc72ecc260f1ae4470a051cc51", "_id": "iconv-lite@0.4.14", "_shasum": "0c4b78106835ecce149ffc7f1b588a9f23bf28e3", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0c4b78106835ecce149ffc7f1b588a9f23bf28e3", "size": 185916, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.14.tgz", "integrity": "sha512-ytSDQ5GzXGclH/4/lAjJ6o7hxQQdXZ8CvnglNXDBbHsts5lBz/cmVbKihWlbgaXZWGm3GzSWosKw6r1uMbiKng=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/iconv-lite-0.4.14.tgz_1479707686517_0.8387471821624786"}, "directories": {}, "publish_time": 1479707688478, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479707688478, "_cnpmcore_publish_time": "2021-12-13T08:15:53.051Z"}, "0.4.13": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.13", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "f5ec51b1e7dd1477a3570824960641eebdc5fbc6", "_id": "iconv-lite@0.4.13", "_shasum": "1f88aba4ab0b1508e8312acc39345f36e992e2f2", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1f88aba4ab0b1508e8312acc39345f36e992e2f2", "size": 185209, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.13.tgz", "integrity": "sha512-QwVuTNQv7tXC5mMWFX5N5wGjmybjNBBD8P3BReTkPmipoxTUFgWM2gXNvldHQr6T14DH0Dh6qBVg98iJt7u4mQ=="}, "directories": {}, "publish_time": 1443759055543, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443759055543, "_cnpmcore_publish_time": "2021-12-13T08:15:53.587Z"}, "0.4.12": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.12", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "5f5f71492e287c9c7231009103ddf9e8884df62d", "_id": "iconv-lite@0.4.12", "_shasum": "ef4bb2cb28f406d3c05fc89feea4504624b5ac87", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ef4bb2cb28f406d3c05fc89feea4504624b5ac87", "size": 185175, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.12.tgz", "integrity": "sha512-qZa6DPRJZE6Z9GyWPaxRV1t0MzlFP+CXt1ZrdcdLoORmIX+YMkPE7mTc3rgV20Of0gTHYmK5Yaaqbm6pRnNUcg=="}, "directories": {}, "publish_time": 1443302270923, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443302270923, "_cnpmcore_publish_time": "2021-12-13T08:15:54.169Z"}, "0.4.11": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.11", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "e285b7c31eb0406cf5a8e3e09bc16fbd2786360f", "_id": "iconv-lite@0.4.11", "_shasum": "2ecb42fd294744922209a2e7c404dac8793d8ade", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2ecb42fd294744922209a2e7c404dac8793d8ade", "size": 183995, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.11.tgz", "integrity": "sha512-8UmnaYeP5puk18SkBrYULVTiq7REcimhx+ykJVJBiaz89DQmVQAfS29ZhHah86la90/t0xy4vRk86/2cCwNodA=="}, "directories": {}, "publish_time": 1435958989475, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435958989475, "_cnpmcore_publish_time": "2021-12-13T08:15:54.794Z"}, "0.4.10": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.10", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "e8af2b49035abbe4fabe826925764bc20f8587c6", "_id": "iconv-lite@0.4.10", "_shasum": "4f1a2562efd36d41c54d45c59999b590951796de", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4f1a2562efd36d41c54d45c59999b590951796de", "size": 183594, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.10.tgz", "integrity": "sha512-nqju2pcdvq9tvfZ4nRL2vzQqJoOzIxwH+IF08DzVULhcHiFgYQJ8XWGl7GV7O/c/TlDF6I0PYZxx4sM8QhAp8g=="}, "directories": {}, "publish_time": 1432707514272, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432707514272, "_cnpmcore_publish_time": "2021-12-13T08:15:55.446Z"}, "0.4.9": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.9", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1"}, "gitHead": "a9a123e22074cb4d8e0392ae037b0e348df1559a", "_id": "iconv-lite@0.4.9", "_shasum": "4d8b3c7f596c558ce95b4bd4562c874010a7df3e", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4d8b3c7f596c558ce95b4bd4562c874010a7df3e", "size": 183476, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.9.tgz", "integrity": "sha512-PflPJxTFsGUHI6zE2NbyBzyDhRw/+9HeGQp6nKnY4kT+lxR1IQdZ/TlId0afhPwqhEUs9VTJkzTM7bBBT+TxqQ=="}, "directories": {}, "publish_time": 1432471649122, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432471649122, "_cnpmcore_publish_time": "2021-12-13T08:15:56.072Z"}, "0.4.8": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.8", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1.4"}, "gitHead": "3dc7d0cb0e223b29634ecb7bff46910c8107ab3d", "_id": "iconv-lite@0.4.8", "_shasum": "c6019a7595f2cefca702eab694a010bcd9298d20", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c6019a7595f2cefca702eab694a010bcd9298d20", "size": 182872, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.8.tgz", "integrity": "sha512-D90rbOiZuEJGtmIBK9wcRpW//ZKLD8bTPOAx5oEsu+O+HhSOstX/HCZFBvNkuyDuiNHunb81cfsqaYzZxcUMYA=="}, "directories": {}, "publish_time": 1429033619824, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429033619824, "_cnpmcore_publish_time": "2021-12-13T08:15:56.735Z"}, "0.4.7": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.7", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "2.1.4"}, "gitHead": "820336d20d947159895c80daab55bac4261ff53c", "_id": "iconv-lite@0.4.7", "_shasum": "89d32fec821bf8597f44609b4bc09bed5c209a23", "_from": ".", "_npmVersion": "2.1.6", "_nodeVersion": "0.10.33", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "89d32fec821bf8597f44609b4bc09bed5c209a23", "size": 182679, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.7.tgz", "integrity": "sha512-Js5rATPL/P+t5hmjXLuSI5wF4YqFYuIZkAwL5HZ/FUFwWUy5jQCx4YAGSUOUeHbCSixY1yyk3LUTfgpQZhb/CQ=="}, "directories": {}, "publish_time": 1423208489590, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423208489590, "_cnpmcore_publish_time": "2021-12-13T08:15:57.461Z"}, "0.4.6": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.6", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "~2.1.4"}, "gitHead": "920dad2303f7c64d92e771ffd379688e0a0d6fc1", "_id": "iconv-lite@0.4.6", "_shasum": "e39c682610a791f3eedc27382ff49e263f91fa09", "_from": ".", "_npmVersion": "2.1.6", "_nodeVersion": "0.10.33", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e39c682610a791f3eedc27382ff49e263f91fa09", "size": 182546, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.6.tgz", "integrity": "sha512-aop+f6/kQEnzTfi6Rv8KLQMt1bY8/0bFTS5oSiYnwXlCH6eI/gJzL+rGjTwsA7soKCcq/hkeDySFC7PwBELX2w=="}, "directories": {}, "publish_time": 1421074010603, "_hasShrinkwrap": false, "_cnpm_publish_time": 1421074010603, "_cnpmcore_publish_time": "2021-12-13T08:15:58.097Z"}, "0.4.5": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.5", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nleush"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "2.47", "unorm": "*", "errto": "*", "async": "*", "istanbul": "*", "iconv": "~2.1.4"}, "gitHead": "0654719791aa2c159bb3820f095b5da8702d091b", "_id": "iconv-lite@0.4.5", "_shasum": "9c574b70c30d615859f2064d2be4335ad6b1a8d6", "_from": ".", "_npmVersion": "2.1.6", "_nodeVersion": "0.10.33", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9c574b70c30d615859f2064d2be4335ad6b1a8d6", "size": 182435, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.5.tgz", "integrity": "sha512-LQ4GtDkFagYaac8u4rE73zWu7h0OUUmR0qVBOgzLyFSoJhoDG2xV9PZJWWyVVcYha/9/RZzQHUinFMbNKiOoAA=="}, "directories": {}, "publish_time": 1416479939501, "_hasShrinkwrap": false, "_cnpm_publish_time": 1416479939501, "_cnpmcore_publish_time": "2021-12-13T08:15:58.792Z"}, "0.4.4": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.4", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "gitHead": "9f0b0a7631d167322f47c2202aa3e5b090945131", "_id": "iconv-lite@0.4.4", "_shasum": "e95f2e41db0735fc21652f7827a5ee32e63c83a8", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e95f2e41db0735fc21652f7827a5ee32e63c83a8", "size": 182574, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.4.tgz", "integrity": "sha512-BnjNp13aZpK4WBGbmjaNHN2MCp3P850n8zd/JLinQJ8Lsnq2Br4o2467C2waMsY5kr7Z41SL1gEqh8Vbfzg15A=="}, "directories": {}, "publish_time": 1405572820014, "_hasShrinkwrap": false, "_cnpm_publish_time": 1405572820014, "_cnpmcore_publish_time": "2021-12-13T08:15:59.477Z"}, "0.4.3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.3", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "gitHead": "42f4a837055c1277a73468ccaedb5f5eac31425d", "_id": "iconv-lite@0.4.3", "_shasum": "9e7887793b769cc695eb22d2546a4fd2d79b7a1e", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9e7887793b769cc695eb22d2546a4fd2d79b7a1e", "size": 179874, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.3.tgz", "integrity": "sha512-fBUZHWVujxJd0hOJLaN4Zj4h1LeOn+qi5qyts4HFFa0jaOo/0E6DO1UsJReZV0qwiIzeaqm/1LhYBbvvGjQkNg=="}, "directories": {}, "publish_time": 1402808256477, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402808256477, "_cnpmcore_publish_time": "2021-12-13T08:16:00.141Z"}, "0.4.2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.2", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Mithgol"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "gitHead": "832e328447c1ea879c329e359a200b258942c2bc", "_id": "iconv-lite@0.4.2", "_shasum": "af57e14c2ccd8b27e945d7b4de071accd59f00bb", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "af57e14c2ccd8b27e945d7b4de071accd59f00bb", "size": 177547, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.2.tgz", "integrity": "sha512-4mCaxNpPcUG33G5e0Ct3huMFSgA5a5WLPQoFQOgvHLftn5YK1tSGCug3+iKAE0U9MkJ1HdONl20evwH7IOKPEA=="}, "directories": {}, "publish_time": 1402617305316, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402617305316, "_cnpmcore_publish_time": "2021-12-13T08:16:00.773Z"}, "0.4.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.1", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "gitHead": "c61800cc51fb7496754f810c14b66b8e543d22b6", "_id": "iconv-lite@0.4.1", "_shasum": "c9d4621aafb06b67979b79676ca99ac4c0378b1a", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c9d4621aafb06b67979b79676ca99ac4c0378b1a", "size": 177519, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.1.tgz", "integrity": "sha512-y6jD7lLVA0FKxT8h1EOMPmpYOwh0Q4gMFVaO49RgKB0RSAL/TVrS0iIt79A8hj9Kw5wJWUAcjsulK+Ij3jOl9w=="}, "directories": {}, "publish_time": 1402549124366, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402549124366, "_cnpmcore_publish_time": "2021-12-13T08:16:01.405Z"}, "0.4.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.0", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "./lib/index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "browser": {"./extend-node": false, "./streams": false}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "~2.1.4"}, "_id": "iconv-lite@0.4.0", "_shasum": "cc77430093c1298e35aba9e8fa38d09582fcdcb7", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "cc77430093c1298e35aba9e8fa38d09582fcdcb7", "size": 177438, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.0.tgz", "integrity": "sha512-d7/ePgG4u3EjP5Q1bchwAmXzVi31co1iSzExDL+o2NtdGiLKZLO4LIPtWhfcMSfD37hyKpAEPF5PZFrZXhygCA=="}, "directories": {}, "publish_time": 1402455977844, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402455977844, "_cnpmcore_publish_time": "2021-12-13T08:16:02.143Z"}, "0.4.0-pre3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.0-pre3", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "2.x"}, "_id": "iconv-lite@0.4.0-pre3", "dist": {"shasum": "bfbdb354cecc2f54d58addda32d62817da843f6a", "size": 144302, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.0-pre3.tgz", "integrity": "sha512-CX6LnbnrxoaDAYlHsst4GCEjVDrWJIdRFur3Y1ZRofTF/JfLBwoeR1jZRr9J6wOcFmDiRuGbKosV6O09swEgHw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398680846683, "_hasShrinkwrap": false, "_cnpm_publish_time": 1398680846683, "_cnpmcore_publish_time": "2021-12-13T08:16:02.946Z"}, "0.4.0-pre2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.0-pre2", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "2.x"}, "_id": "iconv-lite@0.4.0-pre2", "dist": {"shasum": "be0ec485136c00984825c8de63b0e22f7e23193e", "size": 139423, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.0-pre2.tgz", "integrity": "sha512-ZF8EEyTjgWKBVNJBxSl1TYUzwCEFBIHEsUy3CrxrtnfOI08YYrwGTmsOnMAO11sk0sk8gxZbHTDjVmWBhGd4QA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398479746872, "_hasShrinkwrap": false, "_cnpm_publish_time": 1398479746872, "_cnpmcore_publish_time": "2021-12-13T08:16:03.695Z"}, "0.4.0-pre": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.4.0-pre", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha --reporter spec --grep ."}, "devDependencies": {"mocha": "*", "request": "*", "unorm": "*", "errto": "*", "async": "*", "iconv": "2.x"}, "_id": "iconv-lite@0.4.0-pre", "dist": {"shasum": "8ef26bada5b13a311ab299fd53a8685686826c8a", "size": 137632, "noattachment": false, "tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.0-pre.tgz", "integrity": "sha512-BPM0zFUgzniQTe1+js0AEs5HwVtP19WNGoxiH9ugywSln/ibKyiBkIv3G5td2AzkMRyIEOEelxg68Ub9yuRyOg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398462656999, "_hasShrinkwrap": false, "_cnpm_publish_time": 1398462656999, "_cnpmcore_publish_time": "2021-12-13T08:16:04.489Z"}, "0.2.11": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.11", "license": "MIT", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}, {"name": "<PERSON>", "url": "https://github.com/mscdex"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "_id": "iconv-lite@0.2.11", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.11.tgz", "shasum": "1ce60a3a57864a292d1321ff4609ca4bb965adc8", "size": 235160, "noattachment": false, "integrity": "sha512-KhmFWgaQZY83Cbhi+ADInoUQ8Etn6BG5fikM9syeOjQltvR45h7cRKJ/9uvQEuD61I3Uju77yYce0/LhKVClQw=="}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373851397021, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373851397021, "_cnpmcore_publish_time": "2021-12-13T08:16:05.438Z"}, "0.2.10": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.10", "license": "MIT", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "_id": "iconv-lite@0.2.10", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.10.tgz", "shasum": "8839fa77a9e4325a51ca0f8bae6b0cbd490f5a92", "size": 235074, "noattachment": false, "integrity": "sha512-bo8JrNVjsJKQO4YnoqEhkxy6fTDdsLUrgnwb2aeFpGI2BUaHxzyjrSBK834fmaIxw0ypXQfu5boQDyaaQ4Q4KQ=="}, "_from": ".", "_npmVersion": "1.2.23", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1369625638336, "_hasShrinkwrap": false, "_cnpm_publish_time": 1369625638336, "_cnpmcore_publish_time": "2021-12-13T08:16:06.417Z"}, "0.2.9": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.9", "license": "MIT", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}, {"name": "Lee Treveil", "url": "https://github.com/leetreveil"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "_id": "iconv-lite@0.2.9", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.9.tgz", "shasum": "5788ae876660ddb663ab68a45fef14922e16998e", "size": 235038, "noattachment": false, "integrity": "sha512-Zz+xikNZ3yF/WeJwI6QpLo4ZXa57dK7W5Gz++TnC6gZ0V7L1uliIL5uHWVHbtHFx8ryuu3T2eucTBwa7tSt1oA=="}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1369005411894, "_hasShrinkwrap": false, "_cnpm_publish_time": 1369005411894, "_cnpmcore_publish_time": "2021-12-13T08:16:07.296Z"}, "0.2.8": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.8", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}, {"name": "Niggler", "url": "https://github.com/Niggler"}, {"name": "wychi", "url": "https://github.com/wychi"}, {"name": "<PERSON>", "url": "https://github.com/david50407"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/czchen"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "readmeFilename": "README.md", "_id": "iconv-lite@0.2.8", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.8.tgz", "shasum": "8b9ebdc6c0751742951d67786f6fd5c09a9e0109", "size": 234962, "noattachment": false, "integrity": "sha512-CfFrPNxtVpJVW3m5wRRuDV6ctKQVHhFdOcj2QJZt4igkmHDO6+LjLsl0cxyfPAgx/wyeI0RXHguq6QmwsyXSog=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1366144870178, "_hasShrinkwrap": false, "_cnpm_publish_time": 1366144870178, "_cnpmcore_publish_time": "2021-12-13T08:16:08.177Z"}, "0.2.7": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.7", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "main": "index.js", "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "readmeFilename": "README.md", "_id": "iconv-lite@0.2.7", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.7.tgz", "shasum": "45be2390d27af4b7613aac4ee4d957e3f4cbdb54", "size": 136533, "noattachment": false, "integrity": "sha512-U/I1kR5J3PDZf9g3WwDoG4MTj8KfLDYqWv9EtrYDyKw6McL2J87bMqyjYFuZHJ9KE2gI4iAGinleQM66GiT1Kw=="}, "_npmVersion": "1.1.66", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1354689633383, "_hasShrinkwrap": false, "_cnpm_publish_time": 1354689633383, "_cnpmcore_publish_time": "2021-12-13T08:16:09.062Z"}, "0.2.6": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.6", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ">=1.1"}, "readmeFilename": "README.md", "_id": "iconv-lite@0.2.6", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.6.tgz", "shasum": "f4dc95055077fc0580bf829c3e75c20d55824a3e", "size": 136593, "noattachment": false, "integrity": "sha512-XjI/4/S7A2e7F7gib3vEN8NTaYOw3pnubBBPCj4gcTGmhVryf7OKdr/QC1nBXt5j3Ljtm0ncZwC9/z9T7P10qg=="}, "_npmVersion": "1.1.65", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1353397183417, "_hasShrinkwrap": false, "_cnpm_publish_time": 1353397183417, "_cnpmcore_publish_time": "2021-12-13T08:16:09.913Z"}, "0.2.5": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.5", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": "1.1"}, "_id": "iconv-lite@0.2.5", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.5.tgz", "shasum": "e9f2155037f4afd000c095b1d5ad8831c4c5eacc", "size": 136594, "noattachment": false, "integrity": "sha512-02jUjc9BvUMOu138CgxRU6QuDNfKQF7X86meFiXKhOJDjHLwzh2M4PUrTefVvDNxvSC5KmaVLHyNWildubo1ag=="}, "_npmVersion": "1.1.49", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1345949750688, "_hasShrinkwrap": false, "_cnpm_publish_time": 1345949750688, "_cnpmcore_publish_time": "2021-12-13T08:16:10.865Z"}, "0.2.4": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.4", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": "1.1"}, "_id": "iconv-lite@0.2.4", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.4.tgz", "shasum": "03659514658e27e4d1691a63e7aa01f1dca7f296", "size": 144056, "noattachment": false, "integrity": "sha512-j14xF/NLYVcTIGXB30YKjncUGUd9c/xLpJ7xCF3WLq1jew0dPPF7MIBym58wPpw8eATiRlthdG7Ba9kpgQotQA=="}, "_npmVersion": "1.1.49", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1345842648490, "_hasShrinkwrap": false, "_cnpm_publish_time": 1345842648490, "_cnpmcore_publish_time": "2021-12-13T08:16:11.896Z"}, "0.2.3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.3", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": "1.1"}, "_id": "iconv-lite@0.2.3", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.3.tgz", "shasum": "f6b14037951e3a334543932e9829dfd004168755", "size": 135680, "noattachment": false, "integrity": "sha512-y8spz7Utx8I82Oo1jw3PFKz/5Pyq8u2HjddLLFq+gesvNOGM/HuH0Ypnrx30211TW8SE9lXW9hqcoyuVHKqu2g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1342188426630, "_hasShrinkwrap": false, "_cnpm_publish_time": 1342188426630, "_cnpmcore_publish_time": "2021-12-13T08:16:12.793Z"}, "0.2.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.1", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "vows --spec"}, "devDependencies": {"vows": "", "iconv": ""}, "_id": "iconv-lite@0.2.1", "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.1.tgz", "shasum": "011b31b8eeffc57b4cb65521b2a0858ce1ed8bfb", "size": 135665, "noattachment": false, "integrity": "sha512-vw547MtbJ5l7L4mNP8XGuqfCOHAWabdb7OIwHSQfTbNGTnr8fgRJ81EdptxJnQtRmUG0Rx2SmWtraZWao6SXMQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1340981359424, "_hasShrinkwrap": false, "_cnpm_publish_time": 1340981359424, "_cnpmcore_publish_time": "2021-12-13T08:16:13.728Z"}, "0.2.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.2.0", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.2.0", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.18", "_nodeVersion": "v0.6.17", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.2.0.tgz", "shasum": "235d7ca31fbc40ddf1855bed5eb020f45251247c", "size": 133284, "noattachment": false, "integrity": "sha512-g2RqXkWUt7W9zb98QhkNw2H8ntTb617SPimCeWWVbSvzFHlKgzev49r/6siExIGtnsWK2leZO4hugDnd7rQwrA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1336412581137, "_hasShrinkwrap": false, "_cnpm_publish_time": 1336412581137, "_cnpmcore_publish_time": "2021-12-13T08:16:14.763Z"}, "0.1.4": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.4", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "https://github.com/ashtuchkin/iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.4", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.18", "_nodeVersion": "v0.6.17", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.1.4.tgz", "shasum": "d9d9f7f2902ae56c68c800c0d42822cc681e20af", "size": 127933, "noattachment": false, "integrity": "sha512-DiB2UCAockub1RHVwDqwCuMk7nYmXsIaA3QDDPDDWhZRNyMX/FvAqK7p+nbYqSVBlLe5B5S4ksm7aqcBFWU6wg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1336303441572, "_hasShrinkwrap": false, "_cnpm_publish_time": 1336303441572, "_cnpmcore_publish_time": "2021-12-13T08:16:15.834Z"}, "0.1.3": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.3", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}, {"name": "<PERSON>", "url": "https://github.com/pekim"}], "homepage": "http://github.com/ashtuchkin/node-iconv/", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/node-iconv.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.3", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.18", "_nodeVersion": "v0.6.17", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.1.3.tgz", "shasum": "e5b1742382fb90f4900ec0076ac0b868d249615d", "size": 127941, "noattachment": false, "integrity": "sha512-h6P+/VhcJdXcTuJdC2rmb7F/aJB7C+AywEHDmdgvRmssNrDGljGop4U8ajAKahcT2/LADS7GAk0U0bTMUYcsiA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1336298814542, "_hasShrinkwrap": false, "_cnpm_publish_time": 1336298814542, "_cnpmcore_publish_time": "2021-12-13T08:16:16.961Z"}, "0.1.2": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.2", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adamansky"}, {"name": "<PERSON>", "url": "https://github.com/stagas"}], "homepage": "http://github.com/ashtuchkin/node-iconv/", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/node-iconv.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.1.2.tgz", "shasum": "ae828cd32708a17258d6a558c653bde646e84d0a", "size": 126373, "noattachment": false, "integrity": "sha512-XrdCpkcJ7iVnp9Yr6gSwAbycoDD81cFlZK6a1m4d3ZZSKlp/MPZJPaYxdIo9n9TYH4erH/XsgCeUTQskyWpW3w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1331318944510, "_hasShrinkwrap": false, "_cnpm_publish_time": 1331318944510, "_cnpmcore_publish_time": "2021-12-13T08:16:18.005Z"}, "0.1.1": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.1", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "jen<PERSON><PERSON>", "url": "https://github.com/jenkinv"}], "homepage": "http://github.com/ashtuchkin/node-iconv/", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/node-iconv.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": "", "iconv": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.1", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.1.1.tgz", "shasum": "7844849646a553d2b65711d4e8e3188c2d0a5106", "size": 125209, "noattachment": false, "integrity": "sha512-N0TT/dthJLII+xrvRbzWVvDv4GKei8gR7lzEGYTWDGA87moCa7g7+VwRByCbaDjf3YOEUtyLYTbA2fQflhyXCQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322052922201, "_hasShrinkwrap": false, "_cnpm_publish_time": 1322052922201, "_cnpmcore_publish_time": "2021-12-13T08:16:19.032Z"}, "0.1.0": {"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.1.0", "keywords": ["iconv", "convert", "charset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "homepage": "http://github.com/ashtuchkin/node-iconv/", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/node-iconv.git"}, "engines": {"node": ">=0.4.0"}, "devDependencies": {"vows": ""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "iconv-lite@0.1.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.1.0.tgz", "shasum": "bb686e9e87899523e69c313d01ffae9d7850e1eb", "size": 6440, "noattachment": false, "integrity": "sha512-IXs/YqMio5O2gCB5gAc9uSuBqIhXtYuEQ07B2GT+/hCbo+l6j+TGeyAQQNGvxaVd2A779bw4VV86kRTtZsVxFw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1320861065090, "_hasShrinkwrap": false, "_cnpm_publish_time": 1320861065090, "_cnpmcore_publish_time": "2021-12-13T08:16:20.203Z"}}, "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "homepage": "https://github.com/ashtuchkin/iconv-lite", "keywords": ["iconv", "convert", "charset", "icu"], "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "_source_registry_name": "default"}