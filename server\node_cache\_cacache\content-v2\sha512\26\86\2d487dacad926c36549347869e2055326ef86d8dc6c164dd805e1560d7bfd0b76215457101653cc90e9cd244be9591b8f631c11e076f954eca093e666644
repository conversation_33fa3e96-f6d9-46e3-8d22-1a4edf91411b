{"_attachments": {}, "_id": "fs.realpath", "_rev": "682-61f1450ffbcaa28a759458c3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "description": "Use node's fs.realpath, but fall back to the JS implementation if the native one fails", "dist-tags": {"latest": "1.0.0"}, "license": "ISC", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "fs.realpath", "readme": "# fs.realpath\n\nA backwards-compatible fs.realpath for Node v6 and above\n\nIn Node v6, the JavaScript implementation of fs.realpath was replaced\nwith a faster (but less resilient) native implementation.  That raises\nnew and platform-specific errors and cannot handle long or excessively\nsymlink-looping paths.\n\nThis module handles those cases by detecting the new errors and\nfalling back to the JavaScript implementation.  On versions of Node\nprior to v6, it has no effect.\n\n## USAGE\n\n```js\nvar rp = require('fs.realpath')\n\n// async version\nrp.realpath(someLongAndLoopingPath, function (er, real) {\n  // the ELOOP was handled, but it was a bit slower\n})\n\n// sync version\nvar real = rp.realpathSync(someLongAndLoopingPath)\n\n// monkeypatch at your own risk!\n// This replaces the fs.realpath/fs.realpathSync builtins\nrp.monkeypatch()\n\n// un-do the monkeypatching\nrp.unmonkeypatch()\n```\n", "time": {"created": "2022-01-26T12:56:47.938Z", "modified": "2023-07-28T01:54:22.373Z", "1.0.0": "2016-06-15T18:39:05.978Z", "0.0.0": "2016-06-15T18:38:49.471Z"}, "versions": {"1.0.0": {"name": "fs.realpath", "version": "1.0.0", "description": "Use node's fs.realpath, but fall back to the JS implementation if the native one fails", "main": "index.js", "dependencies": {}, "devDependencies": {}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/fs.realpath.git"}, "keywords": ["realpath", "fs", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["old.js", "index.js"], "gitHead": "03e7c884431fe185dfebbc9b771aeca339c1807a", "bugs": {"url": "https://github.com/isaacs/fs.realpath/issues"}, "homepage": "https://github.com/isaacs/fs.realpath#readme", "_id": "fs.realpath@1.0.0", "_shasum": "1504ad2523158caa40db4a2787cb01411994ea4f", "_from": ".", "_npmVersion": "3.9.1", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "1504ad2523158caa40db4a2787cb01411994ea4f", "size": 4434, "noattachment": false, "tarball": "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/fs.realpath-1.0.0.tgz_1466015941059_0.3332864767871797"}, "directories": {}, "publish_time": 1466015945978, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466015945978, "_cnpmcore_publish_time": "2021-12-13T06:42:29.919Z"}, "0.0.0": {"name": "fs.realpath", "version": "0.0.0", "description": "Use node's fs.realpath, but fall back to the JS implementation if the native one fails", "main": "index.js", "dependencies": {}, "devDependencies": {}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/fs.realpath.git"}, "keywords": ["realpath", "fs", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["old.js", "index.js"], "gitHead": "f29be5918ad14bf7dec90f5d33948a792fe5b7db", "bugs": {"url": "https://github.com/isaacs/fs.realpath/issues"}, "homepage": "https://github.com/isaacs/fs.realpath#readme", "_id": "fs.realpath@0.0.0", "_shasum": "ee31b09e1272a8c6e58714abcacded5bd7cf7c6d", "_from": ".", "_npmVersion": "3.9.1", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ee31b09e1272a8c6e58714abcacded5bd7cf7c6d", "size": 4432, "noattachment": false, "tarball": "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-0.0.0.tgz", "integrity": "sha512-mQ7DpI54Eq1EtmaGFFZJ38KOMYOjihJV9Cwt+G5ji451VEU2ElaSsX3KmrQb4scwfBKRvGg9uW0A/tPBJY5Q6g=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/fs.realpath-0.0.0.tgz_1466015928899_0.7730964215006679"}, "directories": {}, "publish_time": 1466015929471, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466015929471, "_cnpmcore_publish_time": "2021-12-13T06:42:30.242Z"}}, "bugs": {"url": "https://github.com/isaacs/fs.realpath/issues"}, "homepage": "https://github.com/isaacs/fs.realpath#readme", "keywords": ["realpath", "fs", "polyfill"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/fs.realpath.git"}, "_source_registry_name": "default"}