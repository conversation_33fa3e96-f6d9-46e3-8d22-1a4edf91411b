{"_attachments": {}, "_id": "chownr", "_rev": "2626-61f149d0b77ea98a7490aee6", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "description": "like `chown -R`", "dist-tags": {"latest": "3.0.0"}, "license": "BlueOak-1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "chownr", "readme": "Like `chown -R`.\n\nTakes the same arguments as `fs.chown()`\n", "time": {"created": "2022-01-26T13:17:04.667Z", "modified": "2024-04-06T21:32:36.752Z", "2.0.0": "2020-02-12T02:33:07.865Z", "1.1.4": "2020-02-12T02:30:54.444Z", "1.1.3": "2019-09-25T05:49:10.172Z", "1.1.2": "2019-07-03T21:32:51.511Z", "1.1.1": "2018-09-16T03:14:08.990Z", "1.1.0": "2018-09-16T00:23:19.205Z", "1.0.1": "2015-08-09T22:24:36.640Z", "1.0.0": "2015-08-09T22:22:45.361Z", "0.0.2": "2015-05-20T07:04:02.130Z", "0.0.1": "2012-06-04T04:01:28.039Z", "3.0.0": "2024-04-06T21:32:30.155Z"}, "versions": {"2.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "^2.7.1", "tap": "^14.10.6"}, "tap": {"check-coverage": true}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "license": "ISC", "engines": {"node": ">=10"}, "gitHead": "f9f9d866bebb2f9ff8efc93b78305f9d999c6f17", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@2.0.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.7", "dist": {"shasum": "15bfbe53d2eab4cf70f18a8cd68ebe5b3cb1dece", "size": 2243, "noattachment": false, "tarball": "https://registry.npmmirror.com/chownr/-/chownr-2.0.0.tgz", "integrity": "sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_2.0.0_1581474787748_0.7116861792550564"}, "_hasShrinkwrap": false, "publish_time": 1581474787865, "_cnpm_publish_time": 1581474787865, "_cnpmcore_publish_time": "2021-12-13T16:05:12.793Z"}, "1.1.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.4", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "^2.7.1", "tap": "^14.10.6"}, "tap": {"check-coverage": true}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "license": "ISC", "gitHead": "814f6422241dcc1bbb324fa29cd6d9f6dc2141ae", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.4", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.7", "dist": {"shasum": "6fc9d7b42d32a583596337666e7d08084da2cc6b", "size": 2226, "noattachment": false, "tarball": "https://registry.npmmirror.com/chownr/-/chownr-1.1.4.tgz", "integrity": "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.4_1581474654243_0.7221239722665642"}, "_hasShrinkwrap": false, "publish_time": 1581474654444, "_cnpm_publish_time": 1581474654444, "_cnpmcore_publish_time": "2021-12-13T16:05:13.073Z"}, "1.1.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.3", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "license": "ISC", "gitHead": "deaa058afe2a04c6528965a218ece1226a9ee2ae", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.3", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "42d837d5239688d55f303003a508230fa6727142", "size": 2093, "noattachment": false, "tarball": "https://registry.npmmirror.com/chownr/-/chownr-1.1.3.tgz", "integrity": "sha512-i70fVHhmV3DtTl6nqvZOnIjbY0Pe4kAUjwHj8z0zAdgBtYrJyYwLKCCuRBQ5ppkyL0AkN7HKRnETdmdp1zqNXw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.3_1569390549998_0.778423331031457"}, "_hasShrinkwrap": false, "publish_time": 1569390550172, "_cnpm_publish_time": 1569390550172, "_cnpmcore_publish_time": "2021-12-13T16:05:13.423Z"}, "1.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.2", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "license": "ISC", "gitHead": "cf3b27b5723045b01f7bbe68a874423d978683f7", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.2", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.1-next.0", "dist": {"shasum": "a18f1e0b269c8a6a5d3c86eb298beb14c3dd7bf6", "size": 2094, "noattachment": false, "tarball": "https://registry.npmmirror.com/chownr/-/chownr-1.1.2.tgz", "integrity": "sha512-GkfeAQh+QNy3wquu9oIZr6SS5x7wGdSgNQvD10X3r+AZr1Oys22HW8kAmDMvNg2+Dm0TeGaEuO8gFwdBXxwO8A=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.2_1562189571355_0.8148922644594261"}, "_hasShrinkwrap": false, "publish_time": 1562189571511, "_cnpm_publish_time": 1562189571511, "_cnpmcore_publish_time": "2021-12-13T16:05:13.780Z"}, "1.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.1", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "license": "ISC", "gitHead": "7a5c3d57c3691eebb9c66fa00fa6003d02ef9440", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "54726b8b8fff4df053c42187e801fb4412df1494", "size": 1794, "noattachment": false, "tarball": "https://registry.npmmirror.com/chownr/-/chownr-1.1.1.tgz", "integrity": "sha512-j38EvO5+LHX84jlo6h4UzmOwi0UgW61WRyPtJz4qaadK5eY3BTS5TY/S1Stc3Uk2lIM6TPevAlULiEJwie860g=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.1_1537067648822_0.8733677031057796"}, "_hasShrinkwrap": false, "publish_time": 1537067648990, "_cnpm_publish_time": 1537067648990, "_cnpmcore_publish_time": "2021-12-13T16:05:14.111Z"}, "1.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "license": "ISC", "gitHead": "76c21fad5b9e518b3dba16a1bd53bd6f5f2c2e5c", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "17405cadd8706bc41f017b564bb2de460381e7d1", "size": 1782, "noattachment": false, "tarball": "https://registry.npmmirror.com/chownr/-/chownr-1.1.0.tgz", "integrity": "sha512-BGowLy8nGWXPbtRR/8imBkaAFdArC2ES+q4HvCy8RruTpKU3MEgxVpT+AxlkAax0ykKqnoNnHAZh+Ryu0eFCIw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.0_1537057399049_0.3682169782824076"}, "_hasShrinkwrap": false, "publish_time": 1537057399205, "_cnpm_publish_time": 1537057399205, "_cnpmcore_publish_time": "2021-12-13T16:05:14.442Z"}, "1.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "files": ["chownr.js"], "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^1.2.0"}, "scripts": {"test": "tap test/*.js"}, "license": "ISC", "gitHead": "c6c43844e80d7c7045e737a72b9fbb1ba0579a26", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.0.1", "_shasum": "e2a75042a9551908bebd25b8523d5f9769d79181", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e2a75042a9551908bebd25b8523d5f9769d79181", "size": 1482, "noattachment": false, "tarball": "https://registry.npmmirror.com/chownr/-/chownr-1.0.1.tgz", "integrity": "sha512-cKnqUJAC8G6cuN1DiRRTifu+s1BlAQNtalzGphFEV0pl0p46dsxJD4l1AOlyKJeLZOFzo3c34R7F3djxaCu8Kw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1439159076640, "_hasShrinkwrap": false, "_cnpm_publish_time": 1439159076640, "_cnpmcore_publish_time": "2021-12-13T16:05:14.798Z"}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^1.2.0"}, "scripts": {"test": "tap test/*.js"}, "license": "ISC", "gitHead": "4f72743895927db8108dbf3d5462c667db22ebce", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.0.0", "_shasum": "02855833d20515cf2681c717d686bb8c1f3ea91a", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "02855833d20515cf2681c717d686bb8c1f3ea91a", "size": 3174, "noattachment": false, "tarball": "https://registry.npmmirror.com/chownr/-/chownr-1.0.0.tgz", "integrity": "sha512-AUNcIMR3gp65x7Qv4ZMbdNURtEd30PN0MF78j5EleWeTveh7/DyHNkL+NisebEupdPrA3zxITYQnrDo6KEcQJQ=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1439158965361, "_hasShrinkwrap": false, "_cnpm_publish_time": 1439158965361, "_cnpmcore_publish_time": "2021-12-13T16:05:15.244Z"}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"tap": "0.2", "mkdirp": "0.3", "rimraf": ""}, "scripts": {"test": "tap test/*.js"}, "license": "ISC", "gitHead": "3cafeb70b2c343e893f710750406b3909ec537cb", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@0.0.2", "_shasum": "2f9aebf746f90808ce00607b72ba73b41604c485", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "2f9aebf746f90808ce00607b72ba73b41604c485", "size": 2724, "noattachment": false, "tarball": "https://registry.npmmirror.com/chownr/-/chownr-0.0.2.tgz", "integrity": "sha512-4sa<PERSON><PERSON><PERSON>+/DavveVRsu49tUbYvLn5cS75w8gLQr14jXlFxSNbuoY7G6gPjcVfgdQ+c4BW02b0hXV5nOXYFD7Fmpw=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432105442130, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432105442130, "_cnpmcore_publish_time": "2021-12-13T16:05:15.680Z"}, "0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"tap": "0.2", "mkdirp": "0.3", "rimraf": ""}, "scripts": {"test": "tap test/*.js"}, "license": "BSD", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "chownr@0.0.1", "dependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.23", "_nodeVersion": "v0.7.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/chownr/-/chownr-0.0.1.tgz", "shasum": "51d18189d9092d5f8afd623f3288bfd1c6bf1a62", "size": 2425, "noattachment": false, "integrity": "sha512-goAG4rAgFydYcD0ixqyMaONTiGLscYfXk9IT7gOYyR18Mu3ZSIffnFivWTT+HPuFeby9RPTopOR8JxbYroiScA=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1338782488039, "_hasShrinkwrap": false, "_cnpm_publish_time": 1338782488039, "_cnpmcore_publish_time": "2021-12-13T16:05:16.083Z"}, "3.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "devDependencies": {"@types/node": "^20.12.5", "mkdirp": "^3.0.1", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.12"}, "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "_id": "chownr@3.0.0", "gitHead": "8b9800ac5fe4da0b58bffc9c66dd618f0721472d", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==", "shasum": "9855e64ecd240a9cc4267ce8a4aa5d24a1da15e4", "tarball": "https://registry.npmmirror.com/chownr/-/chownr-3.0.0.tgz", "fileCount": 13, "unpackedSize": 22048, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICj2JI5k5zfx2BHcQesjOytbMnelQ7RAI5CZ0velowoUAiBotyrWkSaXkpGsKM9m5yZEK7xq9/AaTT9jAzEV7ri8rQ=="}], "size": 4405}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_3.0.0_1712439149991_0.9315611454069008"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-06T21:32:30.155Z", "publish_time": 1712439150155, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "_source_registry_name": "default"}