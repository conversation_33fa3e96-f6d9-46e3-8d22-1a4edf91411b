{"_attachments": {}, "_id": "fetch-blob", "_rev": "490-61f144a6830fd08f52a1f215", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "description": "Blob & File implementation in Node.js, originally from node-fetch.", "dist-tags": {"latest": "4.0.0", "RC": "3.0.0-rc.0"}, "license": "MIT", "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "fetch-blob", "readme": "# fetch-blob\n\n[![npm version][npm-image]][npm-url]\n[![build status][ci-image]][ci-url]\n[![coverage status][codecov-image]][codecov-url]\n[![install size][install-size-image]][install-size-url]\n\nA Blob implementation in Node.js, originally from [node-fetch](https://github.com/node-fetch/node-fetch).\n\nUse the built-in [`Blob`](https://nodejs.org/docs/latest-v18.x/api/buffer.html#class-blob) in Node.js 18 and later.\n\n## Installation\n\n```sh\nnpm install fetch-blob\n```\n\n<details>\n  <summary>Upgrading from 2x to 3x</summary>\n\n  Updating from 2 to 3 should be a breeze since there is not many changes to the blob specification.\n  The major cause of a major release is coding standards.\n    - internal WeakMaps was replaced with private fields\n    - internal Buffer.from was replaced with TextEncoder/Decoder\n    - internal buffers was replaced with Uint8Arrays\n    - CommonJS was replaced with ESM\n    - The node stream returned by calling `blob.stream()` was replaced with whatwg streams\n    - (Read \"Differences from other blobs\" for more info.)\n</details>\n\n<details>\n  <summary>Differences from other Blobs</summary>\n\n  - Unlike NodeJS `buffer.Blob` (Added in: v15.7.0) and browser native Blob this polyfilled version can't be sent via PostMessage\n  - This blob version is more arbitrary, it can be constructed with blob parts that isn't a instance of itself\n  it has to look and behave as a blob to be accepted as a blob part.\n    - The benefit of this is that you can create other types of blobs that don't contain any internal data that has to be read in other ways, such as the `BlobDataItem` created in `from.js` that wraps a file path into a blob-like item and read lazily (nodejs plans to [implement this][fs-blobs] as well)\n  - The `blob.stream()` is the most noticeable differences. It returns a WHATWG stream now. to keep it as a node stream you would have to do:\n\n  ```js\n    import {Readable} from 'stream'\n    const stream = Readable.from(blob.stream())\n  ```\n</details>\n\n## Usage\n\n```js\n// Ways to import\nimport { Blob } from 'fetch-blob'\nimport { File } from 'fetch-blob/file.js'\n\nconst { Blob } = await import('fetch-blob')\n\n\n// Ways to read the blob:\nconst blob = new Blob(['hello, world'])\n\nawait blob.text()\nawait blob.arrayBuffer()\nfor await (let chunk of  blob.stream()) { ... }\nblob.stream().getReader().read()\nblob.stream().getReader({mode: 'byob'}).read(view)\n```\n\n### Blob part backed up by filesystem\n\n`fetch-blob/from.js` comes packed with tools to convert any filepath into either a Blob or a File\nIt will not read the content into memory. It will only stat the file for last modified date and file size.\n\n```js\n// The default export is sync and use fs.stat to retrieve size & last modified as a blob\nimport {File, Blob, blobFrom, blobFromSync, fileFrom, fileFromSync} from 'fetch-blob/from.js'\n\nconst fsFile = fileFromSync('./2-GiB-file.bin', 'application/octet-stream')\nconst fsBlob = await blobFrom('./2-GiB-file.mp4')\n\n// Not a 4 GiB memory snapshot, just holds references\n// points to where data is located on the disk\nconst blob = new Blob([fsFile, fsBlob, 'memory', new Uint8Array(10)])\nconsole.log(blob.size) // ~4 GiB\n```\n\n`blobFrom|blobFromSync|fileFrom|fileFromSync(path, [mimetype])`\n\n### Creating a temporary file on the disk\n(requires [FinalizationRegistry] - node v14.6)\n\nWhen using both `createTemporaryBlob` and `createTemporaryFile`\nthen you will write data to the temporary folder in their respective OS.\nThe arguments can be anything that [fsPromises.writeFile] supports. NodeJS\nv14.17.0+ also supports writing (async)Iterable streams and passing in a\nAbortSignal, so both NodeJS stream and whatwg streams are supported. When the\nfile have been written it will return a Blob/File handle with a references to\nthis temporary location on the disk. When you no longer have a references to\nthis Blob/File anymore and it have been GC then it will automatically be deleted.\n\nThis files are also unlinked upon exiting the process.\n```js\nimport { createTemporaryBlob, createTemporaryFile } from 'fetch-blob/from.js'\n\nconst req = new Request('https://httpbin.org/image/png')\nconst res = await fetch(req)\nconst type = res.headers.get('content-type')\nconst signal = req.signal\nlet blob = await createTemporaryBlob(res.body, { type, signal })\n// const file = createTemporaryBlob(res.body, 'img.png', { type, signal })\nblob = undefined // loosing references will delete the file from disk\n```\n\n- `createTemporaryBlob(data, { type, signal })`\n- `createTemporaryFile(data, FileName, { type, signal, lastModified })`\n\n### Creating Blobs backed up by other async sources\nOur Blob & File class are more generic then any other polyfills in the way that it can accept any blob look-a-like item\nAn example of this is that our blob implementation can be constructed with parts coming from [BlobDataItem](https://github.com/node-fetch/fetch-blob/blob/8ef89adad40d255a3bbd55cf38b88597c1cd5480/from.js#L32) (aka a filepath) or from [buffer.Blob](https://nodejs.org/api/buffer.html#buffer_new_buffer_blob_sources_options), It dose not have to implement all the methods - just enough that it can be read/understood by our Blob implementation. The minium requirements is that it has `Symbol.toStringTag`, `size`, `slice()`, `stream()` methods (the stream method\ncan be as simple as being a sync or async iterator that yields Uint8Arrays. If you then wrap it in our Blob or File `new Blob([blobDataItem])` then you get all of the other methods that should be implemented in a blob or file (aka: text(), arrayBuffer() and type and a ReadableStream)\n\nAn example of this could be to create a file or blob like item coming from a remote HTTP request. Or from a DataBase\n\nSee the [MDN documentation](https://developer.mozilla.org/en-US/docs/Web/API/Blob) and [tests](https://github.com/node-fetch/fetch-blob/blob/master/test.js) for more details of how to use the Blob.\n\n[npm-image]: https://flat.badgen.net/npm/v/fetch-blob\n[npm-url]: https://www.npmjs.com/package/fetch-blob\n[ci-image]: https://github.com/node-fetch/fetch-blob/workflows/CI/badge.svg\n[ci-url]: https://github.com/node-fetch/fetch-blob/actions\n[codecov-image]: https://flat.badgen.net/codecov/c/github/node-fetch/fetch-blob/master\n[codecov-url]: https://codecov.io/gh/node-fetch/fetch-blob\n[install-size-image]: https://flat.badgen.net/packagephobia/install/fetch-blob\n[install-size-url]: https://packagephobia.now.sh/result?p=fetch-blob\n[fs-blobs]: https://github.com/nodejs/node/issues/37340\n[fsPromises.writeFile]: https://nodejs.org/dist/latest-v18.x/docs/api/fs.html#fspromiseswritefilefile-data-options\n[FinalizationRegistry]: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/FinalizationRegistry\n", "time": {"created": "2022-01-26T12:55:02.591Z", "modified": "2024-05-28T17:04:58.650Z", "3.1.3": "2021-10-30T12:19:56.214Z", "3.1.2": "2021-07-16T10:00:12.061Z", "3.1.1": "2021-07-15T22:55:09.550Z", "3.1.0": "2021-07-15T22:47:44.332Z", "3.0.1": "2021-06-19T09:52:38.068Z", "3.0.0": "2021-05-29T09:19:33.770Z", "3.0.0-rc.0": "2021-05-07T09:49:49.451Z", "2.1.2": "2021-04-20T15:52:09.049Z", "2.1.1": "2020-07-28T16:13:29.891Z", "2.1.0": "2020-07-28T15:54:24.787Z", "2.0.1": "2020-06-11T14:31:44.611Z", "2.0.0": "2020-06-10T19:11:32.303Z", "1.0.7": "2020-06-10T14:14:35.034Z", "1.0.6": "2020-05-21T09:06:17.568Z", "1.0.5": "2019-11-23T16:03:56.264Z", "1.0.4": "2019-09-23T10:40:58.371Z", "1.0.3": "2019-05-16T09:00:23.548Z", "1.0.2": "2019-05-16T08:49:35.235Z", "3.1.4": "2022-01-20T13:08:19.018Z", "3.1.5": "2022-03-15T22:49:56.424Z", "3.2.0": "2022-07-06T17:45:31.179Z", "4.0.0": "2023-05-18T11:47:25.810Z"}, "versions": {"3.1.3": {"name": "fetch-blob", "version": "3.1.3", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test": "ava test.js", "report": "c8 --reporter json --reporter text ava test.js", "coverage": "c8 --reporter json --reporter text ava test.js && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"web-streams-polyfill": "^3.0.3"}, "types": "./index.d.ts", "gitHead": "8ab587d34080de94140b54f07168451e7d0b655e", "_id": "fetch-blob@3.1.3", "_nodeVersion": "17.0.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ax1Y5I9w+9+JiM+wdHkhBoxew+zG4AJ2SvAD1v1szpddUIiPERVGBxrMcB2ZqW0Y3PP8bOWYv2zqQq1Jp2kqUQ==", "shasum": "a7dca4855e39d3e3c5a1da62d4ee335c37d26012", "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.1.3.tgz", "fileCount": 10, "unpackedSize": 21679, "size": 7682, "noattachment": false}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.3_1635596396065_0.5964602541736606"}, "_hasShrinkwrap": false, "publish_time": 1635596396214, "_cnpm_publish_time": 1635596396214, "_cnpmcore_publish_time": "2021-12-13T13:55:09.792Z"}, "3.1.2": {"name": "fetch-blob", "version": "3.1.2", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-number-properties": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"web-streams-polyfill": "^3.0.3"}, "gitHead": "0b028432d0fd00841128a94817c2a81a9558463e", "_id": "fetch-blob@3.1.2", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "6bc438675f3851ecea51758ac91f6a1cd1bacabd", "size": 8420, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.1.2.tgz", "integrity": "sha512-hunJbvy/6OLjCD0uuhLdp0mMPzP/yd2ssd1t2FCJsaA7wkWhpbp9xfuNVpv7Ll4jFhzp6T4LAupSiV9uOeg0VQ=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.2_1626429611917_0.45141052844757557"}, "_hasShrinkwrap": false, "publish_time": 1626429612061, "_cnpm_publish_time": 1626429612061, "_cnpmcore_publish_time": "2021-12-13T13:55:10.136Z"}, "3.1.1": {"name": "fetch-blob", "version": "3.1.1", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-number-properties": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"web-streams-polyfill": "^3.0.3"}, "gitHead": "52f296c2839b9fb09cd460d453534f312914d301", "_id": "fetch-blob@3.1.1", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "30ad5a50ba27400005b016cee5a970d1adbf1617", "size": 8631, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.1.1.tgz", "integrity": "sha512-Ny2ibV7UwiZnG4W5f7OS8epXMTcicQ5XcLWcmrhSiK1y6ZTk+DXIrBuIQBWs9sZafwuZqkF9wdGrEg20aeyCZQ=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.1_1626389709381_0.5683454289261796"}, "_hasShrinkwrap": false, "publish_time": 1626389709550, "_cnpm_publish_time": 1626389709550, "_cnpmcore_publish_time": "2021-12-13T13:55:10.520Z"}, "3.1.0": {"name": "fetch-blob", "version": "3.1.0", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-number-properties": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"web-streams-polyfill": "^3.0.3"}, "gitHead": "52f296c2839b9fb09cd460d453534f312914d301", "_id": "fetch-blob@3.1.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "f8ceca3638734047e79641d55b47f8d9bef61177", "size": 8369, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.1.0.tgz", "integrity": "sha512-+M3dfFQfT5urXVV9rtNMHGenkVF1xejn3gEM+9IMpXJ70YYQTTmvwwZP5bAzUlA2Sk8IwPO1e0D7QLu3cdvF7g=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.0_1626389264128_0.8726396966471455"}, "_hasShrinkwrap": false, "publish_time": 1626389264332, "_cnpm_publish_time": 1626389264332, "_cnpmcore_publish_time": "2021-12-13T13:55:10.853Z"}, "3.0.1": {"name": "fetch-blob", "version": "3.0.1", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-number-properties": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "gitHead": "5ba554efd2df7eb674d860f96b7cbd35781d00b1", "_id": "fetch-blob@3.0.1", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "c67d9b2d9e6d7202209b234ba14e13de451c5db9", "size": 8868, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.0.1.tgz", "integrity": "sha512-uq8YWG2SVATaJtkP6YukvWHw++EpNsoT8vuBxFk57vcUXVQaS9jkdH6RKWVrbgO0YUNy3dPrXO+7HnSgTcJipg=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.0.1_1624096357881_0.07193428206370212"}, "_hasShrinkwrap": false, "publish_time": 1624096358068, "_cnpm_publish_time": 1624096358068, "_cnpmcore_publish_time": "2021-12-13T13:55:11.241Z"}, "3.0.0": {"name": "fetch-blob", "version": "3.0.0", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": ">=14.0.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "gitHead": "b5fa0bd79fa2ffbd2442ef985325c1de15d9f929", "_id": "fetch-blob@3.0.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "0ab11d20f63aae7615e8ce314e8bf8bac70480db", "size": 8852, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.0.0.tgz", "integrity": "sha512-zkWSie/M6VryeUOZD9Feeel5PtaB2P7mXTC5fEoRpngVi96e69jP/PA1LagYsBlC2G4tleqWqxoRwC2ttMEy3Q=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.0.0_1622279973572_0.40039732622470137"}, "_hasShrinkwrap": false, "publish_time": 1622279973770, "_cnpm_publish_time": 1622279973770, "_cnpmcore_publish_time": "2021-12-13T13:55:11.603Z"}, "3.0.0-rc.0": {"name": "fetch-blob", "version": "3.0.0-rc.0", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": ">=14.0.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/import-index": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "peerDependenciesMeta": {"domexception": {"optional": true}}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.1", "codecov": "^3.8.1", "domexception": "^2.0.1", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.2.4", "xo": "^0.38.2"}, "gitHead": "a0c0abe1a180b24c2aa2ca48e8e8480a9f6dc20d", "_id": "fetch-blob@3.0.0-rc.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "a7207d9f02d5ee84fb59f1544a3714e07f8ec0d2", "size": 7672, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.0.0-rc.0.tgz", "integrity": "sha512-x5MJQ9puLDQBc65PNOEozYOaNJIVqTJpJwoUs8x3SXWr9cJzrY2nsiUoTpv3Ow3TXIqauwCbF3std23oltPEsw=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.0.0-rc.0_1620380989338_0.6724846358760896"}, "_hasShrinkwrap": false, "publish_time": 1620380989451, "_cnpm_publish_time": 1620380989451, "_cnpmcore_publish_time": "2021-12-13T13:55:11.985Z"}, "2.1.2": {"name": "fetch-blob", "version": "2.1.2", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0 || >=12.3.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/import-index": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "peerDependenciesMeta": {"domexception": {"optional": true}}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.1", "codecov": "^3.8.1", "domexception": "^2.0.1", "get-stream": "^6.0.1", "node-fetch": "^2.6.1", "typescript": "^4.2.4", "xo": "^0.38.2"}, "gitHead": "5584886f0f34402bbb53811ef7b4c63ddcddad2d", "_id": "fetch-blob@2.1.2", "_nodeVersion": "15.8.0", "_npmVersion": "7.5.1", "dist": {"shasum": "a7805db1361bd44c1ef62bb57fb5fe8ea173ef3c", "size": 5322, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-2.1.2.tgz", "integrity": "sha512-YKqtUDwqLyfyMnmbw8XD6Q8j9i/HggKtPEI+pZ1+8bvheBu78biSmNaXWusx1TauGqtUUGx/cBb1mKdq2rLYow=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.1.2_1618933928915_0.5847854579968081"}, "_hasShrinkwrap": false, "publish_time": 1618933929049, "_cnpm_publish_time": 1618933929049, "_cnpmcore_publish_time": "2021-12-13T13:55:12.367Z"}, "2.1.1": {"name": "fetch-blob", "version": "2.1.1", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0 || >=12.3.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "c8": "^7.2.0", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "peerDependencies": {"domexception": "^2.0.1"}, "gitHead": "365330b9e155ffe8720eedab84883c531ae45355", "_id": "fetch-blob@2.1.1", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "a54ab0d5ed7ccdb0691db77b6674308b23fb2237", "size": 5070, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-2.1.1.tgz", "integrity": "sha512-Uf+gxPCe1hTOFXwkxYyckn8iUSk6CFXGy5VENZKifovUTZC9eUODWSBhOBS7zICGrAetKzdwLMr85KhIcePMAQ=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.1.1_1595952809760_0.5215171375097736"}, "_hasShrinkwrap": false, "publish_time": 1595952809891, "_cnpm_publish_time": 1595952809891, "_cnpmcore_publish_time": "2021-12-13T13:55:12.753Z"}, "2.1.0": {"name": "fetch-blob", "version": "2.1.0", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0 || >=12.3.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "c8": "^7.2.0", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "peerDependencies": {"domexception": "^2.0.1"}, "gitHead": "5b2bab5365a5540ae109211d5d7c352485e73a14", "_id": "fetch-blob@2.1.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "29d6f91e0d8ac7ae691a3923f34832365506c1d8", "size": 5044, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-2.1.0.tgz", "integrity": "sha512-K/q9efIbuv/x6CqQkaEaCOFYhRLjZcnocxyFVAb7+IoqQVVpWILIGCbRbrHp9sTGesl4LJl14vPIZRbRW2z6KA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.1.0_1595951664615_0.18159729006565173"}, "_hasShrinkwrap": false, "publish_time": 1595951664787, "_cnpm_publish_time": 1595951664787, "_cnpmcore_publish_time": "2021-12-13T13:55:13.186Z"}, "2.0.1": {"name": "fetch-blob", "version": "2.0.1", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0 || >=12.3.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "c8": "^7.2.0", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "dependencies": {}, "gitHead": "99388a79bbdf4a2f774da891c54d4d1a0c1c0afc", "_id": "fetch-blob@2.0.1", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "64b583cd2bd21685b17706818b388af08cb40b9b", "size": 4325, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-2.0.1.tgz", "integrity": "sha512-1jFpa68M4EzObtFa7XOKZoN1unsaeJ6hGSbxaWaVO+TkHmVvnyzRu1ktZAFbUvTZ9NC/qMKGKJ79dK4MzuSBiw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.0.1_1591885904508_0.1855559850446531"}, "_hasShrinkwrap": false, "publish_time": 1591885904611, "_cnpm_publish_time": 1591885904611, "_cnpmcore_publish_time": "2021-12-13T13:55:13.618Z"}, "2.0.0": {"name": "fetch-blob", "version": "2.0.0", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^15.1.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "dependencies": {}, "gitHead": "38639ec4aa7c9330effb0b6a2e3addc32b68c96c", "_id": "fetch-blob@2.0.0", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "b2e459fe507858b3e568aef984585aef2ba3366b", "size": 4296, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-2.0.0.tgz", "integrity": "sha512-I1mmL/BDg8sDZOVmRkdpuDFNipxHMf0Djy4OaDvp6iP230FI/eJlt4p2ocFJZXte6M2DuMtS7Y9mk0Qe4Am9mw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.0.0_1591816292196_0.1531083274071261"}, "_hasShrinkwrap": false, "publish_time": 1591816292303, "deprecated": "Wrong Node.js engine version was specified in this release. Please upgrade to version 2.0.1 or higher.", "_cnpm_publish_time": 1591816292303, "_cnpmcore_publish_time": "2021-12-13T13:55:14.026Z"}, "1.0.7": {"name": "fetch-blob", "version": "1.0.7", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^15.1.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "dependencies": {}, "gitHead": "62fc8eab22ec15277b63f61806e602bf0490724b", "_id": "fetch-blob@1.0.7", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "c2ceffcde92dac1613c26289cd006019859ec2aa", "size": 4177, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-1.0.7.tgz", "integrity": "sha512-mJyUUpl2bOpjV5FdUNbVTMNYBLESg3q4mvkgNcNlxQqI+l9K0rT7OQLBxrn1w8U+/iof5hWcHBbeYwBm4jcBOA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.7_1591798474835_0.882075840191062"}, "_hasShrinkwrap": false, "publish_time": 1591798475034, "deprecated": "This release contains breaking changes, so it has been renamed to 2.0.0. Changelog: https://git.io/JfSDh", "_cnpm_publish_time": 1591798475034, "_cnpmcore_publish_time": "2021-12-13T13:55:14.433Z"}, "1.0.6": {"name": "fetch-blob", "version": "1.0.6", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": ">=6"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^15.0.1", "xo": "^0.30.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "gitHead": "bec5a3dceb340ccbf3130f0000807395460fcc17", "_id": "fetch-blob@1.0.6", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "d1e7f02df9dfb9729307ece7e255209051e2de6a", "size": 3109, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-1.0.6.tgz", "integrity": "sha512-XTotUY7hVtqdbHE0Ilm/u/nnXRv1T8nepxhMHzB885O0EkVvI05UlZq7rHQSd6hVDCNAGx4HTjbJO60Onjfckw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.6_1590051977443_0.006245879081175687"}, "_hasShrinkwrap": false, "publish_time": 1590051977568, "_cnpm_publish_time": 1590051977568, "_cnpmcore_publish_time": "2021-12-13T13:55:14.863Z"}, "1.0.5": {"name": "fetch-blob", "version": "1.0.5", "description": "A Blob implementation on node.js, originally from node-fetch", "main": "blob.js", "scripts": {"test": "ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/fetch-blob/issues"}, "homepage": "https://github.com/bitinn/fetch-blob#readme", "devDependencies": {"ava": "^1.4.1", "codecov": "^3.6.1", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^14.1.1"}, "gitHead": "b09fdac53198023370ae6cd441d35b8201b8b74c", "_id": "fetch-blob@1.0.5", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "5cc86a8740236c38d8073b153c8b51be5f3dc11e", "size": 3462, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-1.0.5.tgz", "integrity": "sha512-B<PERSON>ggzO037jmCrZmtgntzCD2ymEaWgw9OMJsfX7FOS1jXGqKW9FEhETJN8QK4KxzIJknRl3RQdyzz34of+NNTMQ=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.5_1574525036160_0.5380429482971754"}, "_hasShrinkwrap": false, "publish_time": 1574525036264, "_cnpm_publish_time": 1574525036264, "_cnpmcore_publish_time": "2021-12-13T13:55:15.303Z"}, "1.0.4": {"name": "fetch-blob", "version": "1.0.4", "description": "A Blob implementation on node.js, originally from node-fetch", "main": "blob.js", "scripts": {"test": "ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/fetch-blob/issues"}, "homepage": "https://github.com/bitinn/fetch-blob#readme", "devDependencies": {"ava": "^1.4.1", "codecov": "^3.5.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^14.1.1"}, "gitHead": "b197ea6198d349ca822f7385212aa728fe0695f5", "_id": "fetch-blob@1.0.4", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "23242b3f873d71b7165c0cc1b3d2dbb89584d293", "size": 3432, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-1.0.4.tgz", "integrity": "sha512-4Nb07BSJ2OR/fWJ1nI9QqLJI4vSs3jtei1UP4oPWffeKpc+8aWPsWzBH6cpJXqQTqSqYinl7oro1f2hrIpnK3Q=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.4_1569235258189_0.34324797986165256"}, "_hasShrinkwrap": false, "publish_time": 1569235258371, "_cnpm_publish_time": 1569235258371, "_cnpmcore_publish_time": "2021-12-13T13:55:15.769Z"}, "1.0.3": {"name": "fetch-blob", "version": "1.0.3", "description": "A Blob implementation on node.js, originally from node-fetch", "main": "blob.js", "scripts": {"test": "ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/fetch-blob/issues"}, "homepage": "https://github.com/bitinn/fetch-blob#readme", "devDependencies": {"ava": "^1.4.1", "codecov": "^3.5.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^14.1.1"}, "gitHead": "78ec7f45c0d54e4090423dbe56abd1d455223ba2", "_id": "fetch-blob@1.0.3", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "7d75e3886f1bdb28083e30298b169de575f674f2", "size": 3356, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-1.0.3.tgz", "integrity": "sha512-5MFud4mZ7GMniHz6q0gifn9EcGzFw1AQgia24L4XH8zabWyKLaPzNksWAzeH80jCuP2NEosLtBsTGZRnKhOn/A=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.3_1557997223440_0.1508320811348316"}, "_hasShrinkwrap": false, "publish_time": 1557997223548, "_cnpm_publish_time": 1557997223548, "_cnpmcore_publish_time": "2021-12-13T13:55:16.209Z"}, "1.0.2": {"name": "fetch-blob", "version": "1.0.2", "description": "A Blob implementation on node.js, originally from node-fetch", "main": "index.js", "scripts": {"test": "ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/fetch-blob/issues"}, "homepage": "https://github.com/bitinn/fetch-blob#readme", "devDependencies": {"ava": "^1.4.1", "codecov": "^3.5.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^14.1.1"}, "gitHead": "1acea29a77e145bb4deaeda21d1b2a993398c801", "_id": "fetch-blob@1.0.2", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "f44979e9df2f7d424e1857d2d6584fe6db170e1c", "size": 3331, "noattachment": false, "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-1.0.2.tgz", "integrity": "sha512-yzQVWRNIomfiGsUUueunMpB+LWZA6liJvg/1+Sso9Q0RFw75eNnykwAsS1QozeDl+FCttgjX7LApmqbsimIweA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.2_1557996575048_0.09011285146401549"}, "_hasShrinkwrap": false, "publish_time": 1557996575235, "_cnpm_publish_time": 1557996575235, "_cnpmcore_publish_time": "2021-12-13T13:55:16.659Z"}, "3.1.4": {"name": "fetch-blob", "version": "3.1.4", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "report": "c8 --reporter json --reporter text npm run test", "coverage": "npm run report && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"@types/node": "^17.0.9", "c8": "^7.11.0", "typescript": "^4.5.4"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "types": "./index.d.ts", "gitHead": "dc295883669fbc41d39d502ffbae36f1c12f580a", "_id": "fetch-blob@3.1.4", "_nodeVersion": "17.3.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-Eq5Xv5+VlSrYWEqKrusxY1C3Hm/hjeAsCGVG3ft7pZahlUAChpGZT/Ms1WmSLnEAisEXszjzu/s+ce6HZB2VHA==", "shasum": "e8c6567f80ad7fc22fd302e7dcb72bafde9c1717", "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.1.4.tgz", "fileCount": 10, "unpackedSize": 21755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6V7DCRA9TVsSAnZWagAAHSoP/i5mi90oNuCNbL/ONLpu\n7rAIrHZC8Vx4CazPB4FRyCy/Jg5G3+kgTn8FNmnmXHC20aJSsQWgw/KtRDpk\nDka6t++BBQedZaosXhRCqgVB4Yz/9NMQ6be8c0E8xsmGf3AkEvse+9c1YDtV\nlQpJT7hEpvKITSB2pg/k2AW3wXVqTIAIGOEXkY7jBE8pR+HI9lSt6JHEjqOG\n0ZgChb+48jL9a2R84dtKT5AD7MGdA61jNrk7g5vShRV2wJu9lQEGVNQy6NE0\nYjjQ6814FHi6ejGLwJHSh10qox0KHwqrQkbGM48tigkXbZHosbfhkpaE2vil\nFKWgC43mVYOqQvZL844zju8supUx0z3hbtc4A3L+Aj6+6thhlI3zigXPq00g\nyTvhdNz3GHnDIi7EUKTEW+D6dXRnS+vuLdwGxTV7fGMawq/akJYHkM4LVESd\nyOZozcZ9wBVcp1mueBGKVaMXKZYUUM6F8p6G3K8/U1if4RE91MMsw+I+0l2s\n0FHI35rz8m7kkddbPbvmSS3hY5K4Iq8/oxkzMSLOeIsQxmX8duUe3wbBcc49\ngQ+GE08Gjj4TbPhkkGBC5SZldViWORS0kQPAUdEB/hlXxAHiLSF68mWNducU\nUf6zK5ep/feX7QAkHxwhvfF5PGSo76QcAG82J7ihj1f152x+rXTjW/9M7alb\ne+DT\r\n=H1sO\r\n-----END PGP SIGNATURE-----\r\n", "size": 7690}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.4_1642684098839_0.5539772210551002"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-20T13:15:08.592Z"}, "3.1.5": {"name": "fetch-blob", "version": "3.1.5", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "report": "c8 --reporter json --reporter text npm run test", "coverage": "npm run report && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"@types/node": "^17.0.9", "c8": "^7.11.0", "typescript": "^4.5.4"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "types": "./index.d.ts", "gitHead": "d4632ad83e399f2b259a2a57b3f01be8bcbe7d1d", "_id": "fetch-blob@3.1.5", "_nodeVersion": "17.4.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-N64ZpKqoLejlrwkIAnb9iLSA3Vx/kjgzpcDhygcqJ2KKjky8nCgUQ+dzXtbrLaWZGZNmNfQTsiQ0weZ1svglHg==", "shasum": "0077bf5f3fcdbd9d75a0b5362f77dbb743489863", "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.1.5.tgz", "fileCount": 10, "unpackedSize": 22063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMRgUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8+w/9ErDOi3NPC0SQHjd7hVlN46Ny/i7OPR9BUZgzgSsY90jKlA1u\r\nCqXlYRbxlEWomrUc6imhq7/Nmlyg20DAFwCBAE5LZhzZQkXrH8Zopd2zGHpd\r\nEos3ny3Tf7GOcJvecyYkh+2AqYeGxgwBQG8pM0VXQCtxR3BvqJ95352ZPD7m\r\nPK7gYGtJQU87lL872Kz8A+NQRmSNs93/pXfvc2vh/W8acGEqs1lWH/++smZB\r\nJ7LQCza+rJ/qUoQ9yesgsD70aTbBj9aGjZTpbA/jmnk4EopCBYR93/vzUHMZ\r\nY4Dbq5vTV6E286K8MAvKjSv2pcrN7+pGxoIv8KODRQgvS+oo6tTDUdszi1GR\r\nBPO3fBvZFIB3vvnLrf4ciDs/RTL5B0mOmAqQA4Mdi/blGBuWifhHAYpSt84a\r\nsf4d1VIzalCnCS3NXjApZkLHO8KBoLc8CQj8FXLksTT4zEk6HwaJak5ut3CU\r\nWYIu7XNiWh7X1Lt2Lvvwqi4n0qu1U05Hns1mDxzDxm8n/PH4I+6BYan1VTgz\r\nB0oudN/IkmnAIEBvw9XYl0xr6RVM4of54DMXrJAOATjzn1Frp21mkYPzNhxo\r\ndP9/p/wqEsxJ/rg/W7OQzSWKjtEm/5hs/YyI0w/miWbfMTskmzA3bjHESpKq\r\nH+aJ2ZYAbLMStBOTVe3z1ELBfq2/Z8DAPYU=\r\n=BIAx\r\n-----END PGP SIGNATURE-----\r\n", "size": 7770}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.5_1647384596212_0.9138752697477188"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-15T22:50:00.597Z"}, "3.2.0": {"name": "fetch-blob", "version": "3.2.0", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "report": "c8 --reporter json --reporter text npm run test", "coverage": "npm run report && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"@types/node": "^17.0.9", "c8": "^7.11.0", "typescript": "^4.5.4"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "types": "./index.d.ts", "gitHead": "b8c8176ba48a2118c65c9d646089e7d9fa3cb8c2", "_id": "fetch-blob@3.2.0", "_nodeVersion": "18.3.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "shasum": "f09b8d4bbd45adc6f0c20b7e787e793e309dcce9", "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-3.2.0.tgz", "fileCount": 10, "unpackedSize": 21755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDudNvQVfDmUfiZpLK4mm/ygNwN9RdxPCaScl7kDlYZ8gIgclnWfUyRd+wv36Iu5Id93p+dWAnQC2zJKZbJgrw9/xA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixco7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqghQ/+I2GOv0ckYVLM31o7fHYtNSAh3S8KPRA+aTP1yVxGzfwgHsaZ\r\nXeqq4UKbhA4oVacEYzAkL1YoxUx2Ys9+vRK1lzlbW1eyx6xurAct+XjerqmV\r\naNLtfSd6d6FUwSNtPvf1V3DV66fQbjDloOWjb0VRlUovADMkZ3YcLm+NL0Pk\r\nDhSbuMIEdymuecgmHw1ryAJYAzHi4gpjcAc8fqQ/PMabEIeM9+DMy4Jujh/X\r\nHJWXlo0O6sw6a4f4izTk6G8XDj1T+qR7Bzc7dOf1pezvFn3xTSANpDTt2PKU\r\nnnxGKUWY4wjoXCISZad1uBahFrhI8CM7yK+qS/N/x6EKdJMKM93qJI45IiEz\r\nLzYV+FXMBQYzyYMdtdWshVOCCyr3gew7qi0xGluQpj5WNx31qRs2gG8Mqux5\r\nME9cFJ4lRM6KZokdEazay2NA6kuo+Ps9BnofmPLbb4ZSQNR8z2c/du25YY3w\r\nbAyKenxEgfQwpZ3ZweAqO286OSzpPrKpKKDCEHBkZGUQSWoyPfWvUblF91b2\r\n3/Yx8u7RWREjzVUPzkfNSTGNdB+TrhW4Qk73fcunWcz3L14KuDQ9gyRT8Ykb\r\ndG9EFSsvp0uH8U9EDynxqC3A7Zh5t0fW5P9S3N0zYggc/RADiT3ATcIpKCPA\r\noV44Hl8Cqt3amuKma3aC8bVkXy09du9eoBI=\r\n=tXFl\r\n-----END PGP SIGNATURE-----\r\n", "size": 7689}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.2.0_1657129530976_0.10045095451226915"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-06T18:03:10.848Z"}, "4.0.0": {"name": "fetch-blob", "version": "4.0.0", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "report": "c8 --reporter json --reporter text npm run test", "coverage": "npm run report && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": ">=16.7"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"@types/node": "^16.5.0", "c8": "^7.13.0", "typescript": "^5.0.4"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0"}, "types": "./index.d.ts", "gitHead": "fc7f8e2e5c48a0a1c0fd74179f894df7709112e1", "_id": "fetch-blob@4.0.0", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-nPmnhRmpNMjYWnp9EBMGs6z5lq9RXed5W1vuZcECrsDVQInM8AMQSooVb3X183Aole60adzjWbH9qlRFWzDDTA==", "shasum": "7175579ab41933d047293c8e71dd5a49f9620a52", "tarball": "https://registry.npmmirror.com/fetch-blob/-/fetch-blob-4.0.0.tgz", "fileCount": 9, "unpackedSize": 24185, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDu7VyuolLrMnyTSJoSJF9M5GIh9O9QFBPs3OI2eDqCEAiBrPHm5LvnYlfTBk/ETPmi8V0AArXb2oVjik2fiDloIdw=="}], "size": 8252}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_4.0.0_1684410445646_0.5070895321784545"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T11:47:25.810Z", "publish_time": 1684410445810, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "keywords": ["blob", "file", "node-fetch"], "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "_source_registry_name": "default"}