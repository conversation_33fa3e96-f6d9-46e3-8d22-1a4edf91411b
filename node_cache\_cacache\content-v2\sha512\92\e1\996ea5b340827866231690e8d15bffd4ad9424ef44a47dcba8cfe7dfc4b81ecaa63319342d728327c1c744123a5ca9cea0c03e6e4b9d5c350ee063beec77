{"_attachments": {}, "_id": "jsonwebtoken", "_rev": "2736-61f14a02b677e08f51148117", "author": {"name": "auth0"}, "description": "JSON Web Token implementation (symmetric and asymmetric)", "dist-tags": {"latest": "9.0.2"}, "license": "MIT", "maintainers": [{"name": "charles<PERSON>", "email": "<EMAIL>"}, {"name": "tim<PERSON><PERSON>", "email": "timothy<PERSON><PERSON>+<EMAIL>"}, {"name": "david.renaud.okta", "email": "<EMAIL>"}], "name": "jsonwebtoken", "readme": "# jsonwebtoken\n\n| **Build**                                                                                                                               | **Dependency**                                                                                                         |\n|-----------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------|\n| [![Build Status](https://secure.travis-ci.org/auth0/node-jsonwebtoken.svg?branch=master)](http://travis-ci.org/auth0/node-jsonwebtoken) | [![Dependency Status](https://david-dm.org/auth0/node-jsonwebtoken.svg)](https://david-dm.org/auth0/node-jsonwebtoken) |\n\n\nAn implementation of [JSON Web Tokens](https://tools.ietf.org/html/rfc7519).\n\nThis was developed against `draft-ietf-oauth-json-web-token-08`. It makes use of [node-jws](https://github.com/brianloveswords/node-jws)\n\n# Install\n\n```bash\n$ npm install jsonwebtoken\n```\n\n# Migration notes\n\n* [From v8 to v9](https://github.com/auth0/node-jsonwebtoken/wiki/Migration-Notes:-v8-to-v9)\n* [From v7 to v8](https://github.com/auth0/node-jsonwebtoken/wiki/Migration-Notes:-v7-to-v8)\n\n# Usage\n\n### jwt.sign(payload, secretOrPrivateKey, [options, callback])\n\n(Asynchronous) If a callback is supplied, the callback is called with the `err` or the JWT.\n\n(Synchronous) Returns the JsonWebToken as string\n\n`payload` could be an object literal, buffer or string representing valid JSON. \n> **Please _note_ that** `exp` or any other claim is only set if the payload is an object literal. Buffer or string payloads are not checked for JSON validity.\n\n> If `payload` is not a buffer or a string, it will be coerced into a string using `JSON.stringify`.\n\n`secretOrPrivateKey` is a string (utf-8 encoded), buffer, object, or KeyObject containing either the secret for HMAC algorithms or the PEM\nencoded private key for RSA and ECDSA. In case of a private key with passphrase an object `{ key, passphrase }` can be used (based on [crypto documentation](https://nodejs.org/api/crypto.html#crypto_sign_sign_private_key_output_format)), in this case be sure you pass the `algorithm` option.\nWhen signing with RSA algorithms the minimum modulus length is 2048 except when the allowInsecureKeySizes option is set to true. Private keys below this size will be rejected with an error.\n\n`options`:\n\n* `algorithm` (default: `HS256`)\n* `expiresIn`: expressed in seconds or a string describing a time span [vercel/ms](https://github.com/vercel/ms). \n  > Eg: `60`, `\"2 days\"`, `\"10h\"`, `\"7d\"`. A numeric value is interpreted as a seconds count. If you use a string be sure you provide the time units (days, hours, etc), otherwise milliseconds unit is used by default (`\"120\"` is equal to `\"120ms\"`).\n* `notBefore`: expressed in seconds or a string describing a time span [vercel/ms](https://github.com/vercel/ms). \n  > Eg: `60`, `\"2 days\"`, `\"10h\"`, `\"7d\"`. A numeric value is interpreted as a seconds count. If you use a string be sure you provide the time units (days, hours, etc), otherwise milliseconds unit is used by default (`\"120\"` is equal to `\"120ms\"`).\n* `audience`\n* `issuer`\n* `jwtid`\n* `subject`\n* `noTimestamp`\n* `header`\n* `keyid`\n* `mutatePayload`: if true, the sign function will modify the payload object directly. This is useful if you need a raw reference to the payload after claims have been applied to it but before it has been encoded into a token.\n* `allowInsecureKeySizes`: if true allows private keys with a modulus below 2048 to be used for RSA\n* `allowInvalidAsymmetricKeyTypes`: if true, allows asymmetric keys which do not match the specified algorithm. This option is intended only for backwards compatability and should be avoided.\n\n\n\n> There are no default values for `expiresIn`, `notBefore`, `audience`, `subject`, `issuer`.  These claims can also be provided in the payload directly with `exp`, `nbf`, `aud`, `sub` and `iss` respectively, but you **_can't_** include in both places.\n\nRemember that `exp`, `nbf` and `iat` are **NumericDate**, see related [Token Expiration (exp claim)](#token-expiration-exp-claim)\n\n\nThe header can be customized via the `options.header` object.\n\nGenerated jwts will include an `iat` (issued at) claim by default unless `noTimestamp` is specified. If `iat` is inserted in the payload, it will be used instead of the real timestamp for calculating other things like `exp` given a timespan in `options.expiresIn`.\n\nSynchronous Sign with default (HMAC SHA256)\n\n```js\nvar jwt = require('jsonwebtoken');\nvar token = jwt.sign({ foo: 'bar' }, 'shhhhh');\n```\n\nSynchronous Sign with RSA SHA256\n```js\n// sign with RSA SHA256\nvar privateKey = fs.readFileSync('private.key');\nvar token = jwt.sign({ foo: 'bar' }, privateKey, { algorithm: 'RS256' });\n```\n\nSign asynchronously\n```js\njwt.sign({ foo: 'bar' }, privateKey, { algorithm: 'RS256' }, function(err, token) {\n  console.log(token);\n});\n```\n\nBackdate a jwt 30 seconds\n```js\nvar older_token = jwt.sign({ foo: 'bar', iat: Math.floor(Date.now() / 1000) - 30 }, 'shhhhh');\n```\n\n#### Token Expiration (exp claim)\n\nThe standard for JWT defines an `exp` claim for expiration. The expiration is represented as a **NumericDate**:\n\n> A JSON numeric value representing the number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, ignoring leap seconds.  This is equivalent to the IEEE Std 1003.1, 2013 Edition [POSIX.1] definition \"Seconds Since the Epoch\", in which each day is accounted for by exactly 86400 seconds, other than that non-integer values can be represented.  See RFC 3339 [RFC3339] for details regarding date/times in general and UTC in particular.\n\nThis means that the `exp` field should contain the number of seconds since the epoch.\n\nSigning a token with 1 hour of expiration:\n\n```javascript\njwt.sign({\n  exp: Math.floor(Date.now() / 1000) + (60 * 60),\n  data: 'foobar'\n}, 'secret');\n```\n\nAnother way to generate a token like this with this library is:\n\n```javascript\njwt.sign({\n  data: 'foobar'\n}, 'secret', { expiresIn: 60 * 60 });\n\n//or even better:\n\njwt.sign({\n  data: 'foobar'\n}, 'secret', { expiresIn: '1h' });\n```\n\n### jwt.verify(token, secretOrPublicKey, [options, callback])\n\n(Asynchronous) If a callback is supplied, function acts asynchronously. The callback is called with the decoded payload if the signature is valid and optional expiration, audience, or issuer are valid. If not, it will be called with the error.\n\n(Synchronous) If a callback is not supplied, function acts synchronously. Returns the payload decoded if the signature is valid and optional expiration, audience, or issuer are valid. If not, it will throw the error.\n\n> __Warning:__ When the token comes from an untrusted source (e.g. user input or external requests), the returned decoded payload should be treated like any other user input; please make sure to sanitize and only work with properties that are expected\n\n`token` is the JsonWebToken string\n\n`secretOrPublicKey` is a string (utf-8 encoded), buffer, or KeyObject containing either the secret for HMAC algorithms, or the PEM\nencoded public key for RSA and ECDSA.\nIf `jwt.verify` is called asynchronous, `secretOrPublicKey` can be a function that should fetch the secret or public key. See below for a detailed example\n\nAs mentioned in [this comment](https://github.com/auth0/node-jsonwebtoken/issues/208#issuecomment-231861138), there are other libraries that expect base64 encoded secrets (random bytes encoded using base64), if that is your case you can pass `Buffer.from(secret, 'base64')`, by doing this the secret will be decoded using base64 and the token verification will use the original random bytes.\n\n`options`\n\n* `algorithms`: List of strings with the names of the allowed algorithms. For instance, `[\"HS256\", \"HS384\"]`. \n  > If not specified a defaults will be used based on the type of key provided\n  > * secret - ['HS256', 'HS384', 'HS512']\n  > * rsa - ['RS256', 'RS384', 'RS512']\n  > * ec - ['ES256', 'ES384', 'ES512']\n  > * default - ['RS256', 'RS384', 'RS512']\n* `audience`: if you want to check audience (`aud`), provide a value here. The audience can be checked against a string, a regular expression or a list of strings and/or regular expressions. \n  > Eg: `\"urn:foo\"`, `/urn:f[o]{2}/`, `[/urn:f[o]{2}/, \"urn:bar\"]`\n* `complete`: return an object with the decoded `{ payload, header, signature }` instead of only the usual content of the payload.\n* `issuer` (optional): string or array of strings of valid values for the `iss` field.\n* `jwtid` (optional): if you want to check JWT ID (`jti`), provide a string value here.\n* `ignoreExpiration`: if `true` do not validate the expiration of the token.\n* `ignoreNotBefore`...\n* `subject`: if you want to check subject (`sub`), provide a value here\n* `clockTolerance`: number of seconds to tolerate when checking the `nbf` and `exp` claims, to deal with small clock differences among different servers\n* `maxAge`: the maximum allowed age for tokens to still be valid. It is expressed in seconds or a string describing a time span [vercel/ms](https://github.com/vercel/ms). \n  > Eg: `1000`, `\"2 days\"`, `\"10h\"`, `\"7d\"`. A numeric value is interpreted as a seconds count. If you use a string be sure you provide the time units (days, hours, etc), otherwise milliseconds unit is used by default (`\"120\"` is equal to `\"120ms\"`).\n* `clockTimestamp`: the time in seconds that should be used as the current time for all necessary comparisons.\n* `nonce`: if you want to check `nonce` claim, provide a string value here. It is used on Open ID for the ID Tokens. ([Open ID implementation notes](https://openid.net/specs/openid-connect-core-1_0.html#NonceNotes))\n* `allowInvalidAsymmetricKeyTypes`: if true, allows asymmetric keys which do not match the specified algorithm. This option is intended only for backwards compatability and should be avoided.\n\n```js\n// verify a token symmetric - synchronous\nvar decoded = jwt.verify(token, 'shhhhh');\nconsole.log(decoded.foo) // bar\n\n// verify a token symmetric\njwt.verify(token, 'shhhhh', function(err, decoded) {\n  console.log(decoded.foo) // bar\n});\n\n// invalid token - synchronous\ntry {\n  var decoded = jwt.verify(token, 'wrong-secret');\n} catch(err) {\n  // err\n}\n\n// invalid token\njwt.verify(token, 'wrong-secret', function(err, decoded) {\n  // err\n  // decoded undefined\n});\n\n// verify a token asymmetric\nvar cert = fs.readFileSync('public.pem');  // get public key\njwt.verify(token, cert, function(err, decoded) {\n  console.log(decoded.foo) // bar\n});\n\n// verify audience\nvar cert = fs.readFileSync('public.pem');  // get public key\njwt.verify(token, cert, { audience: 'urn:foo' }, function(err, decoded) {\n  // if audience mismatch, err == invalid audience\n});\n\n// verify issuer\nvar cert = fs.readFileSync('public.pem');  // get public key\njwt.verify(token, cert, { audience: 'urn:foo', issuer: 'urn:issuer' }, function(err, decoded) {\n  // if issuer mismatch, err == invalid issuer\n});\n\n// verify jwt id\nvar cert = fs.readFileSync('public.pem');  // get public key\njwt.verify(token, cert, { audience: 'urn:foo', issuer: 'urn:issuer', jwtid: 'jwtid' }, function(err, decoded) {\n  // if jwt id mismatch, err == invalid jwt id\n});\n\n// verify subject\nvar cert = fs.readFileSync('public.pem');  // get public key\njwt.verify(token, cert, { audience: 'urn:foo', issuer: 'urn:issuer', jwtid: 'jwtid', subject: 'subject' }, function(err, decoded) {\n  // if subject mismatch, err == invalid subject\n});\n\n// alg mismatch\nvar cert = fs.readFileSync('public.pem'); // get public key\njwt.verify(token, cert, { algorithms: ['RS256'] }, function (err, payload) {\n  // if token alg != RS256,  err == invalid signature\n});\n\n// Verify using getKey callback\n// Example uses https://github.com/auth0/node-jwks-rsa as a way to fetch the keys.\nvar jwksClient = require('jwks-rsa');\nvar client = jwksClient({\n  jwksUri: 'https://sandrino.auth0.com/.well-known/jwks.json'\n});\nfunction getKey(header, callback){\n  client.getSigningKey(header.kid, function(err, key) {\n    var signingKey = key.publicKey || key.rsaPublicKey;\n    callback(null, signingKey);\n  });\n}\n\njwt.verify(token, getKey, options, function(err, decoded) {\n  console.log(decoded.foo) // bar\n});\n\n```\n\n<details>\n<summary><em></em>Need to peek into a JWT without verifying it? (Click to expand)</summary>\n\n### jwt.decode(token [, options])\n\n(Synchronous) Returns the decoded payload without verifying if the signature is valid.\n\n> __Warning:__ This will __not__ verify whether the signature is valid. You should __not__ use this for untrusted messages. You most likely want to use `jwt.verify` instead.\n\n> __Warning:__ When the token comes from an untrusted source (e.g. user input or external request), the returned decoded payload should be treated like any other user input; please make sure to sanitize and only work with properties that are expected\n\n\n`token` is the JsonWebToken string\n\n`options`:\n\n* `json`: force JSON.parse on the payload even if the header doesn't contain `\"typ\":\"JWT\"`.\n* `complete`: return an object with the decoded payload and header.\n\nExample\n\n```js\n// get the decoded payload ignoring signature, no secretOrPrivateKey needed\nvar decoded = jwt.decode(token);\n\n// get the decoded payload and header\nvar decoded = jwt.decode(token, {complete: true});\nconsole.log(decoded.header);\nconsole.log(decoded.payload)\n```\n\n</details>\n\n## Errors & Codes\nPossible thrown errors during verification.\nError is the first argument of the verification callback.\n\n### TokenExpiredError\n\nThrown error if the token is expired.\n\nError object:\n\n* name: 'TokenExpiredError'\n* message: 'jwt expired'\n* expiredAt: [ExpDate]\n\n```js\njwt.verify(token, 'shhhhh', function(err, decoded) {\n  if (err) {\n    /*\n      err = {\n        name: 'TokenExpiredError',\n        message: 'jwt expired',\n        expiredAt: 1408621000\n      }\n    */\n  }\n});\n```\n\n### JsonWebTokenError\nError object:\n\n* name: 'JsonWebTokenError'\n* message:\n  * 'invalid token' - the header or payload could not be parsed\n  * 'jwt malformed' - the token does not have three components (delimited by a `.`)\n  * 'jwt signature is required'\n  * 'invalid signature'\n  * 'jwt audience invalid. expected: [OPTIONS AUDIENCE]'\n  * 'jwt issuer invalid. expected: [OPTIONS ISSUER]'\n  * 'jwt id invalid. expected: [OPTIONS JWT ID]'\n  * 'jwt subject invalid. expected: [OPTIONS SUBJECT]'\n\n```js\njwt.verify(token, 'shhhhh', function(err, decoded) {\n  if (err) {\n    /*\n      err = {\n        name: 'JsonWebTokenError',\n        message: 'jwt malformed'\n      }\n    */\n  }\n});\n```\n\n### NotBeforeError\nThrown if current time is before the nbf claim.\n\nError object:\n\n* name: 'NotBeforeError'\n* message: 'jwt not active'\n* date: 2018-10-04T16:10:44.000Z\n\n```js\njwt.verify(token, 'shhhhh', function(err, decoded) {\n  if (err) {\n    /*\n      err = {\n        name: 'NotBeforeError',\n        message: 'jwt not active',\n        date: 2018-10-04T16:10:44.000Z\n      }\n    */\n  }\n});\n```\n\n\n## Algorithms supported\n\nArray of supported algorithms. The following algorithms are currently supported.\n\n| alg Parameter Value | Digital Signature or MAC Algorithm                                     |\n|---------------------|------------------------------------------------------------------------|\n| HS256               | HMAC using SHA-256 hash algorithm                                      |\n| HS384               | HMAC using SHA-384 hash algorithm                                      |\n| HS512               | HMAC using SHA-512 hash algorithm                                      |\n| RS256               | RSASSA-PKCS1-v1_5 using SHA-256 hash algorithm                         |\n| RS384               | RSASSA-PKCS1-v1_5 using SHA-384 hash algorithm                         |\n| RS512               | RSASSA-PKCS1-v1_5 using SHA-512 hash algorithm                         |\n| PS256               | RSASSA-PSS using SHA-256 hash algorithm (only node ^6.12.0 OR >=8.0.0) |\n| PS384               | RSASSA-PSS using SHA-384 hash algorithm (only node ^6.12.0 OR >=8.0.0) |\n| PS512               | RSASSA-PSS using SHA-512 hash algorithm (only node ^6.12.0 OR >=8.0.0) |\n| ES256               | ECDSA using P-256 curve and SHA-256 hash algorithm                     |\n| ES384               | ECDSA using P-384 curve and SHA-384 hash algorithm                     |\n| ES512               | ECDSA using P-521 curve and SHA-512 hash algorithm                     |\n| none                | No digital signature or MAC value included                             |\n\n## Refreshing JWTs\n\nFirst of all, we recommend you to think carefully if auto-refreshing a JWT will not introduce any vulnerability in your system.\n\nWe are not comfortable including this as part of the library, however, you can take a look at [this example](https://gist.github.com/ziluvatar/a3feb505c4c0ec37059054537b38fc48) to show how this could be accomplished.\nApart from that example there are [an issue](https://github.com/auth0/node-jsonwebtoken/issues/122) and [a pull request](https://github.com/auth0/node-jsonwebtoken/pull/172) to get more knowledge about this topic.\n\n# TODO\n\n* X.509 certificate chain is not checked\n\n## Issue Reporting\n\nIf you have found a bug or if you have a feature request, please report them at this repository issues section. Please do not report security vulnerabilities on the public GitHub issue tracker. The [Responsible Disclosure Program](https://auth0.com/whitehat) details the procedure for disclosing security issues.\n\n## Author\n\n[Auth0](https://auth0.com)\n\n## License\n\nThis project is licensed under the MIT license. See the [LICENSE](LICENSE) file for more info.\n", "time": {"created": "2022-01-26T13:17:54.146Z", "modified": "2025-06-05T16:21:00.576Z", "8.5.1": "2019-03-18T12:07:37.715Z", "8.5.0": "2019-02-20T13:42:50.904Z", "8.4.0": "2018-11-14T10:46:49.785Z", "8.3.0": "2018-06-11T15:45:26.006Z", "8.2.2": "2018-05-30T15:17:23.007Z", "8.2.1": "2018-04-05T11:52:55.685Z", "8.2.0": "2018-03-02T14:10:12.425Z", "8.1.1": "2018-01-22T19:27:16.318Z", "8.1.0": "2017-10-09T17:47:52.012Z", "8.0.1": "2017-09-12T11:05:05.886Z", "8.0.0": "2017-09-06T16:03:30.289Z", "7.4.3": "2017-08-17T14:50:46.131Z", "7.4.2": "2017-08-04T12:38:53.520Z", "7.4.1": "2017-05-17T09:37:05.554Z", "7.4.0": "2017-04-24T17:16:17.646Z", "7.3.0": "2017-02-13T18:53:10.077Z", "7.2.1": "2016-12-07T14:02:51.551Z", "7.2.0": "2016-12-06T12:40:10.130Z", "7.1.10": "2016-12-06T12:29:18.399Z", "7.1.9": "2016-08-11T16:23:25.379Z", "7.1.8": "2016-08-10T12:54:09.631Z", "7.1.7": "2016-07-29T18:56:21.831Z", "7.1.6": "2016-07-15T12:32:54.636Z", "7.1.5": "2016-07-15T12:12:27.498Z", "7.1.3": "2016-07-12T13:08:40.761Z", "7.1.1": "2016-07-12T12:59:22.308Z", "7.1.0": "2016-07-12T12:39:33.102Z", "7.0.1": "2016-06-14T17:44:04.114Z", "7.0.0": "2016-05-19T15:50:31.704Z", "6.2.0": "2016-04-29T14:31:49.265Z", "6.1.2": "2016-04-29T11:57:21.271Z", "6.1.1": "2016-04-28T22:55:23.286Z", "6.1.0": "2016-04-27T16:23:34.125Z", "6.0.1": "2016-04-27T15:51:49.872Z", "6.0.0": "2016-04-27T15:44:55.504Z", "5.7.0": "2016-02-16T12:24:00.388Z", "5.6.2": "2016-02-16T11:59:42.854Z", "5.6.0": "2016-02-16T11:49:24.916Z", "5.5.4": "2016-01-04T15:43:25.033Z", "5.5.3": "2016-01-04T12:32:08.430Z", "5.5.2": "2016-01-04T12:06:17.651Z", "5.5.1": "2016-01-04T11:53:49.687Z", "5.5.0": "2015-12-28T17:11:25.150Z", "5.4.1": "2015-10-26T18:10:32.861Z", "5.4.0": "2015-10-02T11:14:50.900Z", "5.3.1": "2015-10-02T11:11:04.351Z", "5.2.0": "2015-10-02T11:05:14.039Z", "5.1.0": "2015-10-02T10:56:36.343Z", "5.0.5": "2015-08-19T13:27:44.172Z", "5.0.4": "2015-07-15T12:01:17.566Z", "5.0.3": "2015-07-15T11:58:09.711Z", "5.0.2": "2015-06-15T12:28:08.501Z", "5.0.1": "2015-05-15T22:01:58.864Z", "5.0.0": "2015-04-10T20:48:37.182Z", "4.2.2": "2015-03-26T19:45:09.289Z", "4.2.1": "2015-03-17T16:17:14.883Z", "4.2.0": "2015-03-16T23:38:56.067Z", "4.1.0": "2015-03-10T17:00:21.891Z", "4.0.0": "2015-03-06T15:10:53.288Z", "3.2.2": "2015-01-15T15:17:52.808Z", "3.2.1": "2015-01-08T11:58:12.337Z", "3.2.0": "2014-12-29T19:13:23.489Z", "3.1.1": "2014-12-29T17:22:53.641Z", "3.1.0": "2014-12-29T16:31:38.234Z", "3.0.0": "2014-12-29T16:08:34.943Z", "2.0.0": "2014-12-20T03:05:25.828Z", "1.3.0": "2014-12-10T23:07:59.481Z", "1.2.0": "2014-11-26T02:17:21.249Z", "1.1.2": "2014-09-15T14:49:28.601Z", "1.1.1": "2014-09-05T11:01:11.046Z", "1.1.0": "2014-08-23T23:14:10.224Z", "1.0.2": "2014-08-23T22:59:10.227Z", "1.0.0": "2014-08-23T22:43:24.229Z", "0.4.1": "2014-07-14T19:17:35.264Z", "0.4.0": "2014-05-03T11:29:01.066Z", "0.3.0": "2014-04-23T01:52:14.417Z", "0.2.0": "2014-03-20T14:21:53.794Z", "0.1.0": "2013-07-01T01:48:10.422Z", "9.0.0": "2022-12-21T13:04:47.371Z", "9.0.1": "2023-07-05T15:59:53.505Z", "9.0.2": "2023-08-30T12:30:13.196Z"}, "versions": {"8.5.1": {"name": "jsonwebtoken", "version": "8.5.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "nyc": {"check-coverage": true, "lines": 95, "statements": 95, "functions": 100, "branches": 95, "exclude": ["./test/**"], "reporter": ["json", "lcov", "text-summary"]}, "scripts": {"lint": "eslint .", "coverage": "nyc mocha --use_strict", "test": "npm run lint && npm run coverage && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^5.6.0"}, "devDependencies": {"atob": "^2.1.2", "chai": "^4.1.2", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "eslint": "^4.19.1", "mocha": "^5.2.0", "nsp": "^2.6.2", "nyc": "^11.9.0", "sinon": "^6.0.0"}, "engines": {"npm": ">=1.4.28", "node": ">=4"}, "gitHead": "7f1f8b4b842ca3168018ab1ef53001105a1a2948", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.5.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "00e71e0b8df54c2121a1f26137df2280673bcc0d", "size": 21515, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.5.1.tgz", "integrity": "sha512-XjwVfRS6jTMsqYs0EsuJ4LGxXV14zQybNd4L2r0UvbVnSF9Af8x7p5MzbJ90Ioz/9TI41/hTCvznF/loiSzn8w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_8.5.1_1552910857619_0.9094548422901785"}, "_hasShrinkwrap": false, "publish_time": 1552910857715, "_cnpm_publish_time": 1552910857715, "_cnpmcore_publish_time": "2021-12-15T10:21:15.462Z"}, "8.5.0": {"name": "jsonwebtoken", "version": "8.5.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "nyc": {"check-coverage": true, "lines": 95, "statements": 95, "functions": 100, "branches": 95, "exclude": ["./test/**"], "reporter": ["json", "lcov", "text-summary"]}, "scripts": {"lint": "eslint .", "coverage": "nyc mocha --use_strict", "test": "npm run lint && npm run coverage && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.2.1", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^5.6.0"}, "devDependencies": {"atob": "^2.1.2", "chai": "^4.1.2", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "eslint": "^4.19.1", "mocha": "^5.2.0", "nsp": "^2.6.2", "nyc": "^11.9.0", "sinon": "^6.0.0"}, "engines": {"npm": ">=1.4.28", "node": ">=4"}, "gitHead": "1c0de55c4a650cf0e894d089c44b74afc91ff78e", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.5.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ebd0ca2a69797816e1c5af65b6c759787252947e", "size": 21347, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.5.0.tgz", "integrity": "sha512-IqEycp0znWHNA11TpYi77bVgyBO/pGESDh7Ajhas+u0ttkGkKYIIAjniL4Bw5+oVejVF+SYkaI7XKfwCCyeTuA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_8.5.0_1550670170710_0.9609500901478192"}, "_hasShrinkwrap": false, "publish_time": 1550670170904, "_cnpm_publish_time": 1550670170904, "_cnpmcore_publish_time": "2021-12-15T10:21:15.765Z"}, "8.4.0": {"name": "jsonwebtoken", "version": "8.4.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "nyc": {"check-coverage": true, "lines": 95, "statements": 95, "functions": 100, "branches": 95, "exclude": ["./test/**"], "reporter": ["json", "lcov", "text-summary"]}, "scripts": {"lint": "eslint .", "coverage": "nyc mocha", "test": "npm run lint && npm run coverage && nsp check && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.1.5", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1"}, "devDependencies": {"atob": "^2.1.2", "chai": "^4.1.2", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "eslint": "^4.19.1", "mocha": "^5.2.0", "nsp": "^2.6.2", "nyc": "^11.9.0", "sinon": "^6.0.0"}, "engines": {"npm": ">=1.4.28", "node": ">=4"}, "gitHead": "86334aa4d8c8034e7d8b52760b9a84f25769513c", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8757f7b4cb7440d86d5e2f3becefa70536c8e46a", "size": 20548, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.4.0.tgz", "integrity": "sha512-coyXjRTCy0pw5WYBpMvWOMN+Kjaik2MwTUIq9cna/W7NpO9E+iYbumZONAz3hcr+tXFJECoQVrtmIoC3Oz0gvg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_8.4.0_1542192409679_0.9085624813045623"}, "_hasShrinkwrap": false, "publish_time": 1542192409785, "_cnpm_publish_time": 1542192409785, "_cnpmcore_publish_time": "2021-12-15T10:21:15.996Z"}, "8.3.0": {"name": "jsonwebtoken", "version": "8.3.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.1.5", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "files": ["lib", "decode.js", "sign.js", "verify.js"], "gitHead": "ad983587159cd89d4ef44aef1f1373b6a34f4662", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.3.0", "_npmVersion": "6.1.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "056c90eee9a65ed6e6c72ddb0a1d325109aaf643", "size": 18513, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.3.0.tgz", "integrity": "sha512-oge/hvlmeJCH+iIz1DwcO7vKPkNGJHhgkspk8OH3VKlw+mbi42WtD4ig1+VXRln765vxptAv+xT26Fd3cteqag=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_8.3.0_1528731925928_0.6562618460989285"}, "publish_time": 152**********, "_hasShrinkwrap": false, "_cnpm_publish_time": 152**********, "_cnpmcore_publish_time": "2021-12-15T10:21:16.188Z"}, "8.2.2": {"name": "jsonwebtoken", "version": "8.2.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.1.5", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "files": ["lib", "decode.js", "sign.js", "verify.js"], "gitHead": "73c4a5ac45d557ef0dc1c43fcae6b7fc9bc3f19e", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.2.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "76d7993fda79660d71bd0f933109e1f133734b20", "size": 18123, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.2.2.tgz", "integrity": "sha512-rFFq7ow/JpPzwgaz4IyRL9cp7f4ptjW92eZgsQyqkysLBmDjSSBhnKfQESoq0GU+qJXK/CQ0o4shgwbUPiFCdw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_8.2.2_1527693442932_0.4026767317652107"}, "_hasShrinkwrap": false, "publish_time": 1527693443007, "_cnpm_publish_time": 1527693443007, "_cnpmcore_publish_time": "2021-12-15T10:21:16.401Z"}, "8.2.1": {"name": "jsonwebtoken", "version": "8.2.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.1.4", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "files": ["lib", "decode.js", "sign.js", "verify.js"], "gitHead": "092d55a02419f3e39fbc10d1abd2e5b89c84fb82", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "333ee39aa8f238f32fa41693e7a2fb7e42f82b31", "size": 17721, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.2.1.tgz", "integrity": "sha512-l8rUBr0fqYYwPc8/ZGrue7GiW7vWdZtZqelxo4Sd5lMvuEeCK8/wS54sEo6tJhdZ6hqfutsj6COgC0d1XdbHGw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_8.2.1_1522929175551_0.2311765420127183"}, "_hasShrinkwrap": false, "publish_time": 1522929175685, "_cnpm_publish_time": 1522929175685, "_cnpmcore_publish_time": "2021-12-15T10:21:16.632Z"}, "8.2.0": {"name": "jsonwebtoken", "version": "8.2.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.1.4", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "files": ["lib", "decode.js", "sign.js", "verify.js"], "gitHead": "dee583a04c6808bec0b17a2121043d2b8c98711d", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "690ec3a9e7e95e2884347ce3e9eb9d389aa598b3", "size": 17568, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.2.0.tgz", "integrity": "sha512-1Wxh8ADP3cNyPl8tZ95WtraHXCAyXupgc0AhMHjU9er98BV+UcKsO7OJUjfhIu0Uba9A40n1oSx8dbJYrm+EoQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_8.2.0_1519999812310_0.7847559431377056"}, "_hasShrinkwrap": false, "publish_time": 1519999812425, "_cnpm_publish_time": 1519999812425, "_cnpmcore_publish_time": "2021-12-15T10:21:16.871Z"}, "8.1.1": {"name": "jsonwebtoken", "version": "8.1.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.1.4", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "files": ["lib", "decode.js", "sign.js", "verify.js"], "gitHead": "7b0a010e8a8ede7b2d31a702cf4fbab1cfc774b7", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.1.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.1.0", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b04d8bb2ad847bc93238c3c92170ffdbdd1cb2ea", "size": 17454, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.1.1.tgz", "integrity": "sha512-+ijVOtfLMlCII8LJkvabaKX3+8tGrGjiCTfzoed2D1b/ebKTO1hIYBQUJHbd9dJ9Fa4kH+dhYEd1qDwyzDLUUw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken-8.1.1.tgz_1516649235141_0.7731763236224651"}, "directories": {}, "publish_time": 151**********, "_hasShrinkwrap": false, "_cnpm_publish_time": 151**********, "_cnpmcore_publish_time": "2021-12-15T10:21:17.065Z"}, "8.1.0": {"name": "jsonwebtoken", "version": "8.1.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.1.4", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "files": ["lib", "decode.js", "sign.js", "verify.js"], "gitHead": "efa517a84473487b74707805a573e79c181b67ee", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.1.0", "_shasum": "c6397cd2e5fd583d65c007a83dc7bb78e6982b83", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.4", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c6397cd2e5fd583d65c007a83dc7bb78e6982b83", "size": 17263, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.1.0.tgz", "integrity": "sha512-KGlASk0MhznKeyI16nTMla+f+Rn/w89tHPnpbRZuQfnTp221suG7FRAR/yY+FeadVSTn72WZsbqF4Yq/B583QA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken-8.1.0.tgz_1507571270012_0.8755452362820506"}, "directories": {}, "publish_time": 150**********, "_hasShrinkwrap": false, "_cnpm_publish_time": 150**********, "_cnpmcore_publish_time": "2021-12-15T10:21:17.302Z"}, "8.0.1": {"name": "jsonwebtoken", "version": "8.0.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.1.4", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "files": ["lib", "decode.js", "sign.js", "verify.js"], "gitHead": "bb39501c1ab62273af166361a5ceaa9d60a04cf7", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.0.1", "_shasum": "50daef8d0a8c7de2cd06bc1013b75b04ccf3f0cf", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "50daef8d0a8c7de2cd06bc1013b75b04ccf3f0cf", "size": 16901, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.0.1.tgz", "integrity": "sha512-taW7<PERSON><PERSON>okle/4dCae6Pdt8Px0KL/JZ6r0E0JYiyQ64eii5O8McCc2R4DDiscdw6Pj3dcoMi4s6YVa1l7M0QlVdg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken-8.0.1.tgz_1505214304737_0.6592458691447973"}, "directories": {}, "publish_time": 150**********, "_hasShrinkwrap": false, "_cnpm_publish_time": 150**********, "_cnpmcore_publish_time": "2021-12-15T10:21:17.520Z"}, "8.0.0": {"name": "jsonwebtoken", "version": "8.0.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.1.4", "lodash.includes": "^4.3.0", "lodash.isarray": "^4.0.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "files": ["lib", "decode.js", "sign.js", "verify.js"], "gitHead": "f3138506fe7fb9383a709e97c12e96148f0c7d7b", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@8.0.0", "_shasum": "7b241aa67c61b3d0b3e641254a9a6f5b66f550e8", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7b241aa67c61b3d0b3e641254a9a6f5b66f550e8", "size": 16829, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-8.0.0.tgz", "integrity": "sha512-vaUn9R7rEWjjlg8d7lVS7yV4oJk5ygNKSgPwbk9fkZ2pHQtx4n5SQG47ZhDk2iMN7hq99EBqRpcogFKJMqPdgw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken-8.0.0.tgz_1504713809025_0.44055463024415076"}, "directories": {}, "publish_time": 1504713810289, "_hasShrinkwrap": false, "_cnpm_publish_time": 1504713810289, "_cnpmcore_publish_time": "2021-12-15T10:21:17.792Z"}, "7.4.3": {"name": "jsonwebtoken", "version": "7.4.3", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.4", "lodash.once": "^4.0.0", "ms": "^2.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "e54e53c70ad3fa0d6b54f916ea4a2a2d5a8c47c2", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.4.3", "_shasum": "77f5021de058b605a1783fa1283e99812e645638", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "77f5021de058b605a1783fa1283e99812e645638", "size": 88802, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.4.3.tgz", "integrity": "sha512-7WWGEZ+/xedHUHLDvSwvN7LbmLIEgOuBNQOBKvfX5zpLok5q6873aCR2zOuJ/DrORp/DlyYImz06nlNoRCWugw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken-7.4.3.tgz_1502981444880_0.00893402541987598"}, "directories": {}, "publish_time": 1502981446131, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502981446131, "_cnpmcore_publish_time": "2021-12-15T10:21:18.008Z"}, "7.4.2": {"name": "jsonwebtoken", "version": "7.4.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.4", "lodash.once": "^4.0.0", "ms": "^2.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "e56f904d9c619b0a15c710e136f3fdfdbdecf3fb", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.4.2", "_shasum": "571b903c07e875c0fc59203d1ac78667d80e09cd", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "571b903c07e875c0fc59203d1ac78667d80e09cd", "size": 84545, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.4.2.tgz", "integrity": "sha512-h0AEMJwI0IfTzg7DxZVIUJwhjBlOrkJBWcLS4A1PKvG1IcguxN5u8DmvctTQ4MjeBWdJzf+ceVkoPTOu70HvCw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken-7.4.2.tgz_1501850332287_0.5490078683942556"}, "directories": {}, "publish_time": 1501850333520, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501850333520, "_cnpmcore_publish_time": "2021-12-15T10:21:18.266Z"}, "7.4.1": {"name": "jsonwebtoken", "version": "7.4.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.4", "lodash.once": "^4.0.0", "ms": "^2.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "5e6dc77cabf556d3f1a6306f895b8818d248fb80", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.4.1", "_shasum": "7ca324f5215f8be039cd35a6c45bb8cb74a448fb", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7ca324f5215f8be039cd35a6c45bb8cb74a448fb", "size": 63167, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.4.1.tgz", "integrity": "sha512-UEiXVGCpewmfbzTow/Aixc7G38JWQxbFb/Gu9359JkVWDdb60TFS4cbZ5bHYAaxWa9AiFOrgqRIAiyZ4I3xtFg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.4.1.tgz_1495013823257_0.9402265790849924"}, "directories": {}, "publish_time": 1495013825554, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495013825554, "_cnpmcore_publish_time": "2021-12-15T10:21:18.472Z"}, "7.4.0": {"name": "jsonwebtoken", "version": "7.4.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.4", "lodash.once": "^4.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "b0e443c9b6c81f40c830349aa2bafb6191281de3", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.4.0", "_shasum": "515bf2bba070ec615bad97fd2e945027eb476946", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "515bf2bba070ec615bad97fd2e945027eb476946", "size": 56161, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.4.0.tgz", "integrity": "sha512-Ufhsu2nyYZ6X+sRZh/UNjeJZIoERj1exXW47cc1FZN4SiauOhFJe4VIZ/4U5f3uIU3yMB9e7lmzy2DfPizBNag=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.4.0.tgz_1493054175364_0.49553341045975685"}, "directories": {}, "publish_time": 1493054177646, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493054177646, "_cnpmcore_publish_time": "2021-12-15T10:21:18.658Z"}, "7.3.0": {"name": "jsonwebtoken", "version": "7.3.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.4", "lodash.once": "^4.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "94007b3e17accb8f0d74f94c2926fdc8924f82b6", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.3.0", "_shasum": "85118d6a70e3fccdf14389f4e7a1c3f9c8a9fbba", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.6", "_npmUser": {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "85118d6a70e3fccdf14389f4e7a1c3f9c8a9fbba", "size": 40428, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.3.0.tgz", "integrity": "sha512-JPSYVU9RM5nObFojvXtjMsSSDhliKQhk61KPwsAyUpD+4plk5CJ2XEYdBy6zIuNYwKcV3CTbCE7Azd0KcymIlw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.3.0.tgz_1487011989040_0.10709377634339035"}, "directories": {}, "publish_time": 1487011990077, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487011990077, "_cnpmcore_publish_time": "2021-12-15T10:21:18.872Z"}, "7.2.1": {"name": "jsonwebtoken", "version": "7.2.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate && nsp check"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.4", "lodash.once": "^4.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "nsp": "^2.6.2", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "8da893ae31e0266cb9277c4b931f79375841e545", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.2.1", "_shasum": "0fc7217473fc02b4c9aa1e188aa70b51bba4fccb", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0fc7217473fc02b4c9aa1e188aa70b51bba4fccb", "size": 25977, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.2.1.tgz", "integrity": "sha512-5scdxBq0PhdqYuSnNEohIddrJurcb5IAGP9Pf7ixzHVsarCtJZ296VySRBc37hp9JFyhJLKb7L2BU9u0xTmycQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.2.1.tgz_1481119369194_0.9982225855346769"}, "directories": {}, "publish_time": 1481119371551, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481119371551, "_cnpmcore_publish_time": "2021-12-15T10:21:19.094Z"}, "7.2.0": {"name": "jsonwebtoken", "version": "7.2.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^10.0.1", "jws": "^3.1.4", "lodash.once": "^4.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "3a8b2b62c3efc3db8489a0f85206b2af746a9a2b", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.2.0", "_shasum": "d21106c7fedda0325007c3f2d4b6918e7249cc42", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d21106c7fedda0325007c3f2d4b6918e7249cc42", "size": 25714, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.2.0.tgz", "integrity": "sha512-J5nwnvwT0A8xiw18W95RVhH74LfRSazeDJfMNq9s4V/RRnauhcJH96qI6e39P6J1+XHlV2q62FPKtJsLBseg0g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.2.0.tgz_1481028007850_0.22671224339865148"}, "directories": {}, "publish_time": 1481028010130, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481028010130, "_cnpmcore_publish_time": "2021-12-15T10:21:19.277Z"}, "7.1.10": {"name": "jsonwebtoken", "version": "7.1.10", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.4", "lodash.once": "^4.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "aecb4d16f020b6c8e1ffbc9ffb29fcd5964ef9af", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.1.10", "_shasum": "a4601a3dddc28c95ea36515c8082e2bd50d4cc54", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a4601a3dddc28c95ea36515c8082e2bd50d4cc54", "size": 25499, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.1.10.tgz", "integrity": "sha512-JaVcxH+TeHQim+KJXDl5Y8d31Yur/rPHOwUzVA9xPwnh9dXmdZZNWrtBosb6F0DHlyCst7K/nfFu0rD3WwNkCw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.1.10.tgz_1481027356228_0.8348256640601903"}, "directories": {}, "publish_time": 1481027358399, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481027358399, "_cnpmcore_publish_time": "2021-12-15T10:21:19.466Z"}, "7.1.9": {"name": "jsonwebtoken", "version": "7.1.9", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.3", "lodash.once": "^4.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "cc0f4d67b649110a035db3df9265f05db269a15a", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.1.9", "_shasum": "847804e5258bec5a9499a8dc4a5e7a3bae08d58a", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "847804e5258bec5a9499a8dc4a5e7a3bae08d58a", "size": 24752, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.1.9.tgz", "integrity": "sha512-e/KWUs23F/CBl0aFrHoqjgNKP8uxcBTFynL5i69U7LNmVHX6R5myva27wc+ZFgFiAme4ma5Pb9y195MlFCCCIA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.1.9.tgz_1470932603683_0.2585906428284943"}, "directories": {}, "publish_time": 1470932605379, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470932605379, "_cnpmcore_publish_time": "2021-12-15T10:21:19.700Z"}, "7.1.8": {"name": "jsonwebtoken", "version": "7.1.8", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.3", "lodash.once": "^4.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "51c4fef050f930498de3b10afdf31041f80f5e2a", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.1.8", "_shasum": "45bcd43cfeb6aebc95979e5c9ae440f5eeff2025", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "45bcd43cfeb6aebc95979e5c9ae440f5eeff2025", "size": 24744, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.1.8.tgz", "integrity": "sha512-aHzDXsffPSLzdZkWw/xoYxsiR8pWai3FbdHgFdGRnbflQSd/HbwANA2tACo3tAVtADkq6l55TQVfa9DVQzVZmw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.1.8.tgz_1470833629327_0.7667830986902118"}, "directories": {}, "publish_time": 1470833649631, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470833649631, "_cnpmcore_publish_time": "2021-12-15T10:21:19.931Z"}, "7.1.7": {"name": "jsonwebtoken", "version": "7.1.7", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "^6.10.1", "jws": "^3.1.3", "lodash.once": "^4.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "5117aacd0118a10331889a64e61d8186112d8a23", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.1.7", "_shasum": "3693ba899be4d15aa06ee0608b912e1100984bf3", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3693ba899be4d15aa06ee0608b912e1100984bf3", "size": 24756, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.1.7.tgz", "integrity": "sha512-2sEIX3NX8TxH67C1IM5uQ5QaWRUyKnxeXl/UEuPkpOY6Gg/4OX0KGK8BPk0lsISKB9qo3nGUipraHby3tiiafQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.1.7.tgz_1469818578603_0.009586702100932598"}, "directories": {}, "publish_time": 1469818581831, "_hasShrinkwrap": false, "_cnpm_publish_time": 1469818581831, "_cnpmcore_publish_time": "2021-12-15T10:21:20.162Z"}, "7.1.6": {"name": "jsonwebtoken", "version": "7.1.6", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"cb": "^0.1.0", "joi": "^6.10.1", "jws": "^3.1.3", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "ae360b56792a875e16cefa8ff4103b87b4a2e386", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.1.6", "_shasum": "2ea9557af144311148f53093cfeec69e1e048abc", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2ea9557af144311148f53093cfeec69e1e048abc", "size": 24483, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.1.6.tgz", "integrity": "sha512-Kzld07BEHFcqcwfovTEimuTTaXYQvGalWX7Xd6t5kgssTnAgFTowHVz7vnlvg2FURPEMMLZoWZUlrCaMTWYPZw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.1.6.tgz_1468585972042_0.5801840056665242"}, "directories": {}, "publish_time": 1468585974636, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468585974636, "_cnpmcore_publish_time": "2021-12-15T10:21:20.394Z"}, "7.1.5": {"name": "jsonwebtoken", "version": "7.1.5", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"cb": "^0.1.0", "joi": "^6.10.1", "jws": "^3.1.3", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "184f28dbc0b1e1e376f96b8fffce368316effaea", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.1.5", "_shasum": "c83d6ac5b846df1cd906968b8ca3816eaa133d4d", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c83d6ac5b846df1cd906968b8ca3816eaa133d4d", "size": 24313, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.1.5.tgz", "integrity": "sha512-VTRzLqZ3C7xW9aycJSGp4gMV+FAFSFH9CB4TtG0PefFbZscLNPlCtC3Z11DEOMyFGhjHjVy0u6pomVaS4/0ErQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.1.5.tgz_1468584744809_0.03413615026511252"}, "directories": {}, "publish_time": 1468584747498, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468584747498, "_cnpmcore_publish_time": "2021-12-15T10:21:20.609Z"}, "7.1.3": {"name": "jsonwebtoken", "version": "7.1.3", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~6.10.1", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "598ed5a048a0550f5235a1efdd85aaf89188e4db", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.1.3", "_shasum": "086bcc40a2ffeba272be76bad30d1a4199123806", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "086bcc40a2ffeba272be76bad30d1a4199123806", "size": 24252, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.1.3.tgz", "integrity": "sha512-lVPF3LT7V/pcR5+/zF0g2SSEJtTDpkvRfymM9jW/GwbvuOinUZ42sElhxA6HPe7SCvfVNpqbhFxh9YzBEomduw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.1.3.tgz_1468328918939_0.5090081093367189"}, "directories": {}, "publish_time": 1468328920761, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468328920761, "_cnpmcore_publish_time": "2021-12-15T10:21:20.920Z"}, "7.1.1": {"name": "jsonwebtoken", "version": "7.1.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~6.10.1", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "7a180a668491ff4fbae045b9e81f64f0a32decf0", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.1.1", "_shasum": "3b6f848ca50f965148a88b8be628d978fa22170e", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3b6f848ca50f965148a88b8be628d978fa22170e", "size": 24159, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.1.1.tgz", "integrity": "sha512-dJLrT1EyL+BXzwkeIlbB3Y7jNunoRf0tUuchErdrxHzFmihyL+4CfKltQ1Q9diwaDH7mClWtU1fGIgoMGG1WUA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.1.1.tgz_1468328360589_0.3888185415416956"}, "directories": {}, "publish_time": 1468328362308, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468328362308, "_cnpmcore_publish_time": "2021-12-15T10:21:21.153Z"}, "7.1.0": {"name": "jsonwebtoken", "version": "7.1.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~6.10.1", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "a32214df81d1a592feb66f13920be7b4b362c4f9", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.1.0", "_shasum": "ab91ddd7d5a4ee11f1b104ea9ae72e35566f6b13", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ab91ddd7d5a4ee11f1b104ea9ae72e35566f6b13", "size": 23859, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.1.0.tgz", "integrity": "sha512-VR2gaIpgPldoqMi0U/JPzlARBKkDapq617XC06TTmx0KSw4N2oVVj8qOVe10t+zM//ItaR3hhf6UKnODEdiY9A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.1.0.tgz_1468327170294_0.33086230396293104"}, "directories": {}, "publish_time": 1468327173102, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468327173102, "_cnpmcore_publish_time": "2021-12-15T10:21:21.369Z"}, "7.0.1": {"name": "jsonwebtoken", "version": "7.0.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~6.10.1", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "2a91a0235d4baec3d2f0e022e6b3fe22126fdfaa", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.0.1", "_shasum": "4aba9fea3552c8f1d415d4117ab80aa09d6af55e", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4aba9fea3552c8f1d415d4117ab80aa09d6af55e", "size": 23678, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.0.1.tgz", "integrity": "sha512-3uL6MByh68nVPeDNYZVLNLW79Q2I8aLDXwxg55l4t2Gq1/IXVvGLvEjBa8hong/4Rk9OOELHwiL4DDt0E1qdXQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.0.1.tgz_1465926239534_0.9313598950393498"}, "directories": {}, "publish_time": 1465926244114, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465926244114, "_cnpmcore_publish_time": "2021-12-15T10:21:21.598Z"}, "7.0.0": {"name": "jsonwebtoken", "version": "7.0.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~6.10.1", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "78bf0083df842e15cdaed5d0269da5b3215b3b41", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@7.0.0", "_shasum": "0e1eb109cffe631db7dc0ec8c3face3b57f8f5c3", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0e1eb109cffe631db7dc0ec8c3face3b57f8f5c3", "size": 23679, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-7.0.0.tgz", "integrity": "sha512-kg01nNV5qt6ye4slh4gOePBf3D+gkS5HzCRSln9dx8GjFjV5ZPkwHF2u7Op/+aS1TbYZVDh2FQh9WgDtBQ/jJQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-7.0.0.tgz_1463673028421_0.4116716361604631"}, "directories": {}, "publish_time": 1463673031704, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463673031704, "_cnpmcore_publish_time": "2021-12-15T10:21:21.834Z"}, "6.2.0": {"name": "jsonwebtoken", "version": "6.2.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~6.10.1", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "31625b5c01f1ac5d63580543867bd75dd3671704", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@6.2.0", "_shasum": "8233154c6cd9fd8760ad0f5a563f539dd391f2f9", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8233154c6cd9fd8760ad0f5a563f539dd391f2f9", "size": 23282, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-6.2.0.tgz", "integrity": "sha512-jkALyXNdG4ikKxqookaYRpU0ZCuh5Zc/rwqjuEYbt6PQEO8wnikbdZ0Js4z5QD0VudVigy7jgINsOSLJhLCccQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-6.2.0.tgz_1461940308920_0.14055088837631047"}, "directories": {}, "publish_time": 1461940309265, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461940309265, "_cnpmcore_publish_time": "2021-12-15T10:21:22.064Z"}, "6.1.2": {"name": "jsonwebtoken", "version": "6.1.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~6.10.1", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}, "gitHead": "d78659fe58eabd47e717a8f6c81d67480f1fb93f", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@6.1.2", "_shasum": "6105fa82f16c7a4f5483bc52751f19a67fc6ab0a", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6105fa82f16c7a4f5483bc52751f19a67fc6ab0a", "size": 22895, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-6.1.2.tgz", "integrity": "sha512-axtJKAw55gN7wKST/PEjRihZ+Ye32ZlrhqMF9B1PnXuumoKTRmgSHzkoeMiLG2YrU/435GuyBuv2KvPY4smYzw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-6.1.2.tgz_1461931039202_0.2594677312299609"}, "directories": {}, "publish_time": 1461931041271, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461931041271, "_cnpmcore_publish_time": "2021-12-15T10:21:22.279Z"}, "6.1.1": {"name": "jsonwebtoken", "version": "6.1.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~8.0.5", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "252110e41049b4da4c12baf0451586c8cc63ceaf", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@6.1.1", "_shasum": "c1f302b48a60eddafc288e8f77c717d70e9ae001", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c1f302b48a60eddafc288e8f77c717d70e9ae001", "size": 22874, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-6.1.1.tgz", "integrity": "sha512-KgfZhiKE1WgYi12CrqwFhxTWuqF1Fdx1RzsEs4qMRAdjeUEjGYRhookhc4qNrguUkRgcwvgnqbTdORFtdYg79A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-6.1.1.tgz_1461884121026_0.11496175057254732"}, "directories": {}, "publish_time": 1461884123286, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461884123286, "_cnpmcore_publish_time": "2021-12-15T10:21:22.502Z"}, "6.1.0": {"name": "jsonwebtoken", "version": "6.1.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~8.0.5", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "75c50a24142f7bb2852144a31b7bdb79bca9a11f", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@6.1.0", "_shasum": "cb9a8977d8f50208b8820faf974bc188d762e4af", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cb9a8977d8f50208b8820faf974bc188d762e4af", "size": 22674, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-6.1.0.tgz", "integrity": "sha512-Geo5yXRx+hAmVSIrUrUHgwKGi3YhB9nUpweW/+dkTKCAUKsLHKAL0u2OtgLf3kSEgiGn3/VoHa7+76RsCF7zHQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-6.1.0.tgz_1461774211490_0.7655251200776547"}, "directories": {}, "publish_time": 1461774214125, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461774214125, "_cnpmcore_publish_time": "2021-12-15T10:21:22.824Z"}, "6.0.1": {"name": "jsonwebtoken", "version": "6.0.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~8.0.5", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "afb3285c8b823e6cd27975846ca5e87e33b46720", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@6.0.1", "_shasum": "68e33eb31d83dd47a8dce707c4ffee10db72e8df", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "68e33eb31d83dd47a8dce707c4ffee10db72e8df", "size": 22642, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-6.0.1.tgz", "integrity": "sha512-4BL7vKrLM7vw27i8RzwQZfllz52SlWTje4sSTr41ZciN5t6J0W4b7nzra+8jc4Wv0uu0r6NOwtKZeM8kPbIncw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-6.0.1.tgz_1461772308811_0.8661865431349725"}, "directories": {}, "publish_time": 1461772309872, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461772309872, "_cnpmcore_publish_time": "2021-12-15T10:21:23.082Z"}, "6.0.0": {"name": "jsonwebtoken", "version": "6.0.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"joi": "~8.0.5", "jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "conventional-changelog": "~1.1.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "5835f552e266e4a46b1b9213eb2df0d730989da6", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@6.0.0", "_shasum": "1488451cd01ab37315ca405fc3647786eee98ae5", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1488451cd01ab37315ca405fc3647786eee98ae5", "size": 22439, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-6.0.0.tgz", "integrity": "sha512-d<PERSON>tzkqeitr90dkTyECPm/3fLkm8DM/HWeCmUXsw+uJGcJdMHLFVozDC3DfCUclwLyCIWdGGII/TZXdN0ThdSSA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-6.0.0.tgz_1461771895124_0.11420765612274408"}, "directories": {}, "publish_time": 1461771895504, "_hasShrinkwrap": false, "_cnpm_publish_time": 1461771895504, "_cnpmcore_publish_time": "2021-12-15T10:21:23.357Z"}, "5.7.0": {"name": "jsonwebtoken", "version": "5.7.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "cb33aabc432408ed7f3826c2f5b5930313b63f1e", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.7.0", "_shasum": "1c90f9a86ce5b748f5f979c12b70402b4afcddb4", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "1c90f9a86ce5b748f5f979c12b70402b4afcddb4", "size": 18311, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.7.0.tgz", "integrity": "sha512-VP2HXX7tJ73v5ZGGTFdgILhOBwGSDlPP4qArdrq6YbDVJ9YJflcNE/fbuGHPlefPkbM1+7rFzz1eTq1xICsJxA=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-5.7.0.tgz_1455625436794_0.319415409816429"}, "directories": {}, "publish_time": 1455625440388, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455625440388, "_cnpmcore_publish_time": "2021-12-15T10:21:23.582Z"}, "5.6.2": {"name": "jsonwebtoken", "version": "5.6.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "195940409e8e9e8a96904ef3fa3f490dfff2a3f8", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.6.2", "_shasum": "a9e829f7a4f0df1b8ba799fa7bef265c7b1cdd2a", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "a9e829f7a4f0df1b8ba799fa7bef265c7b1cdd2a", "size": 18385, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.6.2.tgz", "integrity": "sha512-l7WKn8619h8+97BWZoLQzX1ThuTW1JqTFmua/Op3sSXM3kX9hOpwoFWITTyLZv596c/Eu9/lKUxiH2nPW/J6tQ=="}, "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-5.6.2.tgz_1455623979219_0.8241684073582292"}, "directories": {}, "publish_time": 1455623982854, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455623982854, "_cnpmcore_publish_time": "2021-12-15T10:21:23.798Z"}, "5.6.0": {"name": "jsonwebtoken", "version": "5.6.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "ae1327183792464cf7ca2284f85a6f82966cb33a", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.6.0", "_shasum": "5219c14c887597a5d188c6b1895994b8d5c2ecd4", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "5219c14c887597a5d188c6b1895994b8d5c2ecd4", "size": 18378, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.6.0.tgz", "integrity": "sha512-LdwfytFqeyAY92uhCYbKz9pMOY521LAoS/nYRpdTvmerclqxNzd///0WStQHsmr/6jG7Ct9xNUgMirwDliJzLg=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/jsonwebtoken-5.6.0.tgz_1455623362711_0.9769372697919607"}, "directories": {}, "publish_time": 1455623364916, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455623364916, "_cnpmcore_publish_time": "2021-12-15T10:21:24.027Z"}, "5.5.4": {"name": "jsonwebtoken", "version": "5.5.4", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "10266c139298af1261fb9592838b1877f82fd4e4", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.5.4", "_shasum": "29b0940522e0226cfca1d834a65b7d87b1cacaee", "_from": ".", "_npmVersion": "2.14.14", "_nodeVersion": "0.10.41", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "29b0940522e0226cfca1d834a65b7d87b1cacaee", "size": 18318, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.5.4.tgz", "integrity": "sha512-3I2I/sJiyfQBb12+2jnSM1V1B0Qr8AbedvSJ6UN8YUuW1qzPGgpgrQuqE9P+EZaEwqmSllNGjF/6da+l5vKLqQ=="}, "directories": {}, "publish_time": 1451922205033, "_hasShrinkwrap": false, "_cnpm_publish_time": 1451922205033, "_cnpmcore_publish_time": "2021-12-15T10:21:24.287Z"}, "5.5.3": {"name": "jsonwebtoken", "version": "5.5.3", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "04a76d51f1ea7203be6aa14ca92ab0b1e8b8aa07", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.5.3", "_shasum": "961fe2cdf0ce876f2f1e9eae0c76badac758a528", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "961fe2cdf0ce876f2f1e9eae0c76badac758a528", "size": 18311, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.5.3.tgz", "integrity": "sha512-U88q5LIRdy5KiOOiw/mQNupwjO+nzn1gEaiBwTtmEvEXs32LIL193Q5cE4yUp39uyf5TqTW00IMLvSGsraaFww=="}, "directories": {}, "publish_time": 1451910728430, "_hasShrinkwrap": false, "_cnpm_publish_time": 1451910728430, "_cnpmcore_publish_time": "2021-12-15T10:21:24.477Z"}, "5.5.2": {"name": "jsonwebtoken", "version": "5.5.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "e403a66bee5f1aaabe974a7862cc6eaf0b926d57", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.5.2", "_shasum": "8eed469d091986654b6718d8aef4e52f169deb9a", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "8eed469d091986654b6718d8aef4e52f169deb9a", "size": 18124, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.5.2.tgz", "integrity": "sha512-QWC7TNEwDHdBXmNU3dkEs2nEJzHWLkfJ9y14x1+4pDQqol46NDnEl6yesk5f8MTim8Kg7jFYEZlAz38bGtedHw=="}, "directories": {}, "publish_time": 1451909177651, "_hasShrinkwrap": false, "_cnpm_publish_time": 1451909177651, "_cnpmcore_publish_time": "2021-12-15T10:21:24.794Z"}, "5.5.1": {"name": "jsonwebtoken", "version": "5.5.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha --require test/util/fakeDate"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "42145bc6ef3a556c170aa4fca3229310ad5c8086", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.5.1", "_shasum": "3597b829d51345747798b4ac221d1601a6e0b99f", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "3597b829d51345747798b4ac221d1601a6e0b99f", "size": 18023, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.5.1.tgz", "integrity": "sha512-haFXUIpcaYB/mxmjctYC77wn//8D8fPzLJcoMeDBNhckVjbVgwu3CteQGA3vAHOHaOjYbluyNgosp/2+ToDteg=="}, "directories": {}, "publish_time": 1451908429687, "_hasShrinkwrap": false, "_cnpm_publish_time": 1451908429687, "_cnpmcore_publish_time": "2021-12-15T10:21:25.033Z"}, "5.5.0": {"name": "jsonwebtoken", "version": "5.5.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "f1fb176a1599a6fed82c9ffcf7517c295c5c0541", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.5.0", "_shasum": "8cc6881f0aa3a6e16e29f52051c562fcfec0ce15", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "8cc6881f0aa3a6e16e29f52051c562fcfec0ce15", "size": 17689, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.5.0.tgz", "integrity": "sha512-8DIRsKmQeocNqniVoUhsaJUAcKlCe11SK9BZk9X+NN22pJ7CxUpDuDh54hPUJpz06b6JFQeaODTY5xzhMrQIVg=="}, "directories": {}, "publish_time": 1451322685150, "_hasShrinkwrap": false, "_cnpm_publish_time": 1451322685150, "_cnpmcore_publish_time": "2021-12-15T10:21:25.277Z"}, "5.4.1": {"name": "jsonwebtoken", "version": "5.4.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "eb272d6da05b7d19d24a5e749cf026ac2b413abf", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.4.1", "_shasum": "2055c639195ffe56314fa6a51df02468186a9695", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "3.3.1", "_npmUser": {"name": "rolodato", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "2055c639195ffe56314fa6a51df02468186a9695", "size": 16922, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.4.1.tgz", "integrity": "sha512-g0guae6YzYAM5k1cqVmBpUHNIsH54zZgLV8iaxD/7KWnFtThx7bFMIGW5bPMKD2znF5o6tYUmtEZRdpd2w1FYQ=="}, "directories": {}, "publish_time": 1445883032861, "_hasShrinkwrap": false, "_cnpm_publish_time": 1445883032861, "_cnpmcore_publish_time": "2021-12-15T10:21:25.520Z"}, "5.4.0": {"name": "jsonwebtoken", "version": "5.4.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "c7e34bb4fd4487134b4580b47a1c6a0c6427df72", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.4.0", "_shasum": "01afcfcf012a9a3eae1cbfd9e5218726407492db", "_from": ".", "_npmVersion": "3.3.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "01afcfcf012a9a3eae1cbfd9e5218726407492db", "size": 17053, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.4.0.tgz", "integrity": "sha512-qPn1VCHdQzBWiEqHKcsXIsp0g2ePyD+0SbG+fS71mr4AKwsHdCEHFnR/f/aIS4f69+HukIdozDQiF9N9B6w/IQ=="}, "directories": {}, "publish_time": 1443784490900, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443784490900, "_cnpmcore_publish_time": "2021-12-15T10:21:25.770Z"}, "5.3.1": {"name": "jsonwebtoken", "version": "5.3.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "4b70ae3012a43ce79659dd75b05e9151abd4b97c", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.3.1", "_shasum": "65418ef1bae5eb1f3410f2c133396e8d70c57046", "_from": ".", "_npmVersion": "3.3.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "65418ef1bae5eb1f3410f2c133396e8d70c57046", "size": 16427, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.3.1.tgz", "integrity": "sha512-kHf2uq4qN0VpPZ81PxoAaKGj/bg86oMtX9snu0dpM7wR6tTJoXUEjw0BUOQ1tv4e5exCNXevpOyYebsTB7uVUQ=="}, "directories": {}, "publish_time": 1443784264351, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443784264351, "_cnpmcore_publish_time": "2021-12-15T10:21:26.020Z"}, "5.2.0": {"name": "jsonwebtoken", "version": "5.2.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0", "ms": "^0.7.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "7e8292e768bd3865d10dad333e6bb750a64dda7e", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.2.0", "_shasum": "9e9c12c692c35c8355b512d8118dd18440aa99f8", "_from": ".", "_npmVersion": "3.3.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "9e9c12c692c35c8355b512d8118dd18440aa99f8", "size": 16425, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.2.0.tgz", "integrity": "sha512-IDsE12OmHD0qf5JWAqitGZzk9h5lspl4xA5CBkVAWdiPKKeUWA9Al3Ok2mK6StQZUXamf3NU8LdRsGf/meJnnw=="}, "directories": {}, "publish_time": 1443783914039, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443783914039, "_cnpmcore_publish_time": "2021-12-15T10:21:26.223Z"}, "5.1.0": {"name": "jsonwebtoken", "version": "5.1.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "83d7aff9fb317aadb5bed073a6b0663220f14c7d", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.1.0", "_shasum": "a69f20bc2f27091e6c86a341b0d61d4ce390572a", "_from": ".", "_npmVersion": "3.3.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "a69f20bc2f27091e6c86a341b0d61d4ce390572a", "size": 15592, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.1.0.tgz", "integrity": "sha512-aY0swmcpvL289H5Nk5036ZRU+dtUxJXQwJPBlMrA20Ue8jLN4vQBzn6dst9TviknMwoIfkPiXdg4SjVcr4T4wA=="}, "directories": {}, "publish_time": 1443783396343, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443783396343, "_cnpmcore_publish_time": "2021-12-15T10:21:26.457Z"}, "5.0.5": {"name": "jsonwebtoken", "version": "5.0.5", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "6a715a13992c888db77cc5b274e5fd28633e4c76", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.0.5", "_shasum": "6592cc05ee03dd5ad9e03a910911a4da79afe0f8", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "6592cc05ee03dd5ad9e03a910911a4da79afe0f8", "size": 15418, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.0.5.tgz", "integrity": "sha512-OWh0JVrWWml3X3ykf5FC0xO4VA+NEGHOY1SuFnu0vESdwZrInLPb00AXvoWNno2Vf1gQiTClWH3Xh/e3ZWOK6Q=="}, "directories": {}, "publish_time": 1439990864172, "_hasShrinkwrap": false, "_cnpm_publish_time": 1439990864172, "_cnpmcore_publish_time": "2021-12-15T10:21:26.684Z"}, "5.0.4": {"name": "jsonwebtoken", "version": "5.0.4", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "7b9f509641eff3b0aae79001965272c6bf88ca2a", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.0.4", "_shasum": "4099dd73842da454386bf6a4fc18bc638b15cd03", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "4099dd73842da454386bf6a4fc18bc638b15cd03", "size": 15283, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.0.4.tgz", "integrity": "sha512-ZvdpJ3jJgovIb2478/4TnfMUVthE8gwooJ5gVPAG7w6BDBwxQcT+1uqOkp35X4Wz9VAqOBEZMH0MvoInCoOHJw=="}, "directories": {}, "publish_time": 1436961677566, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436961677566, "_cnpmcore_publish_time": "2021-12-15T10:21:26.907Z"}, "5.0.3": {"name": "jsonwebtoken", "version": "5.0.3", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "815a1a166317985fc97141bdd50991e7332d5099", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.0.3", "_shasum": "6a55cd162a34fa7e2a94c1b189981e7fb1e218a2", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "6a55cd162a34fa7e2a94c1b189981e7fb1e218a2", "size": 15111, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.0.3.tgz", "integrity": "sha512-9tIeHG+QvRYnxZUPcWr7ZeZqQ3UXgIm0E5qCUc4TA7s9ZlfYk6qQszyTDlieL4+cSFkQItltng1sh+mZSfncBw=="}, "directories": {}, "publish_time": 1436961489711, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436961489711, "_cnpmcore_publish_time": "2021-12-15T10:21:27.138Z"}, "5.0.2": {"name": "jsonwebtoken", "version": "5.0.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "118ff1a7baf22d67852a2e0d67e6f01a782418f3", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@5.0.2", "_shasum": "c5d9c6130a1270271306c4bab65934ef8ae4f993", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "c5d9c6130a1270271306c4bab65934ef8ae4f993", "size": 15099, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.0.2.tgz", "integrity": "sha512-33th7LcnotKi8MjQzqL6aYTbVR8VCCTVVCAa+7tWBhd5fapfeGQN0MxwwS9PnkiYVCmDujC4UWHmqZWqi9HTWQ=="}, "directories": {}, "publish_time": 1434371288501, "_hasShrinkwrap": false, "_cnpm_publish_time": 1434371288501, "_cnpmcore_publish_time": "2021-12-15T10:21:27.359Z"}, "5.0.1": {"name": "jsonwebtoken", "version": "5.0.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "214b9c435f0fd7f50d4cdf471e49bedc798216a4", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@5.0.1", "_shasum": "5d85ce580421915be22e4aa7b13d4bb99b280a2a", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "5d85ce580421915be22e4aa7b13d4bb99b280a2a", "size": 14997, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.0.1.tgz", "integrity": "sha512-Gs/51TvsJmLE4mzgGH0srlIa+/OHSHrwaNgEWHDQ6qKiST4LuPNX9tiwhD/PNowa82bQ1Hemkr+y/evxvM43yA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431727318864, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431727318864, "_cnpmcore_publish_time": "2021-12-15T10:21:27.582Z"}, "5.0.0": {"name": "jsonwebtoken", "version": "5.0.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "db8fb93ad7eb2cdc2d35e8efda0f82035c37f296", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@5.0.0", "_shasum": "8244ac5921491a933cfe996f3f142bea1e38adbc", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "0.10.26", "_npmUser": {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "8244ac5921491a933cfe996f3f142bea1e38adbc", "size": 13934, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-5.0.0.tgz", "integrity": "sha512-ADWSQxWTyk5cjcZgZ1G7mB6jzJxhUFXclsILeiC2jmCIBFDsaTFfL4Wg+VTnZLwEZ4lFINjTEx//fsYRE4A/dw=="}, "directories": {}, "publish_time": 1428698917182, "_hasShrinkwrap": false, "_cnpm_publish_time": 1428698917182, "_cnpmcore_publish_time": "2021-12-15T10:21:27.825Z"}, "4.2.2": {"name": "jsonwebtoken", "version": "4.2.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "e46ca6634447cf6a5b7f08298aa2f2450b8df704", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@4.2.2", "_shasum": "1f6ee4b14c545d06ec999a37471b5e67cbe401a0", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.30", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "1f6ee4b14c545d06ec999a37471b5e67cbe401a0", "size": 13230, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-4.2.2.tgz", "integrity": "sha512-2N9yTaQM42kA280DVPqy0RP4KKScJQaTrP2N0TBgemdTbz6at3kq29ZCnM9ifaeyf1lQYatABtDUjn18JWWfYQ=="}, "directories": {}, "publish_time": 1427399109289, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1427399109289, "_cnpmcore_publish_time": "2021-12-15T10:21:28.066Z"}, "4.2.1": {"name": "jsonwebtoken", "version": "4.2.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "4d22d08da2dc51b6eaf9ce47695be431c3c663c9", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@4.2.1", "_shasum": "979b5e2c0a282b81d73a91ed62d9a47970657300", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.30", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "979b5e2c0a282b81d73a91ed62d9a47970657300", "size": 13073, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-4.2.1.tgz", "integrity": "sha512-eq3H2MeU5l5LMBaFnRPh5ng6iTJTEFWMl1jBac0Y/TyicilReu3AxC5vJ8gkl+Qu11S2+TM7058gvpW1NRy6bw=="}, "directories": {}, "publish_time": 1426609034883, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1426609034883, "_cnpmcore_publish_time": "2021-12-15T10:21:28.320Z"}, "4.2.0": {"name": "jsonwebtoken", "version": "4.2.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "7863a84b1d16f0036f703f3dab243bd8de882ca8", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@4.2.0", "_shasum": "1a5ca5f037f0d57ad00aba5b2ee29d2b4f4b8314", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.30", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "1a5ca5f037f0d57ad00aba5b2ee29d2b4f4b8314", "size": 11497, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-4.2.0.tgz", "integrity": "sha512-WGBtsi031X/WO/z/yVfzL4F/ucKRgHtaHAMZRkXTUKHSOHmxLHi0+BOVAgG1nO2pGEFTAUS2DF115zURZna1/A=="}, "directories": {}, "publish_time": 1426549136067, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1426549136067, "_cnpmcore_publish_time": "2021-12-15T10:21:28.537Z"}, "4.1.0": {"name": "jsonwebtoken", "version": "4.1.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "b69d441c6e5e4b2efaafde682b4b9670ac3bcb51", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@4.1.0", "_shasum": "b5263435ad19425376584d2823fc8225ddbb68a2", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.30", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "b5263435ad19425376584d2823fc8225ddbb68a2", "size": 10548, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-4.1.0.tgz", "integrity": "sha512-3zrshyVxl3vOW+c5TVH6znA2eLSCH9Zt2M+2jt29EH1YNLVsjmyd5WaBxQfMu6NG1+r6s/inHfm2z4CMEtyS1g=="}, "directories": {}, "publish_time": 1426006821891, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1426006821891, "_cnpmcore_publish_time": "2021-12-15T10:21:28.817Z"}, "4.0.0": {"name": "jsonwebtoken", "version": "4.0.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "db1cb1c8642167c2ec5b0826537ba11379b3e48b", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@4.0.0", "_shasum": "5a1182a650f635dd45b18901c05c29c79ea0d2f1", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.30", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "5a1182a650f635dd45b18901c05c29c79ea0d2f1", "size": 10231, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-4.0.0.tgz", "integrity": "sha512-VtlRNQrlstBBI/10ypvg0l58rFZSbxG26QbXWzMI/PlublvS79adciBJzVvur5TfLCEJY450HuZlVL2wm/pRKQ=="}, "directories": {}, "publish_time": 1425654653288, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1425654653288, "_cnpmcore_publish_time": "2021-12-15T10:21:29.081Z"}, "3.2.2": {"name": "jsonwebtoken", "version": "3.2.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~1.0.1"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "8d7b90e7992370a854b7a5d91a39b523c9fcb46a", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@3.2.2", "_shasum": "b7911b0b48f7851f41024971b70cd0f8b5fe415a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "b7911b0b48f7851f41024971b70cd0f8b5fe415a", "size": 9377, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-3.2.2.tgz", "integrity": "sha512-ImBSkVMjUCN+f6oSDPWDBZ5gWAupEPzR0Da4frqFaq1IoCqn8IRozlJR47G0GSu6rNTkbvbiq+TmublUKJmZ5w=="}, "directories": {}, "publish_time": 1421335072808, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1421335072808, "_cnpmcore_publish_time": "2021-12-15T10:21:29.293Z"}, "3.2.1": {"name": "jsonwebtoken", "version": "3.2.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": ">=1.4.28"}, "gitHead": "41cc7b7d8767322b39597172814349fbf88df6c2", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@3.2.1", "_shasum": "1eab8a142f74aacd5f71baf72e6ef3b986d787e2", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.30", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "1eab8a142f74aacd5f71baf72e6ef3b986d787e2", "size": 9458, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-3.2.1.tgz", "integrity": "sha512-9/xKeQsv2CXXpf/CSBnTmyy+UkFDtAzrCTMr/586KKLjQXgnAoHGeUsOxzO8OonPS1vd/gKdk8L/34lPNsinEQ=="}, "directories": {}, "publish_time": 1420718292337, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1420718292337, "_cnpmcore_publish_time": "2021-12-15T10:21:29.508Z"}, "3.2.0": {"name": "jsonwebtoken", "version": "3.2.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": "~1.4.28"}, "gitHead": "5e1c03fb90fca456b8d82df217bea4eb895e10f6", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@3.2.0", "_shasum": "6b9108f6889d05e90ae743ad6873f1affc198ae1", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "6b9108f6889d05e90ae743ad6873f1affc198ae1", "size": 9449, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-3.2.0.tgz", "integrity": "sha512-8CsytR3+jYIgA3Gc/mZ94r0yj1iGbKgh9SXxdcPXZarVIZPspToSKep+z39ULui4N0wVd6OyqBds7SoAwoRvGA=="}, "directories": {}, "publish_time": 1419880403489, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1419880403489, "_cnpmcore_publish_time": "2021-12-15T10:21:29.715Z"}, "3.1.1": {"name": "jsonwebtoken", "version": "3.1.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": "~1.4.28"}, "gitHead": "32702c25bdca5286cb272761f3a75078333b8d50", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@3.1.1", "_shasum": "736b77b420d5b73bfac6ec37dfea9b236f2212d9", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "736b77b420d5b73bfac6ec37dfea9b236f2212d9", "size": 9400, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-3.1.1.tgz", "integrity": "sha512-OehG8frN7JuPXlC/MjptfaWZ+LaG+krShAtEByiOdZEvhDnPnca6IVPrHQgBiHOAD6Ix1TBUpTolRzk+AQo9Uw=="}, "directories": {}, "publish_time": 1419873773641, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1419873773641, "_cnpmcore_publish_time": "2021-12-15T10:21:29.922Z"}, "3.1.0": {"name": "jsonwebtoken", "version": "3.1.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": "~1.4.28"}, "gitHead": "3854502087e0c0188326ec7db89a0d368e80ff6d", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@3.1.0", "_shasum": "aee94947138b5d02364c7228775ea2fdb0b5b1e2", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "aee94947138b5d02364c7228775ea2fdb0b5b1e2", "size": 8801, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-3.1.0.tgz", "integrity": "sha512-Dq7qi2bp+HqKpNqi05BglBrv71BEUfv1DJazh3HKhF6Gf3J6jpCuZtXpbrDt4Z3/GoY9A8Bpnr6kX4mf17Jokg=="}, "directories": {}, "publish_time": 1419870698234, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1419870698234, "_cnpmcore_publish_time": "2021-12-15T10:21:30.133Z"}, "3.0.0": {"name": "jsonwebtoken", "version": "3.0.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "engines": {"npm": "~1.4.28"}, "gitHead": "1076414a09113220818e1b28e20988b944b3b2a3", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@3.0.0", "_shasum": "cd8ec263132fc88cb3b25efd303359cebe7b2139", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "cd8ec263132fc88cb3b25efd303359cebe7b2139", "size": 8691, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-3.0.0.tgz", "integrity": "sha512-WN3FIuYXt3ZIgDYjXgMB0ydCfLkNL5SAYmRMaCsqRep9qw93WL8qrT0P1w8M4xm7EkpDigelheY5/chkTT+bVw=="}, "directories": {}, "publish_time": 1419869314943, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1419869314943, "_cnpmcore_publish_time": "2021-12-15T10:21:30.404Z"}, "2.0.0": {"name": "jsonwebtoken", "version": "2.0.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "gitHead": "0aad51615b8252b8e629890df9f3a66275a7b9b4", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@2.0.0", "_shasum": "cb1c47a76bba1ffb26311eb6d6338b85a5f045e7", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.30", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "cb1c47a76bba1ffb26311eb6d6338b85a5f045e7", "size": 8485, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-2.0.0.tgz", "integrity": "sha512-ioQl//Zum8W2opJL+b/i20MKFWkKl6EcIbDmZYNhjwX4NIpUxmVDCzdiB4sH6t0+Ew4jst81tTqkTXYxzLFYGg=="}, "directories": {}, "publish_time": 1419044725828, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1419044725828, "_cnpmcore_publish_time": "2021-12-15T10:21:30.594Z"}, "1.3.0": {"name": "jsonwebtoken", "version": "1.3.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "gitHead": "eea72087db2fd73bdc17195223ed532b25ba3e3d", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@1.3.0", "_shasum": "683ceee1bbe09a92d9e026ab25e67f97bcf3c3e2", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "iaco", "email": "sebastian.i<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "683ceee1bbe09a92d9e026ab25e67f97bcf3c3e2", "size": 8029, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-1.3.0.tgz", "integrity": "sha512-W8+0E18LspD4DhMLZVvWc4odyH0zjs5c5kVNfCQDaJrDwlmjEwvc8F/QZD7po83i9Ij1Yvfx7AaCl6rRlMOjtg=="}, "directories": {}, "publish_time": 1418252879481, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1418252879481, "_cnpmcore_publish_time": "2021-12-15T10:21:30.858Z"}, "1.2.0": {"name": "jsonwebtoken", "version": "1.2.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@1.2.0", "dist": {"shasum": "bfef60d674b036eac59c6178cf778aa7e6ff2212", "size": 7982, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-1.2.0.tgz", "integrity": "sha512-1yaHA62SA/nh4JIZnIwVZfrY3HkBhKv/gxiHAypnB3SI5LvEkTozG0uUSb2u4qdbij7bM2h57mGUGqscXDSf3A=="}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "woloski", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1416968241249, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1416968241249, "_cnpmcore_publish_time": "2021-12-15T10:21:31.152Z"}, "1.1.2": {"name": "jsonwebtoken", "version": "1.1.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "gitHead": "b272ed87a5c959139e0d8cc1dd68720219bd756d", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@1.1.2", "_shasum": "b231c30b96f01429289eed6f93bf5ce5ae4943c5", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "b231c30b96f01429289eed6f93bf5ce5ae4943c5", "size": 7757, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-1.1.2.tgz", "integrity": "sha512-duzI2jqfR2M2fLRQEb4wiN+LZsBTii93k4+OhFeKqkvkYYPnOyInLojQDVRauetzO4EWzR7j5djvcQzn3Zmenw=="}, "directories": {}, "publish_time": 1410792568601, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1410792568601, "_cnpmcore_publish_time": "2021-12-15T10:21:31.352Z"}, "1.1.1": {"name": "jsonwebtoken", "version": "1.1.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "gitHead": "0bf9320df846e14f551280d6a7e0ea1b54160a10", "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@1.1.1", "_shasum": "ff4fefe359a2da7dfa445a3a4dfbada44a0e1bce", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "dist": {"shasum": "ff4fefe359a2da7dfa445a3a4dfbada44a0e1bce", "size": 7696, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-1.1.1.tgz", "integrity": "sha512-p4O5dIOfLk1poXmdLurI3UUEZRS1YphAl3zs0ZTkQBYZmd4QcwSrOGFpW1kTwTe+77uKM2M0G5Bob+pMeUKu5w=="}, "directories": {}, "publish_time": 1409914871046, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1409914871046, "_cnpmcore_publish_time": "2021-12-15T10:21:31.552Z"}, "1.1.0": {"name": "jsonwebtoken", "version": "1.1.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@1.1.0", "dist": {"shasum": "d8c466e40f8a26781f523c8eb497d110684bebf1", "size": 7665, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-1.1.0.tgz", "integrity": "sha512-dDfQjYhXREEGtVfDmRKGh4wEFPtuDC1tVTzz8QT6tEPLEdOQ7awyUhKONL6KelyrZHbEiQXr4yx/5cIQxiTzEA=="}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "woloski", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1408835650224, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1408835650224, "_cnpmcore_publish_time": "2021-12-15T10:21:31.813Z"}, "1.0.2": {"name": "jsonwebtoken", "version": "1.0.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@1.0.2", "dist": {"shasum": "069985dfa4b80f0646b6ad94536ec634f92ba7fb", "size": 7663, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-1.0.2.tgz", "integrity": "sha512-eeRGU8unyWEKrInYC+5lja0cRetiqkZvE5QKChsqQj4IQcu4IhprcYX+XMIeG6HH4H4yA/SIHExQu7+JtKWRhA=="}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "woloski", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1408834750227, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1408834750227, "_cnpmcore_publish_time": "2021-12-15T10:21:32.040Z"}, "1.0.0": {"name": "jsonwebtoken", "version": "1.0.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.2"}, "devDependencies": {"chai": "*", "mocha": "*"}, "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@1.0.0", "dist": {"shasum": "a4bd1e7bde39b101189aceb1d6b24a489138e7c3", "size": 7665, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-1.0.0.tgz", "integrity": "sha512-gFWNia4//dNLXMAuWkc+GcUa5XiY3EIKKyHLN0mXogRAWpXvmoaVgFLFnFMQTjD6u+HSJWTeiYiSc4rGIRk9kA=="}, "_from": ".", "_npmVersion": "1.4.4", "_npmUser": {"name": "woloski", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1408833804229, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1408833804229, "_cnpmcore_publish_time": "2021-12-15T10:21:32.273Z"}, "0.4.1": {"name": "jsonwebtoken", "version": "0.4.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.2"}, "devDependencies": {"chai": "*", "mocha": "*"}, "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@0.4.1", "dist": {"shasum": "741c7a5ca829c4e9e22e942168996c7f9dee11a0", "size": 6929, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-0.4.1.tgz", "integrity": "sha512-2/BzdZ79eMmvbBYvM1Jq+JacJw8/irfIn1Uz7hziXUEzjanPfcz4P1hqcAdne2LVz7iMG369OOBQj9uf7NwZHQ=="}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "woloski", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1405365455264, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1405365455264, "_cnpmcore_publish_time": "2021-12-15T10:21:32.515Z"}, "0.4.0": {"name": "jsonwebtoken", "version": "0.4.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.2"}, "devDependencies": {"chai": "*", "mocha": "*"}, "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@0.4.0", "dist": {"shasum": "7dfa44ac8a588e16e0453c81f11ab6addd0742fe", "size": 6730, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-0.4.0.tgz", "integrity": "sha512-VQlZ5j/m478UhkmopShghzepWmar8tKEXYWuDX0V64rd8a45ANQop6RqHWAkHypQ7DYh4nuqgS1tD+9uSnvqvw=="}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "woloski", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1399116541066, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1399116541066, "_cnpmcore_publish_time": "2021-12-15T10:21:32.721Z"}, "0.3.0": {"name": "jsonwebtoken", "version": "0.3.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.2"}, "devDependencies": {"chai": "*", "mocha": "*"}, "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@0.3.0", "dist": {"shasum": "82b4a85b568d4e83c7e1d6735b0be753997c79fb", "size": 6706, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-0.3.0.tgz", "integrity": "sha512-UnVpO+ZtDTpo499mkmSokNkJ2SXr+c9kwCifgbY3Zv9yUU7fcGiJ02jYXq3GsRFgPIMGeI4H+AT3bDbnb4/PEQ=="}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "woloski", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1398217934417, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1398217934417, "_cnpmcore_publish_time": "2021-12-15T10:21:32.923Z"}, "0.2.0": {"name": "jsonwebtoken", "version": "0.2.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.2", "moment": "~2.0.0"}, "devDependencies": {"chai": "*", "mocha": "*"}, "homepage": "https://github.com/auth0/node-jsonwebtoken", "_id": "jsonwebtoken@0.2.0", "dist": {"shasum": "75ec9d9b17cb958f232cd24bdfa14afa98e28be5", "size": 6713, "noattachment": false, "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-0.2.0.tgz", "integrity": "sha512-brUbXYgVRQKAZ11sll3J2IrrP+Q9sfofw5ugDsF42eXH5Yq3k8sEntPE3+FpOc3m/uDLhQuzYWlJHHfv4dWlpA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1395325313794, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1395325313794, "_cnpmcore_publish_time": "2021-12-15T10:21:33.152Z"}, "0.1.0": {"name": "jsonwebtoken", "version": "0.1.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/auth0/node-jsonwebtoken"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "~0.2.2", "moment": "~2.0.0"}, "devDependencies": {"chai": "*", "mocha": "*"}, "readmeFilename": "README.md", "_id": "jsonwebtoken@0.1.0", "dist": {"tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-0.1.0.tgz", "shasum": "505628492092fe35d08b600fa6768cd06711aaa2", "size": 6055, "noattachment": false, "integrity": "sha512-F+cS4nX8se0apNdOo4zOjrwPj/nQy2EgG/CPVJB/z0juS+apEuHoow1ZuG0gZN4zFjkUcYuOAcU5FbY5KyjgmA=="}, "_from": ".", "_npmVersion": "1.2.22", "_npmUser": {"name": "woloski", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jared<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1372643290422, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/", "_hasShrinkwrap": false, "_cnpm_publish_time": 1372643290422, "_cnpmcore_publish_time": "2021-12-15T10:21:33.350Z"}, "9.0.0": {"name": "jsonwebtoken", "version": "9.0.0", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "nyc": {"check-coverage": true, "lines": 95, "statements": 95, "functions": 100, "branches": 95, "exclude": ["./test/**"], "reporter": ["json", "lcov", "text-summary"]}, "scripts": {"lint": "eslint .", "coverage": "nyc mocha --use_strict", "test": "npm run lint && npm run coverage && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.2.2", "lodash": "^4.17.21", "ms": "^2.1.1", "semver": "^7.3.8"}, "devDependencies": {"atob": "^2.1.2", "chai": "^4.1.2", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "eslint": "^4.19.1", "mocha": "^5.2.0", "nsp": "^2.6.2", "nyc": "^11.9.0", "sinon": "^6.0.0"}, "engines": {"npm": ">=6", "node": ">=12"}, "gitHead": "e1fa9dcc12054a8681db4e6373da1b30cf7016e3", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@9.0.0", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-tuGfYXxkQGDPnLJ7SibiQgVgeDgfbPq2k2ICcbgqW8WxWLBAxKQM/ZCu/IT8SOSwmaYl4dpTFCW5xZv7YbbWUw==", "shasum": "d0faf9ba1cc3a56255fe49c0961a67e520c1926d", "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-9.0.0.tgz", "fileCount": 15, "unpackedSize": 43128, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICFbedBlabLjw7NIzX6NxKRtMJpAm5EmyIbY31lMv0ucAiEAtjD5WkMo3pdQruvAFl+kneaKRdGhZBknnEbeDJ86SdQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjowRvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8vA/+IjrE5dhwnSVORY+0yO8w1hGVsdJW3a778aDOikwBWBzoDqvF\r\n4FraFF2dQpq/69S18YMWoneyhZnNOFexe6/CaB/kNszKNFiwDFzOcbhAa7PI\r\nAo1p3Udtdn7YeHc1bd9Ao7wfaJKiEsbJeLe17LtgQzCUgxBhnI55/Dpw1aDF\r\nZvh1GV68ZSep9mKDgvpUDdCwWyQ7lI7b9RlOeCrW1k+dDkkDVrkrZ0eT0ejC\r\nvqX20BYIOUkWFGHUxPRNWR0WiXN2PA0MiNdTHZbr/ySt7PNcxth8TJUdukfi\r\nzUXZqL3VS9imoOtxMdN9pu8Dr/ZS1t8GV/Y/Cw165HfiQUHsxaUU+NYHAUcF\r\noEoMyeRB1oOO4KHX0BorXV8NfxlWy7l8OMJgsmf35HB6yb9hFr4U/JBAzyZq\r\n27U+cunYTP1mPBOaQnfeWVAtga/srdBbMGLP94Is14YVuL8C0YZ8ph9veWkT\r\nyQYPdbzVSm0SCbhk2aeJ52jxmpDe7PiSqHZMmKX2TiAWMJU5fgNQOTDOaoyR\r\nQQOCz2mduRQVLRf5jMW8b9yZtAzkImPq4dmHJPs/JXDqfiUi0fVnR8NOdMwf\r\nolOqDsUJRfKVzeSSQmoyJzdfUA2ZFQu6a9O5bL3eAuYW8jcRiFKhOwGk4Gjc\r\nJv6ulQcUo9+LNW+zpoCc9xN0fCJujXJErCM=\r\n=juck\r\n-----END PGP SIGNATURE-----\r\n", "size": 12197}, "_npmUser": {"name": "julien.w<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jake.lacey", "email": "<EMAIL>"}, {"name": "lbalmaceda", "email": "balmacedal<PERSON><EMAIL>"}, {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jstrutz", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iaco", "email": "sebastian.i<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "julien.w<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "charles<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_9.0.0_1671627887207_0.9960540910225177"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-21T13:04:51.375Z"}, "9.0.1": {"name": "jsonwebtoken", "version": "9.0.1", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "nyc": {"check-coverage": true, "lines": 95, "statements": 95, "functions": 100, "branches": 95, "exclude": ["./test/**"], "reporter": ["json", "lcov", "text-summary"]}, "scripts": {"lint": "eslint .", "coverage": "nyc mocha --use_strict", "test": "npm run lint && npm run coverage && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.2.2", "lodash": "^4.17.21", "ms": "^2.1.1", "semver": "^7.3.8"}, "devDependencies": {"atob": "^2.1.2", "chai": "^4.1.2", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "eslint": "^4.19.1", "mocha": "^5.2.0", "nsp": "^2.6.2", "nyc": "^11.9.0", "sinon": "^6.0.0"}, "engines": {"npm": ">=6", "node": ">=12"}, "gitHead": "8afff56c07b71b5bfbb41508cda4a03a9c1eb9de", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@9.0.1", "_nodeVersion": "14.21.1", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-K8wx7eJ5TPvEjuiVSkv167EVboBDv9PZdDoF7BgeQnBLVvZWW9clr2PsQHVJDTKaEIH5JBIwHujGcHp7GgI2eg==", "shasum": "81d8c901c112c24e497a55daf6b2be1225b40145", "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-9.0.1.tgz", "fileCount": 16, "unpackedSize": 84221, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHyw1iScPx8Kys/UT3c4e/8rNQG/5Pxu5rDzsYkYDwm9AiEAqOVe8VI23+FnVN3LnrIqsPx/ar8mcGW74fspMJULzjA="}], "size": 24022}, "_npmUser": {"name": "jake.lacey", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "julien.w<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jake.lacey", "email": "<EMAIL>"}, {"name": "lbalmaceda", "email": "balmacedal<PERSON><EMAIL>"}, {"name": "zil<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "woloski", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iaco", "email": "sebastian.i<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "charles<PERSON>", "email": "<EMAIL>"}, {"name": "david.renaud.okta", "email": "<EMAIL>"}, {"name": "madhuri.rm23", "email": "madhuri.ravindra<PERSON><PERSON>@okta.com"}, {"name": "ed<PERSON><PERSON>rivella-okta", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_9.0.1_1688572793249_0.38131361411016296"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-05T15:59:53.505Z", "publish_time": 1688572793505, "_source_registry_name": "default"}, "9.0.2": {"name": "jsonwebtoken", "version": "9.0.2", "description": "JSON Web Token implementation (symmetric and asymmetric)", "main": "index.js", "nyc": {"check-coverage": true, "lines": 95, "statements": 95, "functions": 100, "branches": 95, "exclude": ["./test/**"], "reporter": ["json", "lcov", "text-summary"]}, "scripts": {"lint": "eslint .", "coverage": "nyc mocha --use_strict", "test": "npm run lint && npm run coverage && cost-of-modules"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "keywords": ["jwt"], "author": {"name": "auth0"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "devDependencies": {"atob": "^2.1.2", "chai": "^4.1.2", "conventional-changelog": "~1.1.0", "cost-of-modules": "^1.0.1", "eslint": "^4.19.1", "mocha": "^5.2.0", "nsp": "^2.6.2", "nyc": "^11.9.0", "sinon": "^6.0.0"}, "engines": {"npm": ">=6", "node": ">=12"}, "gitHead": "bc28861f1fa981ed9c009e29c044a19760a0b128", "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "_id": "jsonwebtoken@9.0.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==", "shasum": "65ff91f4abef1784697d40952bb1998c504caaf3", "tarball": "https://registry.npmmirror.com/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "fileCount": 15, "unpackedSize": 43482, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAtSR64IX6I3rst7ZohCDMNviMvI15lEObZtCDlbwvufAiEAjnPgUOosH8ojmbhwaEOwvPgrCpyURGOVd1tjpkR6FiE="}], "size": 12231}, "_npmUser": {"name": "charles<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tim<PERSON><PERSON>", "email": "timothy<PERSON><PERSON>+<EMAIL>"}, {"name": "david.renaud.okta", "email": "<EMAIL>"}, {"name": "charles<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jsonwebtoken_9.0.2_1693398613010_0.22876439310493635"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-30T12:30:13.196Z", "publish_time": 1693398613196, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/auth0/node-jsonwebtoken/issues"}, "homepage": "https://github.com/auth0/node-jsonwebtoken#readme", "keywords": ["jwt"], "repository": {"type": "git", "url": "git+https://github.com/auth0/node-jsonwebtoken.git"}, "_source_registry_name": "default"}