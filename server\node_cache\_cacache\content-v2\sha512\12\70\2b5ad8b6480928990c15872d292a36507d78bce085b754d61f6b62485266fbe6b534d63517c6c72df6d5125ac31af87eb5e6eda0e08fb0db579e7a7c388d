{"_attachments": {}, "_id": "npmlog", "_rev": "1510-61f14693b677e08f51140007", "author": {"name": "GitHub Inc."}, "description": "logger for npm", "dist-tags": {"latest": "7.0.1"}, "license": "ISC", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "name": "npmlog", "readme": "# npmlog\n\nThe logger util that npm uses.\n\nThis logger is very basic.  It does the logging for npm.  It supports\ncustom levels and colored output.\n\nBy default, logs are written to stderr.  If you want to send log messages\nto outputs other than streams, then you can change the `log.stream`\nmember, or you can just listen to the events that it emits, and do\nwhatever you want with them.\n\n# Installation\n\n```console\nnpm install npmlog --save\n```\n\n# Basic Usage\n\n```javascript\nvar log = require('npmlog')\n\n// additional stuff ---------------------------+\n// message ----------+                         |\n// prefix ----+      |                         |\n// level -+   |      |                         |\n//        v   v      v                         v\n    log.info('fyi', 'I have a kitty cat: %j', myKittyCat)\n```\n\n## log.level\n\n* {String}\n\nThe level to display logs at.  Any logs at or above this level will be\ndisplayed.  The special level `silent` will prevent anything from being\ndisplayed ever.\n\n## log.record\n\n* {Array}\n\nAn array of all the log messages that have been entered.\n\n## log.maxRecordSize\n\n* {Number}\n\nThe maximum number of records to keep.  If log.record gets bigger than\n10% over this value, then it is sliced down to 90% of this value.\n\nThe reason for the 10% window is so that it doesn't have to resize a\nlarge array on every log entry.\n\n## log.prefixStyle\n\n* {Object}\n\nA style object that specifies how prefixes are styled.  (See below)\n\n## log.headingStyle\n\n* {Object}\n\nA style object that specifies how the heading is styled.  (See below)\n\n## log.heading\n\n* {String} Default: \"\"\n\nIf set, a heading that is printed at the start of every line.\n\n## log.stream\n\n* {Stream} Default: `process.stderr`\n\nThe stream where output is written.\n\n## log.enableColor()\n\nForce colors to be used on all messages, regardless of the output\nstream.\n\n## log.disableColor()\n\nDisable colors on all messages.\n\n## log.enableProgress()\n\nEnable the display of log activity spinner and progress bar\n\n## log.disableProgress()\n\nDisable the display of a progress bar\n\n## log.enableUnicode()\n\nForce the unicode theme to be used for the progress bar.\n\n## log.disableUnicode()\n\nDisable the use of unicode in the progress bar.\n\n## log.setGaugeTemplate(template)\n\nSet a template for outputting the progress bar. See the [gauge documentation] for details.\n\n[gauge documentation]: https://npmjs.com/package/gauge\n\n## log.setGaugeThemeset(themes)\n\nSelect a themeset to pick themes from for the progress bar. See the [gauge documentation] for details.\n\n## log.pause()\n\nStop emitting messages to the stream, but do not drop them.\n\n## log.resume()\n\nEmit all buffered messages that were written while paused.\n\n## log.log(level, prefix, message, ...)\n\n* `level` {String} The level to emit the message at\n* `prefix` {String} A string prefix.  Set to \"\" to skip.\n* `message...` Arguments to `util.format`\n\nEmit a log message at the specified level.\n\n## log\\[level](prefix, message, ...)\n\nFor example,\n\n* log.silly(prefix, message, ...)\n* log.verbose(prefix, message, ...)\n* log.info(prefix, message, ...)\n* log.http(prefix, message, ...)\n* log.warn(prefix, message, ...)\n* log.error(prefix, message, ...)\n\nLike `log.log(level, prefix, message, ...)`.  In this way, each level is\ngiven a shorthand, so you can do `log.info(prefix, message)`.\n\n## log.addLevel(level, n, style, disp)\n\n* `level` {String} Level indicator\n* `n` {Number} The numeric level\n* `style` {Object} Object with fg, bg, inverse, etc.\n* `disp` {String} Optional replacement for `level` in the output.\n\nSets up a new level with a shorthand function and so forth.\n\nNote that if the number is `Infinity`, then setting the level to that\nwill cause all log messages to be suppressed.  If the number is\n`-Infinity`, then the only way to show it is to enable all log messages.\n\n## log.newItem(name, todo, weight)\n\n* `name` {String} Optional; progress item name.\n* `todo` {Number} Optional; total amount of work to be done. Default 0.\n* `weight` {Number} Optional; the weight of this item relative to others. Default 1.\n\nThis adds a new `are-we-there-yet` item tracker to the progress tracker. The\nobject returned has the `log[level]` methods but is otherwise an\n`are-we-there-yet` `Tracker` object.\n\n## log.newStream(name, todo, weight)\n\nThis adds a new `are-we-there-yet` stream tracker to the progress tracker. The\nobject returned has the `log[level]` methods but is otherwise an\n`are-we-there-yet` `TrackerStream` object.\n\n## log.newGroup(name, weight)\n\nThis adds a new `are-we-there-yet` tracker group to the progress tracker. The\nobject returned has the `log[level]` methods but is otherwise an\n`are-we-there-yet` `TrackerGroup` object.\n\n# Events\n\nEvents are all emitted with the message object.\n\n* `log` Emitted for all messages\n* `log.<level>` Emitted for all messages with the `<level>` level.\n* `<prefix>` Messages with prefixes also emit their prefix as an event.\n\n# Style Objects\n\nStyle objects can have the following fields:\n\n* `fg` {String} Color for the foreground text\n* `bg` {String} Color for the background\n* `bold`, `inverse`, `underline` {Boolean} Set the associated property\n* `bell` {Boolean} Make a noise (This is pretty annoying, probably.)\n\n# Message Objects\n\nEvery log event is emitted with a message object, and the `log.record`\nlist contains all of them that have been created.  They have the\nfollowing fields:\n\n* `id` {Number}\n* `level` {String}\n* `prefix` {String}\n* `message` {String} Result of `util.format()`\n* `messageRaw` {Array} Arguments to `util.format()`\n\n# Blocking TTYs\n\nWe use [`set-blocking`](https://npmjs.com/package/set-blocking) to set\nstderr and stdout blocking if they are tty's and have the setBlocking call.\nThis is a work around for an issue in early versions of Node.js 6.x, which\nmade stderr and stdout non-blocking on OSX. (They are always blocking\nWindows and were never blocking on Linux.) `npmlog` needs them to be blocking\nso that it can allow output to stdout and stderr to be interlaced.\n", "time": {"created": "2022-01-26T13:03:15.210Z", "modified": "2025-06-05T07:17:34.805Z", "6.0.0": "2021-11-16T17:35:18.641Z", "5.0.1": "2021-09-02T14:33:09.109Z", "5.0.0": "2021-07-22T17:06:12.924Z", "4.1.2": "2017-06-26T23:23:14.534Z", "4.1.1": "2017-06-26T20:56:38.797Z", "4.1.0": "2017-05-05T21:04:44.607Z", "4.0.2": "2016-12-12T19:52:18.463Z", "4.0.1": "2016-11-17T01:14:05.535Z", "4.0.0": "2016-07-19T00:29:10.793Z", "3.1.2": "2016-06-16T10:30:53.032Z", "3.1.1": "2016-06-16T06:52:31.337Z", "3.1.0": "2016-06-16T01:25:34.864Z", "3.0.0": "2016-06-07T01:30:36.683Z", "2.0.4": "2016-05-19T00:10:38.359Z", "2.0.3": "2016-03-16T00:43:56.419Z", "2.0.2": "2016-01-28T00:38:33.387Z", "2.0.1": "2016-01-21T18:58:56.587Z", "2.0.0": "2015-11-05T07:19:06.878Z", "1.2.1": "2015-05-19T01:40:29.670Z", "1.2.0": "2015-02-28T23:07:02.634Z", "1.1.0": "2015-02-01T18:37:29.804Z", "1.0.0": "2015-01-07T17:41:44.265Z", "0.1.1": "2014-06-13T23:51:04.789Z", "0.1.0": "2014-06-06T04:26:18.361Z", "0.0.6": "2013-10-24T07:15:02.897Z", "0.0.5": "2013-10-24T07:05:11.752Z", "0.0.4": "2013-07-11T06:16:28.666Z", "0.0.3": "2013-06-20T01:56:10.752Z", "0.0.2": "2012-06-06T21:10:11.014Z", "0.0.1": "2012-06-06T09:08:18.466Z", "6.0.1": "2022-02-09T18:57:17.076Z", "6.0.2": "2022-04-20T21:20:42.967Z", "7.0.0": "2022-10-14T17:52:29.016Z", "7.0.1": "2022-10-19T05:59:37.106Z"}, "versions": {"6.0.0": {"author": {"name": "GitHub Inc."}, "name": "npmlog", "description": "logger for npm", "version": "6.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "lib/log.js", "scripts": {"test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint '**/*.js'", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "npm-template-check", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap"}, "dependencies": {"are-we-there-yet": "^2.0.0", "console-control-strings": "^1.1.0", "gauge": "^4.0.0", "set-blocking": "^2.0.0"}, "devDependencies": {"@npmcli/template-oss": "^2.3.0", "tap": "^15.0.9"}, "license": "ISC", "templateVersion": "2.3.0", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "tap": {"branches": 95}, "gitHead": "4c489761818a03913bf2f132564acc83b9bf400b", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@6.0.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.3", "dist": {"integrity": "sha512-03ppFRGlsyUaQFbGC2C8QWJN/C/K7PsfyD9aQdhVKAQIH4sQBc8WASqFBP7O+Ut4d2oo5LoeoboB3cGdBZSp6Q==", "shasum": "ba9ef39413c3d936ea91553db7be49c34ad0520c", "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-6.0.0.tgz", "fileCount": 4, "unpackedSize": 16841, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk+vWCRA9TVsSAnZWagAAMb8P/itFHGIET+RRqi5yXhAb\ndcGQa44rJM1TeJzKMX/1G49v4a9tkIJqzbNv6fI2baH3fefheJkbb6ILpI+j\nwCszA7B+1Xw/bM0bIRW4UvZZp+MZxTGzXhDkPsNWJ1v1zPeTTCSPxahqmYyv\n3ZhZcRgA79kkLBfXqd2EoZ5W0/oQ92sQ7MjMEMnnS+HY7o1G9FzG0PwfNi5A\nuOFcYJQcZck+PP38nqQofqH7mWkdAlGhgl9ahDCfXrtueCUsh+adYslzXJLs\nND6y/L1Y1z3pOldoOlph8IDvpf9sJSOmHNj3qDhhbSLMCbSl6bTYNBJ3GVK4\nmkMrt1t4uKVMMJ6jLeXRf5b4lV4ZGuipdDsFT65p//O2KDp8cVsfFRJTt+ZA\n4fiHSlaycFFQh6NJZRMSa1SO5C57plfHRq2Hof4Hf3W3MdAYz2eXmgvCrvFa\nz1658BIKUlZgCaVTCU+2L6BzmLin9EjV5Zv27ggg5FCStBcogjxSZgtU+rAb\nPhs2D7+2+2fe74kESFHgcofaWW1smUUyNP8ft85Lqw53yu3BSrTIfyY2D+MD\nBBsFV1CkRavFxR1PxvvVjK2X4H74KxX+rv14MFaA7WnTL3vwpNCXuU2KOGKg\nXjF+Tl4i1KRu4shjHtvOW0+GMYomMIOowbXmO2qyxNZQzVXDhF7SCcoZDuvx\naENi\r\n=gqV2\r\n-----END PGP SIGNATURE-----\r\n", "size": 6015, "noattachment": false}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npmlog_6.0.0_1637084118152_0.858472544940055"}, "_hasShrinkwrap": false, "publish_time": 1637084118641, "_cnpm_publish_time": 1637084118641, "_cnpmcore_publish_time": "2021-12-13T06:50:09.673Z", "deprecated": "This package is no longer supported."}, "5.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "5.0.1", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js --branches=95", "npmclilint": "npmcli-lint", "lint": "npm run npmclilint -- \"*.*js\" \"test/**/*.*js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint --", "postsnap": "npm run lintfix --"}, "dependencies": {"are-we-there-yet": "^2.0.0", "console-control-strings": "^1.1.0", "gauge": "^3.0.0", "set-blocking": "^2.0.0"}, "devDependencies": {"@npmcli/lint": "^1.0.1", "tap": "^15.0.9"}, "license": "ISC", "gitHead": "37ac908491ed4081c8b90d6bf11fc355f5ad097a", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@5.0.1", "_nodeVersion": "14.17.5", "_npmVersion": "7.21.1", "dist": {"shasum": "f06678e80e29419ad67ab964e0fa69959c1eb8b0", "size": 5901, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-5.0.1.tgz", "integrity": "sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw=="}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npmlog_5.0.1_1630593188964_0.7168522351452797"}, "_hasShrinkwrap": false, "publish_time": 1630593189109, "_cnpm_publish_time": 1630593189109, "_cnpmcore_publish_time": "2021-12-13T06:50:09.945Z", "deprecated": "This package is no longer supported."}, "5.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "5.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js --branches=95", "npmclilint": "npmcli-lint", "lint": "npm run npmclilint -- \"*.*js\" \"test/**/*.*js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint --", "postsnap": "npm run lintfix --"}, "dependencies": {"are-we-there-yet": "^1.1.5", "console-control-strings": "^1.1.0", "gauge": "^3.0.0", "set-blocking": "^2.0.0"}, "devDependencies": {"@npmcli/lint": "^1.0.1", "tap": "^15.0.9"}, "license": "ISC", "gitHead": "15366fb7c90a0819dcf0fac1f95be7081a6f07a1", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@5.0.0", "_nodeVersion": "14.17.1", "_npmVersion": "7.20.0", "dist": {"shasum": "e6a41b556e9b34cb29ea132294676c07acb30efb", "size": 5894, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-5.0.0.tgz", "integrity": "sha512-ftpIiLjerL2tUg3dCqN8pOSoB90gqZlzv/gaZoxHaKjeLClrfJIEQ1Pdxi6qSzflz916Bljdy8dTWQ4J7hAFSQ=="}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npmlog_5.0.0_1626973572734_0.5359424754133315"}, "_hasShrinkwrap": false, "publish_time": 1626973572924, "_cnpm_publish_time": 1626973572924, "_cnpmcore_publish_time": "2021-12-13T06:50:10.237Z", "deprecated": "This package is no longer supported."}, "4.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "4.1.2", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "standard && tap test/*.js"}, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}, "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.3"}, "license": "ISC", "gitHead": "f7f9516d35b873c4e45b1aaeb78cff4e43b72c31", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@4.1.2", "_npmVersion": "5.0.4", "_nodeVersion": "8.1.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "08a7f2a8bf734604779a9efa4ad5cc717abb954b", "size": 6509, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-4.1.2.tgz", "integrity": "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npmlog-4.1.2.tgz_1498519394438_0.33615764300338924"}, "directories": {}, "publish_time": 1498519394534, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498519394534, "_cnpmcore_publish_time": "2021-12-13T06:50:10.521Z", "deprecated": "This package is no longer supported."}, "4.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "4.1.1", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "standard && tap test/*.js"}, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}, "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.3"}, "license": "ISC", "gitHead": "a1093e72aebee4febc807a0a6fcece7e253c06c4", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@4.1.1", "_npmVersion": "5.0.4", "_nodeVersion": "8.1.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "301349a832a751633ff63a42a230a6e7ecf02c6e", "size": 6489, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-4.1.1.tgz", "integrity": "sha512-iiAM9qm23XbN/IaaX6zX5pXxFALOjsjq0K4YhrrtBMh6KxrezMv1oPMwfFgqIcgykw/uey7Thg4yWWOcFjeZxw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npmlog-4.1.1.tgz_1498510598709_0.003092980245128274"}, "directories": {}, "publish_time": 1498510598797, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498510598797, "_cnpmcore_publish_time": "2021-12-13T06:50:10.832Z", "deprecated": "This package is no longer supported."}, "4.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "4.1.0", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "standard && tap test/*.js"}, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}, "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.3"}, "license": "ISC", "gitHead": "46f88c4bcb35a39b22aa5b65d0958221dadb2bbe", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@4.1.0", "_npmVersion": "5.0.0-beta.40", "_nodeVersion": "7.9.0", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "dc59bee85f64f00ed424efb2af0783df25d1c0b5", "size": 6481, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-4.1.0.tgz", "integrity": "sha512-ocolIkZYZt8UveuiDS0yAkkIjid1o7lPG8cYm05yNYzBn8ykQtaiPMEGp8fY9tKdDgm8okpdKzkvu1y9hUYugA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/npmlog-4.1.0.tgz_1494018281527_0.3773633665405214"}, "directories": {}, "publish_time": 1494018284607, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494018284607, "_cnpmcore_publish_time": "2021-12-13T06:50:11.180Z", "deprecated": "This package is no longer supported."}, "4.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "4.0.2", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "standard && tap test/*.js"}, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.1", "set-blocking": "~2.0.0"}, "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.0"}, "license": "ISC", "gitHead": "a3b7aed07790b674aa1fecfc81a61481abeaf882", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@4.0.2", "_shasum": "d03950e0e78ce1527ba26d2a7592e9348ac3e75f", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.2.0", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "dist": {"shasum": "d03950e0e78ce1527ba26d2a7592e9348ac3e75f", "size": 6435, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-4.0.2.tgz", "integrity": "sha512-EoRXwMcIkbPu0ufHuR6xqtN+oFW7HiULHVWENwMClAbZpE93wa0sZ8w1YmdEyRAQ0wakKFNXLVb2f441uHyJTA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/npmlog-4.0.2.tgz_1481572338217_0.2948465726803988"}, "directories": {}, "publish_time": 1481572338463, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481572338463, "_cnpmcore_publish_time": "2021-12-13T06:50:11.516Z", "deprecated": "This package is no longer supported."}, "4.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "4.0.1", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "standard && tap test/*.js"}, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.1", "set-blocking": "~2.0.0"}, "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.0"}, "license": "ISC", "gitHead": "c027c276f6f7e6c8d808767b0d611555e3ef5f61", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@4.0.1", "_shasum": "d14f503b4cd79710375553004ba96e6662fbc0b8", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.1.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "d14f503b4cd79710375553004ba96e6662fbc0b8", "size": 6471, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-4.0.1.tgz", "integrity": "sha512-TAleH95lxe4kHiUesHryWWrzHn53Mi2gk9Y+9/dX4ZlM59gnKJBVU4ifxZ9Iq4fJYMKY08MB9oaaPLUeVieJEw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/npmlog-4.0.1.tgz_1479345245313_0.32757814647629857"}, "directories": {}, "publish_time": 1479345245535, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479345245535, "_cnpmcore_publish_time": "2021-12-13T06:50:11.876Z", "deprecated": "This package is no longer supported."}, "4.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "4.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "standard && tap test/*.js"}, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.6.0", "set-blocking": "~2.0.0"}, "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.0"}, "license": "ISC", "gitHead": "3ca8823fdfa66f54c72adde3fd2c4e0237e6302b", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@4.0.0", "_shasum": "e094503961c70c1774eb76692080e8d578a9f88f", "_from": ".", "_npmVersion": "3.10.4", "_nodeVersion": "5.10.1", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "e094503961c70c1774eb76692080e8d578a9f88f", "size": 6230, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-4.0.0.tgz", "integrity": "sha512-4HyeePH0eTvisKApQnKqi1e44wDZERAJvfavvdu2FRcHJQR0EthB+wY5h5IiIzK4dHGHlmHeGWO8LKyjFhl2aQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/npmlog-4.0.0.tgz_1468888150556_0.3835553650278598"}, "directories": {}, "publish_time": 1468888150793, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468888150793, "_cnpmcore_publish_time": "2021-12-13T06:50:12.244Z", "deprecated": "This package is no longer supported."}, "3.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "3.1.2", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "standard && tap test/*.js"}, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.6.0", "set-blocking": "~2.0.0"}, "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.0"}, "license": "ISC", "gitHead": "444e237743fa1339cb91bea2d3f16f710be56984", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@3.1.2", "_shasum": "2d46fa874337af9498a2f12bb43d8d0be4a36873", "_from": ".", "_npmVersion": "3.9.6", "_nodeVersion": "4.4.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "2d46fa874337af9498a2f12bb43d8d0be4a36873", "size": 6222, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-3.1.2.tgz", "integrity": "sha512-M9EjtMS6UQU5jVhfdCBmvPup0/i7QIoDy38ERfX/sLpzr5gida2uCtvf0pE586OB7+/NQx7KZvwwomGRyHQXwA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/npmlog-3.1.2.tgz_1466073052641_0.36111341998912394"}, "directories": {}, "publish_time": 1466073053032, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466073053032, "_cnpmcore_publish_time": "2021-12-13T06:50:12.687Z", "deprecated": "This package is no longer supported."}, "3.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "3.1.1", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "standard && tap test/*.js"}, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.5.3", "set-blocking": "~2.0.0"}, "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.0"}, "license": "ISC", "gitHead": "f66e503c982d9b5c6b6646e984108356dc8fd4a6", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@3.1.1", "_shasum": "29477e7644cf46532782a7f2abe0405016ade755", "_from": ".", "_npmVersion": "3.9.2", "_nodeVersion": "4.4.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "29477e7644cf46532782a7f2abe0405016ade755", "size": 6195, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-3.1.1.tgz", "integrity": "sha512-891eC+Nx8LjOZXEBOQLJiudltKEtAWZ1jGWWmtAZb9/VZ07JYamKSNpMGV4Qz9F3YrqQTDVLjMoIlSTYHfuoQQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/npmlog-3.1.1.tgz_1466059950921_0.06024856795556843"}, "directories": {}, "publish_time": 1466059951337, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466059951337, "_cnpmcore_publish_time": "2021-12-13T06:50:13.090Z", "deprecated": "This package is no longer supported."}, "3.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "3.1.0", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "standard && tap test/*.js"}, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.5.2", "set-blocking": "~2.0.0"}, "devDependencies": {"standard": "~7.1.2", "tap": "~5.7.0"}, "license": "ISC", "gitHead": "accfd23d05a6bcd0b8ff05e7da840964482b7a3c", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@3.1.0", "_shasum": "52fdba095f72aa7c6fc1106b570ce5932d039bb7", "_from": ".", "_npmVersion": "3.9.2", "_nodeVersion": "4.4.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "52fdba095f72aa7c6fc1106b570ce5932d039bb7", "size": 6015, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-3.1.0.tgz", "integrity": "sha512-gkDlSwxxuGXdlXFaMOe1+SLQJZ1Fm7L+nKjeUwsqEPFnVRvVDJTdIa8SOmTpPD62sZlbsfSqSDLdYnsw5+rpOQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/npmlog-3.1.0.tgz_1466040332571_0.0009939647279679775"}, "directories": {}, "publish_time": 1466040334864, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466040334864, "_cnpmcore_publish_time": "2021-12-13T06:50:13.517Z", "deprecated": "This package is no longer supported."}, "3.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "3.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.1", "are-we-there-yet": "~1.1.2", "gauge": "~2.5.0", "set-blocking": "~2.0.0"}, "devDependencies": {"tap": "~5.7.0"}, "license": "ISC", "gitHead": "87f698677996db477309c490c8a5b8371f65cc2b", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@3.0.0", "_shasum": "5b75a32bffcfd6ebf046487a302182224cd17c9b", "_from": ".", "_npmVersion": "3.9.2", "_nodeVersion": "4.4.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "5b75a32bffcfd6ebf046487a302182224cd17c9b", "size": 5258, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-3.0.0.tgz", "integrity": "sha512-dJfGNRmZvXgCOQKvQoQbIWEcDGJ+17fdpmqHZk6bvN68C5wKcreY+kAfsDJnNSdoen6X4vHWrHPtOA4wCVVMFQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/npmlog-3.0.0.tgz_1465263036089_0.9853639076463878"}, "directories": {}, "publish_time": 1465263036683, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465263036683, "_cnpmcore_publish_time": "2021-12-13T06:50:13.941Z", "deprecated": "This package is no longer supported."}, "2.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "2.0.4", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "files": ["log.js"], "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.1", "are-we-there-yet": "~1.1.2", "gauge": "~1.2.5"}, "devDependencies": {"tap": "~5.7.0"}, "license": "ISC", "gitHead": "3732fd4ba1ca2d47c6102343e6c3fb7e66df7fe5", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@2.0.4", "_shasum": "98b52530f2514ca90d09ec5b22c8846722375692", "_from": ".", "_npmVersion": "3.9.1", "_nodeVersion": "5.10.1", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "98b52530f2514ca90d09ec5b22c8846722375692", "size": 4934, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-2.0.4.tgz", "integrity": "sha512-DaL6RTb8Qh4tMe2ttPT1qWccETy2Vi5/8p+htMpLBeXJTr2CAqnF5WQtSP2eFpvaNbhLZ5uilDb98mRm4Q+lZQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/npmlog-2.0.4.tgz_1463616637725_0.461703865788877"}, "directories": {}, "publish_time": 1463616638359, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463616638359, "_cnpmcore_publish_time": "2021-12-13T06:50:14.440Z", "deprecated": "This package is no longer supported."}, "2.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "2.0.3", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.1", "are-we-there-yet": "~1.1.2", "gauge": "~1.2.5"}, "devDependencies": {"tap": "~5.7.0"}, "license": "ISC", "gitHead": "9dfe26296118ceb5443e76f347f256c35e7ca999", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@2.0.3", "_shasum": "020f99351f0c02e399c674ba256e7c4d3b3dd298", "_from": ".", "_npmVersion": "3.8.1", "_nodeVersion": "4.2.2", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "020f99351f0c02e399c674ba256e7c4d3b3dd298", "size": 13077, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-2.0.3.tgz", "integrity": "sha512-fi2RmbJV0702nTZJQsGg8lVOU19VeIUZp+LPd7ce2XaZHe77TEcsKUlpBp6jGdARYVCMJPcHTaIYCU5LY7/tsQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/npmlog-2.0.3.tgz_1458089035965_0.5096880353521556"}, "directories": {}, "publish_time": 1458089036419, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458089036419, "_cnpmcore_publish_time": "2021-12-13T06:50:14.913Z", "deprecated": "This package is no longer supported."}, "2.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "2.0.2", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.1", "are-we-there-yet": "~1.0.6", "gauge": "~1.2.5"}, "devDependencies": {"tap": "~5.1.2"}, "license": "ISC", "gitHead": "79dc582bf1ce4d2010454d89738a0a4dbd113be9", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@2.0.2", "_shasum": "d0470238b9697b7c3c4d16bdea65a00b12a464ab", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.4.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "d0470238b9697b7c3c4d16bdea65a00b12a464ab", "size": 7131, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-2.0.2.tgz", "integrity": "sha512-16UagPcQMyS4ZVwKCKM+mxdHMtrDcGFj9D8UyWzd8p0iKv4qZG3ZkXImTlv9+HYY/TvPhSWDlKS+cKGqEop1og=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1453941513387, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453941513387, "_cnpmcore_publish_time": "2021-12-13T06:50:15.418Z", "deprecated": "This package is no longer supported."}, "2.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "2.0.1", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.1", "are-we-there-yet": "~1.0.5", "gauge": "~1.2.4"}, "devDependencies": {"tap": "~5.1.1"}, "license": "ISC", "gitHead": "c4a1c3e1acd5a324398c3b2d3f9daffd3175b6ed", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@2.0.1", "_shasum": "c2e704d3eb50de738c903f7172001d60bf673fa0", "_from": ".", "_npmVersion": "3.5.4", "_nodeVersion": "5.4.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "c2e704d3eb50de738c903f7172001d60bf673fa0", "size": 7134, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-2.0.1.tgz", "integrity": "sha512-xkPfzGFtodm2Zav19QQs34mTbtZqLTWQoe5+RgbHCkgU5UJUl+HXyuADj6I8KhsMoC+42iNhXjyq+GzZIPEsmg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1453402736587, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453402736587, "_cnpmcore_publish_time": "2021-12-13T06:50:15.949Z", "deprecated": "This package is no longer supported."}, "2.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "2.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.0", "are-we-there-yet": "~1.0.0", "gauge": "~1.2.0"}, "devDependencies": {"tap": "~2.2.0"}, "license": "ISC", "gitHead": "6eaa3f8eec672bb7b56a4df9b55dbfff3b9c6a71", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@2.0.0", "_shasum": "4076c200a3dda51133e6f3cf052130105f78bbdf", "_from": ".", "_npmVersion": "2.14.9", "_nodeVersion": "4.2.1", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "dist": {"shasum": "4076c200a3dda51133e6f3cf052130105f78bbdf", "size": 7111, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-2.0.0.tgz", "integrity": "sha512-dK+uobtc6MZiF2OegYLq5wavFTRLa1d/IVpJmDQxWcYoD51A9Y8STJ8sWIoCl0iOFtgy55ub6qQhOUFoIzGq7Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1446707946878, "_hasShrinkwrap": false, "_cnpm_publish_time": 1446707946878, "_cnpmcore_publish_time": "2021-12-13T06:50:16.467Z", "deprecated": "This package is no longer supported."}, "1.2.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "1.2.1", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.0", "are-we-there-yet": "~1.0.0", "gauge": "~1.2.0"}, "devDependencies": {"tap": ""}, "license": "ISC", "gitHead": "4e1a73a567036064ded425a7d48c863d53550b4f", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "homepage": "https://github.com/isaacs/npmlog#readme", "_id": "npmlog@1.2.1", "_shasum": "28e7be619609b53f7ad1dd300a10d64d716268b6", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "28e7be619609b53f7ad1dd300a10d64d716268b6", "size": 6950, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-1.2.1.tgz", "integrity": "sha512-1J5KqSRvESP6XbjPaXt2H6qDzgizLTM7x0y1cXIjP2PpvdCqyNC7TO3cPRKsuYlElbi/DwkzRRdG2zpmE0IktQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431999629670, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431999629670, "_cnpmcore_publish_time": "2021-12-13T06:50:17.078Z", "deprecated": "This package is no longer supported."}, "1.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "1.2.0", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.0", "are-we-there-yet": "~1.0.0", "gauge": "~1.2.0"}, "devDependencies": {"tap": ""}, "license": "BSD", "gitHead": "1fe2892a8b9dacb775d4fb365315865f421f4ca9", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "homepage": "https://github.com/isaacs/npmlog", "_id": "npmlog@1.2.0", "_shasum": "b512f18ae8696a0192ada78ba00c06dbbd91bafb", "_from": ".", "_npmVersion": "2.6.0", "_nodeVersion": "1.1.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "b512f18ae8696a0192ada78ba00c06dbbd91bafb", "size": 7455, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-1.2.0.tgz", "integrity": "sha512-e7yPyzsH3InhdHei2+flGMm1hLsWh5N4c7oO2nD4DxegLE41yUGTdriewBEdiBlB1oGvlZ5Ol5j0kLz4jF9qig=="}, "directories": {}, "publish_time": 1425164822634, "_hasShrinkwrap": false, "_cnpm_publish_time": 1425164822634, "_cnpmcore_publish_time": "2021-12-13T06:50:17.695Z", "deprecated": "This package is no longer supported."}, "1.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "1.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.0", "are-we-there-yet": "~1.0.0", "gauge": "~1.1.0"}, "devDependencies": {"tap": ""}, "license": "BSD", "gitHead": "d8e2bd3976cc052816ea3eaea2db45e257763d74", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "homepage": "https://github.com/isaacs/npmlog", "_id": "npmlog@1.1.0", "_shasum": "8744168148df1ce3f3387c0bc38154883b4af5f4", "_from": ".", "_npmVersion": "2.4.0", "_nodeVersion": "0.10.33", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "8744168148df1ce3f3387c0bc38154883b4af5f4", "size": 7253, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-1.1.0.tgz", "integrity": "sha512-XtoxhFb6c4dwClIdQRxs9UPq5qtAaNJGX/uF9lITsO0TpZc74OrdMQgFKSS4TMRHH5DoAnI2vZQx2XW/kpsnjg=="}, "directories": {}, "publish_time": 1422815849804, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422815849804, "_cnpmcore_publish_time": "2021-12-13T06:50:18.373Z", "deprecated": "This package is no longer supported."}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.0", "are-we-there-yet": "~1.0.0", "gauge": "~1.0.2"}, "devDependencies": {"tap": ""}, "license": "BSD", "gitHead": "09b2976531b85f39d2c509ebb46c04f6bec8f3a8", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "homepage": "https://github.com/isaacs/npmlog", "_id": "npmlog@1.0.0", "_shasum": "ed2f290b60316887c39e0da9f09f8d13847cef0f", "_from": ".", "_npmVersion": "2.1.11", "_nodeVersion": "0.10.33", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "ed2f290b60316887c39e0da9f09f8d13847cef0f", "size": 7094, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-1.0.0.tgz", "integrity": "sha512-tLHonOJv0KRb/h2DSYCSwBpd4jJtf++R8moSi+WXNf4tEPc9CuTONGrWoCjMbjaDO+JMTeEMTkhjQgMkcGIPXA=="}, "directories": {}, "publish_time": 1420652504265, "_hasShrinkwrap": false, "_cnpm_publish_time": 1420652504265, "_cnpmcore_publish_time": "2021-12-13T06:50:18.941Z", "deprecated": "This package is no longer supported."}, "0.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "0.1.1", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.3.0"}, "devDependencies": {"tap": ""}, "license": "BSD", "gitHead": "b58e360cd99db707d1191ce6125ae53d79f075a1", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "homepage": "https://github.com/isaacs/npmlog", "_id": "npmlog@0.1.1", "_shasum": "8b9b9e4405d7ec48c31c2346965aadc7abaecaa5", "_from": ".", "_npmVersion": "1.4.15", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "8b9b9e4405d7ec48c31c2346965aadc7abaecaa5", "size": 5335, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-0.1.1.tgz", "integrity": "sha512-gEpnmIBogjDV2xuCNl7ooAkDYKnjiLyRcKTXbkg4sO2JZ8MDzo1VTKioUk7In4eedJ0fGzyasJ/f6P0ZKG4Wyg=="}, "directories": {}, "publish_time": 1402703464789, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402703464789, "_cnpmcore_publish_time": "2021-12-13T06:50:19.486Z", "deprecated": "This package is no longer supported."}, "0.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "0.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "^0.3.0"}, "devDependencies": {"tap": ""}, "license": "BSD", "gitHead": "e1bc90b9ce78900fa0b1c30bcaaa630de99763c8", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "homepage": "https://github.com/isaacs/npmlog", "_id": "npmlog@0.1.0", "_shasum": "c88336df9426979b86d848d35357f4e6a558bd90", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "c88336df9426979b86d848d35357f4e6a558bd90", "size": 5267, "noattachment": false, "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-0.1.0.tgz", "integrity": "sha512-HYCrcMjEZgmMhfzdC3+Xi3of8uzTTLzRSmgHW8pTvQ/47r362eSSUTwDNWmQRVQ4Y83auUo58ILccqSLeY/OBw=="}, "directories": {}, "publish_time": 1402028778361, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402028778361, "_cnpmcore_publish_time": "2021-12-13T06:50:20.107Z", "deprecated": "This package is no longer supported."}, "0.0.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "0.0.6", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.2.1"}, "devDependencies": {"tap": ""}, "license": "BSD", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "_id": "npmlog@0.0.6", "dist": {"tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-0.0.6.tgz", "shasum": "685043fe71aa1665d6e3b2acef180640caf40873", "size": 5263, "noattachment": false, "integrity": "sha512-8omfjyaSWsT0lOJq08ccWEUipV49JbY0joZ3fUA9OvC4nHOmpBVSEdnzN7uKvZ1eBO9bYR9QVvqiBWOc4KbfFw=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1382598902897, "_hasShrinkwrap": false, "_cnpm_publish_time": 1382598902897, "_cnpmcore_publish_time": "2021-12-13T06:50:20.672Z", "deprecated": "This package is no longer supported."}, "0.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "0.0.5", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.2.1"}, "devDependencies": {"tap": ""}, "license": "BSD", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "_id": "npmlog@0.0.5", "dist": {"tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-0.0.5.tgz", "shasum": "73c25116265cc9ed252bef4bb65e423a24836308", "size": 5272, "noattachment": false, "integrity": "sha512-WueimYqEm2uVm1yZ5LQu0ig/etEKSJ95sjvI3+T6WKC/rCJVaU3OGGhczk4l/b8o5CegKMHvS3ICIMdoWb9LZw=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1382598311752, "_hasShrinkwrap": false, "_cnpm_publish_time": 1382598311752, "_cnpmcore_publish_time": "2021-12-13T06:50:21.289Z", "deprecated": "This package is no longer supported."}, "0.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "0.0.4", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.1.2"}, "devDependencies": {"tap": ""}, "license": "BSD", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "_id": "npmlog@0.0.4", "dist": {"tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-0.0.4.tgz", "shasum": "a12a7418606b7e0183a2851d97a8729b9a0f3837", "size": 5264, "noattachment": false, "integrity": "sha512-pG1bYtYXWshDw0XL/xZJw6SQlbQWVgREkq1K4cow9UXglL4byxrjcDsU63JtGGoWDIFQHlgSeP29VH0ts+c5cA=="}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373523388666, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373523388666, "_cnpmcore_publish_time": "2021-12-13T06:50:21.944Z", "deprecated": "This package is no longer supported."}, "0.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.1.2"}, "devDependencies": {"tap": ""}, "license": "BSD", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "_id": "npmlog@0.0.3", "dist": {"tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-0.0.3.tgz", "shasum": "c424ad1531af402eef8da201fc3d63bdbd37dacb", "size": 5255, "noattachment": false, "integrity": "sha512-knXDtgdfuorLFlyY1pUzqHdHZsH1qbNYxQm8jM+rvQUPRmPuW23BbX9OLQYY7FGbdc48z9LkDEgZDMOtNaH63A=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1371693370752, "_hasShrinkwrap": false, "_cnpm_publish_time": 1371693370752, "_cnpmcore_publish_time": "2021-12-13T06:50:22.634Z", "deprecated": "This package is no longer supported."}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.1.2"}, "devDependencies": {"tap": ""}, "license": "BSD", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "npmlog@0.0.2", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.7.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-0.0.2.tgz", "shasum": "f0cf4b2c519950c00e91ba8e2868b62bf86254f6", "size": 5249, "noattachment": false, "integrity": "sha512-qkrCR75q6/HmU3SWLHb4cr5oAQa+xUp6Vh6/sOkO7MGbSaNoCS7iubg8XTwJe48PqzlCHslzyux+Vmq1hnfLAA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1339017011014, "_hasShrinkwrap": false, "_cnpm_publish_time": 1339017011014, "_cnpmcore_publish_time": "2021-12-13T06:50:23.293Z", "deprecated": "This package is no longer supported."}, "0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npmlog", "description": "logger for npm", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/npmlog.git"}, "main": "log.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"ansi": "~0.1.2"}, "devDependencies": {"tap": ""}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "npmlog@0.0.1", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.7.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-0.0.1.tgz", "shasum": "121bf400effe0f20c05521593f1344b4befd5e08", "size": 4351, "noattachment": false, "integrity": "sha512-5121eAdx5zS7WmGHecb7qIo5Bj26HFXKRb7j/Nq7Ag+6osFCvH4mH1MnJMFGvao7K0mWb6vykFY4USHVpk/fYA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1338973698466, "_hasShrinkwrap": false, "_cnpm_publish_time": 1338973698466, "_cnpmcore_publish_time": "2021-12-13T06:50:23.931Z", "deprecated": "This package is no longer supported."}, "6.0.1": {"author": {"name": "GitHub Inc."}, "name": "npmlog", "description": "logger for npm", "version": "6.0.1", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "lib/log.js", "scripts": {"test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint '**/*.js'", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "npm-template-check", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "template-copy": "npm-template-copy --force"}, "dependencies": {"are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0", "gauge": "^4.0.0", "set-blocking": "^2.0.0"}, "devDependencies": {"@npmcli/template-oss": "^2.7.1", "tap": "^15.1.6"}, "license": "ISC", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "tap": {"branches": 95}, "templateOSS": {"version": "2.7.1"}, "gitHead": "3c31adc8fa6e64f8d8c4f735d992efa6faca50d0", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@6.0.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.1", "dist": {"integrity": "sha512-BTHDvY6nrRHuRfyjt1MAufLxYdVXZfd099H4+i1f0lPywNQyI4foeNXJRObB/uy+TYqUW0vAD9gbdSOXPst7Eg==", "shasum": "06f1344a174c06e8de9c6c70834cfba2964bba17", "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-6.0.1.tgz", "fileCount": 4, "unpackedSize": 16929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBA6NCRA9TVsSAnZWagAAmZcQAJzTdklvgx9WMBBmWExv\n72Syx/sgDcf17zOSnPGwKH0HvpwnGaqJ+E0cP2bm3sa/HXJTSkVkNYmjHrRq\n/n4uML/giatB30+MuMEP50ZI+TXh3vkpY1++Gp1LWi6NjL8JkcSZIAoaeK9x\niucAgKu0XhYgIJHv5NM2ijEOnZMvw2zaJWSerm6fjMn/F9xKrP5Y8T1lH5w3\nwywa9/1o9WZHoGGv6tRRiSZvz9gJ+ulU5WuiK0nUdZrYLVFqa6O1KU9QPVJ/\nc9nnNIb8w7h5DunwUVVzYREjgoZ/j0QmZDhRFWVZEu0Q/Gc/26HHqLvtQ6UQ\njDNK5DhePjjE2/9ADrS3Naq0yRC2bX8jAbPLw69xXkrJeDVxtXcudiULroY6\n5pe0bsCoa9R0gbryaHqouFfv4UNacQaa+A3Ag9FRyxB0hwdd4/WdpeGisCEx\neXDbb14QlYzecd7bMvY7Yo3rAWSVtV+B3e1Vmll6TNQ+KkGjpWtHd6BsDMVM\nAZA/K1GWR4gwueEWTypJeT3+YuHxq3WVgLd0njC0YB9cVteXyT3Xcqa2p1Pq\nBBrIDyhwzIelUZBrYv6H85o+elAU/imKAOgblEIiOmyNlbT1ECgDCb9feUXA\njp8JbTVphX2Ttmz3grlUDdir2UA8KRRF8WWCSViteveirQP51K6lFQqMZOO5\n6QY0\r\n=7ije\r\n-----END PGP SIGNATURE-----\r\n", "size": 6037}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npmlog_6.0.1_1644433036914_0.4705778977788231"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-09T18:57:27.690Z", "deprecated": "This package is no longer supported."}, "6.0.2": {"author": {"name": "GitHub Inc."}, "name": "npmlog", "description": "logger for npm", "version": "6.0.2", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "lib/log.js", "scripts": {"test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "dependencies": {"are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0", "gauge": "^4.0.3", "set-blocking": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.4.1", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "tap": {"branches": 95}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.4.1"}, "gitHead": "87e9ff0c40065bd1242a19d3f486ee750558c618", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@6.0.2", "_nodeVersion": "16.14.2", "_npmVersion": "8.7.0", "dist": {"integrity": "sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==", "shasum": "c8166017a42f2dea92d6453168dd865186a70830", "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-6.0.2.tgz", "fileCount": 4, "unpackedSize": 17096, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbRBWcBsZrbWZtrpo6HqKxbuGDydOeES1INxipVH8orwIgDa4GBjQ4g9IFb2SvBYz4UZ7iouodVRhReMrsx+ZOD6g="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYHkrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiPw//Wuld0zPI2QP8SgFD3GREYJJ60FbCxVG2w/YQ913KZory0F6Z\r\nksoSsy3tAfxKjwETJ+1r1r3lQXs2Heq1pP9Kb79XRFee/tz5L/UbyBzOhyMZ\r\nL6jeL1TuPiAlDNWuhNEfN+ZRCOQz0tQx0JC7pAtAmzGVC3/8axWZ4vjjp/NU\r\n5KleW2WKCfkFC3IGP9by4hJ2sJcS+qkLX3WfuK+Xpm8MgSEoega20DFVJtj4\r\nAVn6zIC1rhs8QVHx9AM6rVbPr9e7h0GRscXgTreBImyhCT5Zy5L5hriF5kcW\r\n1BySY5QelWKSUmiTw4wV8FriC7OW7H/m4Egl4JYjmxB5Eq6Cdr7EGG4zJEJX\r\n1zJUtTjFnngkM+2TNEHGRjrNc0naIzAlmGkY8gh0CE81S6lUrgno6IfMi9tI\r\nB0Iq+RcCwSdOQHXVq4/nk9ukJ061iC3hgO60P3jWagXN8+VatArdYKrOdTcN\r\nxdAXglBraXjTqse1cVbwWxQdRPMSgbfpYvTudMnJ8vVYzgnwTRNi9IF2ZlBr\r\nEP+3OL0REMBzLsZtMDDE9IFZFF3LX6ySreQxlVztLU3vsjeTscv8v1cwGPKa\r\nSY7bqJh/bJH5wWzN0zensGols1HQl6OVpEbWpRtpglsEnsOwRsdhi4t20dUP\r\nFZx9whkvTN8LYxq0+0jXz/dXGoMcqSOxFf0=\r\n=lUyC\r\n-----END PGP SIGNATURE-----\r\n", "size": 6091}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npmlog_6.0.2_1650489642740_0.8093656921374428"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-20T21:20:46.284Z", "deprecated": "This package is no longer supported."}, "7.0.0": {"author": {"name": "GitHub Inc."}, "name": "npmlog", "description": "logger for npm", "version": "7.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "lib/log.js", "scripts": {"test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "dependencies": {"are-we-there-yet": "^4.0.0", "console-control-strings": "^1.1.0", "gauge": "^5.0.0", "set-blocking": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.5.1", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "tap": {"branches": 95, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.5.1"}, "gitHead": "040c66367057ee96514b1297c1347c7468f2f3c5", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@7.0.0", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-p+OVCIQx1Rehplt2DNgBERrKtE5Ej0/rqdcNz5PbohpKHDPprGAl142qQuozmzWUM9uNjvstEO+A92CwytJPcQ==", "shasum": "09731bbb33019813704d6635b55f35a1b1784b34", "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-7.0.0.tgz", "fileCount": 4, "unpackedSize": 17045, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB19wnJEW1lJ9QKQXFpOuamd2IhoJeUteKtr+NEiSENkAiB8DVoMGo+9IB+RetWy7to82AWEywY/WyKRzZPUiKfZTw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSaHdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAmw/+OrV2bW+IQs9H9I1Iz4f+84g0jdYSU40Hn6l4SvxyrYvAZEUg\r\njaaNu3hoD6N6yhAhXQNJbVD6DjV6S6AZoNDcANP4/lc3r8Z315VcQWjbMQt/\r\njjCAuJfkcdvVEcn1Cv/wYj6zm+iEc5jsjontmSRvMT/xV4Z+eaqMkrvHDlhl\r\n+XsEyiAPzmczQldI3gHoCeHA6ov8PjAzsCxrrPZIia/NNxcwGP8sSNakFQ4U\r\nOjT+PmtBPDsZPjH7yIaVzZjT7aFpsdwtuvBpCFIaId3J+BiaawZ1trWRVo1z\r\ng+86X/cSjPYNxIKR/6b8s+3+45MKTvqsexK+OiUbxIXTYxIf0nmARdMQoF4k\r\nSC9+xkPnrayAnK8oo1vCtpiusBqsIuuG6oaR2l+HuTrsqnZ/q66AG/I9WY2z\r\nGewTv9Yxa+NeFme2ob0PaJIVhPE0aXmc3qfBMrbkWI+6rT3MQKTRACrjGBTV\r\n7U2PDyJv5jvuWVeLydvPA1UZT1Y74PsLBvfUtncHeqBJHvXZNAPvwKuPi0N1\r\n5kG2i7s09gy6kHrYe0nPqQNgC/a2fbgwflby6c7PV726SLwjXu5pQ9lmTtfR\r\n/p1zhOdPeVcBD2iWuJX2NJJ8HK7CrmWOvXF2NKBDEtivAO5U6fwuFkvHJEWt\r\n8BisrrA4r9g3fBYUJAb71bvNr0U94vBL3H8=\r\n=mcz1\r\n-----END PGP SIGNATURE-----\r\n", "size": 6080}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npmlog_7.0.0_1665769948842_0.4028305437232067"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-15T07:52:12.572Z", "deprecated": "This package is no longer supported."}, "7.0.1": {"author": {"name": "GitHub Inc."}, "name": "npmlog", "description": "logger for npm", "version": "7.0.1", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "main": "lib/log.js", "scripts": {"test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "dependencies": {"are-we-there-yet": "^4.0.0", "console-control-strings": "^1.1.0", "gauge": "^5.0.0", "set-blocking": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.6.1", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "tap": {"branches": 95, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.6.1"}, "gitHead": "ae1f107953b0882ea2c44989e12d5ee08849663b", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "_id": "npmlog@7.0.1", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-uJ0YFk/mCQpLBt+bxN88AKd+gyqZvZDbtiNxk6Waqcj2aPRyfVx8ITawkyQynxUagInjdYT1+qj4NfA5KJJUxg==", "shasum": "7372151a01ccb095c47d8bf1d0771a4ff1f53ac8", "tarball": "https://registry.npmmirror.com/npmlog/-/npmlog-7.0.1.tgz", "fileCount": 4, "unpackedSize": 17023, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYUn00VadIpdhJ5gCnWDJXjdZQ81awth5qgaYsQrSM1QIgM9rvN8MjU/DOfpR7ETiCR5OlQ/YSRGQbtsA4lUSI570="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjT5JJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDQQ//XnU3qP0DSlBFajvrOJALD/TVbvLxce3rm9YqAjV7SOxOFmZD\r\nqgtXNfYpdCcJ7bqn8OD9445M7ulQiUAmO+UAxSevtNTfC/yv49YiM2RSGOMB\r\nmxThnWDMqF4VpB8XuYJ+8LrDDWXas3Yxzklt4gMeAuzBJixy3l2yJ8CDUzJe\r\ngh+U9jCE6PFtiN7CNayRnnFKsyTk2FLGDR5+hxmZWihvp7kkh7Xa8pD9AgZZ\r\nhX53glzjWmfpf8JeBdTqLq0Ojdr7hcSekxlldvAVgxRYrrgUqMRVI5kzpwHE\r\nbjRs/Cuqvg4Yz11rH0p1+k0Gd9QWBJRERfgGrjcy8NBhHhHtMAipWLRuEetb\r\nKp0o9UByOWuwqxKt3F2fSBq5Ea90fu4jzNENfLJvDYDxLBdL8Rc/qlyJXcPH\r\nBSQSgdfrPPciiWt8YemQvc+tW0noLR7v3hUv210Z0egCBJC96ePSHcjxacLX\r\ntKSIGDVaePvX/3Unt5lgNlFR4fzYwBZDmk5ypI5bEU30LL23zyDW2et+m/sL\r\nmLIWDAkcy1Im3HrX32caqkqw6ENWMlU6y0iknp8WXmwC74VYureyNtBUWMvi\r\n7QYKAS/jtLgUdsEY2+kgM/p+d78azKkd2LZKbqZsfhIGJ0rQuizuXHrL1l3c\r\niIRXhAVjdBXvFWfeQDjHhK6tuki8O/YylwA=\r\n=axJk\r\n-----END PGP SIGNATURE-----\r\n", "size": 6082}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/npmlog_7.0.1_1666159176927_0.8280302570565217"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-19T06:06:42.175Z", "deprecated": "This package is no longer supported."}}, "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "homepage": "https://github.com/npm/npmlog#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "_source_registry_name": "default"}