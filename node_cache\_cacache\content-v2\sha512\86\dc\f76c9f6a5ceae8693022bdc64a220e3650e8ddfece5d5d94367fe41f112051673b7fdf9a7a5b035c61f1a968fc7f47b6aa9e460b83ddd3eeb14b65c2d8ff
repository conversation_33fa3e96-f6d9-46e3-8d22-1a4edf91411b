{"_attachments": {}, "_id": "lodash.isnumber", "_rev": "2942-61f14a58b77ea98a7490d37c", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "description": "The lodash method `_.isNumber` exported as a module.", "dist-tags": {"latest": "3.0.3"}, "license": "MIT", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "lodash.isnumber", "readme": "# lodash.isnumber v3.0.3\n\nThe [lodash](https://lodash.com/) method `_.isNumber` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isnumber\n```\n\nIn Node.js:\n```js\nvar isNumber = require('lodash.isnumber');\n```\n\nSee the [documentation](https://lodash.com/docs#isNumber) or [package source](https://github.com/lodash/lodash/blob/3.0.3-npm-packages/lodash.isnumber) for more details.\n", "time": {"created": "2022-01-26T13:19:20.085Z", "modified": "2023-07-28T12:12:15.094Z", "3.0.3": "2016-02-03T07:28:49.514Z", "3.0.2": "2016-01-13T11:05:53.918Z", "3.0.1": "2015-03-25T23:35:54.870Z", "3.0.0": "2015-01-26T15:29:24.615Z", "2.4.1": "2013-12-03T17:14:35.262Z", "2.4.0": "2013-11-26T19:55:53.113Z", "2.3.0": "2013-11-11T16:47:46.064Z", "2.2.1": "2013-10-03T18:50:25.229Z", "2.2.0": "2013-09-29T22:09:50.053Z", "2.1.0": "2013-09-23T07:56:24.832Z", "2.0.0": "2013-09-23T07:38:02.142Z"}, "versions": {"3.0.3": {"name": "lodash.isnumber", "version": "3.0.3", "description": "The lodash method `_.isNumber` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isnumber"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isnumber@3.0.3", "_shasum": "3ce76810c5928d03352301ac287317f11c0b1ffc", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3ce76810c5928d03352301ac287317f11c0b1ffc", "size": 2187, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "integrity": "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isnumber-3.0.3.tgz_1454484528642_0.7204555512871593"}, "directories": {}, "publish_time": 1454484529514, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454484529514, "_cnpmcore_publish_time": "2021-12-14T06:01:39.986Z"}, "3.0.2": {"name": "lodash.isnumber", "version": "3.0.2", "description": "The lodash method `_.isNumber` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "isnumber"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isnumber@3.0.2", "_shasum": "282a00f60b02142a8870cbf8b549a28a08d63e3b", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "282a00f60b02142a8870cbf8b549a28a08d63e3b", "size": 2210, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-3.0.2.tgz", "integrity": "sha512-JZgo0TV8Cv3IYX3FrwHzuXH+aYba33abqXlCnYID35lAebLVT3kMmxVFb/x/joTEKGTipiQGz4LR9MIcgVhmcA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452683153918, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452683153918, "_cnpmcore_publish_time": "2021-12-14T06:01:40.239Z"}, "3.0.1": {"name": "lodash.isnumber", "version": "3.0.1", "description": "The modern build of lodash’s `_.isNumber` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isnumber@3.0.1", "_shasum": "628a1f3f198e2ddcd2b7eb9163540b6776255985", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "628a1f3f198e2ddcd2b7eb9163540b6776255985", "size": 2238, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-3.0.1.tgz", "integrity": "sha512-CPbcXi5uYF5EjI2DgfACYZm7HbgTEUcoa3y4P1p0Vilw237Lrd5PZfZiXqp3x3we1b4N4m2Di6gcIW/zJaW2iQ=="}, "directories": {}, "publish_time": 1427326554870, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427326554870, "_cnpmcore_publish_time": "2021-12-14T06:01:40.442Z"}, "3.0.0": {"name": "lodash.isnumber", "version": "3.0.0", "description": "The modern build of lodash’s `_.isNumber` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isnumber@3.0.0", "_shasum": "2bc60cbf4a11aa86ff2f1f1d42250d1cd2ec82b0", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "2bc60cbf4a11aa86ff2f1f1d42250d1cd2ec82b0", "size": 2253, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-3.0.0.tgz", "integrity": "sha512-YUYBtz8KjuhzCDmfEcTqJA6OWEZnougpB1ijAaGsevYAannmAjsnvfNZ5AR5YZauXI0nzw4amISFxpLGQhCH9Q=="}, "directories": {}, "publish_time": 1422286164615, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422286164615, "_cnpmcore_publish_time": "2021-12-14T06:01:40.624Z"}, "2.4.1": {"name": "lodash.isnumber", "version": "2.4.1", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isnumber@2.4.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-2.4.1.tgz", "shasum": "fa57e8890ed618d037da4b117af3f30f3fc2b260", "size": 2189, "noattachment": false, "integrity": "sha512-TEFqBAwhKXCQ6SNWSw+W+VxZoaW2FL63Bh1UaejzudRxD563IGARdVOCte28I5zIsnnz/yX45ATCJuXv3xjuUA=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386090875262, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386090875262, "_cnpmcore_publish_time": "2021-12-14T06:01:40.817Z"}, "2.4.0": {"name": "lodash.isnumber", "version": "2.4.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isnumber@2.4.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-2.4.0.tgz", "shasum": "d492ab459bd84c1b663f6bcbdccea512f8b81faf", "size": 2190, "noattachment": false, "integrity": "sha512-CCNBLvcCvdiM1K9pekMRwYHU2zn1B4LggyNuTPq2QixkjFUPY/zzpudDhXhucZ79NRPABmu+UcyvmyqFq5e9iQ=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1385495753113, "_hasShrinkwrap": false, "_cnpm_publish_time": 1385495753113, "_cnpmcore_publish_time": "2021-12-14T06:01:41.024Z"}, "2.3.0": {"name": "lodash.isnumber", "version": "2.3.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isnumber@2.3.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-2.3.0.tgz", "shasum": "eb1c9ec48a046f4054b4671d59a67bbe2f4da6e2", "size": 2188, "noattachment": false, "integrity": "sha512-kBK+44JoHCR1gH4ZLI55hZulo407kBsbSFcAicbg/Az/0zhYSYFvcoR056CiN/Io28DZrkp5TCHlVM65Uh+a7w=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384188466064, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384188466064, "_cnpmcore_publish_time": "2021-12-14T06:01:41.218Z"}, "2.2.1": {"name": "lodash.isnumber", "version": "2.2.1", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isnumber@2.2.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-2.2.1.tgz", "shasum": "4db02489f7f4f0d23cd8865c74d8d5b1a75cbfd6", "size": 2149, "noattachment": false, "integrity": "sha512-h0ry1B1ZYl2x8HV3R8uOvjn+oEKPQZrGbGa5loDaWEhK6F5kp58FxACbEPSPRRhKWtwVqubdYYSbDNJD7fakwQ=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380826225229, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380826225229, "_cnpmcore_publish_time": "2021-12-14T06:01:41.476Z"}, "2.2.0": {"name": "lodash.isnumber", "version": "2.2.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isnumber@2.2.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-2.2.0.tgz", "shasum": "b51d9d45c46921f0dc553c5c24e16a1190934df4", "size": 2194, "noattachment": false, "integrity": "sha512-0FRlffw995J00SLbksn2HlAnBrc/A290vosr9rqQKTdDlemJcTIaeMWU1ooHB928U/piZJrcqXZaRjgGrsylJA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380492590053, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380492590053, "_cnpmcore_publish_time": "2021-12-14T06:01:41.684Z"}, "2.1.0": {"name": "lodash.isnumber", "version": "2.1.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isnumber@2.1.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-2.1.0.tgz", "shasum": "caa73582435721a1f22cfc22a6247886e6566b81", "size": 2208, "noattachment": false, "integrity": "sha512-xlZstwUA/WPa2v/clHTv6Cbq63nnTBUMNRENvDRZdDNKRiej6slr+EX5ElJH+73Fb0WEILIy0asqSxMMlbD+oQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379922984832, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379922984832, "_cnpmcore_publish_time": "2021-12-14T06:01:41.885Z"}, "2.0.0": {"name": "lodash.isnumber", "version": "2.0.0", "description": "The Lo-Dash function `_.isNumber` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isnumber@2.0.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isnumber/-/lodash.isnumber-2.0.0.tgz", "shasum": "c91b4436135571ebba5aa491c3139428ad056a33", "size": 2169, "noattachment": false, "integrity": "sha512-6fHYX34O29ZBdP82qE2jRaXznRhATSpGxPq+xxwIqEuPPAkR5191KI39tqFdJpdmlkXwe5VW2rU6V2jZmWxKGA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379921882142, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379921882142, "_cnpmcore_publish_time": "2021-12-14T06:01:42.100Z"}}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "isnumber"], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "_source_registry_name": "default"}