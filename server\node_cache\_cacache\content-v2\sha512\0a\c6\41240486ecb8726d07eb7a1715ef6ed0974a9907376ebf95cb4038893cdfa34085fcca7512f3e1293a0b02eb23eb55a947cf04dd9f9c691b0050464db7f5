{"_attachments": {}, "_id": "has-unicode", "_rev": "145757-61f1969cefbf788ede88f794", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Try to guess if your terminal supports unicode", "dist-tags": {"latest": "2.0.1"}, "license": "ISC", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "name": "has-unicode", "readme": "has-unicode\n===========\n\nTry to guess if your terminal supports unicode\n\n```javascript\nvar hasUnicode = require(\"has-unicode\")\n\nif (hasUnicode()) {\n  // the terminal probably has unicode support\n}\n```\n```javascript\nvar hasUnicode = require(\"has-unicode\").tryHarder\nhasUnicode(function(unicodeSupported) {\n  if (unicodeSupported) {\n    // the terminal probably has unicode support\n  }\n})\n```\n\n## Detecting Unicode\n\nWhat we actually detect is UTF-8 support, as that's what Node itself supports.\nIf you have a UTF-16 locale then you won't be detected as unicode capable.\n\n### Windows\n\nSince at least Windows 7, `cmd` and `powershell` have been unicode capable,\nbut unfortunately even then it's not guaranteed. In many localizations it\nstill uses legacy code pages and there's no facility short of running\nprograms or linking C++ that will let us detect this. As such, we\nreport any Windows installation as NOT unicode capable, and recommend\nthat you encourage your users to override this via config.\n\n### Unix Like Operating Systems\n\nWe look at the environment variables `LC_ALL`, `LC_CTYPE`, and `LANG` in\nthat order.  For `LC_ALL` and `LANG`, it looks for `.UTF-8` in the value. \nFor `LC_CTYPE` it looks to see if the value is `UTF-8`.  This is sufficient\nfor most POSIX systems.  While locale data can be put in `/etc/locale.conf`\nas well, AFAIK it's always copied into the environment.\n\n", "time": {"created": "2022-01-26T18:44:44.012Z", "modified": "2023-07-31T11:54:36.915Z", "2.0.1": "2016-06-23T22:10:28.872Z", "2.0.0": "2015-11-26T00:59:19.737Z", "1.0.1": "2015-10-08T00:33:47.037Z", "1.0.0": "2014-12-30T17:33:11.432Z"}, "versions": {"2.0.1": {"name": "has-unicode", "version": "2.0.1", "description": "Try to guess if your terminal supports unicode", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/iarna/has-unicode.git"}, "keywords": ["unicode", "terminal"], "files": ["index.js"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "devDependencies": {"require-inject": "^1.3.0", "tap": "^2.3.1"}, "gitHead": "0a05df154e8d89a7fb9798da60b68c78c2df6646", "_id": "has-unicode@2.0.1", "_shasum": "e0e6fe6a28cf51138855e086d1691e771de2a8b9", "_from": ".", "_npmVersion": "3.10.2", "_nodeVersion": "4.4.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "e0e6fe6a28cf51138855e086d1691e771de2a8b9", "size": 1965, "noattachment": false, "tarball": "https://registry.npmmirror.com/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ=="}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/has-unicode-2.0.1.tgz_1466719828333_0.48896647873334587"}, "directories": {}, "publish_time": 1466719828872, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466719828872, "_cnpmcore_publish_time": "2021-12-13T10:29:50.738Z"}, "2.0.0": {"name": "has-unicode", "version": "2.0.0", "description": "Try to guess if your terminal supports unicode", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/iarna/has-unicode.git"}, "keywords": ["unicode", "terminal"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "devDependencies": {"require-inject": "^1.3.0", "tap": "^2.3.1"}, "gitHead": "fdd5de141a5564bdb5bc991d951209da40f6a598", "_id": "has-unicode@2.0.0", "_shasum": "a3cd96c307ba41d559c5a2ee408c12a11c4c2ec3", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "a3cd96c307ba41d559c5a2ee408c12a11c4c2ec3", "size": 2641, "noattachment": false, "tarball": "https://registry.npmmirror.com/has-unicode/-/has-unicode-2.0.0.tgz", "integrity": "sha512-O9wqHeXNkfa6ktLXg+WYg6oRqdpl5AH8FQdZanjILpQEQ8lqiN0fD6AoGqtMGn2xhRJ4UBKMP2Z17PZxQKoo5Q=="}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1448499559737, "_hasShrinkwrap": false, "_cnpm_publish_time": 1448499559737, "_cnpmcore_publish_time": "2021-12-13T10:29:50.983Z"}, "1.0.1": {"name": "has-unicode", "version": "1.0.1", "description": "Try to guess if your terminal supports unicode", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/iarna/has-unicode.git"}, "keywords": ["unicode", "terminal"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "devDependencies": {"require-inject": "^1.1.1", "tap": "^0.4.13"}, "gitHead": "d4ad300c67b25c197582e42e936ea928f7935d01", "_id": "has-unicode@1.0.1", "_shasum": "c46fceea053eb8ec789bffbba25fca52dfdcf38e", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "4.1.1", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "c46fceea053eb8ec789bffbba25fca52dfdcf38e", "size": 2393, "noattachment": false, "tarball": "https://registry.npmmirror.com/has-unicode/-/has-unicode-1.0.1.tgz", "integrity": "sha512-TnWnzjql9ZsMsXHXcIHxoUZNhd1pvHXI/8iO70KKArH5ykACBQSjdO3QzXpR7IYNbuRxeAwl3X2w1gJ4hsIxPg=="}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1444264427037, "_hasShrinkwrap": false, "_cnpm_publish_time": 1444264427037, "_cnpmcore_publish_time": "2021-12-13T10:29:51.340Z"}, "1.0.0": {"name": "has-unicode", "version": "1.0.0", "description": "Try to guess if your terminal supports unicode", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/iarna/has-unicode"}, "keywords": ["unicode", "terminal"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "devDependencies": {"require-inject": "^1.1.1", "tap": "^0.4.13"}, "gitHead": "a8c3dcf3be5f0c8f8e26a3e7ffea7da24344a006", "_id": "has-unicode@1.0.0", "_shasum": "bac5c44e064c2ffc3b8fcbd8c71afe08f9afc8cc", "_from": ".", "_npmVersion": "2.1.11", "_nodeVersion": "0.10.33", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "dist": {"shasum": "bac5c44e064c2ffc3b8fcbd8c71afe08f9afc8cc", "size": 2397, "noattachment": false, "tarball": "https://registry.npmmirror.com/has-unicode/-/has-unicode-1.0.0.tgz", "integrity": "sha512-JwgOrHApSgA0NsNfYfUt4DUJgCNL54MpbrsdzDH9PMJPcxCfizecciWkOGQtuggG7oBT6ZGXiH5vsKOrCxvD/w=="}, "directories": {}, "publish_time": 1419960791432, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419960791432, "_cnpmcore_publish_time": "2021-12-13T10:29:51.652Z"}}, "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "keywords": ["unicode", "terminal"], "repository": {"type": "git", "url": "git+https://github.com/iarna/has-unicode.git"}, "_source_registry_name": "default"}