{"_attachments": {}, "_id": "media-typer", "_rev": "881-61f14567830fd08f52a22584", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Simple RFC 6838 media type parser and formatter", "dist-tags": {"latest": "1.1.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}], "name": "media-typer", "readme": "# media-typer\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nSimple RFC 6838 media type parser.\n\nThis module will parse a given media type into it's component parts, like type,\nsubtype, and suffix. A formatter is also provided to put them back together and\nthe two can be combined to normalize media types into a canonical form.\n\nIf you are looking to parse the string that represents a media type and it's\nparameters in HTTP (for example, the `Content-Type` header), use the\n[content-type module](https://www.npmjs.com/package/content-type).\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install media-typer\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar typer = require('media-typer')\n```\n\n### typer.parse(string)\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar obj = typer.parse('image/svg+xml')\n```\n\nParse a media type string. This will return an object with the following\nproperties (examples are shown for the string `'image/svg+xml; charset=utf-8'`):\n\n - `type`: The type of the media type (always lower case). Example: `'image'`\n\n - `subtype`: The subtype of the media type (always lower case). Example: `'svg'`\n\n - `suffix`: The suffix of the media type (always lower case). Example: `'xml'`\n\nIf the given type string is invalid, then a `TypeError` is thrown.\n\n### typer.format(obj)\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar obj = typer.format({ type: 'image', subtype: 'svg', suffix: 'xml' })\n```\n\nFormat an object into a media type string. This will return a string of the\nmime type for the given object. For the properties of the object, see the\ndocumentation for `typer.parse(string)`.\n\nIf any of the given object values are invalid, then a `TypeError` is thrown.\n\n### typer.test(string)\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar valid = typer.test('image/svg+xml')\n```\n\nValidate a media type string. This will return `true` is the string is a well-\nformatted media type, or `false` otherwise.\n\n## License\n\n[MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/media-typer/master\n[coveralls-url]: https://coveralls.io/r/jshttp/media-typer?branch=master\n[node-version-image]: https://badgen.net/npm/node/media-typer\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/media-typer\n[npm-url]: https://npmjs.org/package/media-typer\n[npm-version-image]: https://badgen.net/npm/v/media-typer\n[travis-image]: https://badgen.net/travis/jshttp/media-typer/master\n[travis-url]: https://travis-ci.org/jshttp/media-typer\n", "time": {"created": "2022-01-26T12:58:15.179Z", "modified": "2025-05-14T14:56:39.167Z", "1.1.0": "2019-04-25T03:16:05.379Z", "1.0.2": "2019-04-19T21:51:21.389Z", "1.0.1": "2018-10-21T02:17:38.605Z", "1.0.0": "2018-10-21T02:06:30.337Z", "0.3.0": "2014-09-08T04:32:24.363Z", "0.2.0": "2014-06-18T19:00:35.939Z", "0.1.0": "2014-06-18T05:02:41.908Z", "0.0.0": "2014-06-13T19:25:11.301Z"}, "versions": {"1.1.0": {"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/media-typer.git"}, "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "1332b73ed8584b7b25d556c55b6de9d64fa3ce2c", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "homepage": "https://github.com/jshttp/media-typer#readme", "_id": "media-typer@1.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6ab74b8f2d3320f2064b2a87a38e7931ff3a5561", "size": 3658, "noattachment": false, "tarball": "https://registry.npmmirror.com/media-typer/-/media-typer-1.1.0.tgz", "integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/media-typer_1.1.0_1556162165232_0.28874376896252363"}, "_hasShrinkwrap": false, "publish_time": 1556162165379, "_cnpm_publish_time": 1556162165379, "_cnpmcore_publish_time": "2021-12-13T11:21:22.313Z"}, "1.0.2": {"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/media-typer.git"}, "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "95189ea17b0ff63132f027c746ae6eb1b2c783e1", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "homepage": "https://github.com/jshttp/media-typer#readme", "_id": "media-typer@1.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e64b0a709d52c158ddedaf38ebebfa00d8e02c14", "size": 3555, "noattachment": false, "tarball": "https://registry.npmmirror.com/media-typer/-/media-typer-1.0.2.tgz", "integrity": "sha512-/ky7iFD18Y2mN5BdOS4zotSSgu11BsIR2l3L7eK2bTaRWQidoSBmSxGgMFd/XOSGyivlhtQUdDLoUzlr1PWb1g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/media-typer_1.0.2_1555710681221_0.029165072776340617"}, "_hasShrinkwrap": false, "publish_time": 1555710681389, "_cnpm_publish_time": 1555710681389, "_cnpmcore_publish_time": "2021-12-13T11:21:22.578Z"}, "1.0.1": {"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/media-typer.git"}, "devDependencies": {"eslint": "5.7.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.1.0"}, "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "1ee51e645afab21855a35fbf95fe6b1e4c852060", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "homepage": "https://github.com/jshttp/media-typer#readme", "_id": "media-typer@1.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e39d677e19a011c52d2681f430d1adafb299dd41", "size": 3484, "noattachment": false, "tarball": "https://registry.npmmirror.com/media-typer/-/media-typer-1.0.1.tgz", "integrity": "sha512-v42gdPIuqYCoDVH5OiaKsVrv6aJqdMWJzl8KCyDs/KeDyBveYp3Wxq4UWJfsWjkSZUNC0xlLfDlLCPa1h/oo+g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/media-typer_1.0.1_1540088258485_0.9356881090496065"}, "_hasShrinkwrap": false, "publish_time": 1540088258605, "_cnpm_publish_time": 1540088258605, "_cnpmcore_publish_time": "2021-12-13T11:21:22.888Z"}, "1.0.0": {"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/media-typer.git"}, "devDependencies": {"eslint": "5.7.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.1.0"}, "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "dbe811acb83855b086667271010d3b105aeb6e34", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "homepage": "https://github.com/jshttp/media-typer#readme", "_id": "media-typer@1.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ca89611f8e31b936efcb3aa90826772f135e3341", "size": 3473, "noattachment": false, "tarball": "https://registry.npmmirror.com/media-typer/-/media-typer-1.0.0.tgz", "integrity": "sha512-MKXtlq1YKUow2wpJlJ7ep1MU6jQR0BaKpkDyrE294cmNSbhoOU1yziJ4UZOU1guFxcEaFt5t4LHsL/t891D6BQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/media-typer_1.0.0_1540087590178_0.03604841955092852"}, "_hasShrinkwrap": false, "publish_time": 1540087590337, "_cnpm_publish_time": 1540087590337, "_cnpmcore_publish_time": "2021-12-13T11:21:23.181Z"}, "0.3.0": {"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/media-typer"}, "devDependencies": {"istanbul": "0.3.2", "mocha": "~1.21.4", "should": "~4.0.4"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "d49d41ffd0bb5a0655fa44a59df2ec0bfc835b16", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "homepage": "https://github.com/jshttp/media-typer", "_id": "media-typer@0.3.0", "_shasum": "8710d7af0aa626f8fffa1ce00168545263255748", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8710d7af0aa626f8fffa1ce00168545263255748", "size": 4189, "noattachment": false, "tarball": "https://registry.npmmirror.com/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ=="}, "directories": {}, "publish_time": 1410150744363, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410150744363, "_cnpmcore_publish_time": "2021-12-13T11:21:23.449Z"}, "0.2.0": {"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/media-typer"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "bugs": {"url": "https://github.com/expressjs/media-typer/issues"}, "homepage": "https://github.com/expressjs/media-typer", "_id": "media-typer@0.2.0", "dist": {"shasum": "d8a065213adfeaa2e76321a2b6dda36ff6335984", "size": 3903, "noattachment": false, "tarball": "https://registry.npmmirror.com/media-typer/-/media-typer-0.2.0.tgz", "integrity": "sha512-TSggxYk75oP4tae7JkT8InpcFGUP4340zg1dOWjcu9qcphaDKtXEuNUv3OD4vJ+gVTvIDK797W0uYeNm8qqsDg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1403118035939, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403118035939, "_cnpmcore_publish_time": "2021-12-13T11:21:23.804Z"}, "0.1.0": {"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/media-typer"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "bugs": {"url": "https://github.com/expressjs/media-typer/issues"}, "homepage": "https://github.com/expressjs/media-typer", "_id": "media-typer@0.1.0", "dist": {"shasum": "00607eec8005776e49ea593202f5469f703e8060", "size": 3302, "noattachment": false, "tarball": "https://registry.npmmirror.com/media-typer/-/media-typer-0.1.0.tgz", "integrity": "sha512-rQzgJCOLIC5eiHyyKatRNkYw6DLxA/0eRiDtDIWMs2jbBy78RlrzhE4FXl4XtjrbVpjsUTIeD6xGfyDrW4gBtQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1403067761908, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403067761908, "_cnpmcore_publish_time": "2021-12-13T11:21:24.164Z"}, "0.0.0": {"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "0.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/media-typer"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "should": "~4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "bugs": {"url": "https://github.com/expressjs/media-typer/issues"}, "homepage": "https://github.com/expressjs/media-typer", "_id": "media-typer@0.0.0", "dist": {"shasum": "4ee71136eb8612cc771ecfa2bf25ddc153084f5c", "size": 3011, "noattachment": false, "tarball": "https://registry.npmmirror.com/media-typer/-/media-typer-0.0.0.tgz", "integrity": "sha512-xThAi0oNneSuMjpVgV+Ml97ZzaBWgzyiDPv3Ol9V2OvgNAoEXXbnR+vBXvCMDJ41hUh9xzw1q7hxQS0p3WKPsg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1402687511301, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402687511301, "_cnpmcore_publish_time": "2021-12-13T11:21:24.558Z"}}, "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "homepage": "https://github.com/jshttp/media-typer#readme", "repository": {"type": "git", "url": "git+https://github.com/jshttp/media-typer.git"}, "_source_registry_name": "default"}