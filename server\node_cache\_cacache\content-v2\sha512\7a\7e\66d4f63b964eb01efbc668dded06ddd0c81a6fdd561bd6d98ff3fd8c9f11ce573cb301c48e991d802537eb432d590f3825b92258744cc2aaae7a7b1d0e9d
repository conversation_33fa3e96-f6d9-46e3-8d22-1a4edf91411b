{"_attachments": {}, "_id": "path-is-absolute", "_rev": "246-61f14433963ca28f5ee350ef", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "description": "Node.js 0.12 path.isAbsolute() ponyfill", "dist-tags": {"latest": "2.0.0"}, "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "path-is-absolute", "readme": "# Deprecated\n\nThis package is no longer relevant as Node.js 0.12 is unmaintained.\n\n---\n\n# path-is-absolute [![Build Status](https://travis-ci.org/sindresorhus/path-is-absolute.svg?branch=master)](https://travis-ci.org/sindresorhus/path-is-absolute)\n\n> Node.js 0.12 [`path.isAbsolute()`](http://nodejs.org/api/path.html#path_path_isabsolute_path) [ponyfill](https://ponyfill.com)\n\n\n## Install\n\n```\n$ npm install --save path-is-absolute\n```\n\n\n## Usage\n\n```js\nconst pathIsAbsolute = require('path-is-absolute');\n\n// Running on Linux\npathIsAbsolute('/home/<USER>');\n//=> true\npathIsAbsolute('C:/Users/<USER>');\n//=> false\n\n// Running on Windows\npathIsAbsolute('C:/Users/<USER>');\n//=> true\npathIsAbsolute('/home/<USER>');\n//=> false\n\n// Running on any OS\npathIsAbsolute.posix('/home/<USER>');\n//=> true\npathIsAbsolute.posix('C:/Users/<USER>');\n//=> false\npathIsAbsolute.win32('C:/Users/<USER>');\n//=> true\npathIsAbsolute.win32('/home/<USER>');\n//=> false\n```\n\n\n## API\n\nSee the [`path.isAbsolute()` docs](http://nodejs.org/api/path.html#path_path_isabsolute_path).\n\n### pathIsAbsolute(path)\n\n### pathIsAbsolute.posix(path)\n\nPOSIX specific version.\n\n### pathIsAbsolute.win32(path)\n\nWindows specific version.\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "time": {"created": "2022-01-26T12:53:07.606Z", "modified": "2023-07-28T01:54:23.263Z", "2.0.0": "2018-11-08T11:03:52.447Z", "1.0.1": "2016-09-30T04:42:03.803Z", "1.0.0": "2015-02-17T03:11:44.044Z"}, "versions": {"2.0.0": {"name": "path-is-absolute", "version": "2.0.0", "description": "Node.js 0.12 path.isAbsolute() ponyfill", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-is-absolute.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && node test.js"}, "keywords": ["path", "paths", "file", "dir", "absolute", "isabsolute", "is-absolute", "built-in", "util", "utils", "core", "ponyfill", "polyfill", "shim", "is", "detect", "check"], "devDependencies": {"xo": "^0.16.0"}, "gitHead": "339a65c0a372f5f669d23fa12a0f349caa3fbed5", "bugs": {"url": "https://github.com/sindresorhus/path-is-absolute/issues"}, "homepage": "https://github.com/sindresorhus/path-is-absolute#readme", "_id": "path-is-absolute@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cba416f4f3be5d068afe2083d9b3b3707414533d", "size": 1905, "noattachment": false, "tarball": "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-2.0.0.tgz", "integrity": "sha512-ajROpjq1SLxJZsgSVCcVIt+ZebVH+PwJtPnVESjfg6JKwJGwAgHRC3zIcjvI0LnecjIHCJhtfNZ/Y/RregqyXg=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/path-is-absolute_2.0.0_1541675032299_0.04951781937588873"}, "_hasShrinkwrap": false, "deprecated": "This package is no longer relevant as Node.js 0.12 is unmaintained.", "publish_time": 1541675032447, "_cnpm_publish_time": 1541675032447, "_cnpmcore_publish_time": "2021-12-13T06:43:34.904Z"}, "1.0.1": {"name": "path-is-absolute", "version": "1.0.1", "description": "Node.js 0.12 path.isAbsolute() ponyfill", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-is-absolute.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && node test.js"}, "files": ["index.js"], "keywords": ["path", "paths", "file", "dir", "absolute", "isabsolute", "is-absolute", "built-in", "util", "utils", "core", "ponyfill", "polyfill", "shim", "is", "detect", "check"], "devDependencies": {"xo": "^0.16.0"}, "gitHead": "edc91d348b21dac2ab65ea2fbec2868e2eff5eb6", "bugs": {"url": "https://github.com/sindresorhus/path-is-absolute/issues"}, "homepage": "https://github.com/sindresorhus/path-is-absolute#readme", "_id": "path-is-absolute@1.0.1", "_shasum": "174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.6.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f", "size": 1882, "noattachment": false, "tarball": "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/path-is-absolute-1.0.1.tgz_1475210523565_0.9876507974695414"}, "directories": {}, "publish_time": 1475210523803, "_hasShrinkwrap": false, "_cnpm_publish_time": 1475210523803, "_cnpmcore_publish_time": "2021-12-13T06:43:35.145Z"}, "1.0.0": {"name": "path-is-absolute", "version": "1.0.0", "description": "Node.js 0.12 path.isAbsolute() ponyfill", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/path-is-absolute"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["path", "paths", "file", "dir", "absolute", "isabsolute", "is-absolute", "built-in", "util", "utils", "core", "ponyfill", "polyfill", "shim", "is", "detect", "check"], "gitHead": "7a76a0c9f2263192beedbe0a820e4d0baee5b7a1", "bugs": {"url": "https://github.com/sindresorhus/path-is-absolute/issues"}, "homepage": "https://github.com/sindresorhus/path-is-absolute", "_id": "path-is-absolute@1.0.0", "_shasum": "263dada66ab3f2fb10bf7f9d24dd8f3e570ef912", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "263dada66ab3f2fb10bf7f9d24dd8f3e570ef912", "size": 1846, "noattachment": false, "tarball": "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.0.tgz", "integrity": "sha512-hUUTsB/vByumPhn43R+Azhsx4TQPvyQqW+XyCR6UA8ae+FGIjf5Oygj6o/FYK4ZdwnrXth2eIBKk4YlrUU0ElQ=="}, "directories": {}, "publish_time": 1424142704044, "_hasShrinkwrap": false, "_cnpm_publish_time": 1424142704044, "_cnpmcore_publish_time": "2021-12-13T06:43:35.448Z"}}, "bugs": {"url": "https://github.com/sindresorhus/path-is-absolute/issues"}, "homepage": "https://github.com/sindresorhus/path-is-absolute#readme", "keywords": ["path", "paths", "file", "dir", "absolute", "isabsolute", "is-absolute", "built-in", "util", "utils", "core", "ponyfill", "polyfill", "shim", "is", "detect", "check"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-is-absolute.git"}, "_source_registry_name": "default"}