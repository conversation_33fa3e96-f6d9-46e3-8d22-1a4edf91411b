{"_attachments": {}, "_id": "delegates", "_rev": "7242-61f15f01a920628a7b728292", "description": "delegate methods and accessors to another property", "dist-tags": {"latest": "1.0.0"}, "license": "MIT", "maintainers": [{"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "delegates", "readme": "\n# delegates\n\n  Node method and accessor delegation utilty.\n\n## Installation\n\n```\n$ npm install delegates\n```\n\n## Example\n\n```js\nvar delegate = require('delegates');\n\n...\n\ndelegate(proto, 'request')\n  .method('acceptsLanguages')\n  .method('acceptsEncodings')\n  .method('acceptsCharsets')\n  .method('accepts')\n  .method('is')\n  .access('querystring')\n  .access('idempotent')\n  .access('socket')\n  .access('length')\n  .access('query')\n  .access('search')\n  .access('status')\n  .access('method')\n  .access('path')\n  .access('body')\n  .access('host')\n  .access('url')\n  .getter('subdomains')\n  .getter('protocol')\n  .getter('header')\n  .getter('stale')\n  .getter('fresh')\n  .getter('secure')\n  .getter('ips')\n  .getter('ip')\n```\n\n# API\n\n## Delegate(proto, prop)\n\nCreates a delegator instance used to configure using the `prop` on the given\n`proto` object. (which is usually a prototype)\n\n## Delegate#method(name)\n\nAllows the given method `name` to be accessed on the host.\n\n## Delegate#getter(name)\n\nCreates a \"getter\" for the property with the given `name` on the delegated\nobject.\n\n## Delegate#setter(name)\n\nCreates a \"setter\" for the property with the given `name` on the delegated\nobject.\n\n## Delegate#access(name)\n\nCreates an \"accessor\" (ie: both getter *and* setter) for the property with the\ngiven `name` on the delegated object.\n\n## Delegate#fluent(name)\n\nA unique type of \"accessor\" that works for a \"fluent\" API. When called as a\ngetter, the method returns the expected value. However, if the method is called\nwith a value, it will return itself so it can be chained. For example:\n\n```js\ndelegate(proto, 'request')\n  .fluent('query')\n\n// getter\nvar q = request.query();\n\n// setter (chainable)\nrequest\n  .query({ a: 1 })\n  .query({ b: 2 });\n```\n\n# License\n\n  MIT\n", "time": {"created": "2022-01-26T14:47:29.616Z", "modified": "2025-06-04T23:23:00.748Z", "1.0.0": "2015-12-14T19:56:09.247Z", "0.1.0": "2014-10-17T22:35:53.914Z", "0.0.3": "2014-01-13T14:34:41.995Z", "0.0.2": "2014-01-13T14:31:47.744Z", "0.0.1": "2014-01-13T14:22:04.168Z"}, "versions": {"1.0.0": {"name": "delegates", "version": "1.0.0", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-delegates.git"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "gitHead": "c4dc07ef1ed51c2b2a63f3585e5ef949ee577a49", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates#readme", "_id": "delegates@1.0.0", "scripts": {}, "_shasum": "84c6e159b81904fdca59a0ef44cd870d31250f9a", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.1", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "84c6e159b81904fdca59a0ef44cd870d31250f9a", "size": 2848, "noattachment": false, "tarball": "https://registry.npmmirror.com/delegates/-/delegates-1.0.0.tgz", "integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ=="}, "directories": {}, "publish_time": 1450122969247, "_hasShrinkwrap": false, "_cnpm_publish_time": 1450122969247, "_cnpmcore_publish_time": "2021-12-13T10:29:21.011Z"}, "0.1.0": {"name": "delegates", "version": "0.1.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-delegates"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates", "_id": "delegates@0.1.0", "_shasum": "b4b57be11a1653517a04b27f0949bdc327dfe390", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b4b57be11a1653517a04b27f0949bdc327dfe390", "size": 2113, "noattachment": false, "tarball": "https://registry.npmmirror.com/delegates/-/delegates-0.1.0.tgz", "integrity": "sha512-tPYr58xmVlUWcL8zPk6ZAxP6XqiYx5IIn395dkeER12JmMy8P6ipGKnUvgD++g8+uCaALfs/CRERixvKBu1pow=="}, "directories": {}, "publish_time": 1413585353914, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413585353914, "_cnpmcore_publish_time": "2021-12-13T10:29:21.339Z"}, "0.0.3": {"name": "delegates", "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-delegates"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates", "_id": "delegates@0.0.3", "dist": {"shasum": "4f25cbf8e1c061967f834e003f3bd18ded4baeea", "size": 1562, "noattachment": false, "tarball": "https://registry.npmmirror.com/delegates/-/delegates-0.0.3.tgz", "integrity": "sha512-9xrtyihBf7ybA34THs6W4Gwu1xNoVGUNzsegM2Q0XoalOqitE6qw7eGT0tadysvMK1u3xAswHmPcwUCtjsEKKg=="}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1389623681995, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389623681995, "_cnpmcore_publish_time": "2021-12-13T10:29:21.718Z"}, "0.0.2": {"name": "delegates", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-delegates"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates", "_id": "delegates@0.0.2", "dist": {"shasum": "41c8b770dcfff0a65c32192cac022dc037076d77", "size": 1512, "noattachment": false, "tarball": "https://registry.npmmirror.com/delegates/-/delegates-0.0.2.tgz", "integrity": "sha512-T2p1KIVjCH+ZUwlygGJr5/CL6p76DvotXlAUPbT8BLaPwHV1n3biQUljHWnNsi5N/BwgxSfEZ0hsF0U/QEFgvg=="}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1389623507744, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389623507744, "_cnpmcore_publish_time": "2021-12-13T10:29:22.009Z"}, "0.0.1": {"name": "delegates", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-delegates"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "readmeFilename": "Readme.md", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates", "_id": "delegates@0.0.1", "dist": {"shasum": "****************************************", "size": 1453, "noattachment": false, "tarball": "https://registry.npmmirror.com/delegates/-/delegates-0.0.1.tgz", "integrity": "sha512-bqF7Us/lLPTOifV4ALEqlNJkEbuetXuU5wR7f6a/xM3cHBtAS/Owbh2HpQJxK4VGr85L36w/ZJ73b0xrizKuWQ=="}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1389622924168, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389622924168, "_cnpmcore_publish_time": "2021-12-13T10:29:22.308Z"}}, "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates#readme", "keywords": ["delegate", "delegation"], "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-delegates.git"}, "_source_registry_name": "default"}