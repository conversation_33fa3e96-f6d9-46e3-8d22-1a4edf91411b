{"_attachments": {}, "_id": "buffer-equal-constant-time", "_rev": "307064-61f1d74eefbf788ede97b995", "author": {"name": "GoInstant Inc., a salesforce.com company"}, "description": "Constant-time comparison of Buffers", "dist-tags": {"latest": "1.0.1"}, "license": "BSD-3-<PERSON><PERSON>", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "name": "buffer-equal-constant-time", "readme": "# buffer-equal-constant-time\n\nConstant-time `Buffer` comparison for node.js.  Should work with browserify too.\n\n[![Build Status](https://travis-ci.org/goinstant/buffer-equal-constant-time.png?branch=master)](https://travis-ci.org/goinstant/buffer-equal-constant-time)\n\n```sh\n  npm install buffer-equal-constant-time\n```\n\n# Usage\n\n```js\n  var bufferEq = require('buffer-equal-constant-time');\n\n  var a = new Buffer('asdf');\n  var b = new Buffer('asdf');\n  if (bufferEq(a,b)) {\n    // the same!\n  } else {\n    // different in at least one byte!\n  }\n```\n\nIf you'd like to install an `.equal()` method onto the node.js `Buffer` and\n`SlowBuffer` prototypes:\n\n```js\n  require('buffer-equal-constant-time').install();\n\n  var a = new Buffer('asdf');\n  var b = new Buffer('asdf');\n  if (a.equal(b)) {\n    // the same!\n  } else {\n    // different in at least one byte!\n  }\n```\n\nTo get rid of the installed `.equal()` method, call `.restore()`:\n\n```js\n  require('buffer-equal-constant-time').restore();\n```\n\n# Legal\n\n&copy; 2013 GoInstant Inc., a salesforce.com company\n\nLicensed under the BSD 3-clause license.\n", "readmeFilename": "README.md", "time": {"created": "2022-01-26T23:20:46.463Z", "modified": "2023-05-12T22:02:58.742Z", "1.0.1": "2013-12-16T20:12:17.799Z", "1.0.0": "2013-12-16T19:53:25.360Z"}, "versions": {"1.0.1": {"name": "buffer-equal-constant-time", "version": "1.0.1", "description": "Constant-time comparison of Buffers", "main": "index.js", "scripts": {"test": "mocha test.js"}, "repository": {"type": "git", "url": "**************:goinstant/buffer-equal-constant-time.git"}, "keywords": ["buffer", "equal", "constant-time", "crypto"], "author": {"name": "GoInstant Inc., a salesforce.com company"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"mocha": "~1.15.1"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/goinstant/buffer-equal-constant-time/issues"}, "_id": "buffer-equal-constant-time@1.0.1", "dist": {"tarball": "https://registry.npmmirror.com/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "shasum": "f8e71132f7ffe6e01a5c9697a4c6f3e48d5cc819", "size": 3125, "noattachment": false, "integrity": "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "goinstant", "email": "<EMAIL>"}, "maintainers": [{"name": "goinstant", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1387224737799, "_hasShrinkwrap": false, "_cnpm_publish_time": 1387224737799, "_cnpmcore_publish_time": "2021-12-16T15:28:28.754Z"}, "1.0.0": {"name": "buffer-equal-constant-time", "version": "1.0.0", "description": "Constant-time comparison of Buffers", "main": "index.js", "scripts": {"test": "mocha test.js"}, "repository": {"type": "git", "url": "**************:goinstant/buffer-equal-constant-time.git"}, "keywords": ["buffer", "equal", "constant-time", "crypto"], "author": {"name": "GoInstant Inc., a salesforce.com company"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"mocha": "~1.15.1"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/goinstant/buffer-equal-constant-time/issues"}, "_id": "buffer-equal-constant-time@1.0.0", "dist": {"tarball": "https://registry.npmmirror.com/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.0.tgz", "shasum": "cba0775ef5d8bb0482597255b4b297dcac5f44a7", "size": 3123, "noattachment": false, "integrity": "sha512-sSkkf0CE5ZrsgqSBQYtuoToZ4VhHsVXwyZJCJ8BGp8Hzaw1PfDk+fDS2FUG504xGBdZgCgs+foWsHeEccUdX1A=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "goinstant", "email": "<EMAIL>"}, "maintainers": [{"name": "goinstant", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1387223605360, "_hasShrinkwrap": false, "_cnpm_publish_time": 1387223605360, "_cnpmcore_publish_time": "2021-12-16T15:28:28.976Z"}}, "_source_registry_name": "default"}