{"_attachments": {}, "_id": "type-is", "_rev": "876-61f14565a920628a7b6e604d", "description": "Infer the content-type of a request.", "dist-tags": {"latest": "2.0.1", "next": "2.0.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}], "name": "type-is", "readme": "# type-is\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nInfer the content-type of a request.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install type-is\n```\n\n## API\n\n```js\nvar http = require('http')\nvar typeis = require('type-is')\n\nhttp.createServer(function (req, res) {\n  var istext = typeis(req, ['text/*'])\n  res.end('you ' + (istext ? 'sent' : 'did not send') + ' me text')\n})\n```\n\n### typeis(request, types)\n\nChecks if the `request` is one of the `types`. If the request has no body,\neven if there is a `Content-Type` header, then `null` is returned. If the\n`Content-Type` header is invalid or does not matches any of the `types`, then\n`false` is returned. Otherwise, a string of the type that matched is returned.\n\nThe `request` argument is expected to be a Node.js HTTP request. The `types`\nargument is an array of type strings.\n\nEach type in the `types` array can be one of the following:\n\n- A file extension name such as `json`. This name will be returned if matched.\n- A mime type such as `application/json`.\n- A mime type with a wildcard such as `*/*` or `*/json` or `application/*`.\n  The full mime type will be returned if matched.\n- A suffix such as `+json`. This can be combined with a wildcard such as\n  `*/vnd+json` or `application/*+json`. The full mime type will be returned\n  if matched.\n\nSome examples to illustrate the inputs and returned value:\n\n```js\n// req.headers.content-type = 'application/json'\n\ntypeis(req, ['json']) // => 'json'\ntypeis(req, ['html', 'json']) // => 'json'\ntypeis(req, ['application/*']) // => 'application/json'\ntypeis(req, ['application/json']) // => 'application/json'\n\ntypeis(req, ['html']) // => false\n```\n\n### typeis.hasBody(request)\n\nReturns a Boolean if the given `request` has a body, regardless of the\n`Content-Type` header.\n\nHaving a body has no relation to how large the body is (it may be 0 bytes).\nThis is similar to how file existence works. If a body does exist, then this\nindicates that there is data to read from the Node.js request stream.\n\n```js\nif (typeis.hasBody(req)) {\n  // read the body, since there is one\n\n  req.on('data', function (chunk) {\n    // ...\n  })\n}\n```\n\n### typeis.is(mediaType, types)\n\nChecks if the `mediaType` is one of the `types`. If the `mediaType` is invalid\nor does not matches any of the `types`, then `false` is returned. Otherwise, a\nstring of the type that matched is returned.\n\nThe `mediaType` argument is expected to be a\n[media type](https://tools.ietf.org/html/rfc6838) string. The `types` argument\nis an array of type strings.\n\nEach type in the `types` array can be one of the following:\n\n- A file extension name such as `json`. This name will be returned if matched.\n- A mime type such as `application/json`.\n- A mime type with a wildcard such as `*/*` or `*/json` or `application/*`.\n  The full mime type will be returned if matched.\n- A suffix such as `+json`. This can be combined with a wildcard such as\n  `*/vnd+json` or `application/*+json`. The full mime type will be returned\n  if matched.\n\nSome examples to illustrate the inputs and returned value:\n\n```js\nvar mediaType = 'application/json'\n\ntypeis.is(mediaType, ['json']) // => 'json'\ntypeis.is(mediaType, ['html', 'json']) // => 'json'\ntypeis.is(mediaType, ['application/*']) // => 'application/json'\ntypeis.is(mediaType, ['application/json']) // => 'application/json'\n\ntypeis.is(mediaType, ['html']) // => false\n```\n\n### typeis.match(expected, actual)\n\nMatch the type string `expected` with `actual`, taking in to account wildcards.\nA wildcard can only be in the type of the subtype part of a media type and only\nin the `expected` value (as `actual` should be the real media type to match). A\nsuffix can still be included even with a wildcard subtype. If an input is\nmalformed, `false` will be returned.\n\n```js\ntypeis.match('text/html', 'text/html') // => true\ntypeis.match('*/html', 'text/html') // => true\ntypeis.match('text/*', 'text/html') // => true\ntypeis.match('*/*', 'text/html') // => true\ntypeis.match('*/*+json', 'application/x-custom+json') // => true\n```\n\n### typeis.normalize(type)\n\nNormalize a `type` string. This works by performing the following:\n\n- If the `type` is not a string, `false` is returned.\n- If the string starts with `+` (so it is a `+suffix` shorthand like `+json`),\n  then it is expanded to contain the complete wildcard notation of `*/*+suffix`.\n- If the string contains a `/`, then it is returned as the type.\n- Else the string is assumed to be a file extension and the mapped media type is\n  returned, or `false` is there is no mapping.\n\nThis includes two special mappings:\n\n- `'multipart'` -> `'multipart/*'`\n- `'urlencoded'` -> `'application/x-www-form-urlencoded'`\n\n## Examples\n\n### Example body parser\n\n```js\nvar express = require('express')\nvar typeis = require('type-is')\n\nvar app = express()\n\napp.use(function bodyParser (req, res, next) {\n  if (!typeis.hasBody(req)) {\n    return next()\n  }\n\n  switch (typeis(req, ['urlencoded', 'json', 'multipart'])) {\n    case 'urlencoded':\n      // parse urlencoded body\n      throw new Error('implement urlencoded body parsing')\n    case 'json':\n      // parse json body\n      throw new Error('implement json body parsing')\n    case 'multipart':\n      // parse multipart body\n      throw new Error('implement multipart body parsing')\n    default:\n      // 415 error code\n      res.statusCode = 415\n      res.end()\n      break\n  }\n})\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/type-is/master?label=ci\n[ci-url]: https://github.com/jshttp/type-is/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/type-is/master\n[coveralls-url]: https://coveralls.io/r/jshttp/type-is?branch=master\n[node-version-image]: https://badgen.net/npm/node/type-is\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/type-is\n[npm-url]: https://npmjs.org/package/type-is\n[npm-version-image]: https://badgen.net/npm/v/type-is\n[travis-image]: https://badgen.net/travis/jshttp/type-is/master\n[travis-url]: https://travis-ci.org/jshttp/type-is\n", "time": {"created": "2022-01-26T12:58:13.862Z", "modified": "2025-03-27T01:20:38.934Z", "1.6.18": "2019-04-26T13:59:49.224Z", "1.6.17": "2019-04-25T16:08:34.582Z", "1.6.16": "2018-02-16T20:22:03.004Z", "1.6.15": "2017-04-01T03:19:49.693Z", "1.6.14": "2016-11-19T01:11:00.743Z", "1.6.13": "2016-05-19T01:40:52.083Z", "1.6.12": "2016-02-29T06:09:05.369Z", "1.6.11": "2016-01-30T05:09:52.834Z", "1.6.10": "2015-12-01T19:07:05.618Z", "1.6.9": "2015-09-28T04:16:01.110Z", "1.6.8": "2015-09-04T14:57:13.431Z", "1.6.7": "2015-08-20T18:16:35.954Z", "1.6.6": "2015-07-31T17:12:11.577Z", "1.6.5": "2015-07-17T03:36:46.181Z", "1.6.4": "2015-07-02T01:22:52.689Z", "1.6.3": "2015-06-08T18:58:33.938Z", "1.6.2": "2015-05-11T05:52:18.626Z", "1.6.1": "2015-03-14T04:20:19.211Z", "1.6.0": "2015-02-13T03:55:36.313Z", "1.5.7": "2015-02-10T05:35:50.380Z", "1.5.6": "2015-01-30T05:31:55.125Z", "1.5.5": "2014-12-31T05:26:13.133Z", "1.5.4": "2014-12-11T02:42:44.888Z", "1.5.3": "2014-11-09T22:46:49.093Z", "1.5.2": "2014-09-29T05:30:21.355Z", "1.5.1": "2014-09-08T06:33:30.713Z", "1.5.0": "2014-09-05T19:49:01.787Z", "1.4.0": "2014-09-02T08:46:55.515Z", "1.3.2": "2014-06-25T01:04:21.979Z", "1.3.1": "2014-06-20T03:04:57.712Z", "1.3.0": "2014-06-20T02:04:10.178Z", "1.2.2": "2014-06-20T01:21:35.783Z", "1.2.1": "2014-06-04T04:37:46.796Z", "1.2.0": "2014-05-12T03:30:58.338Z", "1.1.0": "2014-04-13T00:23:29.651Z", "1.0.1": "2014-03-30T07:59:36.618Z", "1.0.0": "2013-12-28T00:06:19.362Z", "2.0.0": "2024-08-31T17:28:08.399Z", "2.0.1": "2025-03-27T01:20:01.576Z"}, "versions": {"1.6.18": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.18", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "keywords": ["content", "type", "checking"], "gitHead": "bfebe3d4ac312debccb7dbbc79242e2581dea5f0", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is#readme", "_id": "type-is@1.6.18", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4e552cd05df09467dcbc4ef739de89f2cf37c131", "size": 5872, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/type-is_1.6.18_1556287189103_0.20416863530873397"}, "_hasShrinkwrap": false, "publish_time": 1556287189224, "_cnpm_publish_time": 1556287189224, "_cnpmcore_publish_time": "2021-12-13T10:20:45.827Z"}, "1.6.17": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.17", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "keywords": ["content", "type", "checking"], "gitHead": "c22b4afcd251c5205d1bb49e6d6835b16233121a", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is#readme", "_id": "type-is@1.6.17", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9ef72233f08ffbe83b8fa3c93f4f93ecbc330bc2", "size": 5705, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.17.tgz", "integrity": "sha512-j<PERSON>ZzkOoAPVyQ9vlZ4xEJ4BBbHC4a7hbY1xqyCPe6AiQVVqfbZEulJm0VpqK4B+096O1VQi0l6OBGH210ejx/bA=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/type-is_1.6.17_1556208514463_0.06453249157999785"}, "_hasShrinkwrap": false, "publish_time": 1556208514582, "_cnpm_publish_time": 1556208514582, "_cnpmcore_publish_time": "2021-12-13T10:20:46.740Z"}, "1.6.16": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.16", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.18"}, "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "dc723b95e2c52c689cf9d4cefbc5d91e74f7524a", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is#readme", "_id": "type-is@1.6.16", "_npmVersion": "5.6.0", "_nodeVersion": "6.13.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f89ce341541c672b25ee7ae3c73dee3b2be50194", "size": 5621, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.16.tgz", "integrity": "sha512-HRkVv/5qY2G6I8iab9cI7v1bOIdhm94dVjQCPFElW9W+3GeDOSHmy2EBYe4VTApuzolPcmgFTN3ftVJRKR2J9Q=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/type-is_1.6.16_1518812522921_0.03331830182177953"}, "_hasShrinkwrap": false, "publish_time": 1518812523004, "_cnpm_publish_time": 1518812523004, "_cnpmcore_publish_time": "2021-12-13T10:20:47.027Z"}, "1.6.15": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.15", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.15"}, "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "2.1.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "9e88be851cc628364ad8842433dce32437ea4e73", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is#readme", "_id": "type-is@1.6.15", "_shasum": "cab10fb4909e441c82842eafe1ad646c81804410", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cab10fb4909e441c82842eafe1ad646c81804410", "size": 5395, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.15.tgz", "integrity": "sha512-0uqZYZDiBICTVXEsNcDLueZLPgZ8FgGe8lmVDQ0FcVFUeaxsPbFWiz60ZChVw8VELIt7iGuCehOrZSYjYteWKQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/type-is-1.6.15.tgz_1491016789014_0.6958203655667603"}, "directories": {}, "publish_time": 1491016789693, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491016789693, "_cnpmcore_publish_time": "2021-12-13T10:20:47.312Z"}, "1.6.14": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.14", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.13"}, "devDependencies": {"eslint": "2.10.2", "eslint-config-standard": "5.3.1", "eslint-plugin-promise": "1.1.0", "eslint-plugin-standard": "1.3.2", "istanbul": "0.4.5", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "f88151e69d91c5ed42e29dea78f5566403a5a7ad", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.14", "_shasum": "e219639c17ded1ca0789092dd54a03826b817cb2", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "e219639c17ded1ca0789092dd54a03826b817cb2", "size": 5215, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.14.tgz", "integrity": "sha512-pM3GvwuTj7H0LexCt3FK6R9KcP0SYRnmjZfHQ7RtuZkLVSfvQZmXKvSiHTDu+RFdkwyj9ZRnWXtvOZWlHiMgGQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/type-is-1.6.14.tgz_1479517858770_0.4908413903322071"}, "directories": {}, "publish_time": 1479517860743, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479517860743, "_cnpmcore_publish_time": "2021-12-13T10:20:47.650Z"}, "1.6.13": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.13", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.11"}, "devDependencies": {"eslint": "2.10.2", "eslint-config-standard": "5.3.1", "eslint-plugin-promise": "1.1.0", "eslint-plugin-standard": "1.3.2", "istanbul": "0.4.3", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "88c47523fff910343b3ca7d4928dad40f21ea6cd", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is#readme", "_id": "type-is@1.6.13", "_shasum": "6e83ba7bc30cd33a7bb0b7fb00737a2085bf9d08", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6e83ba7bc30cd33a7bb0b7fb00737a2085bf9d08", "size": 5206, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.13.tgz", "integrity": "sha512-34S1refwO9EIvJN6Yy1IBYL4kpuVsR1E6AoCRmdDw1DWcHJA5qXfROk4hcnqy9uNS5iZ+P0E/sx8e5n4vFFTsA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/type-is-1.6.13.tgz_1463622049206_0.9134831207338721"}, "directories": {}, "publish_time": 1463622052083, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463622052083, "_cnpmcore_publish_time": "2021-12-13T10:20:47.968Z"}, "1.6.12": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.12", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.10"}, "devDependencies": {"istanbul": "0.4.2", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "7ba49c0ccc8e34f4321768c0b13c2ebcccaae28c", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.12", "_shasum": "0352a9dfbfff040fe668cc153cc95829c354173e", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "0352a9dfbfff040fe668cc153cc95829c354173e", "size": 5114, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.12.tgz", "integrity": "sha512-Sp2FHV/pnM3RiYUkUuo5DgbhSP7Wo2OewD63Plfz4Z6ZZkS7ppdajl+qMcELJWRwyS+sZlyiViVO3a6JsLgm+g=="}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/type-is-1.6.12.tgz_1456726142464_0.8247741810046136"}, "directories": {}, "publish_time": 1456726145369, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456726145369, "_cnpmcore_publish_time": "2021-12-13T10:20:48.311Z"}, "1.6.11": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.11", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.9"}, "devDependencies": {"istanbul": "0.4.2", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "8e60e3e78aef84928e0e6c09da950f6950adcdd2", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.11", "_shasum": "42ecde7970f2363738b986c0351efba5aa531648", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "ritch", "email": "<EMAIL>"}], "dist": {"shasum": "42ecde7970f2363738b986c0351efba5aa531648", "size": 5056, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.11.tgz", "integrity": "sha512-LXayRc13t68vhOiY0UDvlWq2HiqGISjyhL30Hp7R/Y+wFfsB+rpWenCIgbS8ciUtLp962K8EUkbpw76FN1uHWA=="}, "directories": {}, "publish_time": 1454130592834, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454130592834, "_cnpmcore_publish_time": "2021-12-13T10:20:48.743Z"}, "1.6.10": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.10", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.8"}, "devDependencies": {"istanbul": "0.4.1", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "072de04e5c6bd4a3dd089dbd70ec2b1d505625a9", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.10", "_shasum": "d27e995b20d8c2a543f3420573f690a3929fd75a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "dist": {"shasum": "d27e995b20d8c2a543f3420573f690a3929fd75a", "size": 5041, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.10.tgz", "integrity": "sha512-7SASBU4gxbf91Zpn7gbEKwMX/dz4zymYSD4RP0SVisDIl1w8wqytilOXFJezHsUf0kVOiJVwswxRb7p7py/qUA=="}, "directories": {}, "publish_time": 1448996825618, "_hasShrinkwrap": false, "_cnpm_publish_time": 1448996825618, "_cnpmcore_publish_time": "2021-12-13T10:20:49.071Z"}, "1.6.9": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.9", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.7"}, "devDependencies": {"istanbul": "0.3.21", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "2f5999d6f2d88f2f36eeb1e8db78c2ec43fdbf13", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.9", "_shasum": "87f3e88b92ff5ac30fbc1acf9a9d00cbc38b3d7a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "dist": {"shasum": "87f3e88b92ff5ac30fbc1acf9a9d00cbc38b3d7a", "size": 4911, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.9.tgz", "integrity": "sha512-9rUXcYSqvKqg3UnwwkiBKZxqOluihsCeyuVTktMbNnPriqzW46e3ZUGUdG+JQmH9/3qeGsKwLtQijYsPatavkA=="}, "directories": {}, "publish_time": 1443413761110, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443413761110, "_cnpmcore_publish_time": "2021-12-13T10:20:49.465Z"}, "1.6.8": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.8", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.6"}, "devDependencies": {"istanbul": "0.3.19", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "6c93143cead7c596072133491b84f03a05403d3e", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.8", "_shasum": "3bac8c0c852754c855143e206d4a16e908bf0315", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "dist": {"shasum": "3bac8c0c852754c855143e206d4a16e908bf0315", "size": 4905, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.8.tgz", "integrity": "sha512-xDBvCf8fFRs4QSVwK7iM8WaMxglDFR6USgJ/bKJOWFfC1LBI11pzQDpyyUEQudGN5dxTUB/XU44Siq/18fX74A=="}, "directories": {}, "publish_time": 1441378633431, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441378633431, "_cnpmcore_publish_time": "2021-12-13T10:20:49.830Z"}, "1.6.7": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.7", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.5"}, "devDependencies": {"istanbul": "0.3.18", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "f162e9e971c19d28c348bb9b9ef660d17fcf1ba0", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.7", "_shasum": "5ec2bc7c7debc37f586d518c0747ab901f76bcec", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "dist": {"shasum": "5ec2bc7c7debc37f586d518c0747ab901f76bcec", "size": 4888, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.7.tgz", "integrity": "sha512-JXEIIlhWBDqCSZS4cy8HpibUJk0yZAwQorGjGFx1qOQ9y5JA6zDLqcD5BAVQzv83hSvZLx50GVN52PFRnzISHA=="}, "directories": {}, "publish_time": 1440094595954, "_hasShrinkwrap": false, "_cnpm_publish_time": 1440094595954, "_cnpmcore_publish_time": "2021-12-13T10:20:50.184Z"}, "1.6.6": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.6", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.4"}, "devDependencies": {"istanbul": "0.3.17", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "f2b12fce6172bf91f771d8898055d6efa0e30422", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.6", "_shasum": "398799519b62360f55c3cd6c486294531975926c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "dist": {"shasum": "398799519b62360f55c3cd6c486294531975926c", "size": 4829, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.6.tgz", "integrity": "sha512-kBAr7Aqr5VAXrEvXzkLE3EZTxzFviKOX8+qgaKDw7awKUA59lfjnPh2bs0LaHHGdN9p2PE7g839KWiwO2bckPg=="}, "directories": {}, "publish_time": 1438362731577, "_hasShrinkwrap": false, "_cnpm_publish_time": 1438362731577, "_cnpmcore_publish_time": "2021-12-13T10:20:50.560Z"}, "1.6.5": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.5", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.3"}, "devDependencies": {"istanbul": "0.3.17", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "b5fd0918ecc05113d32dbb97b02bb18cb635b059", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.5", "_shasum": "92129495c7b7563eaf923b447382c6c471f95de4", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "dist": {"shasum": "92129495c7b7563eaf923b447382c6c471f95de4", "size": 4735, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.5.tgz", "integrity": "sha512-G8AbS/1yQ0dN1xmU9sM0/FQCZIB5YvC1iR10t951cRAJtUgyxFKpYcdDfYLXiJ2ay63/3de0kC0aAp4GvSvufQ=="}, "directories": {}, "publish_time": 1437104206181, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437104206181, "_cnpmcore_publish_time": "2021-12-13T10:20:51.004Z"}, "1.6.4": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.4", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.2"}, "devDependencies": {"istanbul": "0.3.17", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "0edac23cef38f02ded0e072af65a078865af5b66", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.4", "_shasum": "d76fe92f0bcf7b0cf16b64d095e248f71079c318", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "dist": {"shasum": "d76fe92f0bcf7b0cf16b64d095e248f71079c318", "size": 4717, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.4.tgz", "integrity": "sha512-0pTZ+V/YzE9Ugs3EtX7QavdltqWG0XWbK6k/+TfjH78B9dYsaMLlGV9sGB48j0VYl494y/xk7Y0Gl4c+O4Mfbw=="}, "directories": {}, "publish_time": 1435800172689, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435800172689, "_cnpmcore_publish_time": "2021-12-13T10:20:51.495Z"}, "1.6.3": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.3", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.1"}, "devDependencies": {"istanbul": "0.3.14", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "294dff1c93d2ccb9a56191d37e390a8d2ad02e6f", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.3", "_shasum": "d87d201777f76dfc526ac202679715d41a28c580", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "dist": {"shasum": "d87d201777f76dfc526ac202679715d41a28c580", "size": 4675, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.3.tgz", "integrity": "sha512-Md9DP4oiC2rPRJLzs4ngYMIlnRpP2d+ZowJLMMZzgwYJG7AlkeiTkB4ii/ShtfUa8tjVA00LqUYcjTEVEGtU9w=="}, "directories": {}, "publish_time": 1433789913938, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433789913938, "_cnpmcore_publish_time": "2021-12-13T10:20:52.082Z"}, "1.6.2": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.11"}, "devDependencies": {"istanbul": "0.3.9", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "4e33e2fbb1f0daa6ec8c5444dbb60e44292ae314", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.2", "_shasum": "694e83e5d110417e681cea278227f264ae406e33", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "dist": {"shasum": "694e83e5d110417e681cea278227f264ae406e33", "size": 4550, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.2.tgz", "integrity": "sha512-y/Bam+fYWp2AJkG9BHYW3R62W9R4E4NFujhY+PqhZGbJLrsdGD84qux9YN69QwHbAMujexJrQ3Pszzqp6TFpCg=="}, "directories": {}, "publish_time": 1431323538626, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431323538626, "_cnpmcore_publish_time": "2021-12-13T10:20:52.632Z"}, "1.6.1": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.10"}, "devDependencies": {"istanbul": "0.3.7", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "339a7df4d8fed268b0f12d0fdab91d39f88d6f4e", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.1", "_shasum": "49addecb0f6831cbc1d34ba929f0f3a4f21b0f2e", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "49addecb0f6831cbc1d34ba929f0f3a4f21b0f2e", "size": 4539, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.1.tgz", "integrity": "sha512-ZNg3Qk/0sIYxNfm4OV8dppvO3al2LKe3fXuNbVk/HzbO5akyf+egZZpfBprLoHnfm8klTile5WvteLXBX7cIrA=="}, "directories": {}, "publish_time": 1426306819211, "_hasShrinkwrap": false, "_cnpm_publish_time": 1426306819211, "_cnpmcore_publish_time": "2021-12-13T10:20:53.234Z"}, "1.6.0": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.9"}, "devDependencies": {"istanbul": "0.3.5", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "8386837f91cfbf9f21f02758dee36655a901e1c4", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.6.0", "_shasum": "efcb9223fafad5a03be14d8f6c9e1785f2c0e7c3", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "efcb9223fafad5a03be14d8f6c9e1785f2c0e7c3", "size": 4521, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.6.0.tgz", "integrity": "sha512-CqVrPKax6n5hAqPdjcnAINWQd9GH5Hm3epiXvR1KWFcE+P/RuLdO40w3MZ0m2SOQhozG9O5M6c8XfEI1oym40Q=="}, "directories": {}, "publish_time": 1423799736313, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423799736313, "_cnpmcore_publish_time": "2021-12-13T10:20:53.794Z"}, "1.5.7": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.5.7", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.9"}, "devDependencies": {"istanbul": "0.3.5", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "f4335cc563a98ee80366f04f67c50cef089ae803", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.5.7", "_shasum": "b9368a593cc6ef7d0645e78b2f4c64cbecd05e90", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "b9368a593cc6ef7d0645e78b2f4c64cbecd05e90", "size": 4460, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.5.7.tgz", "integrity": "sha512-of68V0oUmVH4thGc1cLR3sKdICPsaL7kzpYc7FX1pcagY4eIllhyMqQcoOq289f+xj2orm8oPWwsCwxiCgVJbQ=="}, "directories": {}, "publish_time": 1423546550380, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423546550380, "_cnpmcore_publish_time": "2021-12-13T10:20:54.337Z"}, "1.5.6": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.5.6", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.8"}, "devDependencies": {"istanbul": "0.3.5", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "18f74f0f51c066c1485344c2e8d88c86c00d3bea", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.5.6", "_shasum": "5be39670ac699b4d0f59df84264cb05be1c9998b", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "5be39670ac699b4d0f59df84264cb05be1c9998b", "size": 4423, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.5.6.tgz", "integrity": "sha512-ZZ0MsMhtVkJkHpZHqEKgh7h7zoLym5N7T+cX5TAyukLX2IIKx4vJR8B3umYusBDNr2Z6cikmpymO0a8dI4xyIA=="}, "directories": {}, "publish_time": 1422595915125, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422595915125, "_cnpmcore_publish_time": "2021-12-13T10:20:54.876Z"}, "1.5.5": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.5.5", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.7"}, "devDependencies": {"istanbul": "0.3.5", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "b13dc3fa142ad60bea775181ba5f50364042691f", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.5.5", "_shasum": "45248af57f96366d0326ea0868f6bc8607dc4b21", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "45248af57f96366d0326ea0868f6bc8607dc4b21", "size": 4407, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.5.5.tgz", "integrity": "sha512-7SoYw4Iv8n47nT6loTary+9S8c4twzxJzEX3cYiYZLdSCfnrAwTbb+9RYpaNAU7eUekwOivpNt79LuzUEi4BtQ=="}, "directories": {}, "publish_time": 1420003573133, "_hasShrinkwrap": false, "_cnpm_publish_time": 1420003573133, "_cnpmcore_publish_time": "2021-12-13T10:20:55.435Z"}, "1.5.4": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.5.4", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.4"}, "devDependencies": {"istanbul": "~0.3.2", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "d604e7a69ce986692e9f241e21b9abe6d4f77eb0", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.5.4", "_shasum": "f2afe8635dcf2d159096202be6e120423fa19837", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "f2afe8635dcf2d159096202be6e120423fa19837", "size": 4344, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.5.4.tgz", "integrity": "sha512-bh9/1GBX5N50VQM67DxvDXF1q5RvJUv6neef3u73hW/o1o5TB5UWj0T6bTxnKwM87gVPCx2ab5rzfQErnN7NBQ=="}, "directories": {}, "publish_time": 1418265764888, "_hasShrinkwrap": false, "_cnpm_publish_time": 1418265764888, "_cnpmcore_publish_time": "2021-12-13T10:20:55.948Z"}, "1.5.3": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.5.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.3"}, "devDependencies": {"istanbul": "~0.3.0", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "202b4823bcc0aeda3595c14a03fdcb2c60cb0ebf", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.5.3", "_shasum": "b7fb92d0abc628393f10dd260932cca65fe9ff68", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "b7fb92d0abc628393f10dd260932cca65fe9ff68", "size": 4244, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.5.3.tgz", "integrity": "sha512-dpX+qPO89XyhBL2KAS5yhwwX0tzQ92gU18IcvMPAKBTAbnZQAZQSlRFdYsUlbsAEuXDnrn/gfl2uwHWt3YDDrA=="}, "directories": {}, "publish_time": 1415573209093, "_hasShrinkwrap": false, "_cnpm_publish_time": 1415573209093, "_cnpmcore_publish_time": "2021-12-13T10:20:56.510Z"}, "1.5.2": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.5.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.2"}, "devDependencies": {"istanbul": "~0.3.0", "mocha": "1"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "53b2d3f2c0177ac89576055d327d543291d36879", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.5.2", "_shasum": "8291bbe845a904acfaffd05a41fdeb234bfa9e5f", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "8291bbe845a904acfaffd05a41fdeb234bfa9e5f", "size": 4219, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.5.2.tgz", "integrity": "sha512-wtS3BNBYRLF0nSop3WQ4ZKdK3IY3O7UGzO3RMPEe5ruTJkXKkkcYZKp0600jQFAwZGBDRXWnsbICsKoO3T5pPA=="}, "directories": {}, "publish_time": 1411968621355, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411968621355, "_cnpmcore_publish_time": "2021-12-13T10:20:57.119Z"}, "1.5.1": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.5.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.0.1"}, "devDependencies": {"istanbul": "~0.3.0", "mocha": "1"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "74d33287453bf7c166f6410fc608c1c7588070ae", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.5.1", "_shasum": "5c1e62d874f79199fb16b34d16972dba376ccbed", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "5c1e62d874f79199fb16b34d16972dba376ccbed", "size": 4224, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.5.1.tgz", "integrity": "sha512-pTEYdpzmUV4nHhsShDnje7FQa2UsqEyw7Zqae5qOKBILkvCNPokgKKG3A4rJeU6dvLaE3+Rw+Z0eWtLUOSFjAA=="}, "directories": {}, "publish_time": 1410158010713, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410158010713, "_cnpmcore_publish_time": "2021-12-13T10:20:57.876Z"}, "1.5.0": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/type-is"}, "dependencies": {"media-typer": "0.2.0", "mime-types": "~2.0.0"}, "devDependencies": {"istanbul": "~0.3.0", "mocha": "1", "should": "4"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "scripts": {"test": "mocha --require should --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["content", "type", "checking"], "gitHead": "1cff718285478905d97bbf6cf666e0ce1c0284e3", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is", "_id": "type-is@1.5.0", "_shasum": "e3539711529c5ee4e7cd9f5bed27487cb819f823", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "e3539711529c5ee4e7cd9f5bed27487cb819f823", "size": 4184, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.5.0.tgz", "integrity": "sha512-omivIx5ZYDiXIsgO/qMzBmZeIJkOn87sgk5ofDbTVGD+574bLQ/hRrEJD+i865S82fV/I2XNGwF1jcvtaXlfDg=="}, "directories": {}, "publish_time": 1409946541787, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409946541787, "_cnpmcore_publish_time": "2021-12-13T10:20:58.599Z"}, "1.4.0": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/expressjs/type-is"}, "dependencies": {"media-typer": "0.2.0", "mime-types": "~2.0.0"}, "devDependencies": {"istanbul": "~0.3.0", "mocha": "1", "should": "4"}, "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "files": ["index.js"], "keywords": ["content", "type", "checking"], "gitHead": "f0483c28a704eaef3da9c0f8d9a2fc9dc6d50d3f", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.4.0", "_shasum": "de51d78a2ccb19a8fa2e137b06784f6b39a88059", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "de51d78a2ccb19a8fa2e137b06784f6b39a88059", "size": 3105, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.4.0.tgz", "integrity": "sha512-LmWmvhpDMep36TWOb6Xok2Ngvap4s+rYBwWowK/hSr3kNIFfxLd3afKoHvVfLlNzSCIHb8FkjpzTcL5HZFAXaw=="}, "directories": {}, "publish_time": 1409647615515, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409647615515, "_cnpmcore_publish_time": "2021-12-13T10:20:59.212Z"}, "1.3.2": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.3.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/expressjs/type-is"}, "dependencies": {"media-typer": "0.2.0", "mime-types": "~1.0.1"}, "devDependencies": {"istanbul": "0.2.11", "mocha": "*", "should": "*"}, "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "d76790909638d4cf1785e09858db5576f91f710f", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.3.2", "_shasum": "4f2a5dc58775ca1630250afc7186f8b36309d1bb", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "4f2a5dc58775ca1630250afc7186f8b36309d1bb", "size": 3963, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.3.2.tgz", "integrity": "sha512-sdIhnvhWEyIP2DKjj1o9tL31m8vFxDfLPD56KXz2absqY5AF2QYkJC7Wrw2fkzsZA9mv+PCtgyB7EqYOgR+r3Q=="}, "directories": {}, "publish_time": 1403658261979, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403658261979, "_cnpmcore_publish_time": "2021-12-13T10:20:59.870Z"}, "1.3.1": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/type-is"}, "dependencies": {"media-typer": "0.2.0", "mime-types": "1.0.0"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "*", "should": "*"}, "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.3.1", "dist": {"shasum": "a6789b5a52138289ade1ef8f6d9f2874ffd70b6b", "size": 3914, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.3.1.tgz", "integrity": "sha512-PLks4DIqAA9z7zHH0VuUv0aZ36t6cq8/K0y0OdHJtTkfSbGHhNvKh3pw1PPakXkjlAskC4apJlxeYcGpKZWvkA=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1403233497712, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403233497712, "_cnpmcore_publish_time": "2021-12-13T10:21:00.591Z"}, "1.3.0": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/type-is"}, "dependencies": {"media-typer": "0.2.0", "mime-types": "1.0.0"}, "devDependencies": {"istanbul": "0.2.10", "mocha": "*", "should": "*"}, "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.3.0", "dist": {"shasum": "131df06aca1476419f95de3e38f2efef8b249c20", "size": 3886, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.3.0.tgz", "integrity": "sha512-sBNA2/5T8h3ihCL3/3ujfMRY4gNmU0uSzGgeZnIaDnBmf737mkLJIaBgtgyzbjpW14imgczu221pO+aeOT+x0Q=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1403229850178, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403229850178, "_cnpmcore_publish_time": "2021-12-13T10:21:01.225Z"}, "1.2.2": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/type-is"}, "dependencies": {"mime-types": "1.0.0"}, "devDependencies": {"mocha": "*", "should": "*"}, "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.2.2", "dist": {"shasum": "dfdbf7cffa57cea0f9b1b55b96f629454e0eee97", "size": 3567, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.2.2.tgz", "integrity": "sha512-cjx3+RhPaTIp5fyZUUYa9+NanqHMKv0MzjYb87RWXavnOTes61QWfnG+mC+8abfkKo7lRV8kwxFSP8jL6OtrKw=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1403227295783, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403227295783, "_cnpmcore_publish_time": "2021-12-13T10:21:02.135Z"}, "1.2.1": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "1.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/type-is"}, "dependencies": {"mime-types": "1.0.0"}, "devDependencies": {"mocha": "*", "should": "*"}, "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.2.1", "_shasum": "73d448080a4f1dd18acb1eefff62968c5b5d54a2", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "73d448080a4f1dd18acb1eefff62968c5b5d54a2", "size": 3534, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.2.1.tgz", "integrity": "sha512-6/sfH4bn0JhSRWTHv1dGhkfIyftWIkYPtpiNRM/G5/45RazNmI8WaeE76vBQOZNijVYkmmxqOTJiwBcRMlBbQw=="}, "directories": {}, "publish_time": 1401856666796, "_hasShrinkwrap": false, "_cnpm_publish_time": 1401856666796, "_cnpmcore_publish_time": "2021-12-13T10:21:02.806Z"}, "1.2.0": {"name": "type-is", "description": "Infer the content type if a request", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/type-is"}, "dependencies": {"mime": "1.2.11"}, "devDependencies": {"mocha": "*", "should": "*"}, "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.2.0", "dist": {"shasum": "a9aaa3f2014850d4813663f6c714cf6318195138", "size": 3490, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.2.0.tgz", "integrity": "sha512-xYm8r1IQeUEVnAL5eaHJ2CYAZy8YJVJ4TDUH13hxJ3bsL9maDnQc9wXhkyqUrY7VfAl/galhFDmynNqE8HnWEg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1399865458338, "_hasShrinkwrap": false, "_cnpm_publish_time": 1399865458338, "_cnpmcore_publish_time": "2021-12-13T10:21:03.477Z"}, "1.1.0": {"name": "type-is", "description": "Infer the content type if a request", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/type-is"}, "dependencies": {"mime": "~1.2.11"}, "devDependencies": {"mocha": "*", "should": "*"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.1.0", "dist": {"shasum": "d0245ec8b2676668d59dd0cf3255060676a57db6", "size": 3243, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.1.0.tgz", "integrity": "sha512-bGTR<PERSON>lk7i/YYyx/d1xYm6gLrALwTcY2HLwsVqAIPKJjjtlI/rGXRgjQrcOln2fcHCbAqi0hrueZ2yPnHvCipQ=="}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1397348609651, "_hasShrinkwrap": false, "_cnpm_publish_time": 1397348609651, "_cnpmcore_publish_time": "2021-12-13T10:21:04.150Z"}, "1.0.1": {"name": "type-is", "description": "Infer the content type if a request", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/expressjs/type-is"}, "dependencies": {"mime": "~1.2.11"}, "devDependencies": {"mocha": "*", "should": "*"}, "scripts": {"test": "mocha --require should --reporter spec"}, "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.0.1", "dist": {"shasum": "ae09d93953c7846f5c083192837575ab363408f1", "size": 2630, "noattachment": false, "tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.0.1.tgz", "integrity": "sha512-YtjULaHVOJ8YVdHXp8YUIw+CI5rFjNz9wt/FvTrvbJt1OWXStWcrTrs9rzMeQSTfS7qCyBzpTArQqbelD72UyQ=="}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1396166376618, "_hasShrinkwrap": false, "_cnpm_publish_time": 1396166376618, "_cnpmcore_publish_time": "2021-12-13T10:21:04.792Z"}, "1.0.0": {"name": "type-is", "description": "Infer the content type if a request", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/expressjs/type-is.git"}, "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dependencies": {"mime": "~1.2.11"}, "devDependencies": {"mocha": "*", "should": "*"}, "scripts": {"test": "make test"}, "readmeFilename": "README.md", "homepage": "https://github.com/expressjs/type-is", "_id": "type-is@1.0.0", "dist": {"tarball": "https://registry.npmmirror.com/type-is/-/type-is-1.0.0.tgz", "shasum": "4ff424e97349a1ee1910b4bfc488595ecdc443fc", "size": 2543, "noattachment": false, "integrity": "sha512-CLdmAJgLeMtSPcTFX3eDdC1+ysfYoVdcYjMtuDtg23/fhHXoP5quNsvobr05ZNlG7og+oHQ4bosEzJX++DlIzQ=="}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1388189179362, "_hasShrinkwrap": false, "_cnpm_publish_time": 1388189179362, "_cnpmcore_publish_time": "2021-12-13T10:21:05.488Z"}, "2.0.0": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "2.0.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test:debug": "mocha --reporter spec --check-leaks --inspect --inspect-brk test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "keywords": ["content", "type", "checking"], "_id": "type-is@2.0.0", "readmeFilename": "README.md", "gitHead": "0d79e2d0c5737206d0f688769c5acffee1de953f", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is#readme", "_nodeVersion": "22.2.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-gd0sGezQYCbWSbkZr75mln4YBidWUN60+devscpLF5mtRDUpiaTvKpBNrdaCvel1NdR2k6vclXybU5fBd2i+nw==", "shasum": "7d249c2e2af716665cc149575dadb8b3858653af", "tarball": "https://registry.npmmirror.com/type-is/-/type-is-2.0.0.tgz", "fileCount": 5, "unpackedSize": 21348, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBhzTqTErs91DLnFW4ojSrKoqkuI3aQhGh6XFG9EVg1FAiAwUhKpjJY2TXZBuA2NWP3iTVUDf/UQhOQ3NCD/sD2Tww=="}], "size": 6648}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/type-is_2.0.0_1725125288247_0.8582864676821773"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-31T17:28:08.399Z", "publish_time": 1725125288399, "_source_registry_name": "default"}, "2.0.1": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "2.0.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test:debug": "mocha --reporter spec --check-leaks --inspect --inspect-brk test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "keywords": ["content", "type", "checking"], "_id": "type-is@2.0.1", "gitHead": "4a16e0850ec60234a45c4f546bf759ae161c6a36", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is#readme", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==", "shasum": "64f6cf03f92fce4015c2b224793f6bdd4b068c97", "tarball": "https://registry.npmmirror.com/type-is/-/type-is-2.0.1.tgz", "fileCount": 5, "unpackedSize": 21269, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIE5L+/qTUCwB+EiPWBST/CXJ8WdJBRs3n7WJtkDB/bgRAiBpEw/y/D/duf9Y+j5tnZxckcv/uhkt8cKe19wFcBHGhA=="}], "size": 6702}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/type-is_2.0.1_1743038401388_0.12399995026228616"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-27T01:20:01.576Z", "publish_time": 1743038401576, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "homepage": "https://github.com/jshttp/type-is#readme", "keywords": ["content", "type", "checking"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "_source_registry_name": "default"}