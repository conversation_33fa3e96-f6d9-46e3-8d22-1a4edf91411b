{"_attachments": {}, "_id": "lodash.isstring", "_rev": "2944-61f14a58b677e08f5114921d", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "description": "The lodash method `_.isString` exported as a module.", "dist-tags": {"latest": "4.0.1"}, "license": "MIT", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "lodash.isstring", "readme": "# lodash.isstring v4.0.1\n\nThe [lodash](https://lodash.com/) method `_.isString` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isstring\n```\n\nIn Node.js:\n```js\nvar isString = require('lodash.isstring');\n```\n\nSee the [documentation](https://lodash.com/docs#isString) or [package source](https://github.com/lodash/lodash/blob/4.0.1-npm-packages/lodash.isstring) for more details.\n", "time": {"created": "2022-01-26T13:19:20.465Z", "modified": "2023-07-28T04:06:42.829Z", "4.0.1": "2016-02-03T07:28:59.695Z", "4.0.0": "2016-01-13T11:06:24.069Z", "3.0.1": "2015-03-25T23:36:10.847Z", "3.0.0": "2015-01-26T15:29:27.553Z", "2.4.1": "2013-12-03T17:14:51.456Z", "2.4.0": "2013-11-26T19:55:59.447Z", "2.3.0": "2013-11-11T16:47:56.139Z", "2.2.1": "2013-10-03T18:50:34.673Z", "2.2.0": "2013-09-29T22:09:59.038Z", "2.1.0": "2013-09-23T07:56:46.906Z", "2.0.0": "2013-09-23T07:38:23.911Z"}, "versions": {"4.0.1": {"name": "lodash.isstring", "version": "4.0.1", "description": "The lodash method `_.isString` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "is<PERSON>ring"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isstring@4.0.1", "_shasum": "d527dfb5456eca7cc9bb95d5daeaf88ba54a5451", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d527dfb5456eca7cc9bb95d5daeaf88ba54a5451", "size": 2177, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/lodash.isstring-4.0.1.tgz_1454484537621_0.8814679116476327"}, "directories": {}, "publish_time": 1454484539695, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454484539695, "_cnpmcore_publish_time": "2021-12-13T06:48:03.569Z"}, "4.0.0": {"name": "lodash.isstring", "version": "4.0.0", "description": "The lodash method `_.isString` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "is<PERSON>ring"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isstring@4.0.0", "_shasum": "d3eada1270d17579d423c0d3dfbec85907569cfc", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d3eada1270d17579d423c0d3dfbec85907569cfc", "size": 2199, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-4.0.0.tgz", "integrity": "sha512-ClsvzyAOZnZBSwgooYIuDAdh0Zig+DI88pRluS+BISaxakylVtfrev7CvdtFkksxgWwpx6ikWZ/DEcvKBtto/g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452683184069, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452683184069, "_cnpmcore_publish_time": "2021-12-13T06:48:03.831Z"}, "3.0.1": {"name": "lodash.isstring", "version": "3.0.1", "description": "The modern build of lodash’s `_.isString` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isstring@3.0.1", "_shasum": "41638944ea042ef67ad67c293aa541d3f3d6e53c", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "41638944ea042ef67ad67c293aa541d3f3d6e53c", "size": 2153, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-3.0.1.tgz", "integrity": "sha512-UiX/KDS9ryxRpGR1q6zSwV9LxsG6PbN3OCD73O48JiRfG1z0cG3LCl3X9AEEdluRNuxduz1u94rS6BW9vSl9Vw=="}, "directories": {}, "publish_time": 1427326570847, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427326570847, "_cnpmcore_publish_time": "2021-12-13T06:48:04.105Z"}, "3.0.0": {"name": "lodash.isstring", "version": "3.0.0", "description": "The modern build of lodash’s `_.isString` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isstring@3.0.0", "_shasum": "c8ebb05b1106973b8ceff29481ec0c2387b52522", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "c8ebb05b1106973b8ceff29481ec0c2387b52522", "size": 2175, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-3.0.0.tgz", "integrity": "sha512-5hPgquHsLlAqJt4x8flsnPFSLFFvZhcA7R91m1kRTIx/jH67E9q3wMbZ4zPB+Bx09GhHzWja7UMM7w7NOmmR9g=="}, "directories": {}, "publish_time": 1422286167553, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422286167553, "_cnpmcore_publish_time": "2021-12-13T06:48:04.405Z"}, "2.4.1": {"name": "lodash.isstring", "version": "2.4.1", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isstring@2.4.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-2.4.1.tgz", "shasum": "3a4a42ee344b5bc09aeaa87ef1dc3097789d0792", "size": 2147, "noattachment": false, "integrity": "sha512-HKYXjltWqkqFqI77A0OwI1p2Ed8qKVGDvNxDQE36uCn3QDQd5zMNHBaRyCzJz/xC7vchWr3OaCoANJAluDV0VQ=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386090891456, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386090891456, "_cnpmcore_publish_time": "2021-12-13T06:48:04.686Z"}, "2.4.0": {"name": "lodash.isstring", "version": "2.4.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isstring@2.4.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-2.4.0.tgz", "shasum": "6c3a746c93bf949c7b61e6c5fb5f3f99502832b8", "size": 2143, "noattachment": false, "integrity": "sha512-uDmRJntVKuCI3Lac8B1JRuSxlzaAIim4HGMVCvRVkWWvoQJZ0tzy8xuwIkS8t0XlKBTnTixXe1AF1DkG7aC52w=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1385495759447, "_hasShrinkwrap": false, "_cnpm_publish_time": 1385495759447, "_cnpmcore_publish_time": "2021-12-13T06:48:05.146Z"}, "2.3.0": {"name": "lodash.isstring", "version": "2.3.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isstring@2.3.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-2.3.0.tgz", "shasum": "c0007a465efd8bd6b30a1adb51d050d43aba3963", "size": 2145, "noattachment": false, "integrity": "sha512-gykVX+taxLW1wo/YOXMgpCSPourI8u0q0ZLGaK2blyGufs6gLkRCv4SizWoZSpfzL12Tz8iI5QbfDFNGm0YCEA=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384188476139, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384188476139, "_cnpmcore_publish_time": "2021-12-13T06:48:05.589Z"}, "2.2.1": {"name": "lodash.isstring", "version": "2.2.1", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isstring@2.2.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-2.2.1.tgz", "shasum": "da8ad3fb99fa0ae8139f53cd75a26e7beedf9461", "size": 2108, "noattachment": false, "integrity": "sha512-8hXoxRt6WIFGt/4/YOYz7raQtf/1GTs7ubX1Lm8+eZdMmrDTUj54WIQRKE95uLMSGShC90E4mM0U0qSqYeEmmA=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380826234673, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380826234673, "_cnpmcore_publish_time": "2021-12-13T06:48:05.913Z"}, "2.2.0": {"name": "lodash.isstring", "version": "2.2.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isstring@2.2.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-2.2.0.tgz", "shasum": "d2358715bf7a704117b781cfd59e3c55a76d7bb0", "size": 2155, "noattachment": false, "integrity": "sha512-cAFU09Nb87NsfQbWS4SfqyZXDzdtPvTu4h1X8WypmxRZSjZluUKs/igUOIMGEESdi57ZziJLNs6K3PT829/mhA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380492599038, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380492599038, "_cnpmcore_publish_time": "2021-12-13T06:48:06.341Z"}, "2.1.0": {"name": "lodash.isstring", "version": "2.1.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isstring@2.1.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-2.1.0.tgz", "shasum": "fe07b0fd817cfad55c3f0f283c19c963669af4fe", "size": 2167, "noattachment": false, "integrity": "sha512-tnRCYFFDrBGDJ52/tRhjZO+x+Fs4+WjkKTpw+fu4ddXmB3PkrtB6TFJUhijETnvtewk3AmH+IYTRADZuE2SEPw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379923006906, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379923006906, "_cnpmcore_publish_time": "2021-12-13T06:48:06.772Z"}, "2.0.0": {"name": "lodash.isstring", "version": "2.0.0", "description": "The Lo-Dash function `_.isString` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isstring@2.0.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isstring/-/lodash.isstring-2.0.0.tgz", "shasum": "652105b4e99cde5642fd8aced2b622ae05898419", "size": 2125, "noattachment": false, "integrity": "sha512-wQ9cuxmJuYw6PdBbcxtyzSJXWxSU0JCujpx9k1R2yDo9p+jHKCm0VMAa4iwGa47f2VvUzI3e/A/L+nQxPH7O8Q=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379921903911, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379921903911, "_cnpmcore_publish_time": "2021-12-13T06:48:07.203Z"}}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "is<PERSON>ring"], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "_source_registry_name": "default"}