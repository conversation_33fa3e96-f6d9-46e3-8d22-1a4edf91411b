{"_attachments": {}, "_id": "jwa", "_rev": "3043-61f14a96a920628a7b6f8a53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "JWA implementation (supports all JWS algorithms)", "dist-tags": {"latest": "2.0.1"}, "license": "MIT", "maintainers": [{"name": "charles<PERSON>", "email": "<EMAIL>"}, {"name": "julien.w<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lozano.okta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "jwa", "readme": "# node-jwa [![Build Status](https://travis-ci.org/brianloveswords/node-jwa.svg?branch=master)](https://travis-ci.org/brianloveswords/node-jwa)\n\nA\n[JSON Web Algorithms](http://tools.ietf.org/id/draft-ietf-jose-json-web-algorithms-08.html)\nimplementation focusing (exclusively, at this point) on the algorithms necessary for\n[JSON Web Signatures](http://self-issued.info/docs/draft-ietf-jose-json-web-signature.html).\n\nThis library supports all of the required, recommended and optional cryptographic algorithms for JWS:\n\nalg Parameter Value | Digital Signature or MAC Algorithm\n----------------|----------------------------\nHS256 | HMAC using SHA-256 hash algorithm\nHS384 | HMAC using SHA-384 hash algorithm\nHS512 | HMAC using SHA-512 hash algorithm\nRS256 | RSASSA using SHA-256 hash algorithm\nRS384 | RSASSA using SHA-384 hash algorithm\nRS512 | RSASSA using SHA-512 hash algorithm\nPS256 | RSASSA-PSS using SHA-256 hash algorithm\nPS384 | RSASSA-PSS using SHA-384 hash algorithm\nPS512 | RSASSA-PSS using SHA-512 hash algorithm\nES256 | ECDSA using P-256 curve and SHA-256 hash algorithm\nES384 | ECDSA using P-384 curve and SHA-384 hash algorithm\nES512 | ECDSA using P-521 curve and SHA-512 hash algorithm\nnone | No digital signature or MAC value included\n\nPlease note that PS* only works on Node 6.12+ (excluding 7.x).\n\n# Requirements\n\nIn order to run the tests, a recent version of OpenSSL is\nrequired. **The version that comes with OS X (OpenSSL 0.9.8r 8 Feb\n2011) is not recent enough**, as it does not fully support ECDSA\nkeys. You'll need to use a version > 1.0.0; I tested with OpenSSL 1.0.1c 10 May 2012.\n\n# Testing\n\nTo run the tests, do\n\n```bash\n$ npm test\n```\n\nThis will generate a bunch of keypairs to use in testing. If you want to\ngenerate new keypairs, do `make clean` before running `npm test` again.\n\n## Methodology\n\nI spawn `openssl dgst -sign` to test OpenSSL sign → JS verify and\n`openssl dgst -verify` to test JS sign → OpenSSL verify for each of the\nRSA and ECDSA algorithms.\n\n# Usage\n\n## jwa(algorithm)\n\nCreates a new `jwa` object with `sign` and `verify` methods for the\nalgorithm. Valid values for algorithm can be found in the table above\n(`'HS256'`, `'HS384'`, etc) and are case-sensitive. Passing an invalid\nalgorithm value will throw a `TypeError`.\n\n\n## jwa#sign(input, secretOrPrivateKey)\n\nSign some input with either a secret for HMAC algorithms, or a private\nkey for RSA and ECDSA algorithms.\n\nIf input is not already a string or buffer, `JSON.stringify` will be\ncalled on it to attempt to coerce it.\n\nFor the HMAC algorithm, `secretOrPrivateKey` should be a string or a\nbuffer. For ECDSA and RSA, the value should be a string representing a\nPEM encoded **private** key.\n\nOutput [base64url](http://en.wikipedia.org/wiki/Base64#URL_applications)\nformatted. This is for convenience as JWS expects the signature in this\nformat. If your application needs the output in a different format,\n[please open an issue](https://github.com/brianloveswords/node-jwa/issues). In\nthe meantime, you can use\n[brianloveswords/base64url](https://github.com/brianloveswords/base64url)\nto decode the signature.\n\nAs of nodejs *v0.11.8*, SPKAC support was introduce. If your nodeJs\nversion satisfies, then you can pass an object `{ key: '..', passphrase: '...' }`\n\n\n## jwa#verify(input, signature, secretOrPublicKey)\n\nVerify a signature. Returns `true` or `false`.\n\n`signature` should be a base64url encoded string.\n\nFor the HMAC algorithm, `secretOrPublicKey` should be a string or a\nbuffer. For ECDSA and RSA, the value should be a string represented a\nPEM encoded **public** key.\n\n\n# Example\n\nHMAC\n```js\nconst jwa = require('jwa');\n\nconst hmac = jwa('HS256');\nconst input = 'super important stuff';\nconst secret = 'shhhhhh';\n\nconst signature = hmac.sign(input, secret);\nhmac.verify(input, signature, secret) // === true\nhmac.verify(input, signature, 'trickery!') // === false\n```\n\nWith keys\n```js\nconst fs = require('fs');\nconst jwa = require('jwa');\nconst privateKey = fs.readFileSync(__dirname + '/ecdsa-p521-private.pem');\nconst publicKey = fs.readFileSync(__dirname + '/ecdsa-p521-public.pem');\n\nconst ecdsa = jwa('ES512');\nconst input = 'very important stuff';\n\nconst signature = ecdsa.sign(input, privateKey);\necdsa.verify(input, signature, publicKey) // === true\n```\n## License\n\nMIT\n\n```\nCopyright (c) 2013 Brian J. Brennan\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n```\n", "time": {"created": "2022-01-26T13:20:22.530Z", "modified": "2025-05-08T10:05:29.788Z", "2.0.0": "2019-12-15T16:14:02.839Z", "1.4.1": "2019-03-16T15:19:55.038Z", "1.4.0": "2019-03-02T03:49:42.532Z", "1.3.0": "2019-02-20T14:55:36.788Z", "1.2.0": "2019-01-25T20:45:46.635Z", "1.1.6": "2018-05-14T15:08:04.572Z", "1.1.5": "2016-12-05T21:12:09.532Z", "1.1.4": "2016-11-03T19:25:45.102Z", "1.1.3": "2016-02-18T15:39:27.215Z", "1.1.2": "2016-02-18T14:25:22.508Z", "1.1.1": "2015-11-10T02:22:15.839Z", "1.1.0": "2015-07-16T17:07:15.670Z", "1.0.2": "2015-07-16T17:02:33.345Z", "1.0.1": "2015-07-01T18:09:15.900Z", "1.0.0": "2015-01-11T16:18:16.427Z", "0.0.3": "2015-01-11T16:18:05.702Z", "0.0.2": "2013-02-10T21:24:57.199Z", "0.0.1": "2013-02-10T02:59:50.477Z", "0.0.0": "2013-02-10T02:02:10.716Z", "2.0.1": "2025-05-07T11:42:50.147Z", "1.4.2": "2025-05-07T11:59:15.769Z"}, "versions": {"2.0.0": {"name": "jwa", "version": "2.0.0", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}, "devDependencies": {"base64url": "^2.0.0", "jwk-to-pem": "^2.0.1", "semver": "4.3.6", "tap": "6.2.0"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gitHead": "8ddd78abc5ebfbb7914e3d1ce5edae1e69f74e8d", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@2.0.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "a7e9c3f29dae94027ebcaf49975c9345593410fc", "size": 4553, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-2.0.0.tgz", "integrity": "sha512-jrZ2Qx916EA+fq9cEAeCROWPTfCwi1IVHqT2tapuqLEVVDKFDENFw1oL+MwrTvH6msKxsd1YTDVw6uKEcsrLEA=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jwa_2.0.0_1576426442702_0.6191557872090363"}, "_hasShrinkwrap": false, "publish_time": 1576426442839, "_cnpm_publish_time": 1576426442839, "_cnpmcore_publish_time": "2021-12-16T22:16:37.385Z"}, "1.4.1": {"name": "jwa", "version": "1.4.1", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}, "devDependencies": {"base64url": "^2.0.0", "jwk-to-pem": "^2.0.1", "semver": "4.3.6", "tap": "6.2.0"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gitHead": "5b3bceef80a1eeb21405d5ea4cdf0725277746e1", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.4.1", "_nodeVersion": "11.10.0", "_npmVersion": "6.7.0", "dist": {"shasum": "743c32985cb9e98655530d53641b66c8645b039a", "size": 4560, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.4.1.tgz", "integrity": "sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jwa_1.4.1_1552749594910_0.7929240352896372"}, "_hasShrinkwrap": false, "publish_time": 1552749595038, "_cnpm_publish_time": 1552749595038, "_cnpmcore_publish_time": "2021-12-16T22:16:37.565Z"}, "1.4.0": {"name": "jwa", "version": "1.4.0", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}, "devDependencies": {"base64url": "^2.0.0", "jwk-to-pem": "^2.0.1", "semver": "4.3.6", "tap": "6.2.0"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gitHead": "9c22a78ad66788a767ba0e0a04a62de875a66eef", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.4.0", "_nodeVersion": "11.10.0", "_npmVersion": "6.7.0", "dist": {"shasum": "8f61dc799acf0309f2d4b22a91ce73d6d2bb206c", "size": 4536, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.4.0.tgz", "integrity": "sha512-mt6IHaq0ZZWDBspg0Pheu3r9sVNMEZn+GJe1zcdYyhFcDSclp3J8xEdO4PjZolZ2i8xlaVU1LetHM0nJejYsEw=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jwa_1.4.0_1551498582405_0.8701429780529393"}, "_hasShrinkwrap": false, "publish_time": 1551498582532, "_cnpm_publish_time": 1551498582532, "_cnpmcore_publish_time": "2021-12-16T22:16:37.959Z"}, "1.3.0": {"name": "jwa", "version": "1.3.0", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}, "devDependencies": {"base64url": "^2.0.0", "semver": "4.3.6", "tap": "6.2.0"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gitHead": "76fbfad71c761ab9036ec2758a675f572722a513", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.3.0", "_nodeVersion": "11.10.0", "_npmVersion": "6.7.0", "dist": {"shasum": "061a7c3bb8ab2b3434bb2f432005a8bb7fca0efa", "size": 4525, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.3.0.tgz", "integrity": "sha512-SxObIyzv9a6MYuZYaSN6DhSm9j3+qkokwvCB0/OTSV5ylPq1wUQiygZQcHT5Qlux0I5kmISx3J86TxKhuefItg=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jwa_1.3.0_1550674536681_0.7044539985125433"}, "_hasShrinkwrap": false, "publish_time": 1550674536788, "_cnpm_publish_time": 1550674536788, "_cnpmcore_publish_time": "2021-12-16T22:16:38.164Z"}, "1.2.0": {"name": "jwa", "version": "1.2.0", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.10", "safe-buffer": "^5.0.1"}, "devDependencies": {"base64url": "^2.0.0", "semver": "4.3.6", "tap": "6.2.0"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gitHead": "7c310248043e607cc9363abb3a425b69959f0e1d", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.4.0", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "606da70c1c6d425cad329c77c99f2df2a981489a", "size": 4357, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.2.0.tgz", "integrity": "sha512-Grku9ZST5NNQ3hqNUodSkDfEBqAmGA1R8yiyPHOnLzEKI0GaCQC/XhFmsheXYuXzFQJdILbh+lYBiliqG5R/Vg=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jwa_1.2.0_1548449146496_0.13255962968062707"}, "_hasShrinkwrap": false, "publish_time": 1548449146635, "_cnpm_publish_time": 1548449146635, "_cnpmcore_publish_time": "2021-12-16T22:16:38.363Z"}, "1.1.6": {"name": "jwa", "version": "1.1.6", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.10", "safe-buffer": "^5.0.1"}, "devDependencies": {"base64url": "^2.0.0", "semver": "4.3.6", "tap": "6.2.0"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gitHead": "95d473253b14bb4162bf0f140ec1d48812fa0d63", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.1.6", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "87240e76c9808dbde18783cf2264ef4929ee50e6", "size": 4211, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.1.6.tgz", "integrity": "sha512-tBO/cf++BUsJkYql/kBbJroKOgHWEigTKBAjjBEmrMGYd1QMBC74Hr4Wo2zCZw6ZrVhlJPvoMrkcOnlWR/DJfw=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jwa_1.1.6_1526310484514_0.6028743666959784"}, "_hasShrinkwrap": false, "publish_time": 1526310484572, "_cnpm_publish_time": 1526310484572, "_cnpmcore_publish_time": "2021-12-16T22:16:38.728Z"}, "1.1.5": {"name": "jwa", "version": "1.1.5", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "2.0.0", "buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.9", "safe-buffer": "^5.0.1"}, "devDependencies": {"semver": "4.3.6", "tap": "6.2.0"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gitHead": "6b50153adc0b0a9b3fbebffb132f8d54178cd6af", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.1.5", "_shasum": "a0552ce0220742cd52e153774a32905c30e756e5", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "a0552ce0220742cd52e153774a32905c30e756e5", "size": 4103, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.1.5.tgz", "integrity": "sha512-wHWLbWf/tdQVgE5oRwPR6FTXHbZJ06k5h3HjYZWeuDEAHRHPf3RzJPA/tMQjFfSwlWnsDUAcVm/Bd/i+RpVFnQ=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/jwa-1.1.5.tgz_1480972328527_0.010812546592205763"}, "publish_time": 1480972329532, "_hasShrinkwrap": false, "_cnpm_publish_time": 1480972329532, "_cnpmcore_publish_time": "2021-12-16T22:16:38.949Z"}, "1.1.4": {"name": "jwa", "version": "1.1.4", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "2.0.0", "buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.7", "safe-buffer": "^5.0.1"}, "devDependencies": {"semver": "4.3.6", "tap": "6.2.0"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gitHead": "cce381215d2dbeabf94a675be8d81ab3102abd57", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.1.4", "_shasum": "dbb01bd38cd409899fa715107e90d90f9bcb161e", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "dbb01bd38cd409899fa715107e90d90f9bcb161e", "size": 4097, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.1.4.tgz", "integrity": "sha512-Dy5lGW+ZU+sV0RqHZ3ynW6GM57f22Zh9cOb/PO3/Zxw57az4zpJ2+b+1RbukBsXMj/xEN/rLNtUUWPK8hpEBFQ=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/jwa-1.1.4.tgz_1478201144552_0.5529128357302397"}, "publish_time": 1478201145102, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478201145102, "_cnpmcore_publish_time": "2021-12-16T22:16:39.173Z"}, "1.1.3": {"name": "jwa", "version": "1.1.3", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~1.0.4", "buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "^1.0.0"}, "devDependencies": {"semver": "^4.3.6", "tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.1.3", "_shasum": "fa9f2f005ff0c630e7c41526a31f37f79733cd6d", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "fa9f2f005ff0c630e7c41526a31f37f79733cd6d", "size": 6211, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.1.3.tgz", "integrity": "sha512-VUr8nmKQLWi3Gf0O6EU7M1/tBlxVMvScdKKMLGvCBYexNbFPVoCMnedScvB8hONjHPs2lTEonny6sLEpVOCduw=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/jwa-1.1.3.tgz_1455809964709_0.6556255409959704"}, "publish_time": 1455809967215, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455809967215, "_cnpmcore_publish_time": "2021-12-16T22:16:39.355Z"}, "1.1.2": {"name": "jwa", "version": "1.1.2", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.4", "buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "^1.0.0"}, "devDependencies": {"semver": "^4.3.6", "tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.1.2", "_shasum": "b3c506de9c7ad3ea5af20c639b238d6d37f165aa", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "b3c506de9c7ad3ea5af20c639b238d6d37f165aa", "size": 6210, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.1.2.tgz", "integrity": "sha512-yZy+Cb6rE4q9Fw+I9MkZ7MTRpK7SrkK+g9e1ro3UTUME+sFbHmm0zcyiiokynHvpKfj+kBYkkn7Z/Itiov3CWA=="}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/jwa-1.1.2.tgz_1455805518363_0.7412215110380203"}, "publish_time": 1455805522508, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455805522508, "_cnpmcore_publish_time": "2021-12-16T22:16:39.658Z"}, "1.1.1": {"name": "jwa", "version": "1.1.1", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.4", "buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "^1.0.0"}, "devDependencies": {"semver": "^4.3.6", "tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.1.1", "_shasum": "b83c05279f0707f55ca5387b7b3f23da9f80195f", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "b83c05279f0707f55ca5387b7b3f23da9f80195f", "size": 6191, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.1.1.tgz", "integrity": "sha512-dNF6zRumC9KNmvS8zhG0vVmKcWp030Ks0Rdwvu3N9VYt0NbBk3hm7RnDhdbdFO/BemGU5maMp+AOJvDfAthD3w=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1447122135839, "_hasShrinkwrap": false, "_cnpm_publish_time": 1447122135839, "_cnpmcore_publish_time": "2021-12-16T22:16:39.982Z"}, "1.1.0": {"name": "jwa", "version": "1.1.0", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.4", "buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "^1.0.0"}, "devDependencies": {"semver": "^4.3.6", "tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.1.0", "_shasum": "c432e7649352d7e17f12587441c18be52fbca52a", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.3", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "c432e7649352d7e17f12587441c18be52fbca52a", "size": 6070, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.1.0.tgz", "integrity": "sha512-VArbHyeASZyzlczfDgXMelXy+cG6ZKKHMoqEOaPpMS2kUokyHQI+aDvy19SF9PhvBb+fLCgWfradsa3HD7JoTw=="}, "publish_time": 1437066435670, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437066435670, "_cnpmcore_publish_time": "2021-12-16T22:16:40.169Z"}, "1.0.2": {"name": "jwa", "version": "1.0.2", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.4", "buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "^1.0.0"}, "devDependencies": {"tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "_id": "jwa@1.0.2", "_shasum": "fd79609f1e772e299dce8ddb76d00659dd83511f", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.3", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "fd79609f1e772e299dce8ddb76d00659dd83511f", "size": 5733, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.0.2.tgz", "integrity": "sha512-3InPmrUtackTvLXsoN6NtvgNMYDUd+dFQJ3od4GmMonXIPpxV9AYl4LyH/7Pb9GwaXtBpwVgAu8ue+uVrG65Lg=="}, "publish_time": 1437066153345, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437066153345, "_cnpmcore_publish_time": "2021-12-16T22:16:40.364Z"}, "1.0.1": {"name": "jwa", "version": "1.0.1", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.4", "buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "^1.0.0"}, "devDependencies": {"tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa", "_id": "jwa@1.0.1", "_shasum": "bd8d786c5eb43f8ac38d798dd42de15377d5ed0d", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.12.2", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "bd8d786c5eb43f8ac38d798dd42de15377d5ed0d", "size": 6666, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.0.1.tgz", "integrity": "sha512-oDRqeHx/nV/7hJnt143drMLTCWsYXlikuF2oj13OR7slq7zqQBPoVee+0TSJnT2b2r+IVniRDsr7bhBy2SbcIg=="}, "publish_time": 1435774155900, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435774155900, "_cnpmcore_publish_time": "2021-12-16T22:16:40.945Z"}, "1.0.0": {"name": "jwa", "version": "1.0.0", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.4", "buffer-equal-constant-time": "^1.0.1"}, "devDependencies": {"tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa", "_id": "jwa@1.0.0", "_shasum": "040b64fb582171a65f3368e96837ea4dcf42f3d8", "_from": ".", "_npmVersion": "2.1.14", "_nodeVersion": "0.10.35", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "040b64fb582171a65f3368e96837ea4dcf42f3d8", "size": 5554, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.0.0.tgz", "integrity": "sha512-66VuofBsvEFDGQf10c6U2R+BHTYVbtutwYV+pH05Sdtu1VvPtaI3EPFsBJFjuEBCoilsbayu0mFGmxBT6YvCrQ=="}, "publish_time": 1420993096427, "_hasShrinkwrap": false, "_cnpm_publish_time": 1420993096427, "_cnpmcore_publish_time": "2021-12-16T22:16:41.144Z"}, "0.0.3": {"name": "jwa", "version": "0.0.3", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.4", "buffer-equal-constant-time": "^1.0.1"}, "devDependencies": {"tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa", "_id": "jwa@0.0.3", "_shasum": "11d0c7cd52b74ad1d263e9cabe82df369032855f", "_from": ".", "_npmVersion": "2.1.14", "_nodeVersion": "0.10.35", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "11d0c7cd52b74ad1d263e9cabe82df369032855f", "size": 5555, "noattachment": false, "tarball": "https://registry.npmmirror.com/jwa/-/jwa-0.0.3.tgz", "integrity": "sha512-DLIVuyPDztcDy64CfC6tCnNScTSdqoPJz878azxwg/TdvwiqUDPQxe9CllodtaxZxuvni6eIhgnkMwR0N8mcsg=="}, "publish_time": 1420993085702, "_hasShrinkwrap": false, "_cnpm_publish_time": 1420993085702, "_cnpmcore_publish_time": "2021-12-16T22:16:41.411Z"}, "0.0.2": {"name": "jwa", "version": "0.0.2", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.4"}, "devDependencies": {"tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "readmeFilename": "README.md", "_id": "jwa@0.0.2", "dist": {"tarball": "https://registry.npmmirror.com/jwa/-/jwa-0.0.2.tgz", "shasum": "e0dfe51b9650d0767817618b0e12638446c4ea76", "size": 5506, "noattachment": false, "integrity": "sha512-dY3auK6BCU/z5+lv1rx6ivy6KDcNReD5+alJV47Dg9PVldFT++peEIWcPrxua00IcjccZUsamXVx6XAvgT37UA=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1360531497199, "_hasShrinkwrap": false, "_cnpm_publish_time": 1360531497199, "_cnpmcore_publish_time": "2021-12-16T22:16:41.630Z"}, "0.0.1": {"name": "jwa", "version": "0.0.1", "description": "JWA implementation (supports all JWS algorithms)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.3"}, "devDependencies": {"tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "readmeFilename": "README.md", "_id": "jwa@0.0.1", "dist": {"tarball": "https://registry.npmmirror.com/jwa/-/jwa-0.0.1.tgz", "shasum": "2d05f54d68f170648c30fe45944731a388cd07cc", "size": 4383, "noattachment": false, "integrity": "sha512-V7zCfMU2IUZNABSan5M+1NlO3rVn/2ukPLHP1d6zN/1Yyajhe8mGC1dr6Zj1rCVme5sULI40BwcxhFueV39iIw=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1360465190477, "_hasShrinkwrap": false, "_cnpm_publish_time": 1360465190477, "_cnpmcore_publish_time": "2021-12-16T22:16:41.851Z"}, "0.0.0": {"name": "jwa", "version": "0.0.0", "description": "JWA implementation, supports all algorithms", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"base64url": "~0.0.3"}, "devDependencies": {"tap": "~0.3.3"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jwa.git"}, "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5ca1d5182bb64ff15f6f52000fb30a6582a4c8b0", "_id": "jwa@0.0.0", "dist": {"tarball": "https://registry.npmmirror.com/jwa/-/jwa-0.0.0.tgz", "shasum": "4147da4378738c594aecf5ad63064bd8cd2106a2", "size": 2781, "noattachment": false, "integrity": "sha512-QgAmn7m/mEt9QTCWCARdcJ/YyIiOXPJhjuoGhv6Or3BebY3n1AuwCQfMhVi4LRGCsLSKKblbzhG12fLCo/oG+g=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1360461730716, "_hasShrinkwrap": false, "_cnpm_publish_time": 1360461730716, "_cnpmcore_publish_time": "2021-12-16T22:16:42.244Z"}, "2.0.1": {"name": "jwa", "version": "2.0.1", "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "jwa@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "texe<PERSON>", "email": "<EMAIL>"}, {"name": "julien.w<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "charles<PERSON>", "email": "<EMAIL>"}, {"name": "lozano.okta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jwa#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "dist": {"shasum": "bf8176d1ad0cd72e0f3f58338595a13e110bc804", "tarball": "https://registry.npmmirror.com/jwa/-/jwa-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-hRF04fqJIP8Abbkq5NKGN0Bbr3JxlQ+qhZufXVr0DvujKy93ZCbXZMHDL4EOtodSbCWxOqR8MS1tXA5hwqCXDg==", "signatures": [{"sig": "MEYCIQC9F0Ze7ctXkWT4nsDvyEEk7U4+T9GNbaiOwF6e46FvTgIhAJJmLMbXzMj76+IBDlp7tU06UWdV9m4OaZILkGjCLyzw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14052, "size": 4720}, "main": "index.js", "gitHead": "02f44f3bfaacb43d5b4199157bfdc715ee2e70f0", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jwa.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "JWA implementation (supports all JWS algorithms)", "directories": {"test": "test"}, "_nodeVersion": "22.15.0", "dependencies": {"safe-buffer": "^5.0.1", "ecdsa-sig-formatter": "1.0.11", "buffer-equal-constant-time": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "6.2.0", "semver": "4.3.6", "base64url": "^2.0.0", "jwk-to-pem": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jwa_2.0.1_1746618169954_0.6941724263193607", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-05-07T11:42:50.147Z", "publish_time": 1746618170147, "_source_registry_name": "default"}, "1.4.2": {"name": "jwa", "version": "1.4.2", "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "jwa@1.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "omsmith", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "texe<PERSON>", "email": "<EMAIL>"}, {"name": "julien.w<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "charles<PERSON>", "email": "<EMAIL>"}, {"name": "lozano.okta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/brianloveswords/node-jwa#readme", "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "dist": {"shasum": "16011ac6db48de7b102777e57897901520eec7b9", "tarball": "https://registry.npmmirror.com/jwa/-/jwa-1.4.2.tgz", "fileCount": 4, "integrity": "sha512-eeH5JO+21J78qMvTIDdBXidBd6nG2kZjg5Ohz/1fpa28Z4CcsWUzJ1ZZyFq/3z3N17aZy+ZuBoHljASbL1WfOw==", "signatures": [{"sig": "MEYCIQCZ99LhnLKWDfk7MnHCjiDGWEPT5gnAjSqaHqiR/KD6zwIhAP/YO0J1OucjsizKWQ09c5qJfU9Z0kdWg/XPrtn7iE1K", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13989, "size": 4656}, "main": "index.js", "gitHead": "0b83ff834d10a8cbf576a96a381665d924229f68", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/brianloveswords/node-jwa.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "JWA implementation (supports all JWS algorithms)", "directories": {"test": "test"}, "_nodeVersion": "22.15.0", "dependencies": {"safe-buffer": "^5.0.1", "ecdsa-sig-formatter": "1.0.11", "buffer-equal-constant-time": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "6.2.0", "semver": "4.3.6", "base64url": "^2.0.0", "jwk-to-pem": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jwa_1.4.2_1746619155609_0.30982315517275594", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-05-07T11:59:15.769Z", "publish_time": 1746619155769, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/brianloveswords/node-jwa/issues"}, "homepage": "https://github.com/brianloveswords/node-jwa#readme", "keywords": ["jwa", "jws", "jwt", "rsa", "ecdsa", "hmac"], "repository": {"url": "git://github.com/brianloveswords/node-jwa.git", "type": "git"}, "_source_registry_name": "default"}