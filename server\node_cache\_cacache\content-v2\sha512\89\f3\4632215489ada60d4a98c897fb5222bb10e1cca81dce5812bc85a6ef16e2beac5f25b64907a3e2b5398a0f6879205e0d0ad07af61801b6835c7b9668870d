{"_attachments": {}, "_id": "mime-types", "_rev": "1236-61f14617fbcaa28a7594a1f2", "description": "The ultimate javascript content-type utility.", "dist-tags": {"latest": "3.0.1", "next": "3.0.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}], "name": "mime-types", "readme": "# mime-types\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nThe ultimate javascript content-type utility.\n\nSimilar to [the `mime@1.x` module](https://www.npmjs.com/package/mime), except:\n\n- __No fallbacks.__ Instead of naively returning the first available type,\n  `mime-types` simply returns `false`, so do\n  `var type = mime.lookup('unrecognized') || 'application/octet-stream'`.\n- No `new Mime()` business, so you could do `var lookup = require('mime-types').lookup`.\n- No `.define()` functionality\n- Bug fixes for `.lookup(path)`\n\nOtherwise, the API is compatible with `mime` 1.x.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install mime-types\n```\n\n## Note on MIME Type Data and Semver\n\nThis package considers the programmatic api as the semver compatibility. Additionally, the package which provides the MIME data\nfor this package (`mime-db`) *also* considers it's programmatic api as the semver contract. This means the MIME type resolution is *not* considered\nin the semver bumps.\n\nIn the past the version of `mime-db` was pinned to give two decision points when adopting MIME data changes. This is no longer true. We still update the\n`mime-db` package here as a `minor` release when necessary, but will use a `^` range going forward. This means that if you want to pin your `mime-db` data\nyou will need to do it in your application. While this expectation was not set in docs until now, it is how the pacakge operated, so we do not feel this is\na breaking change.\n\nIf you wish to pin your `mime-db` version you can do that with overrides via your package manager of choice. See their documentation for how to correctly configure that.\n\n## Adding Types\n\nAll mime types are based on [mime-db](https://www.npmjs.com/package/mime-db),\nso open a PR there if you'd like to add mime types.\n\n## API\n\n```js\nvar mime = require('mime-types')\n```\n\nAll functions return `false` if input is invalid or not found.\n\n### mime.lookup(path)\n\nLookup the content-type associated with a file.\n\n```js\nmime.lookup('json') // 'application/json'\nmime.lookup('.md') // 'text/markdown'\nmime.lookup('file.html') // 'text/html'\nmime.lookup('folder/file.js') // 'application/javascript'\nmime.lookup('folder/.htaccess') // false\n\nmime.lookup('cats') // false\n```\n\n### mime.contentType(type)\n\nCreate a full content-type header given a content-type or extension.\nWhen given an extension, `mime.lookup` is used to get the matching\ncontent-type, otherwise the given content-type is used. Then if the\ncontent-type does not already have a `charset` parameter, `mime.charset`\nis used to get the default charset and add to the returned content-type.\n\n```js\nmime.contentType('markdown') // 'text/x-markdown; charset=utf-8'\nmime.contentType('file.json') // 'application/json; charset=utf-8'\nmime.contentType('text/html') // 'text/html; charset=utf-8'\nmime.contentType('text/html; charset=iso-8859-1') // 'text/html; charset=iso-8859-1'\n\n// from a full path\nmime.contentType(path.extname('/path/to/file.json')) // 'application/json; charset=utf-8'\n```\n\n### mime.extension(type)\n\nGet the default extension for a content-type.\n\n```js\nmime.extension('application/octet-stream') // 'bin'\n```\n\n### mime.charset(type)\n\nLookup the implied default charset of a content-type.\n\n```js\nmime.charset('text/markdown') // 'UTF-8'\n```\n\n### var type = mime.types[extension]\n\nA map of content-types by extension.\n\n### [extensions...] = mime.extensions[type]\n\nA map of extensions by content-type.\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/mime-types/master?label=ci\n[ci-url]: https://github.com/jshttp/mime-types/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/mime-types/master\n[coveralls-url]: https://coveralls.io/r/jshttp/mime-types?branch=master\n[node-version-image]: https://badgen.net/npm/node/mime-types\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/mime-types\n[npm-url]: https://npmjs.org/package/mime-types\n[npm-version-image]: https://badgen.net/npm/v/mime-types\n", "time": {"created": "2022-01-26T13:01:11.618Z", "modified": "2025-05-12T08:55:06.352Z", "2.1.34": "2021-11-09T04:30:21.904Z", "2.1.33": "2021-10-01T17:09:22.903Z", "2.1.32": "2021-07-27T17:39:18.598Z", "2.1.31": "2021-06-01T17:29:08.384Z", "2.1.30": "2021-04-02T05:06:28.101Z", "2.1.29": "2021-02-18T00:33:48.436Z", "2.1.28": "2021-01-02T04:28:59.153Z", "2.1.27": "2020-04-24T03:36:12.387Z", "2.1.26": "2020-01-06T03:47:55.198Z", "2.1.25": "2019-11-12T14:18:16.771Z", "2.1.24": "2019-04-21T03:46:47.001Z", "2.1.23": "2019-04-18T04:41:15.063Z", "2.1.22": "2019-02-14T22:47:38.276Z", "2.1.21": "2018-10-20T03:37:07.996Z", "2.1.20": "2018-08-26T22:07:10.102Z", "2.1.19": "2018-07-18T05:29:59.712Z", "2.1.18": "2018-02-16T17:34:21.420Z", "2.1.17": "2017-09-02T03:26:34.147Z", "2.1.16": "2017-07-25T02:42:39.330Z", "2.1.15": "2017-03-24T03:55:55.438Z", "2.1.14": "2017-01-15T05:29:02.108Z", "2.1.13": "2016-11-18T21:39:28.247Z", "2.1.12": "2016-09-18T22:23:37.122Z", "2.1.11": "2016-05-02T05:02:47.292Z", "2.1.10": "2016-02-15T22:27:21.187Z", "2.1.9": "2016-01-06T17:52:14.411Z", "2.1.8": "2015-12-01T05:55:42.054Z", "2.1.7": "2015-09-20T18:44:04.903Z", "2.1.6": "2015-09-04T00:14:46.978Z", "2.1.5": "2015-08-20T18:14:08.205Z", "2.1.4": "2015-07-31T01:37:15.784Z", "2.1.3": "2015-07-13T22:21:31.913Z", "2.1.2": "2015-06-26T02:31:38.715Z", "2.1.1": "2015-06-08T14:30:21.703Z", "2.1.0": "2015-06-08T03:53:55.197Z", "2.0.14": "2015-06-06T22:55:10.695Z", "2.0.13": "2015-06-01T04:37:42.299Z", "2.0.12": "2015-05-20T03:10:38.358Z", "2.0.11": "2015-05-05T16:56:57.397Z", "2.0.10": "2015-03-14T00:57:09.091Z", "2.0.9": "2015-02-10T05:07:45.349Z", "2.0.8": "2015-01-30T04:58:29.008Z", "2.0.7": "2014-12-30T20:27:02.217Z", "2.0.6": "2014-12-30T17:41:58.705Z", "2.0.5": "2014-12-29T19:57:28.126Z", "2.0.4": "2014-12-10T17:57:57.684Z", "2.0.3": "2014-11-09T18:09:36.046Z", "2.0.2": "2014-09-29T01:50:31.565Z", "2.0.1": "2014-09-08T05:02:04.030Z", "2.0.0": "2014-09-02T08:32:26.198Z", "1.0.2": "2014-08-04T03:44:33.290Z", "1.0.1": "2014-06-24T20:45:58.288Z", "1.0.0": "2014-05-12T20:00:39.325Z", "0.1.0": "2014-05-02T15:54:00.885Z", "2.1.35": "2022-03-12T18:04:43.042Z", "3.0.0": "2024-08-31T13:57:14.134Z", "3.0.1": "2025-03-26T22:53:47.851Z"}, "versions": {"2.1.34": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.34", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.51.0"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.2", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "9.1.3", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "a50dafc08c7d1969ba581a51de5d94493b713bef", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.34", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"shasum": "5a712f9ec1503511a945803640fafe09d3793c24", "size": 5536, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.34.tgz", "integrity": "sha512-6cP692WwGIs9XXdOO4++N+7qjqv0rqxxVvJ3VHPh/Sc9mVZcQP+ZGhkKiTvWMQRr2tbHkJP/Yn7Y0npb3ZBs4A=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.34_1636432221719_0.7160929014759547"}, "_hasShrinkwrap": false, "publish_time": 1636432221904, "_cnpm_publish_time": 1636432221904, "_cnpmcore_publish_time": "2021-12-13T12:34:43.328Z"}, "2.1.33": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.33", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.50.0"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.24.2", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.1.2", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "c6ff9b224577f0cd49f1155f421b24c24a57bc3e", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.33", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"shasum": "1fa12a904472fafd068e48d9e8401f74d3f70edb", "size": 5564, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.33.tgz", "integrity": "sha512-plLElXp7pRDd0bNZHw+nMd52vRYjLwQjygaNg7ddJ2uJtTlmnTCjWuPKxVu6//AdaRuME84SvLW91sIkBqGT0g=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.33_1633108162751_0.35499888332958607"}, "_hasShrinkwrap": false, "publish_time": 1633108162903, "_cnpm_publish_time": 1633108162903, "_cnpmcore_publish_time": "2021-12-13T12:34:43.657Z"}, "2.1.32": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.32", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.49.0"}, "devDependencies": {"eslint": "7.31.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.23.4", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.0.3", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "0cc1e563988dc69ffc9fa642a881e2cf7eef55c9", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.32", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "1d00e89e7de7fe02008db61001d9e02852670fd5", "size": 5542, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.32.tgz", "integrity": "sha512-hJGaVS4G4c9TSMYh2n6SQAGrC4RnfU+daP8G7cSCmaqNjiOoUY0VHCMS42pxnQmVF1GWwFhbHWn3RIxCqTmZ9A=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.32_1627407558456_0.18706801791640593"}, "_hasShrinkwrap": false, "publish_time": 1627407558598, "_cnpm_publish_time": 1627407558598, "_cnpmcore_publish_time": "2021-12-13T12:34:43.920Z"}, "2.1.31": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.31", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.48.0"}, "devDependencies": {"eslint": "7.27.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.23.4", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.0", "eslint-plugin-standard": "4.1.0", "mocha": "8.4.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "cbeaf522ad6f7b51600bd9ec5ee8e7a968e2e18b", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.31", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "a00d76b74317c61f9c2db2218b8e9f8e9c5c9e6b", "size": 5521, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.31.tgz", "integrity": "sha512-XGZnNzm3QvgKxa8dpzyhFTHmpP3l5YNusmne07VUOXxou9CqUqYa/HBy124RqtVh/O2pECas/MOcsDgpilPOPg=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.31_1622568548229_0.7284665852367624"}, "_hasShrinkwrap": false, "publish_time": 1622568548384, "_cnpm_publish_time": 1622568548384, "_cnpmcore_publish_time": "2021-12-13T12:34:44.197Z"}, "2.1.30": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.30", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.47.0"}, "devDependencies": {"eslint": "7.23.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-markdown": "2.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.3.2", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "b0abe0c67c6654cb9a2efb9a92c776eb160d7b27", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.30", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "6e7be8b4c479825f85ed6326695db73f9305d62d", "size": 5542, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.30.tgz", "integrity": "sha512-crmjA4bLtR8m9qLpHvgxSChT+XoSlZi8J4n/aIdn3z92e/U47Z0V/yl+Wh9W046GgFVAmoNR/fmdbZYcSSIUeg=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.30_1617339987950_0.13379782228732684"}, "_hasShrinkwrap": false, "publish_time": 1617339988101, "_cnpm_publish_time": 1617339988101, "_cnpmcore_publish_time": "2021-12-13T12:34:44.472Z"}, "2.1.29": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.29", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.46.0"}, "devDependencies": {"eslint": "7.20.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-markdown": "2.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.3.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "9a05a599f071203318ab2c3d848c6a6e46a59fe3", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.29", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "1d4ab77da64b91f5f72489df29236563754bb1b2", "size": 5500, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.29.tgz", "integrity": "sha512-Y/jMt/S5sR9OaqteJtslsFZKWOIIqMACsJSiHghlCAyhf7jfVYjKBmLiX8OgpWeW+fjJ2b+Az69aPFPkUOY6xQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.29_1613608428286_0.45392395861166324"}, "_hasShrinkwrap": false, "publish_time": 1613608428436, "_cnpm_publish_time": 1613608428436, "_cnpmcore_publish_time": "2021-12-13T12:34:44.769Z"}, "2.1.28": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.28", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.45.0"}, "devDependencies": {"eslint": "7.17.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.2.1", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "4a4f710d9f0c59fc998e444cb70df02d79b4f932", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.28", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "1160c4757eab2c5363888e005273ecf79d2a0ecd", "size": 5495, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.28.tgz", "integrity": "sha512-0TO2yJ5YHYr7M2zzT7gDU1tbwHxEUWBCLt0lscSNpcdAfFyJOVEpRYNS7EXVcTLNj/25QO8gulHC5JtTzSE2UQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.28_1609561739020_0.7011492525542153"}, "_hasShrinkwrap": false, "publish_time": 1609561739153, "_cnpm_publish_time": 1609561739153, "_cnpmcore_publish_time": "2021-12-13T12:34:45.113Z"}, "2.1.27": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.27", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.44.0"}, "devDependencies": {"eslint": "6.8.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.20.2", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "7.1.1", "nyc": "15.0.1"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "47b62ac45e9b176a2af35532d0eea4968bb9eb6d", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.27", "_nodeVersion": "13.12.0", "_npmVersion": "6.14.4", "dist": {"shasum": "47949f98e279ea53119f5722e0f34e529bec009f", "size": 5367, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.27.tgz", "integrity": "sha512-JIhqnCasI9yD+SsmkquHBxTSEuZdQX5BuQnS2Vc7puQQQ+8yiP5AY5uWhpdv4YL4VM5c6iliiYWPgJ/nJQLp7w=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.27_1587699372290_0.7009359023949084"}, "_hasShrinkwrap": false, "publish_time": 1587699372387, "_cnpm_publish_time": 1587699372387, "_cnpmcore_publish_time": "2021-12-13T12:34:45.424Z"}, "2.1.26": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.26", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.43.0"}, "devDependencies": {"eslint": "6.8.0", "eslint-config-standard": "14.1.0", "eslint-plugin-import": "2.19.1", "eslint-plugin-node": "11.0.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "7.0.0", "nyc": "15.0.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "73f9933bfa5247337b459240ec67ea6045cdec84", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.26", "_nodeVersion": "12.12.0", "_npmVersion": "6.11.3", "dist": {"shasum": "9c921fc09b7e149a65dfdc0da4d20997200b0a06", "size": 5301, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.26.tgz", "integrity": "sha512-01paPWYgLrkqAyrlDorC1uDwl2p3qZT7yl806vW7DvDoxwXi46jsjFbg+WdwotBIk6/MbEhO/dh5aZ5sNj/dWQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fishrock123"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.26_1578282475037_0.17516931585429973"}, "_hasShrinkwrap": false, "publish_time": 1578282475198, "_cnpm_publish_time": 1578282475198, "_cnpmcore_publish_time": "2021-12-13T12:34:45.748Z"}, "2.1.25": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.25", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.42.0"}, "devDependencies": {"eslint": "6.6.0", "eslint-config-standard": "14.1.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-node": "10.0.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "6.2.2", "nyc": "14.1.1"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "7aaede09275fb002f7bbd63060ed4c3a98575b9d", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.25", "_nodeVersion": "12.12.0", "_npmVersion": "6.11.3", "dist": {"shasum": "39772d46621f93e2a80a856c53b86a62156a6437", "size": 5235, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.25.tgz", "integrity": "sha512-5KhStqB5xpTAeGqKBAMgwaYMnQik7teQN4IAzC7npDv6kzeU6prfkR67bc87J1kWMPGkoaZSq1npmexMgkmEVg=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fishrock123"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.25_1573568296656_0.4870118202385958"}, "_hasShrinkwrap": false, "publish_time": 1573568296771, "_cnpm_publish_time": 1573568296771, "_cnpmcore_publish_time": "2021-12-13T12:34:46.168Z"}, "2.1.24": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.24", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.40.0"}, "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "e5b5f41ef0d7e4e66eb9baeec7920c0cd9ec81f0", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.24", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b6f8d0b3e951efb77dedeca194cff6d16f676f81", "size": 5208, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.24.tgz", "integrity": "sha512-Wa<PERSON><PERSON>3MCl5fapm3oLxU4eYDw77IQM2ACcxQ9RIxfaC3ooc6PFuBMGZZsYpvoXS5D5QTWPieo1jjLdAm3TBP3cQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fishrock123"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.24_1555818406742_0.4628586923544964"}, "_hasShrinkwrap": false, "publish_time": 1555818407001, "_cnpm_publish_time": 1555818407001, "_cnpmcore_publish_time": "2021-12-13T12:34:46.535Z"}, "2.1.23": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.23", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.39.0"}, "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.3", "nyc": "14.0.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "28b73337cb1aa1583b72d448f30e2f49d6f5f07e", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.23", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d4eacd87de99348a6858fe1e479aad877388d977", "size": 5175, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.23.tgz", "integrity": "sha512-ROk/m+gMVSrRxTkMlaQOvFmFmYDc7sZgrjjM76abqmd2Cc5fCV7jAMA5XUccEtJ3cYiYdgixUVI+fApc2LkXlw=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fishrock123"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.23_1555562474870_0.5469095710208383"}, "_hasShrinkwrap": false, "publish_time": 1555562475063, "_cnpm_publish_time": 1555562475063, "_cnpmcore_publish_time": "2021-12-13T12:34:46.980Z"}, "2.1.22": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.22", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.38.0"}, "devDependencies": {"eslint": "5.13.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "0ac5fe1ad9008b4b141e9f32fb4d63a6478669ba", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.22", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fe6b355a190926ab7698c9a0556a11199b2199bd", "size": 5142, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.22.tgz", "integrity": "sha512-aGl6TZGnhm/li6F7yx82bJiBZwgiEa4Hf6CNr8YO+r5UHr53tSTYZb102zyU50DOWWKeOv0uQLRL0/9EiKWCog=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fishrock123"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.22_1550184458136_0.07147731380368416"}, "_hasShrinkwrap": false, "publish_time": 1550184458276, "_cnpm_publish_time": 1550184458276, "_cnpmcore_publish_time": "2021-12-13T12:34:47.396Z"}, "2.1.21": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.21", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.37.0"}, "devDependencies": {"eslint": "5.7.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "gitHead": "1aed04a9c43b8b920c0f77d1cb160d42079d978c", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.21", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "28995aa1ecb770742fe6ae7e58f9181c744b3f96", "size": 4962, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.21.tgz", "integrity": "sha512-3iL6DbwpyLzjR3xHSFNFeb9Nz/M8WDkX33t1GFQnFOllWk8pOrh/LSrB5OXlnlW5P9LH73X6loW/eogc+F5lJg=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fishrock123"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.21_1540006627863_0.6812190744417017"}, "_hasShrinkwrap": false, "publish_time": 1540006627996, "_cnpm_publish_time": 1540006627996, "_cnpmcore_publish_time": "2021-12-13T12:34:47.864Z"}, "2.1.20": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.20", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.36.0"}, "devDependencies": {"eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.13.0", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.8.0", "eslint-plugin-standard": "3.1.0", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "1b0f6e8b605fa83c961ef352be701e23f66cf917", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.20", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "930cb719d571e903738520f8470911548ca2cc19", "size": 5005, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.20.tgz", "integrity": "sha512-HrkrPaP9vGuWbLK1B1FfgAkbqNjIuy4eHlIYnFi7kamZyLLrGlo2mpcx0bBmNpKqBtYtAfGbodDddIgddSJC2A=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fishrock123"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.20_1535321229921_0.8062784689775195"}, "_hasShrinkwrap": false, "publish_time": 1535321230102, "_cnpm_publish_time": 1535321230102, "_cnpmcore_publish_time": "2021-12-13T12:34:48.306Z"}, "2.1.19": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.19", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.35.0"}, "devDependencies": {"eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.13.0", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.8.0", "eslint-plugin-standard": "3.1.0", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "7c4ce23d7354fbf64c69d7b7be8413c4ba2add78", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.19", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "71e464537a7ef81c15f2db9d97e913fc0ff606f0", "size": 4971, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.19.tgz", "integrity": "sha512-P1tKYHVSZ6uFo26mtnve4HQFE3koh1UWVkp8YUC+ESBHe945xWSoXuHHiGarDqcEZ+whpCDnlNw5LON0kLo+sw=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fishrock123"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.19_1531891799651_0.29011555741327655"}, "_hasShrinkwrap": false, "publish_time": 1531891799712, "_cnpm_publish_time": 1531891799712, "_cnpmcore_publish_time": "2021-12-13T12:34:48.762Z"}, "2.1.18": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.18", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.33.0"}, "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "076f7902e3a730970ea96cd0b9c09bb6110f1127", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.18", "_npmVersion": "5.6.0", "_nodeVersion": "6.13.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6f323f60a83d11146f831ff11fd66e2fe5503bb8", "size": 4896, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.18.tgz", "integrity": "sha512-lc/aahn+t4/SWV/qcmumYjymLsWfN3ELhpmVuUFjgsORruuZPVSwAQryq+HHGvO/SI2KVX26bx+En+zhM8g8hQ=="}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fishrock123"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.18_1518802461370_0.7224202442179994"}, "_hasShrinkwrap": false, "publish_time": 1518802461420, "_cnpm_publish_time": 1518802461420, "_cnpmcore_publish_time": "2021-12-13T12:34:49.395Z"}, "2.1.17": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.17", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.30.0"}, "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "80039fe78213821c2e9b25132d6b02cc37202e8a", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.17", "_shasum": "09d7a393f03e995a79f8af857b70a9e0ab16557a", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "09d7a393f03e995a79f8af857b70a9e0ab16557a", "size": 4759, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.17.tgz", "integrity": "sha512-rOFZoFAbaupSpzARUe5CU1P9mwfX+lIFAuj0soNsEZEnrHu6LZNyV7/FClEB/oF9A1o5KStlumRjW6D4Q2FRCA=="}, "maintainers": [{"email": "<EMAIL>", "name": "fishrock123"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types-2.1.17.tgz_1504322793218_0.6663200033362955"}, "directories": {}, "publish_time": 1504322794147, "_hasShrinkwrap": false, "_cnpm_publish_time": 1504322794147, "_cnpmcore_publish_time": "2021-12-13T12:34:49.831Z"}, "2.1.16": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.16", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.29.0"}, "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "a776f883a8bb1d50588224c46caefa6fc313f790", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.16", "_shasum": "2b858a52e5ecd516db897ac2be87487830698e23", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2b858a52e5ecd516db897ac2be87487830698e23", "size": 4630, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.16.tgz", "integrity": "sha512-YjTLbZxlgVrR0Gv3KxaqEcTDMoxI+kjRw8box2aRPm0IDtIqP6hC6pv5F2ONy7UcgTtSQE6zAqkZE7jDP0gb1g=="}, "maintainers": [{"email": "<EMAIL>", "name": "fishrock123"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types-2.1.16.tgz_1500950558329_0.4321689426433295"}, "directories": {}, "publish_time": 1500950559330, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500950559330, "_cnpmcore_publish_time": "2021-12-13T12:34:50.306Z"}, "2.1.15": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.15", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.27.0"}, "devDependencies": {"eslint": "3.18.0", "eslint-config-standard": "7.1.0", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "2.1.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "c44863eb0463ee16f3eb04576591cc4c4d6b214c", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.15", "_shasum": "a4ebf5064094569237b8cf70046776d09fc92aed", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a4ebf5064094569237b8cf70046776d09fc92aed", "size": 4512, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.15.tgz", "integrity": "sha512-PjleM8evsL+OvsuE6EXom+8QAcSYALjmw+vYFqH8I+/+wNlewVgbM7/O1wcdCVL/ta8SC6l6BEK7A0/mZywpfg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mime-types-2.1.15.tgz_1490327753615_0.3609113476704806"}, "directories": {}, "publish_time": 1490327755438, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490327755438, "_cnpmcore_publish_time": "2021-12-13T12:34:50.873Z"}, "2.1.14": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.14", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.26.0"}, "devDependencies": {"eslint": "3.13.1", "eslint-config-standard": "6.2.1", "eslint-plugin-promise": "3.4.0", "eslint-plugin-standard": "2.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "d7d15e50fe6b3a2da79d855015d25efa50e9f157", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.14", "_shasum": "f7ef7d97583fcaf3b7d282b6f8b5679dab1e94ee", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f7ef7d97583fcaf3b7d282b6f8b5679dab1e94ee", "size": 4382, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.14.tgz", "integrity": "sha512-okJd888hr2XVzvoRxEOnEEgmlsOnLVoIOAKoBgYMLOQPNQFprAx970dULXC3ueiQMIiTsSxUFSpa2y3IlBefCg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/mime-types-2.1.14.tgz_1484458141358_0.7563668976072222"}, "directories": {}, "publish_time": 1484458142108, "_hasShrinkwrap": false, "_cnpm_publish_time": 1484458142108, "_cnpmcore_publish_time": "2021-12-13T12:34:51.420Z"}, "2.1.13": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.13", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.25.0"}, "devDependencies": {"eslint": "3.10.2", "eslint-config-standard": "6.2.1", "eslint-plugin-promise": "3.4.0", "eslint-plugin-standard": "2.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "83e91a5aea93858bc95ec95a99309592cba0ffe3", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.13", "_shasum": "e07aaa9c6c6b9a7ca3012c69003ad25a39e92a88", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "e07aaa9c6c6b9a7ca3012c69003ad25a39e92a88", "size": 4376, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.13.tgz", "integrity": "sha512-ryBDp1Z/6X90UvjUK3RksH0IBPM137T7cmg4OgD5wQBojlAiUwuok0QeELkim/72EtcYuNlmbkrcGuxj3Kl0YQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mime-types-2.1.13.tgz_1479505166253_0.5666956284549087"}, "directories": {}, "publish_time": 1479505168247, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479505168247, "_cnpmcore_publish_time": "2021-12-13T12:34:52.022Z"}, "2.1.12": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.12", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.24.0"}, "devDependencies": {"eslint": "3.5.0", "eslint-config-standard": "6.0.1", "eslint-plugin-promise": "2.0.1", "eslint-plugin-standard": "2.0.0", "istanbul": "0.4.5", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "7193a9094e2efe31da93988350bb0b32ab18b1ea", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.12", "_shasum": "152ba256777020dd4663f54c2e7bc26381e71729", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "152ba256777020dd4663f54c2e7bc26381e71729", "size": 4375, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.12.tgz", "integrity": "sha512-irQD8Ww11AaU8vbCRjMuaq4huvb2ITxVg/VDBrvf8keFtbWZ3zbGO0tvsCMbD7JlR8mOYw0WbAqi4sL8KGUd5w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mime-types-2.1.12.tgz_1474237415119_0.03028594213537872"}, "directories": {}, "publish_time": 1474237417122, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474237417122, "_cnpmcore_publish_time": "2021-12-13T12:34:52.681Z"}, "2.1.11": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.11", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.23.0"}, "devDependencies": {"istanbul": "0.4.3", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "298ffcf490a5d6e60edea7bf7a69036df04846b1", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.11", "_shasum": "c259c471bda808a85d6cd193b430a5fae4473b3c", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c259c471bda808a85d6cd193b430a5fae4473b3c", "size": 4279, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.11.tgz", "integrity": "sha512-14dD2ItPaGFLVyhddUE/Rrtg+g7v8RmBLjN5Xsb3fJJLKunoZOw3I3bK6csjoJKjaNjcXo8xob9kHDyOpJfgpg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mime-types-2.1.11.tgz_1462165365027_0.7217204745393246"}, "directories": {}, "publish_time": 1462165367292, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462165367292, "_cnpmcore_publish_time": "2021-12-13T12:34:53.329Z"}, "2.1.10": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.10", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "~1.22.0"}, "devDependencies": {"istanbul": "0.4.2", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "70785d38e9cc251137b00f73ab3d3257c4aea203", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.10", "_shasum": "b93c7cb4362e16d41072a7e54538fb4d43070837", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b93c7cb4362e16d41072a7e54538fb4d43070837", "size": 4266, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.10.tgz", "integrity": "sha512-cjDtNq/7/kWehCc5gPzRnwBMJ8lx+kmg1rvBtbx8o84v+E3Gtc+hVgAXXhLcpICjQbgQGFYSt8WCCmen5fOQaQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/mime-types-2.1.10.tgz_1455575237256_0.9163766100537032"}, "directories": {}, "publish_time": 1455575241187, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455575241187, "_cnpmcore_publish_time": "2021-12-13T12:34:53.971Z"}, "2.1.9": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.9", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.21.0"}, "devDependencies": {"istanbul": "0.4.1", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "329f1c77e1a77c8fac59b15038e3808e9e314d96", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.9", "_shasum": "dfb396764b5fdf75be34b1f4104bc3687fb635f8", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dfb396764b5fdf75be34b1f4104bc3687fb635f8", "size": 4215, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.9.tgz", "integrity": "sha512-omiqgDW9rOHbvPP6PrGKQvSIpNh5OplM4hfU2aA+cThb51GKaWxL9Fd8OWNK/ZbtxOCJn5ydQ83PXKMyz9XRFg=="}, "directories": {}, "publish_time": 1452102734411, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452102734411, "_cnpmcore_publish_time": "2021-12-13T12:34:54.559Z"}, "2.1.8": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.8", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.20.0"}, "devDependencies": {"istanbul": "0.4.1", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "100876a23fab896d8cf0d904fc9778dbdfc1695b", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.8", "_shasum": "faf57823de04bc7cbff4ee82c6b63946e812ae72", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "faf57823de04bc7cbff4ee82c6b63946e812ae72", "size": 4204, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.8.tgz", "integrity": "sha512-PHhLyM6wFxef+LfMcX2GvMqEY8mAK4Sv3DmqqW+1sYKGMZRmJkyF6ZgvhJGF3W59YL+4yMC7c2CHfG+lcbqY5A=="}, "directories": {}, "publish_time": 1448949342054, "_hasShrinkwrap": false, "_cnpm_publish_time": 1448949342054, "_cnpmcore_publish_time": "2021-12-13T12:34:55.232Z"}, "2.1.7": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.7", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.19.0"}, "devDependencies": {"istanbul": "0.3.20", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "43f860c7df4a70246272194d601348865d550298", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.7", "_shasum": "ff603970e3c731ef6f7f4df3c9a0f463a13c2755", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ff603970e3c731ef6f7f4df3c9a0f463a13c2755", "size": 4189, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.7.tgz", "integrity": "sha512-m+j0nh7H0xUSa4fvA9QuwEhMhKxepAY4jbKOrpjdGJyyzgTCo2219L4kn8zLx0tXuQsmLf//4wMreA6YBpAw+Q=="}, "directories": {}, "publish_time": 1442774644903, "_hasShrinkwrap": false, "_cnpm_publish_time": 1442774644903, "_cnpmcore_publish_time": "2021-12-13T12:34:55.932Z"}, "2.1.6": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.6", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.18.0"}, "devDependencies": {"istanbul": "0.3.19", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "8bfa7c2c9a4ad07f5807c7a0e547e0246155944d", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.6", "_shasum": "949f8788411864ddc70948a0f21c43f29d25667c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "949f8788411864ddc70948a0f21c43f29d25667c", "size": 4178, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.6.tgz", "integrity": "sha512-/Wu40EFhOQ8MUNVFOFM3kuVGjGKrkWoLGzi7OHeTA/BTTKNkHD7jp/VrbX/bU883aDrpnsoFDPl9pxXY57Rq6Q=="}, "directories": {}, "publish_time": 1441325686978, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441325686978, "_cnpmcore_publish_time": "2021-12-13T12:34:56.670Z"}, "2.1.5": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.5", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.17.0"}, "devDependencies": {"istanbul": "0.3.18", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "de48c96e731e5903433ac2cb5c0d9fd056d9d19b", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.5", "_shasum": "2355ac0e1e0c5a68d8df6474b431192743f0a3ea", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2355ac0e1e0c5a68d8df6474b431192743f0a3ea", "size": 4162, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.5.tgz", "integrity": "sha512-yMHYuggR1CpBWq0MKMIDiZCpB8PPW+axaSoBoBiSLvPsOJEFoqX3m3Rq3RwuX7xPSLBlwsyYUfU+cBKHgYYvHg=="}, "directories": {}, "publish_time": 1440094448205, "_hasShrinkwrap": false, "_cnpm_publish_time": 1440094448205, "_cnpmcore_publish_time": "2021-12-13T12:34:57.385Z"}, "2.1.4": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.4", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.16.0"}, "devDependencies": {"istanbul": "0.3.17", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "0d801665a7374c71d905d14bc3afc2b0624bb896", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.4", "_shasum": "6562b328e341245cb63b14473b1d12b40dec5884", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6562b328e341245cb63b14473b1d12b40dec5884", "size": 4146, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.4.tgz", "integrity": "sha512-Drx8+Rsyw683vPZ91L9aFwuuuwy1zsZMmSUBXSIf0MXU+OckhmTxKEzLLI3IYsX71AAcP4+pj9YoVQjRwYLJdw=="}, "directories": {}, "publish_time": 1438306635784, "_hasShrinkwrap": false, "_cnpm_publish_time": 1438306635784, "_cnpmcore_publish_time": "2021-12-13T12:34:58.126Z"}, "2.1.3": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.3", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.15.0"}, "devDependencies": {"istanbul": "0.3.17", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "565c49ad5683d4a123a170da3444ed32ce426c3a", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.3", "_shasum": "f259849c7eb1f85b8f5f826187278a7f74f0c966", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f259849c7eb1f85b8f5f826187278a7f74f0c966", "size": 4140, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.3.tgz", "integrity": "sha512-yG2aUisDDz12mVDXJAqB4akjYZO8pFntdklyHAHCghr7lgAVTp7t8mrH7e5J6O5ngQD7av4h/2bdWRXU3A1goQ=="}, "directories": {}, "publish_time": 1436826091913, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436826091913, "_cnpmcore_publish_time": "2021-12-13T12:34:58.758Z"}, "2.1.2": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.14.0"}, "devDependencies": {"istanbul": "0.3.16", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "5704d7bb0cf90f14b442f0b954ace6c1dbc5f435", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.2", "_shasum": "6545ccd95afe77b9c655d81c2e6ceace36257227", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6545ccd95afe77b9c655d81c2e6ceace36257227", "size": 4132, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.2.tgz", "integrity": "sha512-u531XpUuatmDePEt7aT0ONMOxlsiiY8rCOgPHKL6uZwQ6QgGvgq1Z+oeYpcWTf4ip/4Q6rosnZWjvt6BMLU1cQ=="}, "directories": {}, "publish_time": 1435285898715, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435285898715, "_cnpmcore_publish_time": "2021-12-13T12:34:59.373Z"}, "2.1.1": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.13.0"}, "devDependencies": {"istanbul": "0.3.14", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "c067c0b0d8a2e7df82c356dc295852688be1245c", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.1", "_shasum": "c7b692796d5166f4826d10b4675c8a916657d04e", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c7b692796d5166f4826d10b4675c8a916657d04e", "size": 4112, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.1.tgz", "integrity": "sha512-Ny1adSku8PrRhb8t9UiC7TZ7CI2ik8sgsDxnkGBNFIVHo9H0CcIpurlhLZ2UH7jpoa06HT0GSUfv++7B/lnd+A=="}, "directories": {}, "publish_time": 1433773821703, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433773821703, "_cnpmcore_publish_time": "2021-12-13T12:35:00.104Z"}, "2.1.0": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.13.0"}, "devDependencies": {"istanbul": "0.3.14", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "95130f68c743520b8b54ce7341d84c0833cbb375", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.1.0", "_shasum": "26e401fb4ccc1fa5c8b15bac4a1aefb9af05b0b1", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "26e401fb4ccc1fa5c8b15bac4a1aefb9af05b0b1", "size": 4081, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.0.tgz", "integrity": "sha512-E3ZX8HRfMgWjjMcENMFAK/uT+7ILMM3Tq7sx8lQid8YGdOsNC4LtYA5UWl86TUN5rF653NtAxn5zZBA3WC19ww=="}, "directories": {}, "publish_time": 1433735635197, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433735635197, "_cnpmcore_publish_time": "2021-12-13T12:35:00.739Z"}, "2.0.14": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.14", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.12.0"}, "devDependencies": {"istanbul": "0.3.9", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "7d53a3351581eb3d7ae1e846ea860037bce6fe3f", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.14", "_shasum": "310e159db23e077f8bb22b748dabfa4957140aa6", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "310e159db23e077f8bb22b748dabfa4957140aa6", "size": 3374, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.14.tgz", "integrity": "sha512-2ZHUEstNkIf2oTWgtODr6X0Cc4Ns/RN/hktdozndiEhhAC2wxXejF1FH0XLHTEImE9h6gr/tcnr3YOnSGsxc7Q=="}, "directories": {}, "publish_time": 1433631310695, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433631310695, "_cnpmcore_publish_time": "2021-12-13T12:35:01.483Z"}, "2.0.13": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.13", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.11.0"}, "devDependencies": {"istanbul": "0.3.9", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "4e53ef0ec8b614992d5d8212e5aff1151ee97738", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.13", "_shasum": "333f5579ae6eb203fd8f30a568a869d31a8f1cac", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "333f5579ae6eb203fd8f30a568a869d31a8f1cac", "size": 3362, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.13.tgz", "integrity": "sha512-qIR+xv6bDd85+ec+VDbr2uBERjiXdeqyFLdsAiXGqboCX5RgF41UZeV2S6RpYK/4bu6ZNsi5oIl5Ct/VGrjK8A=="}, "directories": {}, "publish_time": 1433133462299, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433133462299, "_cnpmcore_publish_time": "2021-12-13T12:35:02.305Z"}, "2.0.12": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.12", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.10.0"}, "devDependencies": {"istanbul": "0.3.9", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "a1e3c4b6bbb6afb615c8a058481d58cb57a4cb95", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.12", "_shasum": "87ae9f124e94f8e440c93d1a72d0dccecdb71135", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "87ae9f124e94f8e440c93d1a72d0dccecdb71135", "size": 3365, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.12.tgz", "integrity": "sha512-KeYczXUPHXZR0KJXsGYggj5C3Etc77fn2es24XtW7sIuRmaC2J9M9sB2GAtugIKFaNNoVR1fX0L4orVnzJpJcA=="}, "directories": {}, "publish_time": 1432091438358, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432091438358, "_cnpmcore_publish_time": "2021-12-13T12:35:03.186Z"}, "2.0.11": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.11", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.9.1"}, "devDependencies": {"istanbul": "0.3.9", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "4b233cfbb6704e88eca121e9d9f6e6f23957e48a", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.11", "_shasum": "bf3449042799d877c815c29929d1e74760e72007", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bf3449042799d877c815c29929d1e74760e72007", "size": 3358, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.11.tgz", "integrity": "sha512-+94e9boSSjaNLBNyZw87aPZ2sf184sHucMXLRj1mlBV0E4D2RW8Yi6dup+i1cBc37SzWMlOdn0K4rgWOUdShBA=="}, "directories": {}, "publish_time": 1430845017397, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430845017397, "_cnpmcore_publish_time": "2021-12-13T12:35:03.974Z"}, "2.0.10": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.10", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.8.0"}, "devDependencies": {"istanbul": "0.3.7", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "9d4533a2b3a68af48a7f3ded9f8f525648e7bcc1", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.10", "_shasum": "eacd81bb73cab2a77447549a078d4f2018c67b4d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "eacd81bb73cab2a77447549a078d4f2018c67b4d", "size": 3368, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.10.tgz", "integrity": "sha512-6BrF520aDPQWWSWcQYthxitp0XWLnB1R/EpjtTeA8q80XxcDkOf0G7ba7j6ml0xlpb2VIPR0NmcsARPZb3CfnQ=="}, "directories": {}, "publish_time": 1426294629091, "_hasShrinkwrap": false, "_cnpm_publish_time": 1426294629091, "_cnpmcore_publish_time": "2021-12-13T12:35:04.844Z"}, "2.0.9": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.9", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.7.0"}, "devDependencies": {"istanbul": "0.3.5", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "1c6d55da440b6a9d2c0e9c2faac98e6b1be47fc7", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.9", "_shasum": "e8449aff27b1245ddc6641b524439ae80c4b78a6", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e8449aff27b1245ddc6641b524439ae80c4b78a6", "size": 3320, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.9.tgz", "integrity": "sha512-BbLj70Zd5NxN2pQLkzHQqteIZF8bqHruYJrO/6holhSKEcm24acJ4T+nVLcXOlXn4JRLbRuBsq6C+sr+rDM2/Q=="}, "directories": {}, "publish_time": 1423544865349, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423544865349, "_cnpmcore_publish_time": "2021-12-13T12:35:05.610Z"}, "2.0.8": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.8", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.6.0"}, "devDependencies": {"istanbul": "0.3.5", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "19e01e8bd630a1719ada4a3e3e9b7192b4ddb034", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.8", "_shasum": "5612bf6b9ec8a1285a81184fa4237fbfdbb89a7e", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5612bf6b9ec8a1285a81184fa4237fbfdbb89a7e", "size": 3280, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.8.tgz", "integrity": "sha512-grE/DCZ4qGuR3JLlB8cPoqeCUNIO8AGyWILVh6y24gxTQlmxVmvV8MvVGErka7UroXOAwQaF9WU9B76uqlVJsQ=="}, "directories": {}, "publish_time": 1422593909008, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422593909008, "_cnpmcore_publish_time": "2021-12-13T12:35:06.594Z"}, "2.0.7": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.7", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.5.0"}, "devDependencies": {"istanbul": "0.3.5", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "4216c095dcc3390c2b8f4a96a9eae94d11420f56", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.7", "_shasum": "0cb58d0403aec977357db324eea67e40c32b44b2", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0cb58d0403aec977357db324eea67e40c32b44b2", "size": 3264, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.7.tgz", "integrity": "sha512-vBZyuPcgzFDeTz1nWiY/X4RKKPxgf+VsrygiGo5azqz3P5teR3JDbSlGGWzn9Bu0jSMcaRFH+x15bgiaIq5RvQ=="}, "directories": {}, "publish_time": 1419971222217, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419971222217, "_cnpmcore_publish_time": "2021-12-13T12:35:07.542Z"}, "2.0.6": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.6", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.4.0"}, "devDependencies": {"istanbul": "0.3.5", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "5348665cf1f2b3611a415da91aac17d431ddd019", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.6", "_shasum": "9deae929dc382665ea42e3848d1d3e3c4aef16e9", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9deae929dc382665ea42e3848d1d3e3c4aef16e9", "size": 3253, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.6.tgz", "integrity": "sha512-gVHjR7TGmrp7i5rY5p9NUBlTUJZiToR1Upu+HN/XeHmpBaBDTM3JGxbY4Nh0LebiniHZyleA3lWtU+bpP8Ce4Q=="}, "directories": {}, "publish_time": 1419961318705, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419961318705, "_cnpmcore_publish_time": "2021-12-13T12:35:08.430Z"}, "2.0.5": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.5", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.3.1"}, "devDependencies": {"istanbul": "0.3.5", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "5f45d92a916100f86bf0eca1d953d523fcc2dfaf", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.5", "_shasum": "017db3ef9aff1fc7229e9babe2ac4b032baf837d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "017db3ef9aff1fc7229e9babe2ac4b032baf837d", "size": 3199, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.5.tgz", "integrity": "sha512-m7ETTXgolXpu7j7GII4WZSmIrT8n8mgf/SeTzyYLJcK7Cdf6agUHOClX2xUoGobX2bBEY1xxx/nUTlMgHFA28A=="}, "directories": {}, "publish_time": 1419883048126, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419883048126, "_cnpmcore_publish_time": "2021-12-13T12:35:09.354Z"}, "2.0.4": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.4", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.3.0"}, "devDependencies": {"istanbul": "0", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "63a9b82e6e364d62428ed5459e5486504c489bf2", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.4", "_shasum": "855a612979141d806ba5104294a28c731c6ea790", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "855a612979141d806ba5104294a28c731c6ea790", "size": 3139, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.4.tgz", "integrity": "sha512-OBFm5ilBk5s73L32FCteA+9ai+4j7iIs/MmJe/JjneNym1FTV/D4AgTOlk0DJhHG9eRLPLcg8bCZ8K5c/rgdkw=="}, "directories": {}, "publish_time": 1418234277684, "_hasShrinkwrap": false, "_cnpm_publish_time": 1418234277684, "_cnpmcore_publish_time": "2021-12-13T12:35:10.386Z"}, "2.0.3": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.3", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.2.0"}, "devDependencies": {"istanbul": "0", "mocha": "~1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "7d38db8c0576cf7a2dd49df4818dc129090b3a2f", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.3", "_shasum": "70b5cb5165b55dcb4972839f16d6077b0bb506f4", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "70b5cb5165b55dcb4972839f16d6077b0bb506f4", "size": 3126, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.3.tgz", "integrity": "sha512-gLTMzv0arNJm8WabXwaqtjbPJ9VlqG93T8xCUN90FBESlyGIR1KMIJPmYicpC50AwsoYOhixwtrC7yistoAsgg=="}, "directories": {}, "publish_time": 1415556576046, "_hasShrinkwrap": false, "_cnpm_publish_time": 1415556576046, "_cnpmcore_publish_time": "2021-12-13T12:35:11.373Z"}, "2.0.2": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.1.0"}, "devDependencies": {"istanbul": "0", "mocha": "1"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "7272f212651dcaca233803c58dc251b20668ca61", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.2", "_shasum": "c74b779f2896c367888622bd537aaaad4c0a2c08", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c74b779f2896c367888622bd537aaaad4c0a2c08", "size": 3112, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.2.tgz", "integrity": "sha512-hH83aTR9/ZC0yNJtFn44fLX0G7Ueu75+uIGQM314YtkqHGw3qVxfy9oCHF2FDx1kh3VnAOrdzSBcsVol1sNjlg=="}, "directories": {}, "publish_time": 1411955431565, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411955431565, "_cnpmcore_publish_time": "2021-12-13T12:35:12.223Z"}, "2.0.1": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "dependencies": {"mime-db": "~1.0.1"}, "devDependencies": {"istanbul": "0", "mocha": "1"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "gitHead": "c6e40fb033331aa87d9d4a5f1e5c0245e9edb2f6", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.1", "_shasum": "7f5b4712592e7dd46ca733fd1c5f5d71356de615", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7f5b4712592e7dd46ca733fd1c5f5d71356de615", "size": 3055, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.1.tgz", "integrity": "sha512-eN798SjxgETHm9yA+Stq5CJl7zqF0vxn9HnRX4fv0SXNOI9MaHKeaqNGCOX1qGtcwKJy3XctHZDucFN0tO9MrQ=="}, "directories": {}, "publish_time": 1410152524030, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410152524030, "_cnpmcore_publish_time": "2021-12-13T12:35:13.165Z"}, "2.0.0": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-types"}, "license": "MIT", "dependencies": {"mime-db": "~1.0.1"}, "devDependencies": {"istanbul": "0", "mocha": "1", "should": "3"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --require should test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot --require should test/test.js"}, "files": ["index.js"], "keywords": ["mime", "types"], "gitHead": "c5a3c150ab62deeb61da5bd0b6265115b1b35f27", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types", "_id": "mime-types@2.0.0", "_shasum": "4a85688446a4d94a03909e0ae292766744a3c313", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4a85688446a4d94a03909e0ae292766744a3c313", "size": 2304, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.0.tgz", "integrity": "sha512-63bzg+SNOn8tpkiVG/RNcTkj+LvpB1K2L72Te5uvtbU3Zm41kB+PyEVmZUCADnKS8dBu9L8OJo1wZdpfmUnTUg=="}, "directories": {}, "publish_time": 1409646746198, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409646746198, "_cnpmcore_publish_time": "2021-12-13T12:35:14.044Z"}, "1.0.2": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}], "repository": {"type": "git", "url": "https://github.com/expressjs/mime-types"}, "license": "MIT", "main": "lib", "devDependencies": {"co": "3", "cogent": "0", "mocha": "1", "should": "3"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "make test"}, "gitHead": "e82b23836eb42003b8346fb31769da2fb7eb54e8", "bugs": {"url": "https://github.com/expressjs/mime-types/issues"}, "homepage": "https://github.com/expressjs/mime-types", "_id": "mime-types@1.0.2", "_shasum": "995ae1392ab8affcbfcb2641dd054e943c0d5dce", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "995ae1392ab8affcbfcb2641dd054e943c0d5dce", "size": 18511, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-1.0.2.tgz", "integrity": "sha512-echfutj/t5SoTL4WZpqjA1DCud1XO0WQF3/GJ48YBmc4ZMhCK77QA6Z/w6VTQERLKuJ4drze3kw2TUT8xZXVNw=="}, "directories": {}, "publish_time": 1407123873290, "_hasShrinkwrap": false, "_cnpm_publish_time": 1407123873290, "_cnpmcore_publish_time": "2021-12-13T12:35:14.971Z"}, "1.0.1": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}], "repository": {"type": "git", "url": "git://github.com/expressjs/mime-types"}, "license": "MIT", "main": "lib", "devDependencies": {"co": "3", "cogent": "0", "mocha": "1", "should": "3"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "make test"}, "bugs": {"url": "https://github.com/expressjs/mime-types/issues"}, "homepage": "https://github.com/expressjs/mime-types", "_id": "mime-types@1.0.1", "_shasum": "4d9ad71bcd4cdef6be892c21b5b81645607c0b8f", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "fishrock123", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4d9ad71bcd4cdef6be892c21b5b81645607c0b8f", "size": 18572, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-1.0.1.tgz", "integrity": "sha512-PltPKnzFcnj7RB0hFxrnMmuvn1kNy2dxsVJB4Zjub/E+0lv3zBfg3PL7fthEyiQxHI7LM0CathyZBorEOxk0nA=="}, "directories": {}, "publish_time": 1403642758288, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403642758288, "_cnpmcore_publish_time": "2021-12-13T12:35:15.845Z"}, "1.0.0": {"name": "mime-types", "description": "ultimate mime type utility", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}], "repository": {"type": "git", "url": "git://github.com/expressjs/mime-types"}, "license": "MIT", "main": "lib", "devDependencies": {"co": "3", "cogent": "0", "mocha": "1", "should": "3"}, "scripts": {"test": "make test"}, "bugs": {"url": "https://github.com/expressjs/mime-types/issues"}, "homepage": "https://github.com/expressjs/mime-types", "_id": "mime-types@1.0.0", "_shasum": "6a7b4a6af2e7d92f97afe03f047c7801e8f001d2", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "fishrock123", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}], "dist": {"shasum": "6a7b4a6af2e7d92f97afe03f047c7801e8f001d2", "size": 20254, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-1.0.0.tgz", "integrity": "sha512-aP3BmIq4ZAPJt6KywU5HbiG0UwCTHZA2JWHO9aLaxyr8OhPOiK4RPSZcS6TDS7zNzGDC3AACnq/XTuEsd/M1Kg=="}, "directories": {}, "publish_time": 1399924839325, "_hasShrinkwrap": false, "_cnpm_publish_time": 1399924839325, "_cnpmcore_publish_time": "2021-12-13T12:35:16.692Z"}, "0.1.0": {"name": "mime-types", "description": "ultimate mime type utility", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "repository": {"type": "git", "url": "git://github.com/expressjs/mime-types"}, "license": "MIT", "main": "lib", "devDependencies": {"co": "3", "cogent": "0", "mocha": "1", "should": "3"}, "scripts": {"test": "make test"}, "bugs": {"url": "https://github.com/expressjs/mime-types/issues"}, "homepage": "https://github.com/expressjs/mime-types", "_id": "mime-types@0.1.0", "dist": {"shasum": "480f66fa19ef6ff039712a5393a4bc4901b5bf40", "size": 18753, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-0.1.0.tgz", "integrity": "sha512-pq+MbLrwKOQmBZE0w+la1YxhF/duOl1G009IqhSKqiv5lC8nZOMz8WGs1P8TV1ZCqn3bN2jrpWCLkIs926c8pg=="}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "publish_time": 1399046040885, "_hasShrinkwrap": false, "_cnpm_publish_time": 1399046040885, "_cnpmcore_publish_time": "2021-12-13T12:35:17.683Z"}, "2.1.35": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "2.1.35", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "1.52.0"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "ef932231c20e716ec27ea159c082322c3c485b66", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_id": "mime-types@2.1.35", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "shasum": "381a871b62a734450660ae3deee44813f70d959a", "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "fileCount": 5, "unpackedSize": 18272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiLOC7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpT2w/9GeLyJAyqEq3ZT7nLLZUi4oEEIcrEj7wz8UgiRjyOJPFHZBFG\r\nBJltzgFWbOf62sJvn1ZekIt6kq2OecNFdVjLe1sRHUfE+Irf8Z3oCT9RAwZ8\r\n2JYz8jP7VQ1p6ZsK8gQ43Bc8sBlZB81coPyjCkmwGc8yNy1amT1LMVMU8PRd\r\nuxfMoN0vEis3nARzPlx9fb7KKqS7rFL1zAaSUKhDLomS/gNOLsLtjyrlQo02\r\nvxX43VLGJvK2Wpd7TD/qvGvdGQ5VSm++TUfW3tviXQHwP4Meiy8zU09+OYXO\r\n0+ij2TlSLls7zNoQOWiZgHJYUP0WAZW4doxm9Sk9hA5RLGn3ukgMydYjyq66\r\nZJUCpYr8p2Y+1LcUHEdmgwfqsUdhX55IwyH8DwvlPHf619eZz7dM9aOWtBHS\r\nYAXUuzdJFlem2ezr1FhH1y7A/yPyXr2pqyzx9Qxp/d2yuaSVU82KCa9l7oJI\r\nnV8lubKUFxlJgIbFjkoxmvnDt9q0+MTzntrznHIcuD2wgRRKuk5Ima5udd0o\r\nSy/mLV7inWSaq9dWWzdZpYZDwGve4tcMr8LNlweZ80H2SFWjJdkBhf22i2Gr\r\nKgwvndofh9GfV2hnBFqL6MT/nJDm67lHf/Q/9frsfArA9Pb2OhapTzHthg08\r\nnNpZRTFrJfHM03pS8bEc33gaAQupl0vL+Fw=\r\n=eKhF\r\n-----END PGP SIGNATURE-----\r\n", "size": 5591}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_2.1.35_1647108282894_0.3187771888128905"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-12T18:04:46.723Z"}, "3.0.0": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "3.0.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "^1.53.0"}, "devDependencies": {"eslint": "8.33.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "10.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_id": "mime-types@3.0.0", "readmeFilename": "README.md", "gitHead": "4b3ae3831b18e1ce72af9ee75f691701aef8432f", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_nodeVersion": "22.2.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-XqoSHeCGjVClAmoGFG3lVFqQFRIrTVw2OH3axRqAcfaw+gHWIfnASS92AV+Rl/mk0MupgZTRHQOjxY6YVnzK5w==", "shasum": "148453a900475522d095a445355c074cca4f5217", "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-3.0.0.tgz", "fileCount": 6, "unpackedSize": 22277, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVk46UyrD+LHLM8l14XNi0V4ET7cNqJ0kGXNsmAop5TwIhAOPXSGh/qdfZo8etB72Tp/l54se3tl8bbPXse+Nc2Jdc"}], "size": 7136}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-types_3.0.0_1725112633991_0.3284378548950295"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-31T13:57:14.134Z", "publish_time": 1725112634134, "_source_registry_name": "default"}, "3.0.1": {"name": "mime-types", "description": "The ultimate javascript content-type utility.", "version": "3.0.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "dependencies": {"mime-db": "^1.54.0"}, "devDependencies": {"eslint": "8.33.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "10.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_id": "mime-types@3.0.1", "gitHead": "64e03d9d532c8967ebf611efc8ddd55f445ac9e5", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "homepage": "https://github.com/jshttp/mime-types#readme", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==", "shasum": "b1d94d6997a9b32fd69ebaed0db73de8acb519ce", "tarball": "https://registry.npmmirror.com/mime-types/-/mime-types-3.0.1.tgz", "fileCount": 6, "unpackedSize": 22341, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD5u8WKusYyNP4I1rshLEgWTV+Vx0rpUpms0KBKR2ZcJQIhANmT8tofIVtaYKnpDvY8YAXiw9Fnn9yQhx+RwKb67r+B"}], "size": 7156}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/mime-types_3.0.1_1743029627689_0.14587371785426284"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-26T22:53:47.851Z", "publish_time": 1743029627851, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "homepage": "https://github.com/jshttp/mime-types#readme", "keywords": ["mime", "types"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "_source_registry_name": "default"}