{"_attachments": {}, "_id": "node-domexception", "_rev": "894-61f1456a963ca28f5ee3a910", "author": {"name": "<PERSON>"}, "description": "An implementation of the DOMException class from NodeJS", "dist-tags": {"latest": "2.0.2"}, "license": "MIT", "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "name": "node-domexception", "readme": "# Deprecated\n\n`DOMException` have been implemented for quite some time now. (require Node 18+)\n\nUse your platform's native DOMException instead\nUpdate the `engines` field in `package.json` to Node.js 18 or above: \"node\": \">=18\".\n```json\n{\n  \"engine\": {\n    \"node\": \">=18\"\n  }\n}\n```\n\n\n# DOMException\nAn implementation of the DOMException class from NodeJS\n\nNodeJS has DOMException built in (from v10.5), but it is not easily available... you can't require or import it from somewhere (unless you use node v17 - at which point it got added to global scope)\n\nThis package exposes the [`DOMException`](https://developer.mozilla.org/en-US/docs/Web/API/DOMException) class that comes from NodeJS itself. (including all of the legacy codes)\n\n## Install\n\n```bash\n# makes use of atob | require NodeJS 16+\nnpm install node-domexception\n\n# makes use of worker_thread | require NodeJS 10+\nnpm install node-domexception@1.x\n```\n\nv2.x now depend on global `atob` to obtain `DOMException` from a error.\nwhich also binds it to NodeJS v16+ (at which point `atob` become globally available).\nThis NodeJS dependency/export free solution is better for cross env platform solutions.\nit no longer have any cjs or esm exports and it's attached to globalThis.DOMException only if needed.\n\nv1.x used a older [tech](https://github.com/jimmywarting/node-domexception/blob/c2024740c6502f80ad2f62c8ad58d6cec61b05f3/index.js) which depended on `node:worker_threads` to obtain\n`DOMException` which works all the way down to NodeJS v10.5+\n\nIf you are not supporting older NodeJS versions (before v17) then you won't need this package at all.\nMy personal recommendation is that you update to a newer NodeJS version.\nThis pkg will likely be deprecated once v18 becomes LTS\n\n```js\nimport 'node-domexception'\n\n// You could also do conditional import.\nglobalThis.DOMException || await import('node-domexception')\n\n/********************************************************************/\n\nimport { MessageChannel } from 'worker_threads'\n\ntry {\n  const port = new MessageChannel().port1\n  const ab = new ArrayBuffer()\n  port.postMessage(ab, [ab, ab])\n} catch (err) {\n  console.assert(err.name === 'DataCloneError')\n  console.assert(err.code === 25)\n  console.assert(err.constructor === DOMException)\n}\n\nconst e1 = new DOMException('Something went wrong', 'BadThingsError')\nconsole.assert(e1.name === 'BadThingsError')\nconsole.assert(e1.code === 0)\n\nconst e2 = new DOMException('Another exciting error message', 'NoModificationAllowedError')\nconsole.assert(e2.name === 'NoModificationAllowedError')\nconsole.assert(e2.code === 7)\n\nconsole.assert(DOMException.INUSE_ATTRIBUTE_ERR === 10)\n```\n\n# Background\n\nThe only possible way is to use some web-ish tools that have been introduced into NodeJS that throws a DOMException and catch the constructor. This is exactly what this package does for you and exposes it.<br>\nThis way you will have the same class that NodeJS has and you can check if the error is a instance of DOMException.<br>\nThe instanceof check would not have worked with a custom class such as the DOMException provided by domenic which also is much larger in size since it has to re-construct the whole class from the ground up.\n\nThe DOMException is used in many places such as the Fetch API, File & Blobs, PostMessaging and more. <br>\nWhy they decided to call it **DOM**, I don't know\n\nPlease consider sponsoring if you find this helpful\n", "time": {"created": "2022-01-26T12:58:18.508Z", "modified": "2025-04-20T11:33:42.105Z", "1.0.0": "2021-05-27T20:04:26.418Z", "2.0.0": "2023-07-17T15:57:39.099Z", "2.0.1": "2023-07-17T16:12:34.995Z", "2.0.2": "2025-04-19T13:24:24.658Z"}, "versions": {"1.0.0": {"name": "node-domexception", "version": "1.0.0", "description": "An implementation of the DOMException class from NodeJS", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/jimmywarting/node-domexception.git"}, "engines": {"node": ">=10.5.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/node-domexception/issues"}, "homepage": "https://github.com/jimmywarting/node-domexception#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "gitHead": "824361dc9b02a78828343075ba3763ee601ac4d2", "_id": "node-domexception@1.0.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "shasum": "6888db46a1f71c0b76b3f7555016b63fe64766e5", "tarball": "https://registry.npmmirror.com/node-domexception/-/node-domexception-1.0.0.tgz", "fileCount": 42, "unpackedSize": 29162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgr/tKCRA9TVsSAnZWagAAWsgQAKME3BOqcI3769xP5bf4\nnQtkPStkJ1P1Kf0eS23ehZh0+aJu/aLzr4LAJwufP9Jp2jnbSklzTMtwLNtC\nAOTAYWzgrvw6F2nmNdKsFzRlsC69gdbC/FD2H6nCP6bbvr/rJ+Kc3KbRdRI8\nZX8nR/6YV+gtgo/ng9fe5qHIaS5+sKhf5pIqylq5UrwbLKG1UHf2xO74sB/H\nPG0Xz3BBshZADUzvIub+f2o0dHg9EBRONf6TC3hqmfU3d/q/ZEs4PrbY/Ryr\ncMZqvfjbFNn/ABRAsKWaa1IGN5ee3ZF5N03HzD5F5bU8oFIvN4oiGsfpMrcQ\n/swHHCnEC0l6LYZleREKvTiBANBr3illpfJMUeyV7O61Wu+1NGxZcmnGGsvi\n9OPG3PfILkQjxXBZxj27UEtdu82r9MwRdo8SgjO2l/Hmw/QH72iRaDdEv8cM\nJADSX8DcNbSJhEiCypWXcR2b7mYTkzxNBglQ+USUFie8z8ahRAKK25b+DmzP\nlLmR07d+DHCl5DT/A3aAbUgFczC6GjX3B9ZE9xrviK97+pwvfXLFQPBNnNCj\ngiejUZZIElFGRqIPYXbXWe+KeYDiUf7CoF90xzty2gD93GHpALCToeJU3DTt\niRaWDeKG7HyKw0ht4zm2QTpqEkZ6a8lp8hkCEWT5a/OmeiNitReggGOn0zlG\nKtvB\r\n=Dxq8\r\n-----END PGP SIGNATURE-----\r\n", "size": 3614}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-domexception_1.0.0_1622145866305_0.5682227344226225"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2021-12-20T08:54:04.115Z", "deprecated": "Use your platform's native DOMException instead"}, "2.0.0": {"name": "node-domexception", "version": "2.0.0", "description": "An implementation of the DOMException class from NodeJS", "main": "index.js", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/jimmywarting/node-domexception.git"}, "engines": {"node": ">=16"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/node-domexception/issues"}, "homepage": "https://github.com/jimmywarting/node-domexception#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "gitHead": "499136f98c886324390f2cedd7eb7916e6c90207", "_id": "node-domexception@2.0.0", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-WSQFR8EYrJX87dpn4Af6CBA7CXqwTqg/JtyyopZXWglQSO7vI5SZVTiQ4ursHhHE5r4YEuYkt0Fg0g2nYs/5DQ==", "shasum": "f49532e52b6e6c331f0a65708ae763c94fb0b1c6", "tarball": "https://registry.npmmirror.com/node-domexception/-/node-domexception-2.0.0.tgz", "fileCount": 5, "unpackedSize": 5411, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+Yexmp4MNoY73+98S8vI7Y2mELfTILzP02/rbmmGs3gIhANx3NS2VeZGMjLj9Ax3Zo7JEk6FCese9iR0ckCUTlSJ0"}], "size": 2703}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-domexception_2.0.0_1689609458905_0.9267560166060052"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-17T15:57:39.099Z", "publish_time": 1689609459099, "_source_registry_name": "default", "deprecated": "Use your platform's native DOMException instead"}, "2.0.1": {"name": "node-domexception", "version": "2.0.1", "description": "An implementation of the DOMException class from NodeJS", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/jimmywarting/node-domexception.git"}, "engines": {"node": ">=16"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/node-domexception/issues"}, "homepage": "https://github.com/jimmywarting/node-domexception#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "gitHead": "2c1584e9d48ebf5cc6709f3612df1681831e377e", "_id": "node-domexception@2.0.1", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-M85rnSC7WQ7wnfQTARPT4LrK7nwCHLdDFOCcItZMhTQjyCebJH8GciKqYJNgaOFZs9nFmTmd/VMyi3OW5jA47w==", "shasum": "83b0d101123b5bbf91018fd569a58b88ae985e5b", "tarball": "https://registry.npmmirror.com/node-domexception/-/node-domexception-2.0.1.tgz", "fileCount": 5, "unpackedSize": 5167, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBhonljmiNT9D0sVs5VxMnev8r8oFBu0d3j9bOp51yr8AiBVFlufAwaCHhLl9QP7m+spIm+iV/EdeSJ5ZN6puz4vnw=="}], "size": 2628}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-domexception_2.0.1_1689610354835_0.5882360832306162"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-17T16:12:34.995Z", "publish_time": 1689610354995, "_source_registry_name": "default", "deprecated": "Use your platform's native DOMException instead"}, "2.0.2": {"name": "node-domexception", "version": "2.0.2", "description": "An implementation of the DOMException class from NodeJS", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/jimmywarting/node-domexception.git"}, "engines": {"node": ">=16"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/node-domexception/issues"}, "homepage": "https://github.com/jimmywarting/node-domexception#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "_id": "node-domexception@2.0.2", "gitHead": "ca9b0e0948f5ea3aaf7191143a95fc34894bc413", "_nodeVersion": "23.6.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Qf9vHK9c5MGgUXj8SnucCIS4oEPuUstjRaMplLGeZpbWMfNV1rvEcXuwoXfN51dUfD1b4muPHPQtCx/5Dj/QAA==", "shasum": "9ac29eb0b97ce65d4c647c1ebcd314d4c2859c4a", "tarball": "https://registry.npmmirror.com/node-domexception/-/node-domexception-2.0.2.tgz", "fileCount": 5, "unpackedSize": 5451, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD7jr8IkKvSCZIoLBywCXy5c9nx+Aoy1awSt810/3gYWQIgTcgvQQfORz6VrKwmwCgkaO1D6artxB0OHZ4S6hrsq0c="}], "size": 2743}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/node-domexception_2.0.2_1745069064441_0.35558923055921543"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-19T13:24:24.658Z", "publish_time": 1745069064658, "_source_registry_name": "default", "deprecated": "Use your platform's native DOMException instead"}}, "bugs": {"url": "https://github.com/jimmywarting/node-domexception/issues"}, "homepage": "https://github.com/jimmywarting/node-domexception#readme", "repository": {"type": "git", "url": "git+https://github.com/jimmywarting/node-domexception.git"}, "_source_registry_name": "default"}