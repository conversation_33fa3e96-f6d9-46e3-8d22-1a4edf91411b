{"_attachments": {}, "_id": "lodash.isboolean", "_rev": "2935-61f14a564ce7cf8f58267ca8", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "description": "The lodash method `_.isBoolean` exported as a module.", "dist-tags": {"latest": "3.0.3"}, "license": "MIT", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "lodash.isboolean", "readme": "# lodash.isboolean v3.0.3\n\nThe [lodash](https://lodash.com/) method `_.isBoolean` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isboolean\n```\n\nIn Node.js:\n```js\nvar isBoolean = require('lodash.isboolean');\n```\n\nSee the [documentation](https://lodash.com/docs#isBoolean) or [package source](https://github.com/lodash/lodash/blob/3.0.3-npm-packages/lodash.isboolean) for more details.\n", "time": {"created": "2022-01-26T13:19:18.120Z", "modified": "2023-07-31T13:55:11.403Z", "3.0.3": "2016-02-03T07:28:20.290Z", "3.0.2": "2016-01-13T11:04:25.714Z", "3.0.1": "2015-03-25T23:35:18.792Z", "3.0.0": "2015-01-26T15:29:15.791Z", "2.4.1": "2013-12-03T17:13:57.703Z", "2.4.0": "2013-11-26T19:55:40.164Z", "2.3.0": "2013-11-11T16:47:25.896Z", "2.2.1": "2013-10-03T18:50:03.483Z", "2.2.0": "2013-09-29T22:09:24.606Z", "2.1.0": "2013-09-23T07:55:22.962Z", "2.0.0": "2013-09-23T07:36:56.644Z"}, "versions": {"3.0.3": {"name": "lodash.isboolean", "version": "3.0.3", "description": "The lodash method `_.isBoolean` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isboolean"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isboolean@3.0.3", "_shasum": "6c2e171db2a257cd96802fd43b01b20d5f5870f6", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6c2e171db2a257cd96802fd43b01b20d5f5870f6", "size": 2109, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "integrity": "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/lodash.isboolean-3.0.3.tgz_1454484499476_0.9152139471843839"}, "directories": {}, "publish_time": 1454484500290, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454484500290, "_cnpmcore_publish_time": "2021-12-14T06:01:36.984Z"}, "3.0.2": {"name": "lodash.isboolean", "version": "3.0.2", "description": "The lodash method `_.isBoolean` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "isboolean"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isboolean@3.0.2", "_shasum": "c16898a8189fe8ecf6c67febb2023ebe831af335", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c16898a8189fe8ecf6c67febb2023ebe831af335", "size": 2123, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-3.0.2.tgz", "integrity": "sha512-ujqUiwiii8k/2azePMeRivE+fKPG5na4O+C7+3xELZkU+O6SPTSqeniBu1oqBZwu+2s3dBVIwFOJytEp+lVw+g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452683065714, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452683065714, "_cnpmcore_publish_time": "2021-12-14T06:01:37.228Z"}, "3.0.1": {"name": "lodash.isboolean", "version": "3.0.1", "description": "The modern build of lodash’s `_.isBoolean` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isboolean@3.0.1", "_shasum": "efcc72ba324743aed88a6188b5adaa58d9c319a4", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "efcc72ba324743aed88a6188b5adaa58d9c319a4", "size": 2161, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-3.0.1.tgz", "integrity": "sha512-2yZzgA9HgEWAWWEcsn8qeM5l6Kh8LVBdrarbJSqrJdV1q8ErwIGlC8MNGdLaa5lugMtkCsObL60YFCsiMPWNeQ=="}, "directories": {}, "publish_time": 1427326518792, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427326518792, "_cnpmcore_publish_time": "2021-12-14T06:01:37.426Z"}, "3.0.0": {"name": "lodash.isboolean", "version": "3.0.0", "description": "The modern build of lodash’s `_.isBoolean` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isboolean@3.0.0", "_shasum": "9c695865f12211bad0c059ce0061456c90ea738a", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "9c695865f12211bad0c059ce0061456c90ea738a", "size": 2185, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-3.0.0.tgz", "integrity": "sha512-iOqJTjmOPg3n46DvdIRc3hUxFr/hLtIyxXILT3o9/FHj+3R7vWOWdy4QsCS8W3GOGEuX9K1njg9OirLzJUfG0g=="}, "directories": {}, "publish_time": 1422286155791, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422286155791, "_cnpmcore_publish_time": "2021-12-14T06:01:37.641Z"}, "2.4.1": {"name": "lodash.isboolean", "version": "2.4.1", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isboolean@2.4.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-2.4.1.tgz", "shasum": "9756de9aa9f7a46659c05e43250a09e11dd1502c", "size": 2158, "noattachment": false, "integrity": "sha512-BIgUtb0mbpiSGkGdLHlw60YPcx7PGK3hN3XMTfnFGokgi2Pf6rNJG/lH1uW4EQ0JgFrZVN9QBi4jM8IgYHMzyw=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386090837703, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386090837703, "_cnpmcore_publish_time": "2021-12-14T06:01:37.876Z"}, "2.4.0": {"name": "lodash.isboolean", "version": "2.4.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isboolean@2.4.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-2.4.0.tgz", "shasum": "139eb98c8d94c3a70dc6c3d8c6d182fb5f44b309", "size": 2153, "noattachment": false, "integrity": "sha512-H8e1yOPzq17QYtGxx7e2cFnzfBD2Az8/JmiPyGDi4Atowi/gtrNplZJS6GZKxi/EL7YEIq46lWGVVUZTSStcdA=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1385495740164, "_hasShrinkwrap": false, "_cnpm_publish_time": 1385495740164, "_cnpmcore_publish_time": "2021-12-14T06:01:38.079Z"}, "2.3.0": {"name": "lodash.isboolean", "version": "2.3.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isboolean@2.3.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-2.3.0.tgz", "shasum": "36eb4a5a8e0938dab526517867dfe4aa0b0acd4d", "size": 2148, "noattachment": false, "integrity": "sha512-1buOhyfZ/urQFr2Wg6fqKpdfyQucqU5Lf87XWI6NnXqKrD17jWFYUyCjeTkNDsIeFasM/4yMWCbpLcLp+5JCZw=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384188445896, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384188445896, "_cnpmcore_publish_time": "2021-12-14T06:01:38.317Z"}, "2.2.1": {"name": "lodash.isboolean", "version": "2.2.1", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isboolean@2.2.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-2.2.1.tgz", "shasum": "1c9d800a4f3cca5918d90b2f7a21566aaa6f6a75", "size": 2109, "noattachment": false, "integrity": "sha512-6sOIDjbaXWV50GmFgs2QmO4RD5DifRj/DOxwvlV61IdMPb4sy4ssXFPur+AbORvF/ndT5cAQ/RrChhJj/83hFw=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380826203483, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380826203483, "_cnpmcore_publish_time": "2021-12-14T06:01:38.537Z"}, "2.2.0": {"name": "lodash.isboolean", "version": "2.2.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isboolean@2.2.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-2.2.0.tgz", "shasum": "dbfc682b9a0d63a191d99c98381758cdd87eb3d1", "size": 2154, "noattachment": false, "integrity": "sha512-ZpjYaLbTszCdgW48UgGbVr75ZeFlM01NejAeHPdH31aB+KGAEefaFsMZmHHWY99LDUhzBm2JdzK7SJmumeDI6w=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380492564606, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380492564606, "_cnpmcore_publish_time": "2021-12-14T06:01:38.759Z"}, "2.1.0": {"name": "lodash.isboolean", "version": "2.1.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isboolean@2.1.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-2.1.0.tgz", "shasum": "12fc96fcbe424a08f61716304c2b91f70c9db61c", "size": 2166, "noattachment": false, "integrity": "sha512-CRpnvhF2Wu2ea+NlJp+xVBLqR+VSbCK791Yu7Iwe128WbahqRzBWNFSpnI681nLCeBRffE/vk2DclkEurn1HcQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379922922962, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379922922962, "_cnpmcore_publish_time": "2021-12-14T06:01:38.959Z"}, "2.0.0": {"name": "lodash.isboolean", "version": "2.0.0", "description": "The Lo-Dash function `_.isBoolean` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "readmeFilename": "README.md", "_id": "lodash.isboolean@2.0.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.isboolean/-/lodash.isboolean-2.0.0.tgz", "shasum": "080ad6d6a73c436f4cf0d14bb12dd3ebefa3a6a3", "size": 2128, "noattachment": false, "integrity": "sha512-lRnSjeKA7Ualf+gmSpQyr1D7p/evtAKlc06hLt8cE5LAWGNaiWc9OU+1KStek7YhNlfrHd+bod9HIMowkFl/Ew=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379921816644, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379921816644, "_cnpmcore_publish_time": "2021-12-14T06:01:39.188Z"}}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "isboolean"], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "_source_registry_name": "default"}