{"_attachments": {}, "_id": "is-fullwidth-code-point", "_rev": "2946-61f14a58b77ea98a7490d3a5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "description": "Check if the character represented by a given Unicode code point is fullwidth", "dist-tags": {"latest": "5.0.0"}, "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "is-fullwidth-code-point", "readme": "# is-fullwidth-code-point\n\n> Check if the character represented by a given [Unicode code point](https://en.wikipedia.org/wiki/Code_point) is [fullwidth](https://en.wikipedia.org/wiki/Halfwidth_and_fullwidth_forms)\n\n## Install\n\n```sh\nnpm install is-fullwidth-code-point\n```\n\n## Usage\n\n```js\nimport isFullwidthCodePoint from 'is-fullwidth-code-point';\n\nisFullwidthCodePoint('谢'.codePointAt(0));\n//=> true\n\nisFullwidthCodePoint('a'.codePointAt(0));\n//=> false\n```\n\n## API\n\n### isFullwidthCodePoint(codePoint)\n\n#### codePoint\n\nType: `number`\n\nThe [code point](https://en.wikipedia.org/wiki/Code_point) of a character.\n", "time": {"created": "2022-01-26T13:19:20.810Z", "modified": "2024-05-28T17:04:55.792Z", "4.0.0": "2021-04-16T05:49:23.535Z", "3.0.0": "2019-03-18T08:09:05.035Z", "2.0.0": "2016-09-22T06:42:49.558Z", "1.0.0": "2015-07-16T22:00:18.162Z", "5.0.0": "2023-10-28T13:20:15.377Z"}, "versions": {"4.0.0": {"name": "is-fullwidth-code-point", "version": "4.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "27f5728857a28f9893de14be33e893d33da7f85b", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "_id": "is-fullwidth-code-point@4.0.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fae3167c729e7463f8461ce512b080a49268aa88", "size": 2316, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz", "integrity": "sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ=="}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-fullwidth-code-point_4.0.0_1618552163389_0.6209131775447976"}, "_hasShrinkwrap": false, "publish_time": 1618552163535, "_cnpm_publish_time": 1618552163535, "_cnpmcore_publish_time": "2021-12-13T11:29:16.689Z"}, "3.0.0": {"name": "is-fullwidth-code-point", "version": "3.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check"], "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}, "gitHead": "80e5e314d86e5f76bd1b0573aa9d33e615a372db", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "_id": "is-fullwidth-code-point@3.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f116f8064fe90b3f7844a38997c0b75051269f1d", "size": 2169, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-fullwidth-code-point_3.0.0_1552896544852_0.7785122753639435"}, "_hasShrinkwrap": false, "publish_time": 1552896545035, "_cnpm_publish_time": 1552896545035, "_cnpmcore_publish_time": "2021-12-13T11:29:16.902Z"}, "2.0.0": {"name": "is-fullwidth-code-point", "version": "2.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "char", "string", "str", "codepoint", "code", "point", "is", "detect", "check"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "e94a78056056c5546f2bf4c4cf812a2163a46dae", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "_id": "is-fullwidth-code-point@2.0.0", "_shasum": "a3b30a5c4f199183167aaab93beefae3ddfb654f", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "4.5.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a3b30a5c4f199183167aaab93beefae3ddfb654f", "size": 2063, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/is-fullwidth-code-point-2.0.0.tgz_1474526567505_0.299921662081033"}, "directories": {}, "publish_time": 1474526569558, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474526569558, "_cnpmcore_publish_time": "2021-12-13T11:29:17.134Z"}, "1.0.0": {"name": "is-fullwidth-code-point", "version": "1.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/is-fullwidth-code-point"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "char", "string", "str", "codepoint", "code", "point", "is", "detect", "check"], "dependencies": {"number-is-nan": "^1.0.0"}, "devDependencies": {"ava": "0.0.4", "code-point-at": "^1.0.0"}, "gitHead": "f2152d357f41f82785436d428e4f8ede143b7548", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point", "_id": "is-fullwidth-code-point@1.0.0", "_shasum": "ef9e31386f031a7f0d643af82fde50c457ef00cb", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ef9e31386f031a7f0d643af82fde50c457ef00cb", "size": 2124, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha512-1pqUqRjkhPJ9miNq9SwMfdvi6lBJcd6eFxvfaivQhaH3SgisfiuudvFntdKOmxuee/77l+FPjKrQjWvmPjWrRw=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1437084018162, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437084018162, "_cnpmcore_publish_time": "2021-12-13T11:29:17.381Z"}, "5.0.0": {"name": "is-fullwidth-code-point", "version": "5.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check", "east-asian-width"], "devDependencies": {"ava": "^5.3.1", "tsd": "^0.29.0", "xo": "^0.56.0"}, "dependencies": {"get-east-asian-width": "^1.0.0"}, "types": "./index.d.ts", "gitHead": "1f8fb1ffeab396915a22bf28420f13bf329963fe", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "_id": "is-fullwidth-code-point@5.0.0", "_nodeVersion": "18.18.2", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==", "shasum": "9609efced7c2f97da7b60145ef481c787c7ba704", "tarball": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-5.0.0.tgz", "fileCount": 5, "unpackedSize": 3455, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYAxiF1PI4Lsxh7Qine0172AVw/MCM3znOr5hKZpMHcwIhALE6H30F1HwwF4m9P9oc/tgyx6qf3MkQLQcAbPf++u6p"}], "size": 1640}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-fullwidth-code-point_5.0.0_1698499215099_0.8423970946552544"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-28T13:20:15.377Z", "publish_time": 1698499215377, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check", "east-asian-width"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "_source_registry_name": "default"}