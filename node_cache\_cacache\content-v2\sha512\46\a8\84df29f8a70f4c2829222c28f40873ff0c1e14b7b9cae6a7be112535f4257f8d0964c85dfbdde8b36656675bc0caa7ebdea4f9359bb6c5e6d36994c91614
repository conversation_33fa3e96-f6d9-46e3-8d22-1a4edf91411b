{"_attachments": {}, "_id": "jws", "_rev": "2934-61f14a55830fd08f52a2d06b", "author": {"name": "<PERSON>"}, "description": "Implementation of JSON Web Signatures", "dist-tags": {"latest": "4.0.0"}, "license": "MIT", "maintainers": [{"name": "charles<PERSON>", "email": "<EMAIL>"}, {"name": "julien.w<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lozano.okta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "jws", "readme": "# node-jws [![Build Status](https://secure.travis-ci.org/brianloveswords/node-jws.svg)](http://travis-ci.org/brianloveswords/node-jws)\n\nAn implementation of [JSON Web Signatures](http://self-issued.info/docs/draft-ietf-jose-json-web-signature.html).\n\nThis was developed against `draft-ietf-jose-json-web-signature-08` and\nimplements the entire spec **except** X.509 Certificate Chain\nsigning/verifying (patches welcome).\n\nThere are both synchronous (`jws.sign`, `jws.verify`) and streaming\n(`jws.createSign`, `jws.createVerify`) APIs.\n\n# Install\n\n```bash\n$ npm install jws\n```\n\n# Usage\n\n## jws.ALGORITHMS\n\nArray of supported algorithms. The following algorithms are currently supported.\n\nalg Parameter Value | Digital Signature or MAC Algorithm\n----------------|----------------------------\nHS256 | HMAC using SHA-256 hash algorithm\nHS384 | HMAC using SHA-384 hash algorithm\nHS512 | HMAC using SHA-512 hash algorithm\nRS256 | RSASSA using SHA-256 hash algorithm\nRS384 | RSASSA using SHA-384 hash algorithm\nRS512 | RSASSA using SHA-512 hash algorithm\nPS256 | RSASSA-PSS using SHA-256 hash algorithm\nPS384 | RSASSA-PSS using SHA-384 hash algorithm\nPS512 | RSASSA-PSS using SHA-512 hash algorithm\nES256 | ECDSA using P-256 curve and SHA-256 hash algorithm\nES384 | ECDSA using P-384 curve and SHA-384 hash algorithm\nES512 | ECDSA using P-521 curve and SHA-512 hash algorithm\nnone | No digital signature or MAC value included\n\n## jws.sign(options)\n\n(Synchronous) Return a JSON Web Signature for a header and a payload.\n\nOptions:\n\n* `header`\n* `payload`\n* `secret` or `privateKey`\n* `encoding` (Optional, defaults to 'utf8')\n\n`header` must be an object with an `alg` property. `header.alg` must be\none a value found in `jws.ALGORITHMS`. See above for a table of\nsupported algorithms.\n\nIf `payload` is not a buffer or a string, it will be coerced into a string\nusing `JSON.stringify`.\n\nExample\n\n```js\nconst signature = jws.sign({\n  header: { alg: 'HS256' },\n  payload: 'h. jon benjamin',\n  secret: 'has a van',\n});\n```\n\n## jws.verify(signature, algorithm, secretOrKey)\n\n(Synchronous) Returns `true` or `false` for whether a signature matches a\nsecret or key.\n\n`signature` is a JWS Signature. `header.alg` must be a value found in `jws.ALGORITHMS`.\nSee above for a table of supported algorithms. `secretOrKey` is a string or\nbuffer containing either the secret for HMAC algorithms, or the PEM\nencoded public key for RSA and ECDSA.\n\nNote that the `\"alg\"` value from the signature header is ignored.\n\n\n## jws.decode(signature)\n\n(Synchronous) Returns the decoded header, decoded payload, and signature\nparts of the JWS Signature.\n\nReturns an object with three properties, e.g.\n```js\n{ header: { alg: 'HS256' },\n  payload: 'h. jon benjamin',\n  signature: 'YOWPewyGHKu4Y_0M_vtlEnNlqmFOclqp4Hy6hVHfFT4'\n}\n```\n\n## jws.createSign(options)\n\nReturns a new SignStream object.\n\nOptions:\n\n* `header` (required)\n* `payload`\n* `key` || `privateKey` || `secret`\n* `encoding` (Optional, defaults to 'utf8')\n\nOther than `header`, all options expect a string or a buffer when the\nvalue is known ahead of time, or a stream for convenience.\n`key`/`privateKey`/`secret` may also be an object when using an encrypted\nprivate key, see the [crypto documentation][encrypted-key-docs].\n\nExample:\n\n```js\n\n// This...\njws.createSign({\n  header: { alg: 'RS256' },\n  privateKey: privateKeyStream,\n  payload: payloadStream,\n}).on('done', function(signature) {\n  // ...\n});\n\n// is equivalent to this:\nconst signer = jws.createSign({\n  header: { alg: 'RS256' },\n});\nprivateKeyStream.pipe(signer.privateKey);\npayloadStream.pipe(signer.payload);\nsigner.on('done', function(signature) {\n  // ...\n});\n```\n\n## jws.createVerify(options)\n\nReturns a new VerifyStream object.\n\nOptions:\n\n* `signature`\n* `algorithm`\n* `key` || `publicKey` || `secret`\n* `encoding` (Optional, defaults to 'utf8')\n\nAll options expect a string or a buffer when the value is known ahead of\ntime, or a stream for convenience.\n\nExample:\n\n```js\n\n// This...\njws.createVerify({\n  publicKey: pubKeyStream,\n  signature: sigStream,\n}).on('done', function(verified, obj) {\n  // ...\n});\n\n// is equivilant to this:\nconst verifier = jws.createVerify();\npubKeyStream.pipe(verifier.publicKey);\nsigStream.pipe(verifier.signature);\nverifier.on('done', function(verified, obj) {\n  // ...\n});\n```\n\n## Class: SignStream\n\nA `Readable Stream` that emits a single data event (the calculated\nsignature) when done.\n\n### Event: 'done'\n`function (signature) { }`\n\n### signer.payload\n\nA `Writable Stream` that expects the JWS payload. Do *not* use if you\npassed a `payload` option to the constructor.\n\nExample:\n\n```js\npayloadStream.pipe(signer.payload);\n```\n\n### signer.secret<br>signer.key<br>signer.privateKey\n\nA `Writable Stream`. Expects the JWS secret for HMAC, or the privateKey\nfor ECDSA and RSA. Do *not* use if you passed a `secret` or `key` option\nto the constructor.\n\nExample:\n\n```js\nprivateKeyStream.pipe(signer.privateKey);\n```\n\n## Class: VerifyStream\n\nThis is a `Readable Stream` that emits a single data event, the result\nof whether or not that signature was valid.\n\n### Event: 'done'\n`function (valid, obj) { }`\n\n`valid` is a boolean for whether or not the signature is valid.\n\n### verifier.signature\n\nA `Writable Stream` that expects a JWS Signature. Do *not* use if you\npassed a `signature` option to the constructor.\n\n### verifier.secret<br>verifier.key<br>verifier.publicKey\n\nA `Writable Stream` that expects a public key or secret. Do *not* use if you\npassed a `key` or `secret` option to the constructor.\n\n# TODO\n\n* It feels like there should be some convenience options/APIs for\n  defining the algorithm rather than having to define a header object\n  with `{ alg: 'ES512' }` or whatever every time.\n\n* X.509 support, ugh\n\n# License\n\nMIT\n\n```\nCopyright (c) 2013-2015 Brian J. Brennan\n\nPermission is hereby granted, free of charge, to any person obtaining a\ncopy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be included\nin all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\nOR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n```\n\n[encrypted-key-docs]: https://nodejs.org/api/crypto.html#crypto_sign_sign_private_key_output_format\n", "time": {"created": "2022-01-26T13:19:17.627Z", "modified": "2025-05-08T10:05:29.332Z", "4.0.0": "2019-12-16T17:22:34.743Z", "3.2.2": "2019-03-16T15:26:17.735Z", "3.2.1": "2019-01-27T13:52:37.693Z", "3.2.0": "2019-01-25T21:09:08.237Z", "3.1.5": "2018-05-14T14:52:31.898Z", "3.1.4": "2016-11-03T19:52:30.393Z", "3.1.3": "2016-02-18T15:39:48.138Z", "3.1.2": "2016-02-18T14:26:29.916Z", "3.1.1": "2016-01-17T20:48:24.996Z", "3.1.0": "2015-07-16T17:24:01.001Z", "3.0.0": "2015-04-08T15:21:54.795Z", "2.0.0": "2015-01-30T15:40:03.901Z", "1.0.1": "2015-01-11T16:20:20.131Z", "1.0.0": "2014-12-07T19:40:02.336Z", "0.2.6": "2014-08-07T17:39:19.155Z", "0.2.5": "2014-01-30T14:58:39.557Z", "0.2.4": "2014-01-28T19:40:39.504Z", "0.2.3": "2013-11-06T02:13:40.508Z", "0.2.2": "2013-03-12T22:27:43.257Z", "0.2.1": "2013-02-25T23:12:43.884Z", "0.2.0": "2013-02-10T20:03:40.963Z", "0.0.2": "2013-01-18T23:58:55.837Z", "0.0.1": "2013-01-16T20:17:39.718Z"}, "versions": {"4.0.0": {"name": "jws", "version": "4.0.0", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"jwa": "^2.0.0", "safe-buffer": "^5.0.1"}, "devDependencies": {"semver": "^5.1.0", "tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@4.0.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "2d4e8cf6a318ffaa12615e9dec7e86e6c97310f4", "size": 5884, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-4.0.0.tgz", "integrity": "sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jws_4.0.0_1576516954551_0.4290706248028404"}, "_hasShrinkwrap": false, "publish_time": 1576516954743, "_cnpm_publish_time": 1576516954743, "_cnpmcore_publish_time": "2021-12-15T11:19:57.171Z"}, "3.2.2": {"name": "jws", "version": "3.2.2", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}, "devDependencies": {"semver": "^5.1.0", "tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@3.2.2", "_nodeVersion": "11.10.0", "_npmVersion": "6.7.0", "dist": {"shasum": "001099f3639468c9414000e99995fa52fb478304", "size": 5884, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.2.2.tgz", "integrity": "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jws_3.2.2_1552749977567_0.16467472869709399"}, "_hasShrinkwrap": false, "publish_time": 1552749977735, "_cnpm_publish_time": 1552749977735, "_cnpmcore_publish_time": "2021-12-15T11:19:57.409Z"}, "3.2.1": {"name": "jws", "version": "3.2.1", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"jwa": "^1.2.0", "safe-buffer": "^5.0.1"}, "devDependencies": {"semver": "^5.1.0", "tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@3.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "d79d4216a62c9afa0a3d5e8b5356d75abdeb2be5", "size": 5916, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.2.1.tgz", "integrity": "sha512-bGA2omSrFUkd72dhh05bIAN832znP4wOU3lfuXtRBuGTbsmNmDXMQg28f0Vsxaxgk4myF5YkKQpz6qeRpMgX9g=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jws_3.2.1_1548597157601_0.25617724445426715"}, "_hasShrinkwrap": false, "publish_time": 1548597157693, "_cnpm_publish_time": 1548597157693, "_cnpmcore_publish_time": "2021-12-15T11:19:57.602Z"}, "3.2.0": {"name": "jws", "version": "3.2.0", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"jwa": "^1.2.0", "safe-buffer": "^5.0.1"}, "devDependencies": {"semver": "^5.1.0", "tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@3.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.4.0", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "b89dcfc20846504506f4dcf69ad5395f8a34680b", "size": 5873, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.2.0.tgz", "integrity": "sha512-OmaE/N+SlUTMyja5XeM7aaGP3nNCUlypCCEtlDXf3887yq3tm1zk5O7l/dSz7TQ5p+imRxERZ8Fmk4Z6xcWIIw=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jws_3.2.0_1548450548143_0.774259911509247"}, "_hasShrinkwrap": false, "publish_time": 1548450548237, "_cnpm_publish_time": 1548450548237, "_cnpmcore_publish_time": "2021-12-15T11:19:57.826Z"}, "3.1.5": {"name": "jws", "version": "3.1.5", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"jwa": "^1.1.5", "safe-buffer": "^5.0.1"}, "devDependencies": {"semver": "^5.1.0", "tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@3.1.5", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "80d12d05b293d1e841e7cb8b4e69e561adcf834f", "size": 5885, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.1.5.tgz", "integrity": "sha512-GsCSexFADNQUr8T5HPJvayTjvPIfoyJPtLQBwn5a4WZQchcrPMPMAWcC1AzJVRDKyD6ZPROPAxgv6rfHViO4uQ=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jws_3.1.5_1526309551808_0.23434356668208833"}, "_hasShrinkwrap": false, "publish_time": 1526309551898, "_cnpm_publish_time": 1526309551898, "_cnpmcore_publish_time": "2021-12-15T11:19:58.026Z"}, "3.1.4": {"name": "jws", "version": "3.1.4", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"base64url": "^2.0.0", "jwa": "^1.1.4", "safe-buffer": "^5.0.1"}, "devDependencies": {"semver": "^5.1.0", "tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@3.1.4", "_shasum": "f9e8b9338e8a847277d6444b1464f61880e050a2", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "f9e8b9338e8a847277d6444b1464f61880e050a2", "size": 5910, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.1.4.tgz", "integrity": "sha512-9b5xCA+0jei+IJP6rs33ecRBoYIh4vCn8M5wmmlzDIrh755b3UCqUlu9JwWLS+z4ykpI6zZqflAHy92b4HGNZw=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/jws-3.1.4.tgz_1478202748192_0.3927526210900396"}, "publish_time": 1478202750393, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478202750393, "_cnpmcore_publish_time": "2021-12-15T11:19:58.256Z"}, "3.1.3": {"name": "jws", "version": "3.1.3", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"base64url": "~1.0.4", "jwa": "^1.1.2"}, "devDependencies": {"semver": "^5.1.0", "tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@3.1.3", "_shasum": "b88f1b4581a2c5ee8813c06b3fdf90ea9b5c7e6c", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "b88f1b4581a2c5ee8813c06b3fdf90ea9b5c7e6c", "size": 8353, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.1.3.tgz", "integrity": "sha512-KiwFJo7lfoVFV6AbEjZLvfY6dUnFVHliKdoEKtd0P+AN5wBGv8a25qYf0qgXrTzB6vArpnXi67reRpXo2EBUKw=="}, "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/jws-3.1.3.tgz_1455809983684_0.8235816163942218"}, "publish_time": 1455809988138, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455809988138, "_cnpmcore_publish_time": "2021-12-15T11:19:58.476Z"}, "3.1.2": {"name": "jws", "version": "3.1.2", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"base64url": "~1.0.4", "jwa": "^1.1.2"}, "devDependencies": {"semver": "^5.1.0", "tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@3.1.2", "_shasum": "0d675a4612035fb318118e1b2b9de3f8ecaa1ae2", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "0d675a4612035fb318118e1b2b9de3f8ecaa1ae2", "size": 8318, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.1.2.tgz", "integrity": "sha512-xWhGGcSJZDVCO/HGu5uMgODlqt91VX953MMAgGsrYKirLdXGXUzw38XaglbhL3JjY+xADJ7WunEgLllDYZXPDg=="}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/jws-3.1.2.tgz_1455805587314_0.43562568444758654"}, "publish_time": 1455805589916, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455805589916, "_cnpmcore_publish_time": "2021-12-15T11:19:58.845Z"}, "3.1.1": {"name": "jws", "version": "3.1.1", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"base64url": "~1.0.4", "jwa": "^1.1.0"}, "devDependencies": {"semver": "^5.1.0", "tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@3.1.1", "_shasum": "34f5a424e628af4551121e860ba279f55cfa6629", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.4.1", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "dist": {"shasum": "34f5a424e628af4551121e860ba279f55cfa6629", "size": 8321, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.1.1.tgz", "integrity": "sha512-xWmdc62OIKSOTwuXOD4pffjXQ0J3J7nYS1o2g9/nVrFa6SUcQzveqfnl/hFkfnf2njM1vMABWc7OZkDw0xQ0aw=="}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1453063704996, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453063704996, "_cnpmcore_publish_time": "2021-12-15T11:19:59.043Z"}, "3.1.0": {"name": "jws", "version": "3.1.0", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"base64url": "~1.0.4", "jwa": "^1.1.0"}, "devDependencies": {"tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "_id": "jws@3.1.0", "_shasum": "885a89127d24119a2a93f234ddd492337a7c85a0", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.3", "_npmUser": {"name": "omsmith", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "885a89127d24119a2a93f234ddd492337a7c85a0", "size": 7631, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.1.0.tgz", "integrity": "sha512-dIhjVxxfhs93IKornyqxfkx/H/fupqwrUzXAXu/zMkgnPyGH0qXKVtet0Fu7I7o0BlV3SDUkAKOCHpzPItPOoQ=="}, "publish_time": 1437067441001, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437067441001, "_cnpmcore_publish_time": "2021-12-15T11:19:59.270Z"}, "3.0.0": {"name": "jws", "version": "3.0.0", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"jwa": "~1.0.0", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws", "_id": "jws@3.0.0", "_shasum": "da5f267897dd4e9cf8137979db33fc54a3c05418", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "da5f267897dd4e9cf8137979db33fc54a3c05418", "size": 7645, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-3.0.0.tgz", "integrity": "sha512-Me<PERSON>ywrnys7VjF8S0ipMSOrQGe5UFeIOg6oLLey7Dqpbtl6ZBRwLeOZ5VAHFmvcxlyPFtey2XAaYKmzG6bjt7lA=="}, "publish_time": 1428506514795, "_hasShrinkwrap": false, "_cnpm_publish_time": 1428506514795, "_cnpmcore_publish_time": "2021-12-15T11:19:59.570Z"}, "2.0.0": {"name": "jws", "version": "2.0.0", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"jwa": "~1.0.0", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws", "_id": "jws@2.0.0", "_shasum": "b494b6b99268b76b21209d52a49a227d412d6bdf", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "b494b6b99268b76b21209d52a49a227d412d6bdf", "size": 8904, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-2.0.0.tgz", "integrity": "sha512-W5b0BrKQXJ6+SaAKW08Wk5rUYU7vaOafQPR4I7zpmWUiwa9lbnfuYz1nzWsdmbxpKvQev/3S4CcHVcuYuOCPaw=="}, "publish_time": 1422632403901, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1422632403901, "_cnpmcore_publish_time": "2021-12-15T11:19:59.760Z"}, "1.0.1": {"name": "jws", "version": "1.0.1", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"jwa": "~1.0.0", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws", "_id": "jws@1.0.1", "_shasum": "4c72d091652e63404ee95864954d082aaf334950", "_from": ".", "_npmVersion": "2.1.14", "_nodeVersion": "0.10.35", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "4c72d091652e63404ee95864954d082aaf334950", "size": 6420, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-1.0.1.tgz", "integrity": "sha512-etTaSZOx6ncBF/nc/FD9Df3axk6zjxDCQd4TltsYj5+az87A5unRolWjcNQykpHqaAKvTGHDA29dgBCYxkv7XA=="}, "publish_time": 1420993220131, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1420993220131, "_cnpmcore_publish_time": "2021-12-15T11:20:00.030Z"}, "1.0.0": {"name": "jws", "version": "1.0.0", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"jwa": "~0.0.2", "base64url": "~1.0.4"}, "devDependencies": {"tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws", "_id": "jws@1.0.0", "_shasum": "327c7690d53a4866624333f041e6156653049a2e", "_from": ".", "_npmVersion": "2.1.11", "_nodeVersion": "0.10.33", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "327c7690d53a4866624333f041e6156653049a2e", "size": 6413, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-1.0.0.tgz", "integrity": "sha512-A1ZPjz9qhgwkA8P/nHYMszNzdHWJce2Y+YAOkJseuNqWw3f7che7+jHLxDM8OwyLRDafglpy9VTKT5hZ8WBXJg=="}, "publish_time": 1417981202336, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1417981202336, "_cnpmcore_publish_time": "2021-12-15T11:20:00.264Z"}, "0.2.6": {"name": "jws", "version": "0.2.6", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"base64url": "0.0.6", "jwa": "0.0.1"}, "devDependencies": {"tape": "~2.14.0"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws", "_id": "jws@0.2.6", "_shasum": "e9b7e9ac8d2ac1067413233bc6c20fbd8868e9ba", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "dist": {"shasum": "e9b7e9ac8d2ac1067413233bc6c20fbd8868e9ba", "size": 6424, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-0.2.6.tgz", "integrity": "sha512-ZjMgMG4UIf1KKjjYY4fKdB6X9W5fyrkFCTC35XgsrIvQGk088xiwGSsGI88e9oadSp+yZ/aRVQ7Cy1CS4SC/hA=="}, "publish_time": 1407433159155, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1407433159155, "_cnpmcore_publish_time": "2021-12-15T11:20:00.473Z"}, "0.2.5": {"name": "jws", "version": "0.2.5", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"base64url": "0.0.6", "jwa": "0.0.1"}, "devDependencies": {"tap": "~0.3.3"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "_id": "jws@0.2.5", "dist": {"shasum": "f738d45b38e5d9f6b7171aadd0ed5ac9f5a62555", "size": 6248, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-0.2.5.tgz", "integrity": "sha512-ZCeoWn9ZAj5jl3ZID7AuIIB9QTPF/ooxhUR42SkSuRWjqaZTHwuxsJFfPv9F4ICCk/bxOrE8i49LusxrbAGdSQ=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1391093919557, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1391093919557, "_cnpmcore_publish_time": "2021-12-15T11:20:00.670Z"}, "0.2.4": {"name": "jws", "version": "0.2.4", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"base64url": "0.0.6", "jwa": "0.0.1"}, "devDependencies": {"tap": "~0.3.3"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "_id": "jws@0.2.4", "dist": {"shasum": "3d37329052f111837867d363c040fb7717c6612b", "size": 6207, "noattachment": false, "tarball": "https://registry.npmmirror.com/jws/-/jws-0.2.4.tgz", "integrity": "sha512-qgMnvXv4l43dhF0hH69UKePq4+MArg2zOzq8p8NoCqBHecIVsqGXHCiWf1N8sJ/XtCrOWRj/iHP3wGSLKxuGcQ=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1390938039504, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1390938039504, "_cnpmcore_publish_time": "2021-12-15T11:20:00.870Z"}, "0.2.3": {"name": "jws", "version": "0.2.3", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "readmeFilename": "readme.md", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"base64url": "0.0.3", "jwa": "0.0.1"}, "devDependencies": {"tap": "~0.3.3"}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "_id": "jws@0.2.3", "dist": {"tarball": "https://registry.npmmirror.com/jws/-/jws-0.2.3.tgz", "shasum": "0aebe20a042365c4a15de24596dd0ddbe7aa422e", "size": 6202, "noattachment": false, "integrity": "sha512-f4GoHGfA1AqYQPN5AyaCUO0EN1fY//R3MtNnJ5Ffdb5nbvx5VzwvhwXSv+Pv4RjcQpHm3mDELJTQWqnms58LgA=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1383704020508, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1383704020508, "_cnpmcore_publish_time": "2021-12-15T11:20:01.077Z"}, "0.2.2": {"name": "jws", "version": "0.2.2", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "readmeFilename": "readme.md", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"tap": "~0.3.3", "base64url": "0.0.3", "jwa": "0.0.1"}, "_id": "jws@0.2.2", "dist": {"tarball": "https://registry.npmmirror.com/jws/-/jws-0.2.2.tgz", "shasum": "0889baae5ea0b07f9fc022cbd977debf154ba20b", "size": 6191, "noattachment": false, "integrity": "sha512-mUOiYVD51NCJze4Qdj+19EKxT2+shnNXVUQGlXCEmlukiO1LNU9mZKEX6G9OFF9YhT68R0fwbLMd/uIgDYMfsw=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1363127263257, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1363127263257, "_cnpmcore_publish_time": "2021-12-15T11:20:01.324Z"}, "0.2.1": {"name": "jws", "version": "0.2.1", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "readmeFilename": "readme.md", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"tap": "~0.3.3", "base64url": "0.0.3", "jwa": "0.0.1"}, "_id": "jws@0.2.1", "dist": {"tarball": "https://registry.npmmirror.com/jws/-/jws-0.2.1.tgz", "shasum": "361e7750aa40f64913dba402d6f183df64381e2d", "size": 5873, "noattachment": false, "integrity": "sha512-VqkRkYpbeywE4o4qsxlntiBMeJXdWO4tXqm9X2koueLTGJ8EaQ0TCW5PNSv+tw1R37R3cIAiZTBhLRWbgptohQ=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1361833963884, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1361833963884, "_cnpmcore_publish_time": "2021-12-15T11:20:01.515Z"}, "0.2.0": {"name": "jws", "version": "0.2.0", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "readmeFilename": "readme.md", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"tap": "~0.3.3", "base64url": "0.0.3", "jwa": "0.0.1"}, "_id": "jws@0.2.0", "dist": {"tarball": "https://registry.npmmirror.com/jws/-/jws-0.2.0.tgz", "shasum": "b7db28748768b182b42e5c7ac223902c1a306c06", "size": 5754, "noattachment": false, "integrity": "sha512-kRceOYXq5Kpx5knUU8N1RqgrkuMA+G6503xhApe5onET98G0Pau2+G327bGvnaZrDfiI62D3cNNUCENWEYncDg=="}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1360526620963, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1360526620963, "_cnpmcore_publish_time": "2021-12-15T11:20:01.733Z"}, "0.0.2": {"name": "jws", "version": "0.0.2", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "readmeFilename": "readme.md", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"tap": "~0.3.3", "base64url": "0.0.3"}, "_id": "jws@0.0.2", "dist": {"tarball": "https://registry.npmmirror.com/jws/-/jws-0.0.2.tgz", "shasum": "8c6916977183cce3361da48c8c2e0c606e7a95c6", "size": 6720, "noattachment": false, "integrity": "sha512-o7PCeWei86NxZFQmeyvpdwDY7QGSr4D5QkbFBau6cDNwFyG4hi6IGS75Z1EPQ+V7erMCBMKJkQYlPiphMNOWEg=="}, "_npmVersion": "1.2.0", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1358553535837, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1358553535837, "_cnpmcore_publish_time": "2021-12-15T11:20:01.915Z"}, "0.0.1": {"name": "jws", "version": "0.0.1", "description": "Implementation of JSON Web Signatures", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "keywords": ["jws", "json", "web", "signatures"], "author": {"name": "<PERSON>"}, "license": "MIT", "readmeFilename": "readme.md", "gitHead": "c0f6b27bcea5a2ad2e304d91c2e842e4076a6b03", "dependencies": {"tap": "~0.3.3", "base64url": "0.0.3"}, "_id": "jws@0.0.1", "dist": {"tarball": "https://registry.npmmirror.com/jws/-/jws-0.0.1.tgz", "shasum": "76a4b3afd3e03844ff01c4b3fabc22e0d43d6787", "size": 1951, "noattachment": false, "integrity": "sha512-Z+RLy0vvN1zYkW97tJIOTsYIPhN0l5eXEQ0yPRTm3X8qDbUgAGhuDsA3OZ5MzFR0woG1/kP9F2+w2vXnrNpqvA=="}, "_npmVersion": "1.2.0", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "omsmith", "email": "<EMAIL>"}, {"name": "stenington", "email": "<EMAIL>"}], "publish_time": 1358367459718, "deprecated": "Security update: Versions below 3.0.0 are deprecated.", "_hasShrinkwrap": false, "_cnpm_publish_time": 1358367459718, "_cnpmcore_publish_time": "2021-12-15T11:20:02.146Z"}}, "bugs": {"url": "https://github.com/brianloveswords/node-jws/issues"}, "homepage": "https://github.com/brianloveswords/node-jws#readme", "keywords": ["jws", "json", "web", "signatures"], "repository": {"type": "git", "url": "git://github.com/brianloveswords/node-jws.git"}, "_source_registry_name": "default"}