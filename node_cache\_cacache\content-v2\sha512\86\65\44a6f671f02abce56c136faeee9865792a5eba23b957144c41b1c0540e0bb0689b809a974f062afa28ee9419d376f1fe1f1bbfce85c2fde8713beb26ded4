{"_attachments": {}, "_id": "node-addon-api", "_rev": "1247-61f1461ca920628a7b6e93ca", "description": "Node.js API (Node-API)", "dist-tags": {"latest": "8.3.1"}, "license": "MIT", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "name": "node-addon-api", "readme": "# **node-addon-api module**\n\n[![codecov](https://codecov.io/gh/nodejs/node-addon-api/branch/main/graph/badge.svg)](https://app.codecov.io/gh/nodejs/node-addon-api/tree/main)\n\n[![NPM](https://nodei.co/npm/node-addon-api.png?downloads=true&downloadRank=true)](https://nodei.co/npm/node-addon-api/) [![NPM](https://nodei.co/npm-dl/node-addon-api.png?months=6&height=1)](https://nodei.co/npm/node-addon-api/)\n\nThis module contains  **header-only C++ wrapper classes** which simplify\nthe use of the C based [Node-API](https://nodejs.org/dist/latest/docs/api/n-api.html)\nprovided by Node.js when using C++. It provides a C++ object model\nand exception handling semantics with low overhead.\n\n- [API References](doc/README.md)\n- [Badges](#badges)\n- [Contributing](#contributing)\n- [License](#license)\n\n## API References\n\nAPI references are available in the [doc](doc/README.md) directory.\n\n<!-- x-release-please-start-version -->\n## Current version: 8.3.1\n<!-- x-release-please-end -->\n\n(See [CHANGELOG.md](CHANGELOG.md) for complete Changelog)\n\nnode-addon-api is based on [Node-API](https://nodejs.org/api/n-api.html) and supports using different Node-API versions.\nThis allows addons built with it to run with Node.js versions which support the targeted Node-API version.\n**However** the node-addon-api support model is to support only the active LTS Node.js versions. This means that\nevery year there will be a new major which drops support for the Node.js LTS version which has gone out of service.\n\nThe oldest Node.js version supported by the current version of node-addon-api is Node.js 18.x.\n\n## Badges\n\nThe use of badges is recommended to indicate the minimum version of Node-API\nrequired for the module. This helps to determine which Node.js major versions are\nsupported. Addon maintainers can consult the [Node-API support matrix][] to determine\nwhich Node.js versions provide a given Node-API version. The following badges are\navailable:\n\n![Node-API v1 Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20v1%20Badge.svg)\n![Node-API v2 Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20v2%20Badge.svg)\n![Node-API v3 Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20v3%20Badge.svg)\n![Node-API v4 Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20v4%20Badge.svg)\n![Node-API v5 Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20v5%20Badge.svg)\n![Node-API v6 Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20v6%20Badge.svg)\n![Node-API v7 Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20v7%20Badge.svg)\n![Node-API v8 Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20v8%20Badge.svg)\n![Node-API v9 Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20v9%20Badge.svg)\n![Node-API Experimental Version Badge](https://github.com/nodejs/abi-stable-node/blob/doc/assets/Node-API%20Experimental%20Version%20Badge.svg)\n\n## Contributing\n\nWe love contributions from the community to **node-addon-api**!\nSee [CONTRIBUTING.md](CONTRIBUTING.md) for more details on our philosophy around extending this module.\n\n## Team members\n\n### Active\n\n| Name                | GitHub Link                                           |\n| ------------------- | ----------------------------------------------------- |\n| Anna Henningsen     | [addaleax](https://github.com/addaleax)               |\n| Chengzhong Wu       | [legendecas](https://github.com/legendecas)           |\n| Jack Xia            | [JckXia](https://github.com/JckXia)                   |\n| Kevin Eady          | [KevinEady](https://github.com/KevinEady)             |\n| Michael Dawson      | [mhdawson](https://github.com/mhdawson)               |\n| Nicola Del Gobbo    | [NickNaso](https://github.com/NickNaso)               |\n| Vladimir Morozov    | [vmoroz](https://github.com/vmoroz)                   |\n\n<details>\n\n<summary>Emeritus</summary>\n\n### Emeritus\n\n| Name                | GitHub Link                                           |\n| ------------------- | ----------------------------------------------------- |\n| Arunesh Chandra     | [aruneshchandra](https://github.com/aruneshchandra)   |\n| Benjamin Byholm     | [kkoopa](https://github.com/kkoopa)                   |\n| Gabriel Schulhof    | [gabrielschulhof](https://github.com/gabrielschulhof) |\n| Hitesh Kanwathirtha | [digitalinfinity](https://github.com/digitalinfinity) |\n| Jason Ginchereau    | [jasongin](https://github.com/jasongin)               |\n| Jim Schlight        | [jschlight](https://github.com/jschlight)             |\n| Sampson Gao         | [sampsongao](https://github.com/sampsongao)           |\n| Taylor Woll         | [boingoing](https://github.com/boingoing)             |\n\n</details>\n\n## License\n\nLicensed under [MIT](./LICENSE.md)\n\n[Node-API support matrix]: https://nodejs.org/dist/latest/docs/api/n-api.html#n_api_n_api_version_matrix\n", "time": {"created": "2022-01-26T13:01:16.948Z", "modified": "2025-06-05T15:58:50.368Z", "4.2.0": "2021-09-17T16:56:24.180Z", "4.1.0": "2021-08-25T12:07:07.771Z", "4.0.0": "2021-06-15T13:27:27.332Z", "3.2.1": "2021-05-28T18:25:15.697Z", "3.2.0": "2021-05-17T17:28:07.534Z", "3.1.0": "2020-12-17T00:41:18.137Z", "3.0.2": "2020-09-18T16:08:41.104Z", "3.0.1": "2020-07-13T16:05:44.718Z", "2.0.2": "2020-07-01T14:44:17.785Z", "2.0.1": "2020-06-02T19:14:41.201Z", "1.7.2": "2020-06-02T19:07:47.267Z", "3.0.0": "2020-04-30T00:21:18.730Z", "2.0.0": "2019-11-28T12:19:08.099Z", "1.7.1": "2019-07-23T22:07:46.657Z", "1.7.0": "2019-07-23T15:31:53.195Z", "1.6.3": "2019-04-03T21:36:40.886Z", "1.6.2": "2018-11-29T00:32:53.499Z", "1.6.1": "2018-11-14T19:43:49.848Z", "1.6.0": "2018-11-02T22:38:08.964Z", "1.5.0": "2018-10-03T05:59:27.663Z", "1.4.0": "2018-07-19T22:15:26.032Z", "1.3.0": "2018-05-08T19:40:32.175Z", "1.2.0": "2018-01-11T23:10:32.346Z", "1.1.0": "2017-11-20T15:47:47.630Z", "1.0.0": "2017-09-27T13:28:45.780Z", "0.6.3": "2017-08-25T18:44:49.305Z", "0.6.2": "2017-08-21T15:27:44.115Z", "0.5.1": "2017-07-18T21:32:30.392Z", "0.5.0": "2017-07-18T16:21:20.691Z", "0.4.0": "2017-07-10T23:35:36.279Z", "0.3.5": "2017-06-26T19:39:24.416Z", "0.3.4": "2017-06-13T21:19:29.962Z", "0.3.1": "2017-05-25T05:49:45.540Z", "0.3.0": "2017-05-23T17:48:32.810Z", "0.2.0": "2017-05-18T18:46:40.908Z", "0.1.0": "2017-05-04T23:12:20.155Z", "4.3.0": "2022-01-21T15:06:36.896Z", "5.0.0": "2022-05-02T10:51:50.672Z", "5.1.0": "2023-01-13T11:39:21.661Z", "6.0.0": "2023-02-10T17:22:59.896Z", "6.1.0": "2023-04-20T14:27:30.305Z", "7.0.0": "2023-06-15T13:55:48.692Z", "7.1.0": "2024-01-19T08:46:25.251Z", "8.0.0": "2024-03-05T07:02:28.228Z", "8.1.0": "2024-07-08T08:57:54.301Z", "7.1.1": "2024-07-12T10:15:07.595Z", "8.2.0": "2024-09-30T14:30:16.924Z", "8.2.1": "2024-10-11T15:07:24.071Z", "8.2.2": "2024-11-08T13:24:19.573Z", "8.3.0": "2024-11-29T16:19:40.909Z", "8.3.1": "2025-02-21T16:13:10.012Z"}, "versions": {"4.2.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "lint:fix": "node tools/clang-format --fix"}, "pre-commit": "lint", "version": "4.2.0", "support": true, "gitHead": "79d5651a11ed9f7b922cf1d5534b9ed5b37d742d", "dependencies": {}, "_id": "node-addon-api@4.2.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"shasum": "117cbb5a959dff0992e1c586ae0393573e4d2a87", "size": 55649, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-4.2.0.tgz", "integrity": "sha512-eazsqzwG2lskuzBqCGPi7Ac2UgOoMz8JVOXVhTvvPDYhthvNpefx8jWD8Np7Gv+2Sz0FlPWZk0nJV0z598Wn8Q=="}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_4.2.0_1631897783997_0.5996735517896796"}, "_hasShrinkwrap": false, "publish_time": 1631897784180, "_cnpm_publish_time": 1631897784180, "_cnpmcore_publish_time": "2021-12-13T06:48:39.554Z"}, "4.1.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "lint:fix": "node tools/clang-format --fix"}, "pre-commit": "lint", "version": "4.1.0", "support": true, "gitHead": "4a3de56c3e4ed0031635a2f642b27efeeed00add", "dependencies": {}, "_id": "node-addon-api@4.1.0", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"shasum": "f1722f1f60793584632ffffb79e12ca042c48bd0", "size": 55474, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-4.1.0.tgz", "integrity": "sha512-Zz1o1BDX2VtduiAt6kgiUl8jX1Vm3NMboljFYKQJ6ee8AGfiTvM2mlZFI3xPbqjs80rCQgiVJI/DjQ/1QJ0HwA=="}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_4.1.0_1629893227569_0.690821099562914"}, "_hasShrinkwrap": false, "publish_time": 1629893227771, "_cnpm_publish_time": 1629893227771, "_cnpmcore_publish_time": "2021-12-13T06:48:39.936Z"}, "4.0.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "lint:fix": "node tools/clang-format --fix"}, "pre-commit": "lint", "version": "4.0.0", "support": true, "gitHead": "ad76ad07f914fab02be5778ec67485916c4626d9", "dependencies": {}, "_id": "node-addon-api@4.0.0", "_nodeVersion": "16.3.0", "_npmVersion": "7.15.1", "dist": {"shasum": "ac128f43eff7fac4b5f5ef2f39d6d7c2709efead", "size": 52696, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-4.0.0.tgz", "integrity": "sha512-ALmRVBFzfwldBfk3SbKfl6+PVMXiCPKZBEfsJqB/EjXAMAI+MfFrEHR+GMRBuI162DihZ1QjEZ8ieYKuRCJ8Hg=="}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_4.0.0_1623763647162_0.5015393581552483"}, "_hasShrinkwrap": false, "publish_time": 1623763647332, "_cnpm_publish_time": 1623763647332, "_cnpmcore_publish_time": "2021-12-13T06:48:40.281Z"}, "3.2.1": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "lint:fix": "node tools/clang-format --fix"}, "pre-commit": "lint", "version": "3.2.1", "support": true, "gitHead": "a0c0492f7ceee6a35de9b272ba2231824a61beca", "dependencies": {}, "_id": "node-addon-api@3.2.1", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "81325e0a2117789c0128dab65e7e38f07ceba161", "size": 67002, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.2.1.tgz", "integrity": "sha512-mmcei9JghVNDYydghQmeDX8KoAm0FAiYyIcUt/N4nhyAipB17pllZQDOJD2fotxABnt4Mdz+dKTO7eftLg4d0A=="}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_3.2.1_1622226315540_0.2932902386891143"}, "_hasShrinkwrap": false, "publish_time": 1622226315697, "_cnpm_publish_time": 1622226315697, "_cnpmcore_publish_time": "2021-12-13T06:48:40.624Z"}, "3.2.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "lint:fix": "node tools/clang-format --fix"}, "pre-commit": "lint", "version": "3.2.0", "support": true, "gitHead": "60348d1e38ddd334e6d56a8c69e88be89ab6aeb6", "dependencies": {}, "_id": "node-addon-api@3.2.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "7028b56a7eb572b73873aed731a7f9c9365f5ee4", "size": 66952, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.2.0.tgz", "integrity": "sha512-kcwSAWhPi4+QzAtsL2+2s/awvDo2GKLsvMCwNRxb5BUshteXU8U97NCyvQDsGKs/m0He9WcG4YWew/BnuLx++w=="}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_3.2.0_1621272487345_0.12156209064080126"}, "_hasShrinkwrap": false, "publish_time": 1621272487534, "_cnpm_publish_time": 1621272487534, "_cnpmcore_publish_time": "2021-12-13T06:48:41.000Z"}, "3.1.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "fs-extra": "^9.0.1", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format.js", "lint:fix": "git-clang-format '*.h', '*.cc'"}, "pre-commit": "lint", "version": "3.1.0", "support": true, "gitHead": "1bdc7584cdea3c5761385c7c08a06e12d9852972", "_id": "node-addon-api@3.1.0", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"shasum": "98b21931557466c6729e51cb77cd39c965f42239", "size": 122233, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.1.0.tgz", "integrity": "sha512-flmrDNB06LIl5lywUz7YlNGZH/5p0M7W28k8hzd9Lshtdh1wshD2Y+U4h9LD6KObOy1f+fEVdgprPrEymjM5uw=="}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_3.1.0_1608165677935_0.7147698416734058"}, "_hasShrinkwrap": false, "publish_time": 1608165678137, "_cnpm_publish_time": 1608165678137, "_cnpmcore_publish_time": "2021-12-13T06:48:41.463Z"}, "3.0.2": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"benchmark": "^2.1.4", "fs-extra": "^9.0.1", "bindings": "^1.5.0", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "3.0.2", "gitHead": "79a777e1f85305b374878d757fd4a2c60e70abe7", "_id": "node-addon-api@3.0.2", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"shasum": "04bc7b83fd845ba785bb6eae25bc857e1ef75681", "size": 138851, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.0.2.tgz", "integrity": "sha512-+D4s2HCnxPd5PjjI0STKwncjXTUKKqm74MDMz9OPXavjsGmjkvwgLtA5yoxJUdmpj52+2u+RrXgPipahKczMKg=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_3.0.2_1600445320926_0.9558192508787307"}, "_hasShrinkwrap": false, "publish_time": 1600445321104, "_cnpm_publish_time": 1600445321104, "_cnpmcore_publish_time": "2021-12-13T06:48:41.946Z"}, "3.0.1": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"benchmark": "^2.1.4", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "3.0.1", "gitHead": "61c98463939630bb52438bb8ea1fb1fdf651af1c", "_id": "node-addon-api@3.0.1", "_nodeVersion": "12.18.1", "_npmVersion": "6.14.5", "dist": {"shasum": "990544a2607ec3f538443df4858f8c40089b7783", "size": 134912, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.0.1.tgz", "integrity": "sha512-YUpjl57P55u2yUaKX5Bgy4t5s6SCNYMg+62XNg+k41aYbBL1NgWrZfcgljR5MxDxHDjzl0qHDNtH6SkW4DXNCA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_3.0.1_1594656344563_0.1674197905666035"}, "_hasShrinkwrap": false, "publish_time": 1594656344718, "_cnpm_publish_time": 1594656344718, "_cnpmcore_publish_time": "2021-12-13T06:48:42.406Z"}, "2.0.2": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "2.0.2", "gitHead": "7582fafebb7d92570fafc4cf0939f538b629c06f", "_id": "node-addon-api@2.0.2", "_nodeVersion": "12.18.1", "_npmVersion": "6.14.5", "dist": {"shasum": "432cfa82962ce494b132e9d72a15b29f71ff5d32", "size": 150809, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-2.0.2.tgz", "integrity": "sha512-Ntyt4AIXyaLIuMHF6IOoTakB3K+RWxwtsHNRxllEoA6vPwP9o4866g6YWDLUdnucilZhmkxiHwHr11gAENw+QA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_2.0.2_1593614657658_0.495650358650533"}, "_hasShrinkwrap": false, "publish_time": 1593614657785, "_cnpm_publish_time": 1593614657785, "_cnpmcore_publish_time": "2021-12-13T06:48:43.023Z"}, "2.0.1": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "2.0.1", "gitHead": "2b78b54b52c427926e7bb851a7205b103dc4e447", "_id": "node-addon-api@2.0.1", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "dist": {"shasum": "4fd0931bf6d7e48b219ff3e6abc73cbb0252b7a3", "size": 149933, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-2.0.1.tgz", "integrity": "sha512-2WVfwRfIr1AVn3dRq4yRc2Hn35ND+mPJH6inC6bjpYCZVrpXPB4j3T6i//OGVfqVsR1t/X/axRulDsheq4F0LQ=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_2.0.1_1591125281032_0.753966058856824"}, "_hasShrinkwrap": false, "publish_time": 1591125281201, "_cnpm_publish_time": 1591125281201, "_cnpmcore_publish_time": "2021-12-13T06:48:43.461Z"}, "1.7.2": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.7.2", "gitHead": "74a09c7c4f24445aaca37948624c10dc2e771a1e", "_id": "node-addon-api@1.7.2", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "dist": {"shasum": "3df30b95720b53c24e59948b49532b662444f54d", "size": 143727, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.7.2.tgz", "integrity": "sha512-ibPK3iA+vaY1eEjESkQkM0BbCqFOaZMiXRTtdB0u7b4djtY6JnsjvPdUHVMg6xQt3B8fpTTWHI9A+ADjM9frzg=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.7.2_1591124867121_0.18325729961033654"}, "_hasShrinkwrap": false, "publish_time": 1591124867267, "_cnpm_publish_time": 1591124867267, "_cnpmcore_publish_time": "2021-12-13T06:48:43.889Z"}, "3.0.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"benchmark": "^2.1.4", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "3.0.0", "gitHead": "081cdc2f732ce759df777ffd69e0b4c2bd318b0d", "_id": "node-addon-api@3.0.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"shasum": "812446a1001a54f71663bed188314bba07e09247", "size": 133550, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.0.0.tgz", "integrity": "sha512-sSHCgWfJ+Lui/u+0msF3oyCgvdkhxDbkCS6Q8uiJquzOimkJBvX6hl5aSSA7DR1XbMpdM8r7phjcF63sF4rkKg=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_3.0.0_1588206078552_0.23174397823089143"}, "_hasShrinkwrap": false, "publish_time": 1588206078730, "_cnpm_publish_time": 1588206078730, "_cnpmcore_publish_time": "2021-12-13T06:48:44.476Z"}, "2.0.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "2.0.0", "gitHead": "f677794b31876c7b235e69b3a636b59310b2d5c0", "_id": "node-addon-api@2.0.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "f9afb8d777a91525244b01775ea0ddbe1125483b", "size": 149990, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-2.0.0.tgz", "integrity": "sha512-ASCL5U13as7HhOExbT6OlWJJUV/lLzL2voOSP1UVehpRD8FbSrSDjfScK/KwAvVTI5AS6r4VwbOMlIqtvRidnA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_2.0.0_1574943547928_0.09148514299582189"}, "_hasShrinkwrap": false, "publish_time": 1574943548099, "_cnpm_publish_time": 1574943548099, "_cnpmcore_publish_time": "2021-12-13T06:48:44.965Z"}, "1.7.1": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.7.1", "gitHead": "5a7f8b2c7daae0ed898c198e13e56332169b3571", "_id": "node-addon-api@1.7.1", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"shasum": "cf813cd69bb8d9100f6bdca6755fc268f54ac492", "size": 143222, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.7.1.tgz", "integrity": "sha512-2+DuKodWvwRTrCfKOeR24KIc5unKjOh8mz17NCzVnHWfjAdDqbfbjqh7gUT+BkXBRQM52+xCHciKWonJ3CbJMQ=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.7.1_1563919666527_0.4381203217246479"}, "_hasShrinkwrap": false, "publish_time": 1563919666657, "_cnpm_publish_time": 1563919666657, "_cnpmcore_publish_time": "2021-12-13T06:48:45.429Z"}, "1.7.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.7.0", "gitHead": "0a1380c896f657bbd619755f4fafa84880db4824", "_id": "node-addon-api@1.7.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"shasum": "d2897e0a2f35895676133004abbe6a7af6e55f79", "size": 143065, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.7.0.tgz", "integrity": "sha512-TaiwMuQqmonaIz/dI+a3V2XE67872jC2Z+fOzKuH4piwxGZN48NwVy75hL8shzQL09Nfl/Avk7md7dVcMG0zlA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.7.0_1563895912920_0.3449440298137434"}, "_hasShrinkwrap": false, "publish_time": 1563895913195, "_cnpm_publish_time": 1563895913195, "_cnpmcore_publish_time": "2021-12-13T06:48:45.928Z"}, "1.6.3": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.6.3", "gitHead": "c12c42519c108b859c7d9f3a3ebb902f948eabd6", "_id": "node-addon-api@1.6.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "3998d4593e2dca2ea82114670a4eb003386a9fe1", "size": 133826, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.6.3.tgz", "integrity": "sha512-FXWH6mqjWgU8ewuahp4spec8LkroFZK2NicOv6bNwZC3kcwZUI8LeZdG80UzTSLLhK4T7MsgNwlYDVRlDdfTDg=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.6.3_1554327400750_0.06315390023221634"}, "_hasShrinkwrap": false, "publish_time": 1554327400886, "_cnpm_publish_time": 1554327400886, "_cnpmcore_publish_time": "2021-12-13T06:48:46.404Z"}, "1.6.2": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.6.2", "gitHead": "d8e9c2245abb7fbc67f5696bcaebd10085a4688e", "_id": "node-addon-api@1.6.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "d8aad9781a5cfc4132cc2fecdbdd982534265217", "size": 131613, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.6.2.tgz", "integrity": "sha512-479Bjw9nTE5DdBSZZWprFryHGjUaQC31y1wHo19We/k0BZlrmhqQitWoUL0cD8+scljCbIUL+E58oRDEakdGGA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.6.2_1543451573406_0.5831295775900345"}, "_hasShrinkwrap": false, "publish_time": 1543451573499, "_cnpm_publish_time": 1543451573499, "_cnpmcore_publish_time": "2021-12-13T06:48:46.948Z"}, "1.6.1": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.6.1", "gitHead": "269bf12e5f1908aeaa04de6f2efb26eb286cea0a", "_id": "node-addon-api@1.6.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "a9881c8dbc6400bac6ddedcb96eccf8051678536", "size": 131555, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.6.1.tgz", "integrity": "sha512-GcLOYrG5/enbqH4SMsqXt6GQUQGGnDnE3FLDZzXYkCgQHuZV5UDFR+EboeY8kpG0avroyOjpFQ2qLEBosFcRIA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.6.1_1542224629730_0.3889545930678706"}, "_hasShrinkwrap": false, "publish_time": 1542224629848, "_cnpm_publish_time": 1542224629848, "_cnpmcore_publish_time": "2021-12-13T06:48:47.501Z"}, "1.6.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.6.0", "gitHead": "322dc0943e52950954bd0559b5967ee321d2556f", "_id": "node-addon-api@1.6.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "bbb1a32395245fd2cddcfed8312b3dbd6511b6c4", "size": 131320, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.6.0.tgz", "integrity": "sha512-HEUPBHfdH4CLR1Qq4/Ek8GT/qFSvpApjJQmcYdLCL51ADU/Y11kMuFAdIevhNrPh3ylqVGA8k6vI/oi4YUAHbA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.6.0_1541198288833_0.4982463173971634"}, "_hasShrinkwrap": false, "publish_time": 1541198288964, "_cnpm_publish_time": 1541198288964, "_cnpmcore_publish_time": "2021-12-13T06:48:48.060Z"}, "1.5.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.5.0", "gitHead": "ffebf9ba9a3cfefe602756ffe0fc700a39e078ca", "_id": "node-addon-api@1.5.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "55be6b3da36e746f4b1f2af16c2adf67647d1ff8", "size": 130419, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.5.0.tgz", "integrity": "sha512-YsL/8dpBWxCFj3wAVAa/ceN4TlT8lACK8EgpuN0q/4ecflWHDuKpodb+tt7Rx22r/6FJ2f+IT25XSsXnZGwYgA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.5.0_1538546367447_0.4017298088344614"}, "_hasShrinkwrap": false, "publish_time": 1538546367663, "_cnpm_publish_time": 1538546367663, "_cnpmcore_publish_time": "2021-12-13T06:48:48.619Z"}, "1.4.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.4.0", "gitHead": "2885c185912655076e0eb01de6d7e5e6393cbd19", "_id": "node-addon-api@1.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "4c43e4c1ed8fbf3176ce71003f352329adad87eb", "size": 114847, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.4.0.tgz", "integrity": "sha512-agquHPHnxYGox7Rjz2+TZQeOiH8IVbNFSTyTPA+peMUAP6klgrBH5dcwHsNNChQh7l/dtF0JNmZPbCqd5OXOIQ=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.4.0_1532038525974_0.37633167406305157"}, "_hasShrinkwrap": false, "publish_time": 1532038526032, "_cnpm_publish_time": 1532038526032, "_cnpmcore_publish_time": "2021-12-13T06:48:49.257Z"}, "1.3.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.3.0", "gitHead": "a750ed1932e4b03c457b7b93e08cb07ec3ae8201", "_id": "node-addon-api@1.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "11.0.0-pre", "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "dist": {"shasum": "9280a6afd4d0bd9401ef3c60b936beeea0a6b3d3", "size": 99476, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.3.0.tgz", "integrity": "sha512-yagD4yKkZLeG4EJkh+8Qbqhqw+owDQ/PowqD8vb5a5rfNXS/PRC21SGyIbUVXfPp/jl4s+jyeZj6xnLnDPLazw=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_1.3.0_1525808431928_0.4385847245093317"}, "_hasShrinkwrap": false, "publish_time": 1525808432175, "_cnpm_publish_time": 1525808432175, "_cnpmcore_publish_time": "2021-12-13T06:48:49.849Z"}, "1.2.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.2.0", "gitHead": "4b5a261d804574f77d426fd2093d4389288ac163", "_id": "node-addon-api@1.2.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "dist": {"shasum": "2d378bbed60cbb7b9e2c505c6833eed8723f41c4", "size": 93635, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.2.0.tgz", "integrity": "sha512-VDpTV5VK4kCqTfK5I7JAphXrL3txLbfshhjtscdYTuI4PkGvjWBFxNkctYdcRsMwgObyqVcm12tsiKZD76/d8g=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-1.2.0.tgz_1515712231150_0.22229782515205443"}, "publish_time": 1515712232346, "_hasShrinkwrap": false, "_cnpm_publish_time": 1515712232346, "_cnpmcore_publish_time": "2021-12-13T06:48:50.409Z"}, "1.1.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.1.0", "gitHead": "128ca1b21e1b04279505591177df295e39dd83f5", "_id": "node-addon-api@1.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "dist": {"shasum": "9f14bf703b4f5cc1bfe6bc4a30d8c8280b2d87b9", "size": 86927, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.1.0.tgz", "integrity": "sha512-WSGZ/xdGcyhNY2neCHS/JKQXdbOU5qZp8EGwxpEWSqOmI9+sCwO93npSH93ipa4z13+KZhGUgA5g/EAKH/F+Wg=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-1.1.0.tgz_1511192866461_0.5753951275255531"}, "publish_time": 1511192867630, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511192867630, "_cnpmcore_publish_time": "2021-12-13T06:48:51.318Z"}, "1.0.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.0.0", "gitHead": "82c2fa5a20a4c76ebd656a5aa676c34ac6ca1a28", "_id": "node-addon-api@1.0.0", "_shasum": "ce62bc6865f60f8d65c0441f2d1a5bdba5cda590", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.3", "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "dist": {"shasum": "ce62bc6865f60f8d65c0441f2d1a5bdba5cda590", "size": 82487, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.0.0.tgz", "integrity": "sha512-7hQ0EKaQFt6plfAuTHfG5Vh5kCN+pyEZvjG0Xal3Tzv19spQnhIrh8IP7+UxbvJ0meTbktgrlN+tR5kFr14yZg=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-1.0.0.tgz_1506518924654_0.23994539212435484"}, "publish_time": 1506518925780, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506518925780, "_cnpmcore_publish_time": "2021-12-13T06:48:51.938Z"}, "0.6.3": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "0.6.3", "gitHead": "790de9f473d05c2a4a3c0eeddbc6415dfd47bed3", "_id": "node-addon-api@0.6.3", "_npmVersion": "5.3.0", "_nodeVersion": "9.0.0-pre", "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "dist": {"shasum": "3905f0876eaa391d19e0cfdc7f10661e1897a475", "size": 80363, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.6.3.tgz", "integrity": "sha512-FikxTt0OaQ716zlsKKxZoRRyd0aAOJNRKK2ne4dS2R4FoEJXb7HyyxDTnYnLc22u0N2S2wiYuRm2gvehEdn4zQ=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-0.6.3.tgz_1503686688126_0.5833845392335206"}, "publish_time": 1503686689305, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503686689305, "_cnpmcore_publish_time": "2021-12-13T06:48:52.575Z"}, "0.6.2": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test"}, "version": "0.6.2", "gitHead": "ca9254832e15494ede96249302ba93ec2fb51720", "_id": "node-addon-api@0.6.2", "_npmVersion": "5.3.0", "_nodeVersion": "9.0.0-pre", "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "dist": {"shasum": "54f2719ca4a618b4eed70224181f0b816373325e", "size": 79791, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.6.2.tgz", "integrity": "sha512-bMxUO1Fo50bb3B23G7ivNtwmMZfm262xxrkrDn9OSLZ+3eDatp0/vab5nPintawCnls3gA8LXY2wpSbPMFfyKw=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-0.6.2.tgz_1503329262827_0.9881750061176717"}, "publish_time": 1503329264115, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503329264115, "_cnpmcore_publish_time": "2021-12-13T06:48:53.288Z"}, "0.5.1": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test"}, "version": "0.5.1", "gitHead": "0986f83e57307e38c1aac6aa9bfe230bdfe095e2", "_id": "node-addon-api@0.5.1", "_npmVersion": "5.0.3", "_nodeVersion": "9.0.0-pre", "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "dist": {"shasum": "b1bf4f6d91e4d10ae5d697fbcc5b0a57886fc6a2", "size": 78775, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.5.1.tgz", "integrity": "sha512-3//80u0PIUHvozitcQ/DBaaS46qa4iO9FNyQEGPybMpRyCVXyKz/I64Eok0pu+vIRriqqHyL4nhlnuTwC0d9ow=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-0.5.1.tgz_1500413548729_0.030465385410934687"}, "publish_time": 1500413550392, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500413550392, "_cnpmcore_publish_time": "2021-12-13T06:48:53.969Z"}, "0.5.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test"}, "version": "0.5.0", "gitHead": "d455c5215c7bbf5a19abc2d34f11f60077b72d55", "_id": "node-addon-api@0.5.0", "_npmVersion": "5.0.3", "_nodeVersion": "9.0.0-pre", "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "dist": {"shasum": "f378d18447cc33a7190d689a0e78d8e059c3c0eb", "size": 78778, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.5.0.tgz", "integrity": "sha512-c0IqEJ4gqRk2r2XhZIPRhQ+KjTZERXYrd9VrPs50kPhwjDFOQH6CLwHb69tWJH3+FeQR11FXaN86/OK4MmFwHw=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-0.5.0.tgz_1500394879124_0.3414635672233999"}, "publish_time": 1500394880691, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500394880691, "_cnpmcore_publish_time": "2021-12-13T06:48:54.823Z"}, "0.4.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test"}, "version": "0.4.0", "gitHead": "10ef2932508ab6a5575707fc060f966fb9b25cd4", "_id": "node-addon-api@0.4.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "15e5256750ca0682c5f4060302f802097f753740", "size": 75090, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.4.0.tgz", "integrity": "sha512-yMDJsQiaA01fhm6MO/tBqLXkXUf1FW40+JQ90Tm+RbKmewVGMZR4jD7ZK99Zse+/YC4723r3aJ6h7wZbKRdH3g=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-0.4.0.tgz_1499729735037_0.8656034101732075"}, "publish_time": 1499729736279, "_hasShrinkwrap": false, "_cnpm_publish_time": 1499729736279, "_cnpmcore_publish_time": "2021-12-13T06:48:55.493Z"}, "0.3.5": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test"}, "version": "0.3.5", "gitHead": "b1ef1b7df542266691d4f43eaaffb54c4c1fa587", "_id": "node-addon-api@0.3.5", "_shasum": "67e7a4abba26918561efa383fa5be4c91525c48b", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "67e7a4abba26918561efa383fa5be4c91525c48b", "size": 74911, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.3.5.tgz", "integrity": "sha512-TKtnrlB7rq/v9m4u3gAHt3O1YREgo4x/pgOucxUWb1bTEvte1+vAY1JXExK6WlXjMOnquX0vzejR3mZMt4AT9w=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-0.3.5.tgz_1498505964341_0.5701525383628905"}, "publish_time": 1498505964416, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498505964416, "_cnpmcore_publish_time": "2021-12-13T06:48:56.254Z"}, "0.3.4": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test"}, "version": "0.3.4", "gitHead": "3aab97b2bd19563a851ac223c8220e3ca3d05661", "_id": "node-addon-api@0.3.4", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "af3259040fc861c1812886ddd149b36d3aa63183", "size": 74500, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.3.4.tgz", "integrity": "sha512-gZ7oL1NuwRwq01zde4MeRjSGMvxU2TsJ0l6anf2yHMoSc3b43wBaSzGpme/Jbul+CAmQONIIrONVp0cpaJCarg=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-0.3.4.tgz_1497388769678_0.06508532632142305"}, "publish_time": 1497388769962, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497388769962, "_cnpmcore_publish_time": "2021-12-13T06:48:57.040Z"}, "0.3.1": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test"}, "version": "0.3.1", "gitHead": "22416e8dcba25ff6ff1a600097ee9fd8ac30f875", "_id": "node-addon-api@0.3.1", "_npmVersion": "5.0.0-beta.56", "_nodeVersion": "8.0.0-nightly20170524260cd411d4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3ac3393e5b2b778549e0d99c1b8a32f79ce05922", "size": 70448, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.3.1.tgz", "integrity": "sha512-/8kB0PYn5lMHDt8k8OmuUIkP2PoiYbQSqjGZNZDR2iriK4cNHutN56nun7ogtMWXhmU1HMR/SnnDUfSWxRdabg=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-0.3.1.tgz_1495691385433_0.4386101649142802"}, "publish_time": 1495691385540, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495691385540, "_cnpmcore_publish_time": "2021-12-13T06:48:57.767Z"}, "0.3.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test"}, "version": "0.3.0", "gitHead": "b3531140475d7c11d8ad4eac930e56f9b96f93c0", "_id": "node-addon-api@0.3.0", "_shasum": "9f78149f519144cb47dde3e658bb395c835d610f", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-nightly201705185de722ab6d", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9f78149f519144cb47dde3e658bb395c835d610f", "size": 70677, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.3.0.tgz", "integrity": "sha512-XTR4rYvYFWIEb91Lo8+Wi+2C2QJADI29cVkxMAP+Tq7DixkGmGv0pOW5nc6oF2QwPrg5+Sfg9CqWUYgxuHY4OA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api-0.3.0.tgz_1495561712478_0.711710260482505"}, "publish_time": 1495561712810, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495561712810, "_cnpmcore_publish_time": "2021-12-13T06:48:58.492Z"}, "0.2.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"node-gyp": "^3.6.0"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node --expose-gc test"}, "version": "0.2.0", "gitHead": "df3c99ecf3254cb8cc7fc66b247f4ad3f73d849e", "_id": "node-addon-api@0.2.0", "_shasum": "cf0f8ecd37b5c4088b8065c138730ad4f6629264", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.1", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cf0f8ecd37b5c4088b8065c138730ad4f6629264", "size": 66166, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.2.0.tgz", "integrity": "sha512-OprZzZqB2Pn9GSo4FXum3l1b5Q1JNg1D/ABKmxAcBM00wSv5yzV6Gm94iDHmYd11NtFVVFC+BbHpgaIw74lB4g=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-addon-api-0.2.0.tgz_1495133200671_0.6417490739841014"}, "publish_time": 1495133200908, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495133200908, "_cnpmcore_publish_time": "2021-12-13T06:48:59.200Z"}, "0.1.0": {"bugs": {"url": "https://github.com/nodejs/node-api/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"node-gyp": "^3.6.0"}, "directories": {}, "homepage": "https://github.com/nodejs/node-api", "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/nodejs/node-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node --expose-gc test"}, "version": "0.1.0", "gitHead": "53b0b8b9ce55919e36f0006b36c91217b6f56816", "_id": "node-addon-api@0.1.0", "_shasum": "f0a473c8e462773ff01a1e2be375178c4de39af1", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.1", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f0a473c8e462773ff01a1e2be375178c4de39af1", "size": 36950, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-0.1.0.tgz", "integrity": "sha512-ysffDGPYHfpaVmMi/QCWmd5vKxadnc0c4Zg9c/ElsE/spGuPHlvvfXkFbspdEenMAEYs3eFkt96eUcycMxQaGA=="}, "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-addon-api-0.1.0.tgz_1493939539921_0.5477822034154087"}, "publish_time": 1493939540155, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493939540155, "_cnpmcore_publish_time": "2021-12-13T06:48:59.850Z"}, "4.3.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "4.3.0", "support": true, "gitHead": "133e13d0d51e5e4bc75295b9bad07d0483da178d", "_id": "node-addon-api@4.3.0", "_nodeVersion": "17.4.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-73sE9+3UaLYYFmDsFZnqCInzPyh3MqIwZO9cw58yIqAZhONrrabrYyYe3TuIqtIiOuTXVhsGau8hcrhhwSsDIQ==", "shasum": "52a1a0b475193e0928e98e0426a0d1254782b77f", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-4.3.0.tgz", "fileCount": 18, "unpackedSize": 384042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sv8CRA9TVsSAnZWagAA6csP/0+vUmMkOtOapGaIgSqh\nZ/jUf+9H5Cz1pqGUY1dqe6QoHawi/zCaTByPCNnEM5gfIcPAv3GO023RvUYq\n8Nz9ZJ4rrWTMb3QEJlJcLtbOsEAP0as/zG6mNWVvviVAiTpV9JZiaemSusuM\nY3/rH6iCmLFtwWnSzrd8Z3ZhFHExKns8Lq4YTPnIhGlbJjvPlPicTtqGS+ho\nty5N+2ZjVB2JloW1yaFpicWjv/l7+2gzdDAi/rkLygYybijkDHVwyhkeKfR5\n3YtMTzu//fVUR1FGSwiGpZFKUOE+uUF9dnNODCurOoFdpLadXq+4cC1qOZWx\nG8+5PIO0Zmr13tVc8ZNbW/RUn8VA62qLDMy9oTEtlyNzVtX4FNSUv41GvJdY\nLcbahh/Z6vg0vPGWGS25+bAUlBLglvkcX4aJRNbdyAnxu2GBOJYH1n5zdsE/\nOJYeb7PfbaWnxsNNqOOqT7dq9QFYlKHRzxxCJjihZnUGXHHf7GHwnnJGwyL9\nI0OG/wb+wiW12vT8IgU/vZGz5K9ky3m0x6i67SopCOEqdrWubp0p5HAwMXnG\n/hEKsFFfOVeFK2rwOPEIPvdBvCLrLWI1S3gXIkFmmuXG5oO6eDiuM+2aYLsP\n/bl7bWs00i2ZcPXEXuk7tLeeQTA2FTDOOAagjUlk+tNOkNJ+kFcb7PxyiZch\nnI1e\r\n=lQKC\r\n-----END PGP SIGNATURE-----\r\n", "size": 57446}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_4.3.0_1642777596668_0.14728813538445684"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-21T15:08:54.333Z"}, "5.0.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "5.0.0", "support": true, "gitHead": "e469ee6b88c2efeb20e05c9a19c16e73a43592d5", "_id": "node-addon-api@5.0.0", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-CvkDw2OEnme7ybCykJpVcKH+uAOLV2qLqiyla128dN9TkEWfrYmxG6C2boDe5KcNQqZF3orkqzGgOMvZ/JNekA==", "shasum": "7d7e6f9ef89043befdb20c1989c905ebde18c501", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-5.0.0.tgz", "fileCount": 18, "unpackedSize": 387017, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmTr7OsBXjkvX1a1pIoBgUEbOcObR7oRZmoyIJTq4gSQIgEZ85Ny60c7G3g1YZz0Dgk7UE73/MJd1Efq3087px/M4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib7fGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/WA/+PaK+VRE7ssR0weWbl1LJgde0I1WDsbpL19izska3umTPme5G\r\nJEhrMMLq1VGkchBLmwIplAQki6BPApJ1TECfD3zub1+QjxfQIEevKD2W2fRN\r\np2c9486Q2CCHGR/Wpe9TqiHb3NXUfZpe+q/bZ10QlVLRQuJZ7QnVjsPBipkR\r\ncvPrUNc95LZDWIozWnf4UIoeXrJ5HMLvQ4v9S8V5TrluR3IshSdlcxirsogU\r\nIPZNmmd+f9eSvosDluIUIV/OuAcAb3f1r8WNI2r7UmSXbW3zf59nOO780eEj\r\nDN8pb3BjCvbhZOLNSkUZVHtbDInFFqOtrvfJM5Y+DoYLOGuuYHz7yWdG48N0\r\nBLdh6g5XlGyWo/aMPrEyXj0sIIofM196U1hBAhEWn/AgtjE9AH50v+TVv0+N\r\nOat6w6w25AOroQTqGPMOwKzqhK+aEiXsMmRIb50BEhixt1fmSeQ7dWamaWq4\r\n2fL17U0jazPwcHTYvMmpmKkVPm8Fnl6RnSp3MeuQfX0J7G1ydUj0c9vMoDsN\r\nv78ezy9QyCJx/a+noSJlDB0DIdR0cVO5VNNGbAbG9k6o8tgCjNPRvemH6876\r\nUhMFY1h6hi+OOOA5sMqCXmyvFN01ZIS1F8TUAKloVTeCG2PuzPGBPhTulpVu\r\naWL3Y86KFIo+5L0+JZ2GW9qCSnD75wIqYZs=\r\n=/WY+\r\n-----END PGP SIGNATURE-----\r\n", "size": 58013}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_5.0.0_1651488710517_0.7269271347383355"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-02T10:51:55.527Z"}, "5.1.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "5.1.0", "support": true, "gitHead": "34221c1fe69bbbe4762a6a3bede80456327dd3c2", "_id": "node-addon-api@5.1.0", "_nodeVersion": "16.18.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-eh0GgfEkpnoWDq+VY8OyvYhFEzBk6jIYbRKdIlyTiAXIVJ8PyBaKb0rp7oDtoddbdoHWhq8wwr+XZ81F1rpNdA==", "shasum": "49da1ca055e109a23d537e9de43c09cca21eb762", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-5.1.0.tgz", "fileCount": 18, "unpackedSize": 379971, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGKHQYKR8EoyskwDDPfLWnWgIk4BQ16l2/ef5Ovne7aQIhAPyKrR8bi2u/EX6khmyNj3s/Pzbu95hnnXThfnfCVc5c"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwULpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ8A/9E/pwiR1N5T91twxyYGVlvgf0n6SNZeqPDFJwDbZyC1mMfn2S\r\n2dpjMShLAGD7ZmG3NH7RpxqNHc6onnOJNIywIB8MH5lI+cdTMm4bfExJo/cs\r\n0S6DR6F79DElW+K6pQX2z6RrFwgFOZci1XaqF3ouXMKyytjgR4L+V6NFNAv+\r\nurynpdjRrwh88t9HArQz6WRQLrmv5DPcuhWLEGYq9xk+tD0gwMaLABysNxFQ\r\nbzpKPLMDCjwE00cAtkglUzEJaaiLS9i/JHUe+z8hN2Qkcqj5oYtVG2CM0Xj3\r\nSgEMXAjriXKvihExvavb2W8hWcEHGcHHu5dWjO8XoLLRb2NpWGRNUqG/q6ot\r\n93W0wuv2LizPGAz45TyxL0zGlKDTpKSOaiWySwhYOpATjyt1+Ds34fzPsOU9\r\n/oF5cvhie2kt3oj8XHZYGRRiGdnuYWBNLrj4lFuVb7F44w8TRMDlsHTDRZQX\r\nS39bVtG7qyoBZxA1JjzRCg24b4poJ6Bv1iPi5zH5/PcmdiiVoKnd13OdKu+W\r\nt+nHA933ZQmIGcieC6g2TylFWk7fHDMvqIg4oIm8cgwd6h2kVCGzWDcgX+Ty\r\nX8wpzDDYsPuJE1mBvbn8blROmH0F+P2ND5lyFGHsRyABAthuCJfDN4Q8dwml\r\noxaL0MboMKzS3D0SXUX1rXjtdH8l/ySXo8Q=\r\n=I+q+\r\n-----END PGP SIGNATURE-----\r\n", "size": 58175}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_5.1.0_1673609961479_0.7021857707619277"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-13T11:39:21.661Z", "publish_time": 1673609961661}, "6.0.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "6.0.0", "support": true, "gitHead": "c51d6b321fa4fbbc134630ce0c72bdf9a2664b2d", "_id": "node-addon-api@6.0.0", "_nodeVersion": "19.5.0", "_npmVersion": "9.3.1", "dist": {"integrity": "sha512-GyHvgPvUXBvAkXa0YvYnhilSB1A+FRYMpIVggKzPZqdaZfevZOuzfWzyvgzOwRLHBeo/MMswmJFsrNF4Nw1pmA==", "shasum": "cfb3574e6df708ff71a30db6c4762d9e06e11c27", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-6.0.0.tgz", "fileCount": 18, "unpackedSize": 379710, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/kSRgJ1SrAfUbdHkOhW25MkzDS0oBWmM3cLIlukmx/gIgeelRJg4C23MHRWmHEm3ladBYAk35593nWmHpdlsfpz8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5n1zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAPw/7BUC0mETAmvJUxEVjvJAqqr7mUYTX1RlgVwtKMEtWDwmJcGUe\r\nY4K6eSp10AX6QvIWwURVhzY//ULQMqK/lX7njPuUyxSMpp0Wh4TCx16EhCL/\r\nLhn5KGEcsOe3OaH+2aq21NBP2tTMbPE8mQja8VHb9LyuZgQE3XOcxXIfTGBu\r\nclSkK8LNcOvCh3Z+pP7doCOXBm/5BgvaymUwQ8q0+hAedJj5KXhZBaG6rGkp\r\npz/11Y/qWT+1C+NigojYl41DQlWT8+BL3z20CbiGqfWthb7zRVoiHlHI1A+o\r\n6hAWM+PSFmzHlXP+30NWKBr8+J8B5J4jDq7cOurAlfvghwXdKWcKXy5i6Pdw\r\nYPSgd2XgFHH8oYBQTNfGbkyH1CnNK8iwpJBcRAFH+iG6IylyeRaz+7AjJyIU\r\nWyAxLCIYJQe4vFJqdzHTn6P665eOq9ipK4h8wDjdjJHVfWMSVg976BOGehtg\r\nIivih065Jun4ZNK9He3j1HcanlNWXteXmaHvztL95PtGG6JCSq7ITnH/Ax80\r\n19P6H0EEjElaw8ZFTJZJGtN1Jfwca5NJCiOBMdrNtNQpTN1j/BcV20I78OOA\r\n9Z6JT3v/hIhx/np9Y6Ri4EG9DtlOPomzP323Ayw7pFZjMFqXQtom7TuMw6t+\r\n86GQ0nycaFuSH17F1kwy8Ptc99xkyIqp9A8=\r\n=O8mV\r\n-----END PGP SIGNATURE-----\r\n", "size": 58157}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_6.0.0_1676049779732_0.07094269885607285"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-10T17:22:59.896Z", "publish_time": 1676049779896}, "6.1.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "6.1.0", "support": true, "gitHead": "0e34f228399b7ef7b2223e8320d8260e6a707e03", "_id": "node-addon-api@6.1.0", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-+eawOlIgy680F0kBzPUNFhMZGtJ1YmqM6l4+Crf4IkImjYrO/mqPwRMh352g23uIaQKFItcQ64I7KMaJxHgAVA==", "shasum": "ac8470034e58e67d0c6f1204a18ae6995d9c0d76", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-6.1.0.tgz", "fileCount": 18, "unpackedSize": 393771, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCepwXNKYPG1xYGIJPpVCIhf4Zf8QotuNkY7DIeYPhFaQIhALIJjWmIp0YD/jysdP9Qj9yyUURKJ5yaT69BqvJ1b4IV"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQUvSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreWBAAgtePyzNCext5Hp+0q3oiMEUBtG5EHvOR1KGo8Mfo6VdNs2io\r\n/rtsPuPPp5867qy+Vj+30y8UPcBdVVIzd6tFs7W653Dr7RWDkymq+GOwDVUw\r\njXC2P5y6/w6PtHWdjEUcSe8A5Oc/gZX5onioOKwxJq9Mfil/ngUaOgR1Hq1r\r\nTQPrhi8wDpgcy92VDYEFduLZPTMSu01dWkzFuPVy41GS3lQjSB2SW6xwECll\r\n5ThpFuirFC4nvhMlBjHcoWlUYJXjII27iNIjvU72EyfCkQeWd58vvzBaT7v0\r\nwrGUqQoUt1gxuayYjytQ5JwCudz8pGIALw2uEAK3xCVs0mJPX2Jwyd1/ocAo\r\n2bNIDCOZrIq+uDQ/naHYqMuNv3Nu/hDM32J1sCxh0Aq0rQvR3GNUYsYvJqTH\r\nUdGQ+dpOE8K4sOpCQ7HAu5WTfcAByEhqmyVQXs3Rn0oyt4ikebttW6jujc2z\r\nkKu4rydf9dEL7OXkOBtAjM03pSY/JJZX7J6IJFacxRJnTNapNviZBYtrzmqL\r\n7KZgVT2zCKNyhPkwUpYIOvZjRSh05FsKhnnv9KkM2sZNJyZvfyrs+Alk8yBd\r\n5wGGjXR8ikzSqsmxTv/bwmKIBM6TROsRe2s1oW62ay0GA53FJJ2XQRdOdTzR\r\nWdDKgeFY+wiLv0ABXyr3zBQo6r4s+xZaRgs=\r\n=O4QY\r\n-----END PGP SIGNATURE-----\r\n", "size": 59623}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_6.1.0_1682000850093_0.23091534096277933"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-20T14:27:30.305Z", "publish_time": 1682000850305}, "7.0.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^9.0.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "7.0.0", "support": true, "gitHead": "39a25bf27788ff7a7ea5c64978c4dcd1e7b9d80d", "_id": "node-addon-api@7.0.0", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-vgbBJTS4m5/KkE16t5Ly0WW9hz46swAstv0hYYwMtbG7AznRhNyfLRe8HZAiWIpcHzoO7HxhLuBQj9rJ/Ho0ZA==", "shasum": "8136add2f510997b3b94814f4af1cce0b0e3962e", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-7.0.0.tgz", "fileCount": 18, "unpackedSize": 394960, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVQCkEY5AOexkrn1XlJwa6qtfPtdIMNWCVhn4XWhkvBgIhAI7SPi0z2r74j/sM2Yu6WXSacMjaeIndKhQ/7l8baLwV"}], "size": 59884}, "_npmUser": {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_7.0.0_1686837348497_0.048134329676088994"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-15T13:55:48.692Z", "publish_time": 1686837348692, "_source_registry_name": "default"}, "7.1.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "7.1.0", "support": true, "engines": {"node": "^16 || ^18 || >= 20"}, "_id": "node-addon-api@7.1.0", "gitHead": "185dda1b2f32a3e9f9aba612bf7cbed3ad7196d4", "_nodeVersion": "18.19.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-mNcltoe1R8o7STTegSOHdnJNN7s5EUvhoS7ShnTHDyOSd+8H+UdWODq6qSv67PjC8Zc5JRT8+oLAMCr0SIXw7g==", "shasum": "71f609369379c08e251c558527a107107b5e0fdb", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-7.1.0.tgz", "fileCount": 19, "unpackedSize": 396366, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA0OtSxTKEQBE8iO0FUCCRS4E2NqBSSgMgCpIQoI3OHgAiEA3lZ7t/VZJl+x1M/++iLHBPWd3G4eGxkihSuvsOb+jew="}], "size": 60055}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_7.1.0_1705653985118_0.47525771598566946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-19T08:46:25.251Z", "publish_time": 1705653985251, "_source_registry_name": "default"}, "8.0.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "8.0.0", "support": true, "engines": {"node": "^18 || ^20 || >= 21"}, "_id": "node-addon-api@8.0.0", "gitHead": "d1ba547e91b192152bfc314ab85436de1538b4ec", "_nodeVersion": "18.19.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-ipO7rsHEBqa9STO5C5T10fj732ml+5kLN1cAG8/jdHd56ldQeGj3Q7+scUS+VHK/qy1zLEwC4wMK5+yM0btPvw==", "shasum": "5453b7ad59dd040d12e0f1a97a6fa1c765c5c9d2", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.0.0.tgz", "fileCount": 19, "unpackedSize": 387097, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGNFKrMIWHvXgjGAd9zAmgvVh9wM6vMlU9rJ1m6rSSApAiBkvsMgjix15XaejKZ8gVBECfjMAIh4n4FY/wneT0iSfA=="}], "size": 57115}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_8.0.0_1709622148067_0.23918645999912647"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-05T07:02:28.228Z", "publish_time": 1709622148228, "_source_registry_name": "default"}, "8.1.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "semver": "^7.6.0"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "create-coverage": "npm test --coverage", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "8.1.0", "support": true, "engines": {"node": "^18 || ^20 || >= 21"}, "_id": "node-addon-api@8.1.0", "gitHead": "bc5acef9dd5298cbbcabd5c01c9590ada683951d", "_nodeVersion": "20.15.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-yBY+qqWSv3dWKGODD6OGE6GnTX7Q2r+4+DfpqxHSHh8x0B4EKP9+wVGLS6U/AM1vxSNNmUEuIV5EGhYwPpfOwQ==", "shasum": "55a573685dd4bd053f189cffa4e6332d2b1f1645", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.1.0.tgz", "fileCount": 19, "unpackedSize": 391869, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7o+YSYz7Nj0bFdstH1ALmtUb9NYLQ1x/TyggYiwbMEQIgVQlk21YbROhD7cM0l7ufFIy0e07LiRyDO1I6XsqOA7A="}], "size": 58296}, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_8.1.0_1720429074135_0.5276460310288342"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-08T08:57:54.301Z", "publish_time": 1720429074301, "_source_registry_name": "default"}, "7.1.1": {"name": "node-addon-api", "version": "7.1.1", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@7.1.1", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/wanlu", "name": "wanlu wang"}, {"url": "https://github.com/chearon", "name": "<PERSON>"}, {"url": "https://github.com/MarxJiao", "name": "<PERSON>"}, {"url": "https://github.com/tuhalf", "name": "Ömer AKGÜL"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "1aba6693b0f255258a049d621329329322aad558", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-7.1.1.tgz", "fileCount": 19, "integrity": "sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==", "signatures": [{"sig": "MEUCIEXMQqSmOJMzuSxQGyLOE1l0PNqOV5eFnpxIY/DmOnkaAiEA+B6rRlodLNAxw3pcP+UXGdVZrKMHZnizga+70Z2z28I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396312, "size": 60031}, "main": "index.js", "gitHead": "5e96a5460f2538a06f87e592d6aa349a7f08b04a", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "10.3.0", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "22.2.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^11.1.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_7.1.1_1720779307443_0.7401679488879174", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-07-12T10:15:07.595Z", "publish_time": 1720779307595, "_source_registry_name": "default"}, "8.2.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "semver": "^7.6.0"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "create-coverage": "npm test --coverage", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "8.2.0", "support": true, "engines": {"node": "^18 || ^20 || >= 21"}, "_id": "node-addon-api@8.2.0", "gitHead": "5b1a57d9e3c8b1c496c63bf31514f7ce4a7c1a8b", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-qnyuI2ROiCkye42n9Tj5aX1ns7rzj6n7zW1XReSnLSL9v/vbLeR6fJq6PU27YU/ICfYw6W7Ouk/N7cysWu/hlw==", "shasum": "ad92cacecc86834304053fd0089f718b72ff4e65", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.2.0.tgz", "fileCount": 19, "unpackedSize": 400952, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBUr5oFs9TT/ieYjOqqDAdSRA4pLrzz6C2uoHYpnvbeNAiBI3bhqf/yfe2KyzUpqPHQM3dsO1kNVuH37gCyesrHRlw=="}], "size": 59775}, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_8.2.0_1727706616679_0.8751262390896011"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-30T14:30:16.924Z", "publish_time": 1727706616924, "_source_registry_name": "default"}, "8.2.1": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "semver": "^7.6.0"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "create-coverage": "npm test --coverage", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "8.2.1", "support": true, "engines": {"node": "^18 || ^20 || >= 21"}, "_id": "node-addon-api@8.2.1", "gitHead": "49652bd698a1285a857c3d8a675c31c4c016dd30", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-vmEOvxwiH8tlOcv4SyE8RH34rI5/nWVaigUeAUPawC6f0+HoDthwI0vkMu4tbtsZrXq6QXFfrkhjofzKEs5tpA==", "shasum": "43a993f110b88e22ba48bcd65e16b92165a6b002", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.2.1.tgz", "fileCount": 19, "unpackedSize": 402038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfvwrjh9pz8BZLCZ8XCWeM0THiQGS1DtQkgZKsi+/23QIhALB8lg9ngWe0dzFauiSnqh60pOi5+iA1gH1ntphzizfx"}], "size": 59975}, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_8.2.1_1728659243786_0.42222731566238214"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-11T15:07:24.071Z", "publish_time": 1728659244071, "_source_registry_name": "default"}, "8.2.2": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "semver": "^7.6.0"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "create-coverage": "npm test --coverage", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "8.2.2", "support": true, "engines": {"node": "^18 || ^20 || >= 21"}, "_id": "node-addon-api@8.2.2", "gitHead": "264bc1b94fea7c05fcbed3eac47b205c61b199ac", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-9emqXAKhVoNrQ792nLI/wpzPpJ/bj/YXxW0CvAau1+RdGBcCRF1Dmz7719zgVsQNrzHl9Tzn3ImZ4qWFarWL0A==", "shasum": "3658f78d04d260aa95931d3bbc45f22ce433b821", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.2.2.tgz", "fileCount": 19, "unpackedSize": 402588, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-addon-api@8.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZtkwrazen7HDzwEReYhJ61TGdX3CnR7vQqYkNx0+HwgIgK1Cm8jUOcTjKFDYuffVmVg8U011TKb9g7oA9sjK5cHA="}], "size": 60125}, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_8.2.2_1731072259348_0.002851102518716342"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-08T13:24:19.573Z", "publish_time": 1731072259573, "_source_registry_name": "default"}, "8.3.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^9.13.0", "fs-extra": "^11.1.1", "neostandard": "^0.11.7", "pre-commit": "^1.2.2", "semver": "^7.6.0"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "create-coverage": "npm test --coverage", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "eslint && node tools/clang-format", "lint:fix": "eslint --fix && node tools/clang-format --fix"}, "pre-commit": "lint", "version": "8.3.0", "support": true, "engines": {"node": "^18 || ^20 || >= 21"}, "_id": "node-addon-api@8.3.0", "gitHead": "398eec6b0b396fa5fae7d1a8bcac9f01758878ae", "_nodeVersion": "22.11.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-8VOpLHFrOQlAH+qA0ZzuGRlALRA6/LVh8QJldbrC4DY0hXoMP0l4Acq8TzFC018HztWiRqyCEj2aTWY2UvnJUg==", "shasum": "ec3763f18befc1cdf66d11e157ce44d5eddc0603", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.3.0.tgz", "fileCount": 18, "unpackedSize": 403298, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-addon-api@8.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQu9cjCj+utwmBSGDUVR0AMcuy9SsrVUVd+DKOeJ/jkgIhANXVNV2V8le1jot7Xt1Yu0ElsQULDEKJif3HMK5VGrM6"}], "size": 60028}, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_8.3.0_1732897180679_0.7989046002536839"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-29T16:19:40.909Z", "publish_time": 1732897180909, "_source_registry_name": "default"}, "8.3.1": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^9.13.0", "fs-extra": "^11.1.1", "neostandard": "^0.12.0", "pre-commit": "^1.2.2", "semver": "^7.6.0"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "create-coverage": "npm test --coverage", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "eslint && node tools/clang-format", "lint:fix": "eslint --fix && node tools/clang-format --fix"}, "pre-commit": "lint", "version": "8.3.1", "support": true, "engines": {"node": "^18 || ^20 || >= 21"}, "_id": "node-addon-api@8.3.1", "gitHead": "723f094374bc3ae9bd94d9bcc4b44604c70a5425", "_nodeVersion": "22.13.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-lytcDEdxKjGJPTLEfW4mYMigRezMlyJY8W4wxJK8zE533Jlb8L8dRuObJFWg2P+AuOIxoCgKF+2Oq4d4Zd0OUA==", "shasum": "53bc8a4f8dbde3de787b9828059da94ba9fd4eed", "tarball": "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-8.3.1.tgz", "fileCount": 18, "unpackedSize": 403298, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-addon-api@8.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDJkhXQSE38S4xFjHBpU2vz59Yha4yZQKcaL5C4QDo26AIhANJ6VkS7cWjJT32sLuKggbzgucUMlPbKOwVtuBAdFLAx"}], "size": 60027}, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/node-addon-api_8.3.1_1740154389813_0.24275203080377095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-21T16:13:10.012Z", "publish_time": 1740154390012, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "_source_registry_name": "default"}