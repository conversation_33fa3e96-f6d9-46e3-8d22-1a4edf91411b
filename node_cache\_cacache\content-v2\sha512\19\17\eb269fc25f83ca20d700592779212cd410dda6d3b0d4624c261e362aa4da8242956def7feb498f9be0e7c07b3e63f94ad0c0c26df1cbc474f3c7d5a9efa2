{"_attachments": {}, "_id": "read-cmd-shim", "_rev": "2915-61f14a4c963ca28f5ee47196", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "dist-tags": {"latest": "5.0.0"}, "license": "ISC", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "name": "read-cmd-shim", "readme": "# read-cmd-shim\n\nFigure out what a [`cmd-shim`](https://github.com/ForbesLindesay/cmd-shim)\nis pointing at.  This acts as the equivalent of\n[`fs.readlink`](https://nodejs.org/api/fs.html#fs_fs_readlink_path_callback).\n\n### Usage\n\n```\nconst readCmdShim = require('read-cmd-shim')\n\nreadCmdShim('/path/to/shim.cmd').then(destination => {\n  …\n})\n\nconst destination = readCmdShim.sync('/path/to/shim.cmd')\n```\n\n### readCmdShim(path) -> Promise\n\nReads the `cmd-shim` located at `path` and resolves with the _relative_\npath that the shim points at. Consider this as roughly the equivalent of\n`fs.readlink`.\n\nThis can read both `.cmd` style that are run by the Windows Command Prompt\nand Powershell, and the kind without any extension that are used by Cygwin.\n\nThis can return errors that `fs.readFile` returns, except that they'll\ninclude a stack trace from where `readCmdShim` was called.  Plus it can\nreturn a special `ENOTASHIM` exception, when it can't find a cmd-shim in the\nfile referenced by `path`.  This should only happen if you pass in a\nnon-command shim.\n\n### readCmdShim.sync(path)\n\nSame as above but synchronous. Errors are thrown.\n", "time": {"created": "2022-01-26T13:19:08.768Z", "modified": "2025-05-14T20:10:26.656Z", "2.0.0": "2020-01-27T07:42:54.965Z", "1.0.5": "2019-11-05T19:18:53.758Z", "1.0.4": "2019-08-20T19:46:05.226Z", "1.0.3": "2019-08-14T19:42:29.904Z", "1.0.2": "2019-08-14T19:11:05.273Z", "1.0.1": "2015-09-09T21:48:26.193Z", "1.0.0": "2015-09-09T20:14:50.889Z", "3.0.0": "2022-04-05T16:39:22.059Z", "3.0.1": "2022-08-16T17:15:10.594Z", "4.0.0": "2022-10-14T05:23:02.527Z", "5.0.0": "2024-09-24T19:23:22.743Z"}, "versions": {"2.0.0": {"name": "read-cmd-shim", "version": "2.0.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "index.js", "devDependencies": {"cmd-shim": "^4.0.0", "rimraf": "^3.0.0", "tap": "^14.10.6"}, "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "test": "tap"}, "tap": {"check-coverage": true}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "license": "ISC", "homepage": "https://github.com/npm/read-cmd-shim#readme", "gitHead": "8280d613d917054384fa3b4cb47afee3cbfe412b", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "_id": "read-cmd-shim@2.0.0", "_nodeVersion": "13.4.0", "_npmVersion": "6.13.6", "dist": {"shasum": "4a50a71d6f0965364938e9038476f7eede3928d9", "size": 2201, "noattachment": false, "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-2.0.0.tgz", "integrity": "sha512-HJpV9bQpkl6KwjxlJcBoqu9Ba0PQg8TqSNIOrulGt54a0uup0HtevreFHzYzkm0lpnleRdNBzXznKrgxglEHQw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/read-cmd-shim_2.0.0_1580110974833_0.7312494442755049"}, "_hasShrinkwrap": false, "publish_time": 1580110974965, "_cnpm_publish_time": 1580110974965, "_cnpmcore_publish_time": "2021-12-13T18:16:20.553Z"}, "1.0.5": {"name": "read-cmd-shim", "version": "1.0.5", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "index.js", "dependencies": {"graceful-fs": "^4.1.2"}, "devDependencies": {"cmd-shim": "^3.0.0", "rimraf": "^2.4.3", "standard": "^5.2.2", "tap": "^12.7.0"}, "scripts": {"pretest": "standard", "test": "tap test/*.js --100"}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "homepage": "https://github.com/npm/read-cmd-shim#readme", "gitHead": "4f69e6131b9cb460fbec4b81bd510228ded7d082", "_id": "read-cmd-shim@1.0.5", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.1", "dist": {"shasum": "87e43eba50098ba5a32d0ceb583ab8e43b961c16", "size": 2117, "noattachment": false, "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-1.0.5.tgz", "integrity": "sha512-v5yCqQ/7okKoZZkBQUAfTsQ3sVJtXdNfbPnI5cceppoxEVLYA3k+VtV2omkeo8MS94JCy4fSiUwlRBAwCVRPUA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/read-cmd-shim_1.0.5_1572981533661_0.7967034134792066"}, "_hasShrinkwrap": false, "publish_time": 1572981533758, "_cnpm_publish_time": 1572981533758, "_cnpmcore_publish_time": "2021-12-13T18:16:20.838Z"}, "1.0.4": {"name": "read-cmd-shim", "version": "1.0.4", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "index.js", "dependencies": {"graceful-fs": "^4.1.2"}, "devDependencies": {"cmd-shim": "^3.0.0", "rimraf": "^2.4.3", "standard": "^5.2.2", "tap": "^12.7.0"}, "scripts": {"pretest": "standard", "test": "tap test/*.js --100"}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "homepage": "https://github.com/npm/read-cmd-shim#readme", "gitHead": "9679b59d8fe2103ed2736862ba4b28742bfea3d7", "_id": "read-cmd-shim@1.0.4", "_nodeVersion": "12.6.0", "_npmVersion": "6.11.0", "dist": {"shasum": "b4a53d43376211b45243f0072b6e603a8e37640d", "size": 2084, "noattachment": false, "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-1.0.4.tgz", "integrity": "sha512-Pqpl3qJ/QdOIjRYA0q5DND/gLvGOfpIz/fYVDGYpOXfW/lFrIttmLsBnd6IkyK10+JHU9zhsaudfvrQTBB9YFQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/read-cmd-shim_1.0.4_1566330365094_0.544572457369793"}, "_hasShrinkwrap": false, "publish_time": 1566330365226, "_cnpm_publish_time": 1566330365226, "_cnpmcore_publish_time": "2021-12-13T18:16:21.267Z"}, "1.0.3": {"name": "read-cmd-shim", "version": "1.0.3", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "index.js", "dependencies": {"graceful-fs": "^4.1.2"}, "devDependencies": {"cmd-shim": "^3.0.0", "rimraf": "^2.4.3", "standard": "^5.2.2", "tap": "^12.7.0"}, "scripts": {"pretest": "standard", "test": "tap test/*.js --100"}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "homepage": "https://github.com/npm/read-cmd-shim#readme", "gitHead": "aa9e076fcfab9e4159da4833f0b257dfb4471ea6", "_id": "read-cmd-shim@1.0.3", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.3", "dist": {"shasum": "b246608c8e76e332a99be7811c096a4baf60015a", "size": 4428, "noattachment": false, "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-1.0.3.tgz", "integrity": "sha512-HUHb2imlZ8xBJjiZZRx0Ag9JfZ3jxQRfORMQXWCDeHE6PCCnpQrMq6LhyNqEPnMXhMDDIyq/BK7pBbhNy9zDDA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/read-cmd-shim_1.0.3_1565811749782_0.9952839009438261"}, "_hasShrinkwrap": false, "publish_time": 1565811749904, "_cnpm_publish_time": 1565811749904, "_cnpmcore_publish_time": "2021-12-13T18:16:21.484Z"}, "1.0.2": {"name": "read-cmd-shim", "version": "1.0.2", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "index.js", "dependencies": {"graceful-fs": "^4.1.2"}, "devDependencies": {"cmd-shim": "^2.1.0", "rimraf": "^2.4.3", "standard": "^5.2.2", "tap": "^12.7.0"}, "scripts": {"pretest": "standard", "test": "tap test/*.js --100"}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "homepage": "https://github.com/npm/read-cmd-shim#readme", "gitHead": "a4c4bb0a8f2d681af44ed651f51606403e12f09d", "_id": "read-cmd-shim@1.0.2", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.3", "dist": {"shasum": "75a8324a7f1e0b2a830fbd1c3391a25fc657cc54", "size": 4421, "noattachment": false, "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-1.0.2.tgz", "integrity": "sha512-7c52L8k5B4Rag4EmfgwU//r/2XGGZqDz7yCHBPX3Tt1fAyIyG1TkRsRZxQhCWvAwQBruu6Wx/P3ptOVqKgfcLA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/read-cmd-shim_1.0.2_1565809865114_0.6581198524062861"}, "_hasShrinkwrap": false, "publish_time": 1565809865273, "_cnpm_publish_time": 1565809865273, "_cnpmcore_publish_time": "2021-12-13T18:16:21.736Z"}, "1.0.1": {"name": "read-cmd-shim", "version": "1.0.1", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "index.js", "dependencies": {"graceful-fs": "^4.1.2"}, "devDependencies": {"cmd-shim": "^2.0.1", "rimraf": "^2.4.3", "standard": "^5.2.2", "tap": "^1.4.1"}, "scripts": {"test": "standard && tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "homepage": "https://github.com/npm/read-cmd-shim#readme", "gitHead": "7c50879bf49743a1c69f9d7f0ba1638fc46bb40c", "_id": "read-cmd-shim@1.0.1", "_shasum": "2d5d157786a37c055d22077c32c53f8329e91c7b", "_from": ".", "_npmVersion": "3.3.0", "_nodeVersion": "3.1.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "2d5d157786a37c055d22077c32c53f8329e91c7b", "size": 2239, "noattachment": false, "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-1.0.1.tgz", "integrity": "sha512-ncStF2gqrdDvUwvpTnf+lQ2FG8W47ejCeh6R2Pf3Ze0iTNnfLXyilYVNTJNibDcWtzoNixO2rMfOf/3kAcaH5A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1441835306193, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441835306193, "_cnpmcore_publish_time": "2021-12-13T18:16:21.962Z"}, "1.0.0": {"name": "read-cmd-shim", "version": "1.0.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "index.js", "dependencies": {"graceful-fs": "^4.1.2"}, "devDependencies": {"cmd-shim": "^2.0.1", "rimraf": "^2.4.3", "standard": "^5.2.2", "tap": "^1.4.1"}, "scripts": {"test": "standard && tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "homepage": "https://github.com/npm/read-cmd-shim#readme", "gitHead": "094627616b319c37456abe9f2bb2ce513966eaad", "_id": "read-cmd-shim@1.0.0", "_shasum": "00c4abc7b92d623caeb7ed4bbf580bf5e43e9a25", "_from": ".", "_npmVersion": "3.3.0", "_nodeVersion": "3.1.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "00c4abc7b92d623caeb7ed4bbf580bf5e43e9a25", "size": 1895, "noattachment": false, "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-1.0.0.tgz", "integrity": "sha512-kGfykCflcXdupBfzS1OLlRAWADRDv7Ht5S8YdIFVRuV2VKUVObbb1L8IfXezWoqwZBpvNCTS2SJYjuDWgZ3Ygg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1441829690889, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441829690889, "_cnpmcore_publish_time": "2021-12-13T18:16:22.151Z"}, "3.0.0": {"name": "read-cmd-shim", "version": "3.0.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "lib/index.js", "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "cmd-shim": "^4.0.0", "rimraf": "^3.0.0", "tap": "^16.0.1"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "tap": {"check-coverage": true}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "license": "ISC", "homepage": "https://github.com/npm/read-cmd-shim#readme", "author": {"name": "GitHub Inc."}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}, "gitHead": "bb6ab3b06c4b953df5959e163f214e97e72bd344", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "_id": "read-cmd-shim@3.0.0", "_nodeVersion": "16.14.2", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-KQDVjGqhZk92PPNRj9ZEXEuqg8bUobSKRw+q0YQ3TKI5xkce7bUJobL4Z/OtiEbAAv70yEpYIXp4iQ9L8oPVog==", "shasum": "62b8c638225c61e6cc607f8f4b779f3b8238f155", "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-3.0.0.tgz", "fileCount": 4, "unpackedSize": 5231, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHr9rvkZfLVDysChouJrfO9K8nNfuoBz2UTjPTTT86zzAiAIf+nj1xsnCS4LQmOTNHrgopB0x7fWslObjRiYtH5BFw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTHC6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqG7w//emPh4fELc+Ak8FS1EcsNKk6CZ6BF49RIvwTdUdvYWXbAtL6Y\r\nOLyIo40JydRX9VZXQkWavXxA4xZVvlU/Cn6CcdodAq4qAamkJgUR7PVzaWuB\r\nzVXAd0rGvVIbqOrgk2lowPOKcotfOYOxOIzrkSs7mSWQIlSTZwbs1nmmV+Uk\r\nffj/9OIrcHvNgkfmOzoVJhTLzqURgqJFrNe/8DipXPk5D0BL0Q5MIa6PPJms\r\nx/wNK9Zgoa2fVh/yxYn/wB98IqBkm/tWAQ/saPrAQgbUDrxD8EVLonmBPqmx\r\nmzKP2L9bVilHDdSz5UZLhDDhVkscnEMmpebiy/b6qlpqe3vn4lvK5pCn5vjV\r\nJI6ifr/kqAn7nLS/Hyll0gk1FmYmK7rvqC+OoTcoepBA8719uyEHe+REAJqx\r\nGIzM6fESX+MF3KP0MXztbV2RWvXeDZbl6cjUKKzEiQJkWSxJcjoiAJuMX3G2\r\nnxP1xotaFI5EH/tIMqOyc4XBqeZ7zTOHtpxHCNtKBuLLhs5upAdTTSOrc2/o\r\ncNnsScZxoknubzbl7RZjgtPwMkkOMRBRZn8CnEc++PNAlesRXnk3Ln7J7wck\r\n/+sLBUf678M3bZYO2kM4bMpMKFFBXksFuaPCS+h/ysvl/oApCetF4KIiKJ7b\r\nkVhLO+OxjJRPsUZrX+a2jooVjczAS/S9Bso=\r\n=CZqR\r\n-----END PGP SIGNATURE-----\r\n", "size": 2422}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/read-cmd-shim_3.0.0_1649176761903_0.8609925005031223"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-05T19:01:38.537Z"}, "3.0.1": {"name": "read-cmd-shim", "version": "3.0.1", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "lib/index.js", "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "cmd-shim": "^5.0.0", "rimraf": "^3.0.0", "tap": "^16.0.1"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "tap": {"check-coverage": true}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "license": "ISC", "homepage": "https://github.com/npm/read-cmd-shim#readme", "author": {"name": "GitHub Inc."}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}, "gitHead": "5933ebef1036d2a4692a9f570063399b7225d73c", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "_id": "read-cmd-shim@3.0.1", "_nodeVersion": "18.6.0", "_npmVersion": "8.17.0", "dist": {"integrity": "sha512-kEmDUoYf/CDy8yZbLTmhB1X9kkjf9Q80PCNsDMb7ufrGd6zZSQA1+UyjrO+pZm5K/S4OXCWJeiIt1JA8kAsa6g==", "shasum": "868c235ec59d1de2db69e11aec885bc095aea087", "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-3.0.1.tgz", "fileCount": 4, "unpackedSize": 5214, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiSLiZYxfd7FPjj/lrc90/HuzYd7hNHBH8SMEyu8FDNQIgPCMZm3iSK/MH47CadAsS7Dub81Y98Zr/9f+qdhh0mB8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+9CeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3xxAAhwG4zC+kXK51B6pa4oC7tyJk99yK+Ha9423dgom4JA/vgJiT\r\nwvq/XAewqvOBAHufduV6hS/CaHWyXfLJAhTkhxcLFTJi/JcuagAl5Kc6rK7H\r\nE8dhfVSK0rNVle1AmbrwSO/ySYYcB20YiclhGxUk9r1xY4jH73phVLFaMQVW\r\nPitVZ41x/bm2vvzvkMXu732eRVge5VLmqp+NDlQerkdm6ruMnCSjqIp/QhdX\r\nkWwB0l8vzUpAbfM/Fbdi+V5SOLprH6dAqFmZK6wjPainr9kYYzMw/fWJG2PO\r\nKpkvNJFRgO5hQugBiLKYX/jfO4ZC3/nuvf7v7sZfp7pzKuDe6i/MMfwfihx0\r\neETBEWoBtkob7yJABT9dpGgi/0tVYYMV/EoRzn2K9BqSUj8K9bdjGcjXG54D\r\nvKKzgEUPWRPNqPsB+pkKlGq7Ouzd7INSzDWA/AHcIncIQoB8uN3FnE0bQmq6\r\nihC+MItKNdrv73dSXbY2nSR7fd5/XXqGSboaGwgSGJqoTuxPrr2spquYCuVV\r\nIIWhCDt017xNaa1J8dXFy5qbPoCDOrnsuh2PvI3jUKvRWLxPthwZw2sjLZVu\r\ntrywkwYmmq6A0NJv2WTaAbJ6mgHs5Q2msSIm25nWfgw8lxGKlhVDU9khDruY\r\nz4P8i0POKxXooWU7Lk91rQxFgO98iBTwmQE=\r\n=Mrig\r\n-----END PGP SIGNATURE-----\r\n", "size": 2411}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/read-cmd-shim_3.0.1_1660670110301_0.33665384273513843"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-16T17:15:14.009Z"}, "4.0.0": {"name": "read-cmd-shim", "version": "4.0.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "lib/index.js", "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "4.5.1", "cmd-shim": "^5.0.0", "rimraf": "^3.0.0", "tap": "^16.0.1"}, "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "license": "ISC", "homepage": "https://github.com/npm/read-cmd-shim#readme", "author": {"name": "GitHub Inc."}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.5.1"}, "gitHead": "c038d3e89f5a400bd111bbf91428a1b18181aee8", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "_id": "read-cmd-shim@4.0.0", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-yILWifhaSEEytfXI76kB9xEEiG1AiozaCJZ83A87ytjRiN+jVibXjedjCRNjoZviinhG+4UkalO3mWTd8u5O0Q==", "shasum": "640a08b473a49043e394ae0c7a34dd822c73b9bb", "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-4.0.0.tgz", "fileCount": 4, "unpackedSize": 5163, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHzv6vehDkllXhhEaK1aXmFGe4HKJk4/Y6gfjFLM3zaSAiEAyQUykfPLoUUNRt236maz1iWiEAixrbEEBYQeA1PTYb4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPI2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3lw/8CxOTrKtRS0oqAFtoG5OZ8HzmWuAV0eyi/0/Dr1k4sCutPHnI\r\nYbIQ45UWX9rBH6wKwhQ4Ic65aN2pBHZi14xlIRmM1CFTw3FPFclzVvlFAS4w\r\nz0FTkv+PIFf8d0fr9+ziuA5tzm+GSg6oqGjj2VW2YlDa6+GJbTnsn2GqAgdu\r\nLFxS8t/6ocax1iCoipteSkiWQdp8uWSGvu9BfCZ1Z8dVwPqpIl8ouqjdglHJ\r\n1YUdgHiQ9nznwcu1lvFOGR8kb8lYR/2fHdRVE+IAjj6hHQ+l/4lLA4TRfG9K\r\nTr+dYaZLohaAf5Eyl9fWcVoLzaytCvbcE0tA0V7iR+Om+6UFehZ+sDQGY6re\r\n5rulrcG4GqYmtmLmsYt5c5mreTLnqI5xII6QqnieeUVv+hHYzpfFQvl5mbbW\r\nXyBJJse6zM1Zp50MkeMxZRFDLAtfYv7htnf/mRncIVdzSvvVYKVLoSEYX7kk\r\nTC0H1CdnMBZ/AsNm8W/5uxBvw3yvsdsNTQ7lJ0K0fbwih2MkgqR5DrYZPAOA\r\nifJyn+Ct7nkm/B7KfVZ477x57bs9/4nMI9SGMFY8/kFuAp6qBWdNapmeInRJ\r\nArBavMmFyXIVnWEVQbRuJsjTAxkp7RH7cgcNdpRgIw/sj3jIFuShOhqZCTci\r\n9ZHBxj7q7Xyr83i45jEqx3NsdPJ9VGnK89w=\r\n=t2/y\r\n-----END PGP SIGNATURE-----\r\n", "size": 2402}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/read-cmd-shim_4.0.0_1665724982357_0.24341209080620319"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-14T16:57:17.276Z"}, "5.0.0": {"name": "read-cmd-shim", "version": "5.0.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "main": "lib/index.js", "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "cmd-shim": "^7.0.0", "tap": "^16.0.1"}, "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "license": "ISC", "homepage": "https://github.com/npm/read-cmd-shim#readme", "author": {"name": "GitHub Inc."}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}, "_id": "read-cmd-shim@5.0.0", "gitHead": "3a9c4014da4b81556b070ad2e3ac82ac29bd3a36", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-SEbJV7tohp3DAAILbEMPXavBjAnMN0tVnh4+9G8ihV4Pq3HYF9h8QNez9zkJ1ILkv9G2BjdzwctznGZXgu/HGw==", "shasum": "6e5450492187a0749f6c80dcbef0debc1117acca", "tarball": "https://registry.npmmirror.com/read-cmd-shim/-/read-cmd-shim-5.0.0.tgz", "fileCount": 4, "unpackedSize": 5209, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/read-cmd-shim@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCm0fe7LH9fv39D+wrOCEHeA5dHm2SmljvUu65EJSKWFwIgLPrK2SPU99cK2n9Vu6bpT59JQlB+1fVSDXGXCCfH3/8="}], "size": 2439}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/read-cmd-shim_5.0.0_1727205802551_0.4202968054470553"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-24T19:23:22.743Z", "publish_time": 1727205802743, "_source_registry_name": "default"}}, "author": {"name": "GitHub Inc."}, "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "homepage": "https://github.com/npm/read-cmd-shim#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/read-cmd-shim.git"}, "_source_registry_name": "default"}