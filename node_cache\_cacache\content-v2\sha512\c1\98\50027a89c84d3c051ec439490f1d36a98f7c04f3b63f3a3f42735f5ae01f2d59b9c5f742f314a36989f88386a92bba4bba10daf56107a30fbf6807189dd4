{"_attachments": {}, "_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_rev": "2274-61f148acfbcaa28a75953124", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "description": "An incremental implementation of MurmurHash3", "dist-tags": {"latest": "0.1.4"}, "license": "MIT", "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readme": "iMurmurHash.JS\n==============\n\nAn incremental implementation of the MurmurHash3 (32-bit) hashing algorithm for JavaScript based on [<PERSON>'s implementation](https://github.com/garycourt/murmurhash-js).\n\nInstallation\n------------\n\nTo use iMurmurHash in the browser, [download the latest version](https://raw.github.com/jensyt/imurmurhash-js/master/imurmurhash.min.js) and include it as a script on your site.\n\n```html\n<script type=\"text/javascript\" src=\"/scripts/imurmurhash.min.js\"></script>\n<script>\n// Your code here, access iMurmurHash using the global object MurmurHash3\n</script>\n```\n\n---\n\nTo use iMurmurHash in Node.js, install the module using NPM:\n\n```bash\nnpm install imurmurhash\n```\n\nThen simply include it in your scripts:\n\n```javascript\nMurmurHash3 = require('imurmurhash');\n```\n\nQuick Example\n-------------\n\n```javascript\n// Create the initial hash\nvar hashState = MurmurHash3('string');\n\n// Incrementally add text\nhashState.hash('more strings');\nhashState.hash('even more strings');\n\n// All calls can be chained if desired\nhashState.hash('and').hash('some').hash('more');\n\n// Get a result\nhashState.result();\n// returns 0x29d3f1e3\n```\n\nFunctions\n---------\n\n### MurmurHash3 ([string], [seed])\nGet a hash state object, optionally initialized with the given _string_ and _seed_. _Seed_ must be a positive integer if provided. Calling this function without the `new` keyword will return a cached state object that has been reset. This is safe to use as long as the object is only used from a single thread and no other hashes are created while operating on this one. If this constraint cannot be met, you can use `new` to create a new state object. For example:\n\n```javascript\n// Use the cached object, calling the function again will return the same\n// object (but reset, so the current state would be lost)\nhashState = MurmurHash3();\n...\n\n// Create a new object that can be safely used however you wish. Calling the\n// function again will simply return a new state object, and no state loss\n// will occur, at the cost of creating more objects.\nhashState = new MurmurHash3();\n```\n\nBoth methods can be mixed however you like if you have different use cases.\n\n---\n\n### MurmurHash3.prototype.hash (string)\nIncrementally add a _string_ to the hash. This can be called as many times as you want for the hash state object, including after a calls to `result()`. Returns `this` so calls can be chained.\n\n---\n\n### MurmurHash3.prototype.result ()\nGet the result of the hash as a 32-bit positive integer. This performs the tail and finalizer portions of the algorithm, but does not store the result in the state object. This means that it is perfectly safe to get results and then continue adding strings via `hash`.\n\n```javascript\n// Do the whole string at once\nMurmurHash3('this is a test string').result();\n// 0x70529328\n\n// Do part of the string, get a result, then the other part\nvar m = MurmurHash3('this is a');\nm.result();\n// 0xbfc4f834\nm.hash(' test string').result();\n// 0x70529328 (same as above)\n```\n\n---\n\n### MurmurHash3.prototype.reset (seed)\nReset the state object for reuse, optionally using the given _seed_ (defaults to 0 like constructor). Returns `this` so calls can be chained.\n\n---\n\nLicense (MIT)\n-------------\nCopyright (c) 2013 Gary Court, Jens Taylor\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md", "time": {"created": "2022-01-26T13:12:12.714Z", "modified": "2023-07-28T03:42:47.436Z", "0.1.4": "2013-08-24T20:45:23.169Z", "0.1.3": "2013-08-02T14:10:27.116Z", "0.1.2": "2013-08-02T13:38:41.852Z", "0.1.1": "2013-08-02T13:33:50.247Z", "0.0.1": "2013-07-31T23:41:37.908Z"}, "versions": {"0.1.4": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.4", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readmeFilename": "README.md", "_id": "im<PERSON><PERSON><PERSON><PERSON><PERSON>@0.1.4", "dist": {"tarball": "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz", "shasum": "9218b9b2b928a238b13dc4fb6b6d576f231453ea", "size": 4312, "noattachment": false, "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377377123169, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377377123169, "_cnpmcore_publish_time": "2021-12-13T18:17:08.119Z"}, "0.1.3": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.3", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readmeFilename": "README.md", "_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@0.1.3", "dist": {"tarball": "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.3.tgz", "shasum": "306c9e3a1562fadae1761a778b82e815f5d8e6e4", "size": 4328, "noattachment": false, "integrity": "sha512-5yqpIIjRrfVtSGS/RbbjB3xA0xQeelX5m5TUccfDtbKmJqlY4l8Wa6fb41obsmcNYmHYpVtun3kv4K4r6dJJTQ=="}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375452627116, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375452627116, "_cnpmcore_publish_time": "2021-12-13T18:17:08.342Z"}, "0.1.2": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.2", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readmeFilename": "README.md", "_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@0.1.2", "dist": {"tarball": "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.2.tgz", "shasum": "34d7409511b41c1fbf2026829e27e6b59161618a", "size": 4384, "noattachment": false, "integrity": "sha512-5S4ayL97Nv+xz8lfLiFk4qBZdPX21Ng5BQlt5ebRya0QVi9FFJ03pI06Yh2r0F1Az/+q3pxcD126n1x6O5mHVA=="}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375450721852, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375450721852, "_cnpmcore_publish_time": "2021-12-13T18:17:08.537Z"}, "0.1.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.1", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readmeFilename": "README.md", "_id": "im<PERSON><PERSON><PERSON><PERSON><PERSON>@0.1.1", "dist": {"tarball": "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.1.tgz", "shasum": "a0bf1bd6a1556f2625159230d797ba3d5b622257", "size": 4381, "noattachment": false, "integrity": "sha512-lMnpsInz+a1oHlk1MTQEVxd3JHPobdYFPA4OnhLJVBuAzrZ5xFUswgTPreSupvySFCtVo8lzMP4Otlj86uJsew=="}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375450430247, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375450430247, "_cnpmcore_publish_time": "2021-12-13T18:17:08.839Z"}, "0.0.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readmeFilename": "README.md", "_id": "im<PERSON><PERSON><PERSON><PERSON><PERSON>@0.0.1", "dist": {"tarball": "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.0.1.tgz", "shasum": "951d3f9ce9393309aa1bf6408f6c7f00f2d9e5c9", "size": 4377, "noattachment": false, "integrity": "sha512-GfrN68aWqzZ7szLpD3R13ZLfPWrsfC/t2PL8pUfIh1V4tMGmccsI91A/Akpbtmo5xPtVeyykAeryRamQLeEW/g=="}, "_from": "imurmurhash-js/", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1375314097908, "_hasShrinkwrap": false, "_cnpm_publish_time": 1375314097908, "_cnpmcore_publish_time": "2021-12-13T18:17:09.093Z"}}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "homepage": "https://github.com/jensyt/imurmurhash-js", "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "_source_registry_name": "default"}