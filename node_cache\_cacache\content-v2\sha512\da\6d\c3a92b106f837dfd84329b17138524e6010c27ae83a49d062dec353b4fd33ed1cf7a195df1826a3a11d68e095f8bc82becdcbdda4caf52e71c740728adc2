{"_attachments": {}, "_id": "lodash.isinteger", "_rev": "2937-61f14a5623990e8a812f9d04", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "description": "The lodash method `_.isInteger` exported as a module.", "dist-tags": {"latest": "4.0.4"}, "license": "MIT", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "lodash.isinteger", "readme": "# lodash.isinteger v4.0.4\n\nThe [lodash](https://lodash.com/) method `_.isInteger` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isinteger\n```\n\nIn Node.js:\n```js\nvar isInteger = require('lodash.isinteger');\n```\n\nSee the [documentation](https://lodash.com/docs#isInteger) or [package source](https://github.com/lodash/lodash/blob/4.0.4-npm-packages/lodash.isinteger) for more details.\n", "time": {"created": "2022-01-26T13:19:18.969Z", "modified": "2022-01-26T13:19:18.969Z", "4.0.4": "2016-08-13T17:40:41.538Z", "4.0.3": "2016-05-12T14:11:46.975Z", "4.0.2": "2016-04-03T03:50:00.330Z", "4.0.1": "2016-02-03T07:28:36.257Z", "4.0.0": "2016-01-13T11:05:12.068Z"}, "versions": {"4.0.4": {"name": "lodash.isinteger", "version": "4.0.4", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.4", "_shasum": "619c0af3d03f8b04c31f5882840b77b11cd68343", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "619c0af3d03f8b04c31f5882840b77b11cd68343", "size": 3464, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "integrity": "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.isinteger-4.0.4.tgz_1471110038989_0.8096776001621038"}, "directories": {}, "publish_time": 1471110041538, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471110041538, "_cnpmcore_publish_time": "2021-12-16T11:29:03.810Z"}, "4.0.3": {"name": "lodash.isinteger", "version": "4.0.3", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.3", "_shasum": "dda95f19161331a0ef136d9906fe2308b8fa1475", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "dda95f19161331a0ef136d9906fe2308b8fa1475", "size": 3666, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isinteger/-/lodash.isinteger-4.0.3.tgz", "integrity": "sha512-ZjzKxSNhzv0hTAeEwTY4vSuk1fbeH7OrAIwmWZu0ax4l8ba7g4dF3/ywNbBEl4r598tHIjyjm/ktwvnHd7kVJg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.isinteger-4.0.3.tgz_1463062306476_0.03799888282082975"}, "directories": {}, "publish_time": 1463062306975, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463062306975, "_cnpmcore_publish_time": "2021-12-16T11:29:04.021Z"}, "4.0.2": {"name": "lodash.isinteger", "version": "4.0.2", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.2", "_shasum": "6d5cf9c18f4c811ca50a5377cebaf652a394801f", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6d5cf9c18f4c811ca50a5377cebaf652a394801f", "size": 3591, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isinteger/-/lodash.isinteger-4.0.2.tgz", "integrity": "sha512-YSww7IGgaM2yltelBVQvxr3FKAL2QD5odTZ3/TAm1b1cZr5J8SWo1r7R0s7X3Uw2JRE2dAq/3jo7tN70ucZw1w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.isinteger-4.0.2.tgz_1459655399707_0.9143900412600487"}, "directories": {}, "publish_time": 1459655400330, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459655400330, "_cnpmcore_publish_time": "2021-12-16T11:29:04.221Z"}, "4.0.1": {"name": "lodash.isinteger", "version": "4.0.1", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.1", "_shasum": "083f627e30538d51d7d27dfb3f1fb0e2a14e661f", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "083f627e30538d51d7d27dfb3f1fb0e2a14e661f", "size": 3043, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isinteger/-/lodash.isinteger-4.0.1.tgz", "integrity": "sha512-yrcZDPS2fmHFNZEpacyZkHGMRWgQYPAGIf/V6VD6Kd61l+zzbppATNVIohX8oCdwXDJe69NDWzJ4jHeHNulcUg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isinteger-4.0.1.tgz_1454484515482_0.2861711150035262"}, "directories": {}, "publish_time": 1454484516257, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454484516257, "_cnpmcore_publish_time": "2021-12-16T11:29:04.467Z"}, "4.0.0": {"name": "lodash.isinteger", "version": "4.0.0", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.0", "_shasum": "ceb8348ced619a7d0fb4883332c7902130955a40", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ceb8348ced619a7d0fb4883332c7902130955a40", "size": 3119, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.isinteger/-/lodash.isinteger-4.0.0.tgz", "integrity": "sha512-i4hTXknLPCw5Kw3KgnUJ9zFX6iZbSQzn2PPP7/2iivAfzSExWfOlSRe7XvyV7vympSyh3qfyPSRHy+o6GNVuvw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452683112068, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452683112068, "_cnpmcore_publish_time": "2021-12-16T11:29:04.671Z"}}, "_source_registry_name": "default"}