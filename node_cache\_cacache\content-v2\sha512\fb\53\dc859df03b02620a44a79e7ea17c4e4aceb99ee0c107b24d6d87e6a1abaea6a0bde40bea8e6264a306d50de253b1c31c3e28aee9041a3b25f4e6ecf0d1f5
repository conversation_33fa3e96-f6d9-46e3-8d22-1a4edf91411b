{"_attachments": {}, "_id": "write-file-atomic", "_rev": "2024-61f14839fbcaa28a75951214", "author": {"name": "GitHub Inc."}, "description": "Write files in an atomic fashion w/configurable ownership", "dist-tags": {"latest": "6.0.0"}, "license": "ISC", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "name": "write-file-atomic", "readme": "write-file-atomic\n-----------------\n\nThis is an extension for node's `fs.writeFile` that makes its operation\natomic and allows you set ownership (uid/gid of the file).\n\n### `writeFileAtomic(filename, data, [options], [callback])`\n\n#### Description:\n\nAtomically and asynchronously writes data to a file, replacing the file if it already\nexists.  data can be a string or a buffer.\n\n#### Options:\n* filename **String**\n* data **String** | **Buffer**\n* options **Object** | **String**\n  * chown **Object** default, uid & gid of existing file, if any\n    * uid **Number**\n    * gid **Number**\n  * encoding **String** | **Null** default = 'utf8'\n  * fsync **Boolean** default = true\n  * mode **Number** default, from existing file, if any\n  * tmpfileCreated **Function** called when the tmpfile is created\n* callback **Function**\n\n#### Usage:\n\n```js\nvar writeFileAtomic = require('write-file-atomic')\nwriteFileAtomic(filename, data, [options], [callback])\n```\n\nThe file is initially named `filename + \".\" + murmurhex(__filename, process.pid, ++invocations)`.\nNote that `require('worker_threads').threadId` is used in addition to `process.pid` if running inside of a worker thread.\nIf writeFile completes successfully then, if passed the **chown** option it will change\nthe ownership of the file. Finally it renames the file back to the filename you specified. If\nit encounters errors at any of these steps it will attempt to unlink the temporary file and then\npass the error back to the caller.\nIf multiple writes are concurrently issued to the same file, the write operations are put into a queue and serialized in the order they were called, using Promises. Writes to different files are still executed in parallel.\n\nIf provided, the **chown** option requires both **uid** and **gid** properties or else\nyou'll get an error.  If **chown** is not specified it will default to using\nthe owner of the previous file.  To prevent chown from being ran you can\nalso pass `false`, in which case the file will be created with the current user's credentials.\n\nIf **mode** is not specified, it will default to using the permissions from\nan existing file, if any.  Expicitly setting this to `false` remove this default, resulting\nin a file created with the system default permissions.\n\nIf options is a String, it's assumed to be the **encoding** option. The **encoding** option is ignored if **data** is a buffer. It defaults to 'utf8'.\n\nIf the **fsync** option is **false**, writeFile will skip the final fsync call.\n\nIf the **tmpfileCreated** option is specified it will be called with the name of the tmpfile when created.\n\nExample:\n\n```javascript\nwriteFileAtomic('message.txt', 'Hello Node', {chown:{uid:100,gid:50}}, function (err) {\n  if (err) throw err;\n  console.log('It\\'s saved!');\n});\n```\n\nThis function also supports async/await:\n\n```javascript\n(async () => {\n  try {\n    await writeFileAtomic('message.txt', 'Hello Node', {chown:{uid:100,gid:50}});\n    console.log('It\\'s saved!');\n  } catch (err) {\n    console.error(err);\n    process.exit(1);\n  }\n})();\n```\n\n### `writeFileAtomicSync(filename, data, [options])`\n\n#### Description:\n\nThe synchronous version of **writeFileAtomic**.\n\n#### Usage:\n```js\nvar writeFileAtomicSync = require('write-file-atomic').sync\nwriteFileAtomicSync(filename, data, [options])\n```\n\n", "time": {"created": "2022-01-26T13:10:17.064Z", "modified": "2025-06-05T09:30:00.962Z", "3.0.3": "2020-02-24T22:27:04.013Z", "3.0.2": "2020-02-24T20:46:06.260Z", "3.0.1": "2019-10-16T04:23:05.682Z", "3.0.0": "2019-05-24T19:20:20.733Z", "2.4.3": "2019-05-24T16:48:52.689Z", "2.4.2": "2019-01-23T01:23:41.137Z", "2.4.1": "2019-01-23T00:27:34.206Z", "2.4.0": "2019-01-22T23:52:33.211Z", "2.3.0": "2017-08-18T22:54:58.294Z", "2.1.0": "2017-05-13T19:45:53.878Z", "2.0.0": "2017-04-26T23:40:28.497Z", "1.3.4": "2017-04-26T23:39:58.237Z", "1.3.3": "2017-04-22T01:58:28.962Z", "1.3.2": "2017-04-21T02:52:18.885Z", "1.3.1": "2017-01-07T08:02:24.984Z", "1.3.0": "2017-01-06T00:39:35.969Z", "1.2.0": "2016-08-18T20:32:52.191Z", "1.1.4": "2015-12-03T22:04:24.581Z", "1.1.3": "2015-09-08T21:17:40.905Z", "1.1.2": "2015-05-22T01:53:50.418Z", "1.1.1": "2015-05-22T01:40:50.343Z", "1.1.0": "2014-09-19T23:32:52.849Z", "1.0.3": "2014-09-15T23:33:40.116Z", "1.0.2": "2014-09-11T23:53:33.815Z", "1.0.1": "2014-09-10T23:27:54.551Z", "1.0.0": "2014-09-10T23:26:27.142Z", "4.0.0": "2022-01-18T20:57:49.368Z", "4.0.1": "2022-02-10T14:45:53.214Z", "4.0.2": "2022-08-16T17:12:53.613Z", "5.0.0": "2022-10-14T05:22:38.937Z", "5.0.1": "2023-04-26T19:28:58.689Z", "6.0.0": "2024-09-25T17:36:47.579Z"}, "versions": {"3.0.3": {"name": "write-file-atomic", "version": "3.0.3", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "standard", "postlint": "rimraf chowncopy good nochmod nochown nofsync nofsyncopt noopen norename \"norename nounlink\" nowrite", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.4", "rimraf": "^2.6.3", "standard": "^14.3.1", "tap": "^14.10.6"}, "tap": {"100": true}, "gitHead": "eb8dff15f83f16be1e0b89be54fa80200356614a", "_id": "write-file-atomic@3.0.3", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"shasum": "56bd5c5a5c70481cd19c571bd39ab965a5de56e8", "size": 4794, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-3.0.3.tgz", "integrity": "sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_3.0.3_1582583223897_0.8414533271003164"}, "_hasShrinkwrap": false, "publish_time": 1582583224013, "_cnpm_publish_time": 1582583224013, "_cnpmcore_publish_time": "2021-12-13T18:14:31.007Z"}, "3.0.2": {"name": "write-file-atomic", "version": "3.0.2", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "standard", "postlint": "rimraf chowncopy good nochmod nochown nofsync nofsyncopt noopen norename \"norename nounlink\" nowrite", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.4", "rimraf": "^2.6.3", "standard": "^14.3.1", "tap": "^14.10.6"}, "tap": {"100": true}, "gitHead": "e322288d985d54992142b144ed2e6366f8865a45", "_id": "write-file-atomic@3.0.2", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"shasum": "0c55003305e31dee5e62b3693dcb553410ac11e3", "size": 4616, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-3.0.2.tgz", "integrity": "sha512-w+Lx4LO0eSgy3FypOovx1+vXrW+MilikDICu4sTe0YSmBT2D4MQmQ6X3wQ/gaP6TwAcsQgu03zhIfYdjYioVZQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_3.0.2_1582577166119_0.8984137433054395"}, "_hasShrinkwrap": false, "publish_time": 1582577166260, "_cnpm_publish_time": 1582577166260, "_cnpmcore_publish_time": "2021-12-13T18:14:31.198Z"}, "3.0.1": {"name": "write-file-atomic", "version": "3.0.1", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"pretest": "standard", "test": "tap", "posttest": "rimraf chowncopy good nochmod nochown nofsync nofsyncopt noopen norename \"norename nounlink\" nowrite", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.4", "rimraf": "^2.6.3", "standard": "^12.0.1", "tap": "^14.1.1"}, "tap": {"100": true}, "gitHead": "4eeacc621736d9d1f4ad58f4ac6084a630b7ddc8", "_id": "write-file-atomic@3.0.1", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.0", "dist": {"shasum": "558328352e673b5bb192cf86500d60b230667d4b", "size": 4594, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-3.0.1.tgz", "integrity": "sha512-JPStrIyyVJ6oCSz/691fAjFtefZ6q+fP6tm+OS4Qw6o+TGQxNp1ziY2PgS+X/m0V8OWhZiO/m4xSj+Pr4RrZvw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_3.0.1_1571199785525_0.9882528754344773"}, "_hasShrinkwrap": false, "publish_time": 1571199785682, "_cnpm_publish_time": 1571199785682, "_cnpmcore_publish_time": "2021-12-13T18:14:31.418Z"}, "3.0.0": {"name": "write-file-atomic", "version": "3.0.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"pretest": "standard", "test": "tap", "posttest": "rimraf chowncopy good nochmod nochown nofsync nofsyncopt noopen norename \"norename nounlink\" nowrite", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.4", "rimraf": "^2.6.3", "standard": "^12.0.1", "tap": "^14.1.1"}, "tap": {"100": true}, "gitHead": "73c3b6f94ce7d57f63d30d4b7b33f17d4004bf1e", "_id": "write-file-atomic@3.0.0", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "dist": {"shasum": "1b64dbbf77cb58fd09056963d63e62667ab4fb21", "size": 4485, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-3.0.0.tgz", "integrity": "sha512-EIgkf60l2oWsffja2Sf2AL384dx328c0B+cIYPTQq5q2rOYuDV00/iPFBOUiDKKwKMOhkymH8AidPaRvzfxY+Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_3.0.0_1558725620595_0.8040002107095305"}, "_hasShrinkwrap": false, "publish_time": 1558725620733, "_cnpm_publish_time": 1558725620733, "_cnpmcore_publish_time": "2021-12-13T18:14:31.621Z"}, "2.4.3": {"name": "write-file-atomic", "version": "2.4.3", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --100 test/*.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^12.0.1", "tap": "^12.1.3"}, "gitHead": "f3b025d7ec066b3579c0737a400e668dbed42262", "_id": "write-file-atomic@2.4.3", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "dist": {"shasum": "****************************************", "size": 4495, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-2.4.3.tgz", "integrity": "sha512-GaETH5wwsX+GcnzhPgKcKjJ6M2Cq3/iZp1WyY/X1CSqrW+jVNM9Y7D8EC2sM4ZG/V8wZlSniJnCKWPmBYAucRQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_2.4.3_1558716532556_0.2880615077337976"}, "_hasShrinkwrap": false, "publish_time": 1558716532689, "_cnpm_publish_time": 1558716532689, "_cnpmcore_publish_time": "2021-12-13T18:14:31.827Z"}, "2.4.2": {"name": "write-file-atomic", "version": "2.4.2", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --100 test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^12.0.1", "tap": "^12.1.3"}, "gitHead": "2191d03e97ecf447af30057d752b25fa07c1f32a", "_id": "write-file-atomic@2.4.2", "_npmVersion": "6.4.1", "_nodeVersion": "11.3.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "a7181706dfba17855d221140a9c06e15fcdd87b9", "size": 4377, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-2.4.2.tgz", "integrity": "sha512-s0b6vB3xIVRLWywa6X9TOMA7k9zio0TMOsl9ZnDkliA/cfJlpHXAscj0gbHVJiTdIuAYpIyqS5GW91fqm6gG5g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_2.4.2_1548206621021_0.3144530936854546"}, "_hasShrinkwrap": false, "publish_time": 1548206621137, "_cnpm_publish_time": 1548206621137, "_cnpmcore_publish_time": "2021-12-13T18:14:32.017Z"}, "2.4.1": {"name": "write-file-atomic", "version": "2.4.1", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --100 test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^12.0.1", "tap": "^12.1.3"}, "gitHead": "8ea1b4ab6e2e9630fc495118084058fbeb09bcbc", "_id": "write-file-atomic@2.4.1", "_npmVersion": "6.5.0-next.0", "_nodeVersion": "11.6.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "d0b05463c188ae804396fd5ab2a370062af87529", "size": 4239, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-2.4.1.tgz", "integrity": "sha512-TGHFeZEZMnv+gBFRfjAcxL5bPHrsGKtnb4qsFAws7/vlh+QfwAaySIw4AXP9ZskTTh5GWu3FLuJhsWVdiJPGvg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_2.4.1_1548203254097_0.2775843084072045"}, "_hasShrinkwrap": false, "publish_time": 1548203254206, "_cnpm_publish_time": 1548203254206, "_cnpmcore_publish_time": "2021-12-13T18:14:32.215Z"}, "2.4.0": {"name": "write-file-atomic", "version": "2.4.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --100 test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^12.0.1", "tap": "^12.1.3"}, "gitHead": "152992ae232070bb25951a7b0484a3bd31160460", "_id": "write-file-atomic@2.4.0", "_nodeVersion": "10.11.0", "_npmVersion": "6.6.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "cf0f9bc37c2d26c455161c3b75f407f5bd1cad64", "size": 4144, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-2.4.0.tgz", "integrity": "sha512-rpYM0txDxd2GV1RJeyAEhDasBPUG51fFpSaW22JKulFA6mKrOYfht5mkq62TbTRfKdUDFj0b8TAz95+F5sc+Ow=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_2.4.0_1548201153104_0.7017856282373696"}, "_hasShrinkwrap": false, "publish_time": 1548201153211, "_cnpm_publish_time": 1548201153211, "_cnpmcore_publish_time": "2021-12-13T18:14:32.436Z"}, "2.3.0": {"name": "write-file-atomic", "version": "2.3.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --100 test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^10.0.2", "tap": "^10.3.2"}, "files": ["index.js"], "gitHead": "1597785cb9e1152056d905a357b3891b3295506d", "_id": "write-file-atomic@2.3.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.3.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "1ff61575c2e2a4e8e510d6fa4e243cce183999ab", "size": 3539, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-2.3.0.tgz", "integrity": "sha512-xuPeK4OdjWqtfi59ylvVL0Yn35SF3zgcAcv7rBPFHVaEapaDr4GdGgm3j7ckTwH9wHL7fGmgfAnb0+THrHb8tA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic-2.3.0.tgz_1503096898166_0.22769020055420697"}, "directories": {}, "publish_time": 1503096898294, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503096898294, "_cnpmcore_publish_time": "2021-12-13T18:14:32.617Z"}, "2.1.0": {"name": "write-file-atomic", "version": "2.1.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --100 test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^10.0.2", "tap": "^10.3.2"}, "files": ["index.js"], "gitHead": "4c63f8e504845ceca4a11cfff779e5b4839243f9", "_id": "write-file-atomic@2.1.0", "_npmVersion": "4.6.1", "_nodeVersion": "7.9.0", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "1769f4b551eedce419f0505deae2e26763542d37", "size": 2982, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-2.1.0.tgz", "integrity": "sha512-0TZ20a+xcIl4u0+Mj5xDH2yOWdmQiXlKf9Hm+TgDXjTMsEYb+gDrmb8e8UNAzMCitX8NBqG4Z/FUQIyzv/R1JQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/write-file-atomic-2.1.0.tgz_1494704753639_0.7767078222241253"}, "directories": {}, "publish_time": 1494704753878, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494704753878, "_cnpmcore_publish_time": "2021-12-13T18:14:32.856Z"}, "2.0.0": {"name": "write-file-atomic", "version": "2.0.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --coverage test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^9.0.2", "tap": "^10.3.2"}, "files": ["index.js"], "gitHead": "eabc44b01044a78bcc7b4e41f42ece6bf9d1ca5d", "_id": "write-file-atomic@2.0.0", "_npmVersion": "4.6.1", "_nodeVersion": "7.7.4", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "bb99a5440d0d31dd860a68da392bffeef66251a1", "size": 2951, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-2.0.0.tgz", "integrity": "sha512-fdiCHCFsKXHKnx7pLSj5S2cTK2R86TDdOrZ+9boV14j9+FBxUQNEK3Hfj4RMRDJT55oLa91t9Tcvhs4be59fvA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/write-file-atomic-2.0.0.tgz_1493250025531_0.17179739149287343"}, "directories": {}, "publish_time": 1493250028497, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493250028497, "_cnpmcore_publish_time": "2021-12-13T18:14:33.077Z"}, "1.3.4": {"name": "write-file-atomic", "version": "1.3.4", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --coverage test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^9.0.2", "tap": "^10.3.2"}, "files": ["index.js"], "gitHead": "8f7d56f6a62600a38e816a8276a128883f4e7436", "_id": "write-file-atomic@1.3.4", "_npmVersion": "4.6.1", "_nodeVersion": "7.7.4", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "f807a4f0b1d9e913ae7a48112e6cc3af1991b45f", "size": 2950, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.3.4.tgz", "integrity": "sha512-SdrHoC/yVBPpV0Xq/mUZQIpW2sWXAShb/V4pomcJXh92RuaO+f3UTWItiR3Px+pLnV2PvC2/bfn5cwr5X6Vfxw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/write-file-atomic-1.3.4.tgz_1493249998004_0.7851631820667535"}, "directories": {}, "publish_time": 1493249998237, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493249998237, "_cnpmcore_publish_time": "2021-12-13T18:14:33.325Z"}, "1.3.3": {"name": "write-file-atomic", "version": "1.3.3", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --coverage test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^5.4.1", "tap": "^2.3.1"}, "files": ["index.js"], "gitHead": "fce59c7e3675131712b3965d52e5880e2a5df2ca", "_id": "write-file-atomic@1.3.3", "_shasum": "831dd22d491bdc135180bb996a0eb3f8bf587791", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "4.6.1", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "831dd22d491bdc135180bb996a0eb3f8bf587791", "size": 2916, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.3.3.tgz", "integrity": "sha512-FDy1rVWascTeXXe4z2vAl/bFj/+LWYko3Wvl/6Jsv+J5n8ZGiQn7ci/Nmfsn3zdTkG++1LKCOw621qIK/44cog=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/write-file-atomic-1.3.3.tgz_1492826308726_0.5064767252188176"}, "directories": {}, "publish_time": 1492826308962, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492826308962, "_cnpmcore_publish_time": "2021-12-13T18:14:33.537Z"}, "1.3.2": {"name": "write-file-atomic", "version": "1.3.2", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --coverage test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^5.4.1", "tap": "^2.3.1"}, "files": ["index.js"], "gitHead": "d9c5f54df01043671f4ce6542cf7ebcf770e6d43", "_id": "write-file-atomic@1.3.2", "_npmVersion": "4.5.0", "_nodeVersion": "7.7.4", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "f80ac5e06d3a38996ab51b5d310db57102deb902", "size": 2856, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.3.2.tgz", "integrity": "sha512-+oKsjAwslFT7dnTE43USg70xJloTbVQiLit9itkbZbIpWOuYEjhu607+Qh7A0znA/vyVry6T5pdaNMNrJJenVg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/write-file-atomic-1.3.2.tgz_1492743137041_0.5345788700506091"}, "directories": {}, "publish_time": 1492743138885, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492743138885, "_cnpmcore_publish_time": "2021-12-13T18:14:33.835Z"}, "1.3.1": {"name": "write-file-atomic", "version": "1.3.1", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --coverage test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^5.4.1", "tap": "^2.3.1"}, "files": ["index.js"], "gitHead": "56fef5763513905d7e43aa685adad80002445474", "_id": "write-file-atomic@1.3.1", "_shasum": "7d45ba32316328dd1ec7d90f60ebc0d845bb759a", "_from": ".", "_npmVersion": "4.1.1", "_nodeVersion": "7.3.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "7d45ba32316328dd1ec7d90f60ebc0d845bb759a", "size": 2690, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.3.1.tgz", "integrity": "sha512-RCTmbZJFENrUmJVmdaf3SiIDlP1YQGFub6P/WbrTxKHKLWmhnSgaM/cYsjxDwnzD0gVE2tlTUpX6Zr/9V4+DQg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/write-file-atomic-1.3.1.tgz_1483776142964_0.11427561868913472"}, "directories": {}, "publish_time": 1483776144984, "_hasShrinkwrap": false, "_cnpm_publish_time": 1483776144984, "_cnpmcore_publish_time": "2021-12-13T18:14:34.123Z"}, "1.3.0": {"name": "write-file-atomic", "version": "1.3.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --coverage test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^5.4.1", "tap": "^2.3.1"}, "files": ["index.js"], "gitHead": "8c382bc10b903ca92b36fcaeb19e2630bb6c2c58", "_id": "write-file-atomic@1.3.0", "_shasum": "d13e4831d52ee4e3d9a266ee1c9a1592f7fbbf3d", "_from": ".", "_npmVersion": "4.1.1", "_nodeVersion": "4.6.1", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "d13e4831d52ee4e3d9a266ee1c9a1592f7fbbf3d", "size": 2659, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.3.0.tgz", "integrity": "sha512-KS/Xb2t9HcQxjsC6q1aCmRiSUBEGoLsRClN7o55hnvBQu5BCHzmt/FPa1iNm3xRUbqr4HPqwBbKFN04BDSfz3g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/write-file-atomic-1.3.0.tgz_1483663174188_0.5587220122106373"}, "directories": {}, "publish_time": 1483663175969, "_hasShrinkwrap": false, "_cnpm_publish_time": 1483663175969, "_cnpmcore_publish_time": "2021-12-13T18:14:34.331Z"}, "1.2.0": {"name": "write-file-atomic", "version": "1.2.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --coverage test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.2", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "standard": "^5.4.1", "tap": "^2.3.1", "tmp": "0.0.28"}, "files": ["index.js"], "gitHead": "c29f37cb5955f597066ad7aedea7aa6f7408a5b7", "_id": "write-file-atomic@1.2.0", "_shasum": "14c66d4e4cb3ca0565c28cf3b7a6f3e4d5938fab", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "4.5.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "14c66d4e4cb3ca0565c28cf3b7a6f3e4d5938fab", "size": 2619, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.2.0.tgz", "integrity": "sha512-sF5pW8ltbsziEH1LOXylQqZc970+21ryQmxAY/jtxc8L6MNpDRnY2ME30HTEIGhyfGyNh8YDq0uVWes3j54vDQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/write-file-atomic-1.2.0.tgz_1471552371956_0.5829780481290072"}, "directories": {}, "publish_time": 1471552372191, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471552372191, "_cnpmcore_publish_time": "2021-12-13T18:14:34.553Z"}, "1.1.4": {"name": "write-file-atomic", "version": "1.1.4", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --coverage test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.2", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "standard": "^5.4.1", "tap": "^2.3.1"}, "gitHead": "42dc04a17af96ac045f4979c8c951ee5a14a8b8b", "_id": "write-file-atomic@1.1.4", "_shasum": "b1f52dc2e8dc0e3cb04d187a25f758a38a90ca3b", "_from": ".", "_npmVersion": "3.5.1", "_nodeVersion": "5.1.0", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "dist": {"shasum": "b1f52dc2e8dc0e3cb04d187a25f758a38a90ca3b", "size": 4070, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.1.4.tgz", "integrity": "sha512-c5qespPIeoD/YQTLgdOTe9mcjhK0MhK/URjnIlpuF+4Hoec1flfMRcZY+SWrqGHHRC1oGY1VyNC44wiLQgJMiw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1449180264581, "_hasShrinkwrap": false, "_cnpm_publish_time": 1449180264581, "_cnpmcore_publish_time": "2021-12-13T18:14:34.740Z"}, "1.1.3": {"name": "write-file-atomic", "version": "1.1.3", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.2", "slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "tap": "^0.4.12"}, "gitHead": "65a1e2e156c0d0bfb7acac2e039b943d6ec9876d", "_id": "write-file-atomic@1.1.3", "_shasum": "60eaca258a0b559b37aca82b21d64a293b4b90d0", "_from": ".", "_npmVersion": "3.3.0", "_nodeVersion": "3.1.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "60eaca258a0b559b37aca82b21d64a293b4b90d0", "size": 3005, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.1.3.tgz", "integrity": "sha512-e5nX+j6V/58iDNVqB+9JC0FeDVnz+I10jZQ7R+qMjtxbgx2W0fdeBSaoTNRO34Y4EgNgHc3kMd0p5bd0DO4yaw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1441747060905, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441747060905, "_cnpmcore_publish_time": "2021-12-13T18:14:34.963Z"}, "1.1.2": {"name": "write-file-atomic", "version": "1.1.2", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "**************:iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^3.0.2", "slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "tap": "^0.4.12"}, "gitHead": "b721f8a71223bcf162f1ee4ff4677f31de1c061f", "_id": "write-file-atomic@1.1.2", "_shasum": "ff3d61f1c2f5bb71e8ebe491a7157bf7d60435a4", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.6.2", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "ff3d61f1c2f5bb71e8ebe491a7157bf7d60435a4", "size": 2997, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.1.2.tgz", "integrity": "sha512-v74MFElvm262ej04D/7ZtpbrD94n61tCjObcMJPSsW3cuPWtONbitTQOGp1ZEeP0pCZJ4WKKhVP7SADKaiW7uQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432259630418, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432259630418, "_cnpmcore_publish_time": "2021-12-13T18:14:35.173Z"}, "1.1.1": {"name": "write-file-atomic", "version": "1.1.1", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "**************:iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^3.0.2", "slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "tap": "^0.4.12"}, "gitHead": "2e85f44a3d176e5e1ed453c016adbe4bcc25eb5c", "_id": "write-file-atomic@1.1.1", "_shasum": "cf085df5f415638d9bc9e56ad0f33349f18ba2e4", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.6.2", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "cf085df5f415638d9bc9e56ad0f33349f18ba2e4", "size": 3260, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.1.1.tgz", "integrity": "sha512-9b8/p180qkY2o+p1P7HDd1LnbBT7qIToiuL13COcY/Ia6dKkm9OsRw1BWokm91ndfem85s9O5V7NTSIumOCwJw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432258850343, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432258850343, "_cnpmcore_publish_time": "2021-12-13T18:14:35.386Z"}, "1.1.0": {"name": "write-file-atomic", "version": "1.1.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "**************:iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^3.0.2", "slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "tap": "^0.4.12"}, "gitHead": "28e4df86547c6728eab0b51bca6f00cf44ef392c", "_id": "write-file-atomic@1.1.0", "_shasum": "e114cfb8f82188353f98217c5945451c9b4dc060", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "e114cfb8f82188353f98217c5945451c9b4dc060", "size": 2497, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.1.0.tgz", "integrity": "sha512-hBzSvmsCxfn7IHmgGWYFiR1EaRO+I1wQL6ELLNV/wBi6jPO3tz+mYd8wPxQTSRNAbCv6a/rLlQU8Hkfq/q/Row=="}, "directories": {}, "publish_time": 1411169572849, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411169572849, "_cnpmcore_publish_time": "2021-12-13T18:14:35.615Z"}, "1.0.3": {"name": "write-file-atomic", "version": "1.0.3", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "**************:iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "tap": "^0.4.12"}, "gitHead": "5e85cfc7b38e4f0659b15b1c1d979e6a4a7bf6d5", "_id": "write-file-atomic@1.0.3", "_shasum": "597fcb824a0d1ad337db90b7028d2772e9e8e7af", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "597fcb824a0d1ad337db90b7028d2772e9e8e7af", "size": 2487, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.0.3.tgz", "integrity": "sha512-sjKqLWndsbTNVKX3nfyvRTvrLBe0rN0kdyQ3oReLgu3YZc3Q1N5lY2X/QvUeoWdcvxphFBJevuCcBxtBS1eO3Q=="}, "directories": {}, "publish_time": 1410824020116, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410824020116, "_cnpmcore_publish_time": "2021-12-13T18:14:35.817Z"}, "1.0.2": {"name": "write-file-atomic", "version": "1.0.2", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "**************:iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "tap": "^0.4.12"}, "gitHead": "84058cea16834f9f53ae1948fbb828937f9ab697", "_id": "write-file-atomic@1.0.2", "_shasum": "080b0439cf29b3abdcf321a86fc47e9429b9984e", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "080b0439cf29b3abdcf321a86fc47e9429b9984e", "size": 2646, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.0.2.tgz", "integrity": "sha512-YygOy8BFyFeyQjbo1cSHfVCJOEG0lmhmvoflSse8p3GKYmARxE40OwhxMCtcz/SjunmrTJmKN2DqVWLmHDzwIw=="}, "directories": {}, "publish_time": 1410479613815, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410479613815, "_cnpmcore_publish_time": "2021-12-13T18:14:36.252Z"}, "1.0.1": {"name": "write-file-atomic", "version": "1.0.1", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "**************:iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "tap": "^0.4.12"}, "gitHead": "93f18a172fa79d0fb67c6c1dc37be62a106f13a5", "_id": "write-file-atomic@1.0.1", "_shasum": "0592ccdc2cee973e7427525f4cd5f67d352afeb9", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "0592ccdc2cee973e7427525f4cd5f67d352afeb9", "size": 2477, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.0.1.tgz", "integrity": "sha512-ZJK0UP9+4+N+SR6pOYxYgT2IKMzeWFyiG7VbPA+aq0TkEdVETypLNdpAXq+QwPltVHYMCJNjJU++JAzaWP8dSQ=="}, "directories": {}, "publish_time": 1410391674551, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410391674551, "_cnpmcore_publish_time": "2021-12-13T18:14:36.443Z"}, "1.0.0": {"name": "write-file-atomic", "version": "1.0.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "**************:iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"slide": "^1.1.5"}, "devDependencies": {"require-inject": "^1.1.0", "tap": "^0.4.12"}, "gitHead": "5ef6f374dfd1b6034f11d80a0d5616e7aa5bcaeb", "_id": "write-file-atomic@1.0.0", "_shasum": "20750a1710e6cd09de5911d3043fa14e4a948445", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "20750a1710e6cd09de5911d3043fa14e4a948445", "size": 2481, "noattachment": false, "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-1.0.0.tgz", "integrity": "sha512-YTzFJHmA40RP8NXofYRaFG54SaN0Wtoo5UIyTWsy1i/fYXjBVU7NlibyjcrBg+jiYpnQXf71SsFOPWTKapdkrQ=="}, "directories": {}, "publish_time": 1410391587142, "_hasShrinkwrap": false, "_cnpm_publish_time": 1410391587142, "_cnpmcore_publish_time": "2021-12-13T18:14:36.653Z"}, "4.0.0": {"name": "write-file-atomic", "version": "4.0.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "./lib/index.js", "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "eslint '**/*.js'", "postlint": "npm-template-check", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lintfix": "npm run lint -- --fix", "snap": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^4.0.0"}, "devDependencies": {"@npmcli/template-oss": "^2.5.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "tap": "^15.1.6"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "templateOSS": {"windowsCI": false, "version": "2.5.1"}, "gitHead": "5c14a2f20a3dbceb55413270fe8d9982c239fb3f", "_id": "write-file-atomic@4.0.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-<PERSON><PERSON><PERSON>WoKffJNF7ivO9yflBhc7tn3wKnokMUfWpBriM9yCXj4ePQnRPcWglBkkg1AHC8nsW/EfxwwhqsLtOy59djA==", "shasum": "0eff5dc687d3e22535ca3fca8558124645a4b053", "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-4.0.0.tgz", "fileCount": 4, "unpackedSize": 12186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ynNCRA9TVsSAnZWagAAzogP/2AVgMqY2GR9SXLrnvgH\nrJCd/EcoNK0oGxJEWALxtOIcu8xxU2oBVrwObQCTN7i7vRXiIztndUBwKuMM\nb4JSz1MDfrd76JPtJc5azCGbGt0/tMZKRBrwIyiIhyhq4ff0DO2Eq2faxWJ5\nIKFSsqZ+THBWRWQyczBiL/ZkqNMg/nu1lo3sjOv/gC0oGCMMDt2D0VLVksU0\nW9lt6v/7jKQL6DX/Y+U2kq2Z110wpuIGEs6+lCHgCmTvShn0GIHfk/5fAl+j\nWfTu8gIEx+3UslxfgtAiDRN0StdIYLzenFTF+k8nVeQDI8KLj5TPM/5wGfP3\nI/9UCF+ts6+g324PVkcm9071MWEUxk2wu/sVMg2n95N8xZNm9U/vI0GbF/Kr\nQH7kedVYBgvsoR47z+LXMDwZ71iq14ZJUxne84yG2f8xWXVQ5WjKaYUBhD/R\nBwKu13ywR6F7T1F4jN49gtS3citFMxoZAOtQ7Bb4+Wkm/r4KeVM7UfPz9D47\nwASEoVv5JDYNS/F3uMG+lzbcENWrSyUkgM3yuh8u6L8V0WvYgqRGLXo4h8ok\ntdc6ffwTMcJmnwlg+xHyEeQy5IbOr8wmSu8S5L9X2aFU5qttwyJJ0yiTSeAm\nN047t9NMv6oFvz3oy4WceTqpnzcj85BPRVB30jEo5Bvix0MUa5Kpm/wwtkG2\n2F+M\r\n=T5ZW\r\n-----END PGP SIGNATURE-----\r\n", "size": 4404}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_4.0.0_1642539469227_0.11293768248957403"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-18T21:07:57.529Z"}, "4.0.1": {"name": "write-file-atomic", "version": "4.0.1", "description": "Write files in an atomic fashion w/configurable ownership", "main": "./lib/index.js", "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "eslint '**/*.js'", "postlint": "npm-template-check", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lintfix": "npm run lint -- --fix", "snap": "tap", "template-copy": "npm-template-copy --force"}, "repository": {"type": "git", "url": "git://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "devDependencies": {"@npmcli/template-oss": "^2.7.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "tap": "^15.1.6"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "templateOSS": {"windowsCI": false, "version": "2.7.1"}, "gitHead": "bf8ca7f9f5e6b7f955c97fd0e9fdbc2d6adb1e3c", "_id": "write-file-atomic@4.0.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.1", "dist": {"integrity": "sha512-nSKUxgAbyioruk6hU87QzVbY279oYT6uiwgDoujth2ju4mJ+TZau7SQBhtbTmUyuNYTuXnSyRn66FV0+eCgcrQ==", "shasum": "9faa33a964c1c85ff6f849b80b42a88c2c537c8f", "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-4.0.1.tgz", "fileCount": 4, "unpackedSize": 11920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBSUhCRA9TVsSAnZWagAAMuwP/0r0E6bCPezsTlGykNy0\nedF/WiAMTCBTaqhXPnWVIWafdKcvHEkKd8lwNWG4FuG1C4kCS1d+rowIgt7H\ny18aAUYYkM/Rc4iSwg/S0jQOSk/0Laalo9dZSQdNvP9XMRRiVhTAPyVZk6VO\nl/EV0NM8cBlxeTULAbiYgxWl0ombYG4IDuP41W6DLhwgTqoQjbWbzCCtpkO0\nrcGdf0uvRXl0JiRt0A0TypMD31C23YEAd0pODmzHvf3jnWNPZSlaoeGSIbZl\nojbaPIHXl3uRspffJhQ7tq331RtbMT7JnblR3dXy0Ua7OwqOQfi9jG7fdyCx\nOd76t6NDrCftvu4+TbUbWmeFL/+LvPKLMqSe6tAdQnPZBOoxE4qjfb4brq1U\nmhtqSNaqvJKkz2brpj11FBbfxqe8PC0RclMiEoCuLS3dlg5XytOaS6RW1ivN\nulRG0E9OwdNONt1JYQTGSk9a9P7RMc235rXWmCdeR0JBM4S+sfhjOuLPR1Mg\nyFDE7Etr06iVy0t+P0NQRvQzRQfx5vDKNOY4JblsHYAV2BI+ajWSe7aZjfOo\n2hcVkybjv2oZ90XgzfCF8cme4yRJt3A0FhgxP9f5oMgv948FajTJNonf5yHo\nUH5RF2pywmm8G6NeosTaA6MYLMoCSjwzD+85YS8qAtaXTVFzfQLgfqILmbf6\nGxhT\r\n=ltxU\r\n-----END PGP SIGNATURE-----\r\n", "size": 4355}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_4.0.1_1644504353063_0.04931648875822048"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-10T14:45:58.640Z"}, "4.0.2": {"name": "write-file-atomic", "version": "4.0.2", "description": "Write files in an atomic fashion w/configurable ownership", "main": "./lib/index.js", "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lintfix": "npm run lint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "3.5.0"}, "gitHead": "9688d63c4013595dd6a2b2128273b67544be3ca4", "_id": "write-file-atomic@4.0.2", "_nodeVersion": "18.6.0", "_npmVersion": "8.17.0", "dist": {"integrity": "sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==", "shasum": "a9df01ae5b77858a027fd2e80768ee433555fcfd", "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "fileCount": 4, "unpackedSize": 12210, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFN0rOXzb7vlZ/Ne3ZvDXqdi5kBnplBRC9V4av29mFsWAiBMVWOVfqqC6a/LHIYhD3RQS5zZkwd3Sb6ysZQcemlJmQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+9AVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrD1w/+LfZiCgVS2tRWWLxCSoeZZzdH8PwknIv4CDmjof2jguBbQPOI\r\nozz3mQP9CWxREtHuQ5/CwAzE03/mKBp+DZbY3WbgfK9OdXWGQERBLS+M3Bwp\r\nDHGK3PPrWfqMnLrbsO+uWKxsXcoNCAk8+F/A9meLUb9ymeSa62LRfViZeLFn\r\nQGVkNSAFhD7EHbEz7QlIZZWdJmkr87mGXiS6BhOSK9yIBf5FoZofcq6whbTU\r\nyJDc1AX7JBbw/B7dCE3GpNDajx7j4sJ7ZEL4bqllrc3Bocb9rxVCBG0GrGJ7\r\n6RXE3zzS0sxWnf2LSluNKQ8VtGUtCzbZII9qr+lw5JryqdwXw11CIHmprz/j\r\nm2UH79gIGBZejE0SG0vtW3Y3QCJ5rvjcPdvez0woA76jVXq37j1MMoNCwx9p\r\npWdDQVTE47T5X17bU8fcF3sT4doTfUFqbRaWjKSyj1kiJCYAGWArTxBaOIIb\r\n1azEV5mSMZQ/kzS7EAApstgC8ZM2VXgMmBS8ET1sfkRnPCnlD5AxAQbwWY4F\r\nyyGJzSGz04ZlJ+N7FUfIWumZcfNtZRH5GJzDhoph2kL7Xd8rwdc8AVOB62fP\r\nGdGhVpfmIM93At+jvgxTiecFeKlEap0gCRy7IyFsWdSHVoXppTWPIZrpsid5\r\nhAsbdCCGxU1wvxdzebk9Tn6OUOwBdFqS2ek=\r\n=5qqS\r\n-----END PGP SIGNATURE-----\r\n", "size": 4427}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_4.0.2_1660669973372_0.12770302065333472"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-16T17:13:31.910Z"}, "5.0.0": {"name": "write-file-atomic", "version": "5.0.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "./lib/index.js", "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "4.5.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.5.1"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "gitHead": "af873aab3169b7362d990532b3e60889d71d28b2", "_id": "write-file-atomic@5.0.0", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-R7NYMnHSlV42K54lwY9lvW6MnSm1HSJqZL3xiSgi9E7//FYaI74r2G0rd+/X6VAMkHEdzxQaU5HUOXWUz5kA/w==", "shasum": "54303f117e109bf3d540261125c8ea5a7320fab0", "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-5.0.0.tgz", "fileCount": 4, "unpackedSize": 12174, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE6PBSPffISfxPWaCbEz4dcFN45vbBu2uedNlaK0tSWhAiBgZjEiNxukcAgoY9bNxCHzCEGta7HlvrUePfSvkap3IQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPIeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpegQ//UwQTHr9v/4PTaG+mVdV03IzOUAd0wvIbN1yTR5CuKIUP3saF\r\nHy3vETe3e9WSmV9PQ5ha+lCQvZ0GHukyMQSgjEsZzfYYepLnx2OqalG8XQzt\r\nXQS1OB19ywQsP+ZckdfkvYSfBk9jPqU950a22NcK3N2o8QQj9C3N14NnNJqU\r\nYLV+WZ6KYnhNVzdkCqX1eOj+HzPDXK1wOKbcj96g58z/rulNP56rBiSwPUoL\r\nVzkBstZxQp9zea7YpeLuaOSYJjeLtBQHBzCcxdNN3ys9PyT5Dr/AICM3TTDK\r\nksXF5A9wCm+qmQYH/uKk98NEjj6FifDAyoJcD41SYvxdsR5x/UOzfqOprlWC\r\n7w9jJMKlV4XaFLMbedwFQQ8nRMIAUpxOuRk6ndMNBvX2Kp24nfc7YNWN4jit\r\nsIB5JnfxwnLjH5uPzpo/PvGUomq/SaaWpPDM3DH8zAGU+JC3CDB1tGa2SVAI\r\nnAWa9v7Xyk7b5G+AvjGcA+9/IVerkFfBLbNU0ZgcoYxDWx/TydwhKvwhIsc4\r\nCHo9XMI4MUvXYkbppAtnXvUbF5POHpa/RZJviu5fgJsVwfGrQNL/8FsF4dGV\r\npqT1WIMOxYOL9RqhcgZYZF7gWvMLAx5X2iAGxJk5rzfG8egR2Lseqt7z8c9j\r\nHua4O8tqYVCnSr1STVazJ85oB4aJcz+aX/o=\r\n=azqX\r\n-----END PGP SIGNATURE-----\r\n", "size": 4422}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_5.0.0_1665724958752_0.9578277453246526"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-14T11:02:37.173Z"}, "5.0.1": {"name": "write-file-atomic", "version": "5.0.1", "description": "Write files in an atomic fashion w/configurable ownership", "main": "./lib/index.js", "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^4.0.1"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.14.1", "tap": "^16.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.14.1", "publish": "true"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "gitHead": "042cff721108a0c1d8a831ba9fa8dca2e5e390da", "_id": "write-file-atomic@5.0.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-+QU2zd6OTD8XWIJCbffaiQeH9U73qIqafo1x6V1snCWYGJf6cVE0cDR4D8xRzcEnfI21IFrUPzPGtcPf8AC+Rw==", "shasum": "68df4717c55c6fa4281a7860b4c2ba0a6d2b11e7", "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-5.0.1.tgz", "fileCount": 4, "unpackedSize": 12155, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/write-file-atomic@5.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqqykBTtym4VY08wDXnXSrulqKJQKw8QI+L+2OL6BWmwIhAPoR81vEraEZI16hWXBL2MXCu9w7HZIe1CJOl340rbz0"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSXt6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpm7g//Uq4rbtZOYVoM9VWATgOeIek0XxGsP/Tli7fgAIg1ExYKdCcZ\r\n5olEczsvVgYj7KUJRvuYY59Zbj36OgxbgRlmkhZt4vLh7O/7MCsuHlZ+SrTs\r\npsO/mTMXtXgDvlQc2bxVwkRJ0srluC0vhLyhn4JQh2wFM4JwO/os4CG4ihs1\r\ndY3MGEIJ5VWp2FDKEVfMAsliPnU0VwTuFlrqt9lFodwQtPDM8HzTePkdtJ0O\r\nS9Fsd0ynmVyBMusKc09ZPE8mfhCZDqcN8zv2hl/PqHBuxqZlQbcFTvenIQuV\r\nNgI7GOoMgOozsuv/Tu+JU/qFaHHp2iSa9H/F1FSt56ZhVytohCoHMI2KAiUe\r\niUMheHsfz7Ego3FqTWh7pJ+vc3jVKUx5x+tqQ1Rg6HfKqUI72O87CDdOEC8p\r\nkTSkhBbEShvw5YsQf5j/oc0W3gyiWLWawb7BhQ2WIAfdNvu/0JqKiy17+vMZ\r\nUlseyYXPPJZOG5e4m564MxqyLv8vjeEO1H+xC08IRG1cK3kW2bxMvH3I3q2+\r\nuUMRptb2nBBLp++Vfq4cahtd8O0xbjmABoyVu0GDCrlsor3ntUNWyPg+nwoW\r\ndsDp0xkrZYD7vh50KAxfqjP68Pm10J6vONFlApiglDLcrGtIzkQeSHLTbPsy\r\nC3CCzfk3m4JHWszKOTKUerZSOeJ9Sv3SZtU=\r\n=oyQz\r\n-----END PGP SIGNATURE-----\r\n", "size": 4408}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_5.0.1_1682537338540_0.8323328526091589"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-26T19:28:58.689Z", "publish_time": 1682537338689, "_source_registry_name": "default"}, "6.0.0": {"name": "write-file-atomic", "version": "6.0.0", "description": "Write files in an atomic fashion w/configurable ownership", "main": "./lib/index.js", "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^4.0.1"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.0.1"}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.23.3", "publish": "true"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "_id": "write-file-atomic@6.0.0", "gitHead": "9fcd4021b8a0c86bf54deded4905aec68d968161", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-GmqrO8WJ1NuzJ2DrziEI2o57jKAVIQNf8a18W3nCYU3H7PNWqCCVTeH6/NQE93CIllIgQS98rrmVkYgTX9fFJQ==", "shasum": "e9c89c8191b3ef0606bc79fb92681aa1aa16fa93", "tarball": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-6.0.0.tgz", "fileCount": 4, "unpackedSize": 12202, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/write-file-atomic@6.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHRVsY6kOqYyc5+lzTFlZ3XeUCFCezFPnZzqsK5HVEuxAiEAncU2Wq7vuEmGjBtWbUc7oI+mavj8AzUhEHXn8/YYtvk="}], "size": 4444}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/write-file-atomic_6.0.0_1727285807414_0.2588495531603523"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-25T17:36:47.579Z", "publish_time": 1727285807579, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "keywords": ["writeFile", "atomic"], "repository": {"type": "git", "url": "git+https://github.com/npm/write-file-atomic.git"}, "_source_registry_name": "default"}