{"_attachments": {}, "_id": "signal-exit", "_rev": "2275-61f148aca920628a7b6f2665", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "when you want to fire an event no matter how a process exits.", "dist-tags": {"latest": "4.1.0", "next": "3.0.1"}, "license": "ISC", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "name": "signal-exit", "readme": "# signal-exit\n\nWhen you want to fire an event no matter how a process exits:\n\n- reaching the end of execution.\n- explicitly having `process.exit(code)` called.\n- having `process.kill(pid, sig)` called.\n- receiving a fatal signal from outside the process\n\nUse `signal-exit`.\n\n```js\n// Hybrid module, either works\nimport { onExit } from 'signal-exit'\n// or:\n// const { onExit } = require('signal-exit')\n\nonExit((code, signal) => {\n  console.log('process exited!', code, signal)\n})\n```\n\n## API\n\n`remove = onExit((code, signal) => {}, options)`\n\nThe return value of the function is a function that will remove\nthe handler.\n\nNote that the function _only_ fires for signals if the signal\nwould cause the process to exit. That is, there are no other\nlisteners, and it is a fatal signal.\n\nIf the global `process` object is not suitable for this purpose\n(ie, it's unset, or doesn't have an `emit` method, etc.) then the\n`onExit` function is a no-op that returns a no-op `remove` method.\n\n### Options\n\n- `alwaysLast`: Run this handler after any other signal or exit\n  handlers. This causes `process.emit` to be monkeypatched.\n\n### Capturing Signal Exits\n\nIf the handler returns an exact boolean `true`, and the exit is a\ndue to signal, then the signal will be considered handled, and\nwill _not_ trigger a synthetic `process.kill(process.pid,\nsignal)` after firing the `onExit` handlers.\n\nIn this case, it your responsibility as the caller to exit with a\nsignal (for example, by calling `process.kill()`) if you wish to\npreserve the same exit status that would otherwise have occurred.\nIf you do not, then the process will likely exit gracefully with\nstatus 0 at some point, assuming that no other terminating signal\nor other exit trigger occurs.\n\nPrior to calling handlers, the `onExit` machinery is unloaded, so\nany subsequent exits or signals will not be handled, even if the\nsignal is captured and the exit is thus prevented.\n\nNote that numeric code exits may indicate that the process is\nalready committed to exiting, for example due to a fatal\nexception or unhandled promise rejection, and so there is no way to\nprevent it safely.\n\n### Browser Fallback\n\nThe `'signal-exit/browser'` module is the same fallback shim that\njust doesn't do anything, but presents the same function\ninterface.\n\nPatches welcome to add something that hooks onto\n`window.onbeforeunload` or similar, but it might just not be a\nthing that makes sense there.\n", "time": {"created": "2022-01-26T13:12:12.922Z", "modified": "2025-06-04T22:32:07.491Z", "3.0.7": "2022-02-03T21:05:34.544Z", "3.0.6": "2021-11-18T16:55:47.372Z", "3.0.5": "2021-09-29T20:45:14.203Z", "3.0.4": "2021-09-15T23:30:10.675Z", "3.0.3": "2020-03-26T19:32:54.645Z", "3.0.2": "2016-12-04T03:21:02.792Z", "3.0.1": "2016-09-08T17:13:05.740Z", "3.0.0": "2016-06-13T22:35:49.825Z", "3.0.0-candidate": "2016-06-10T16:14:07.934Z", "2.1.2": "2015-05-25T10:07:11.052Z", "2.0.0": "2015-05-25T09:53:31.879Z", "2.1.1": "2015-05-25T06:01:08.097Z", "2.1.0": "2015-05-25T02:45:00.437Z", "1.3.1": "2015-05-20T15:48:08.812Z", "1.3.0": "2015-05-19T04:50:16.726Z", "1.2.0": "2015-05-18T01:45:01.230Z", "1.1.0": "2015-05-16T19:20:17.735Z", "1.0.1": "2015-05-16T07:06:50.535Z", "1.0.0": "2015-05-16T06:45:54.221Z", "4.0.0": "2023-04-16T00:32:53.803Z", "4.0.1": "2023-04-16T06:14:51.490Z", "4.0.2": "2023-05-10T20:16:38.095Z", "4.0.3": "2023-07-29T00:27:33.736Z", "4.1.0": "2023-07-29T05:44:56.721Z"}, "versions": {"3.0.7": {"name": "signal-exit", "version": "3.0.7", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^3.1.1", "nyc": "^15.1.0", "standard-version": "^9.3.1", "tap": "^15.1.1"}, "gitHead": "03dd77a96caa309c6a02c59274d58c812a2dce45", "_id": "signal-exit@3.0.7", "_nodeVersion": "17.4.0", "_npmVersion": "8.4.0", "dist": {"integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "shasum": "a9a1767f8af84155114eaabd73f99273c8f59ad9", "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz", "fileCount": 5, "unpackedSize": 9958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/EOeCRA9TVsSAnZWagAAX3AP/2P+1N10ku3PdJf0ldO6\npwxiTPHMWvDzPdr5eqaQCygilmRFPEO3pTza6jDS7gUcmvL6I8KpqPlxXpwP\nOX2DNCQtAvX5m1hOA+HOXfCFBX1byvBjhOGx6vbQ45ZPJke6N9Csp4SAuG9D\nJYycXaym80r4MnQtPV4V9mxxQy3AE32O0gbTWKCJnkMge19OvSt5W+fXMbyd\n7OvlVAxQ1MPJFOSGGk1hyxSbJRndx4s6ud1jcP6pD+/p0eWPj59EVl+g1zj8\n2yvTRJW73UKkpsBtUb2zRr4Mg2ya1BvWYofII8NHEU6wtM79AQGGD6YI9XMm\n8WPvk/XAyQ9UESl2bl5SKenjrDgKfzkhwge8Z1tOMoKDLzblsYJ8Z8Xk8YeX\nUaNO2alrM7wO8YHYloI9HSoRhcCBRfr2TknZpBzP6oS7GneiHpdOC+Jo9EMS\nzztVeGkZJGYhsozZ+v/abF+79mERVU5JOfkSupUv1a3Y15ncJDtlA+D4blhl\na/9HZGVtrwbxefKXQJcZobG9/cM3B9tjdbO2TZr3rlzCfyDLSidUoOmvxY1m\nINnVxPx3kOTsqhWfNYZ66p4U86vxIi2BNIDe4De9wAIlVvzMrD0cTzPFYmdw\nqO/bMgGJjcSYCa+j4pruHT34x4kTpNLCWtmFQtIgU/Sqjo0qoywepGNDIgYB\nLt2r\r\n=bZwh\r\n-----END PGP SIGNATURE-----\r\n", "size": 3851}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_3.0.7_1643922334281_0.1668442524954039"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-03T21:05:43.328Z"}, "3.0.6": {"name": "signal-exit", "version": "3.0.6", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^3.1.1", "nyc": "^15.1.0", "standard-version": "^9.3.1", "tap": "^15.1.1"}, "gitHead": "2d6a8a29bd8d4265ad769b772415bb85c6b5be8b", "_id": "signal-exit@3.0.6", "_nodeVersion": "16.5.0", "_npmVersion": "8.1.3", "dist": {"integrity": "sha512-sDl4qMFpijcGw22U5w63KmD3cZJfBuFlVNbVMKje2keoKML7X2UzWbc4XrmEbDwg0NXJc3yv4/ox7b+JWb57kQ==", "shasum": "24e630c4b0f03fea446a2bd299e62b4a6ca8d0af", "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.6.tgz", "fileCount": 5, "unpackedSize": 9914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhloWTCRA9TVsSAnZWagAA3BAP/2ZHXB9mcWDoenvVH23N\n0jiTtmPeJMfLTW/iUNOySdXCO4ul+86lpZ6b8kFHNMsZ5X1FzyDQsd9tX4Wm\nEHeOuJwjD3ewEMlfKmpYvY9+VddVzK0bzi0WPtZcaxtT/F9CCDAG07v+J4fN\nRt2r0bI2+Ae8Z0masFcDoCuv54Q/oYnvgIbyj9qoGDW/+rFsITRiVivLPjY3\n894hVGFNuONIjuE747Ly6epwa7zRZLC6LEV3JEoj7+e32UC+fdCemypGsOcl\nE77lD6sgpAcmWOMnbmmQs138mItJ4hVaxf6BLgSrnO11Ao+tLLdE0IwfrkPL\nfw13Rp7C5pzisOuifBzSRAliLRikP8CXRbVZ4s0/Zv3zEebeZf8uEhFDg5rc\nKwU7Zms5EgPTTZIb8nz3aEbawy2jQqP9KJLGyZcwssc9Ib+O2+Ep2dWB7Fvb\n+zU5PvPTZgexivbMQqVcr47/oDPEE9OqGIK3Dovm3TlW8g/gOX4YgaaOocsC\nq2AL4OWky1373L+y+iFdhGPhOQRSNqFoOFtaENov4gTWR2nGr9UNoGMWhft8\nIbcMgFzmkbYgnp9g4//d+GIIlNzml4dF7Au1XQptaxKJ8KFCqS3XY63gsTD7\nbxyxspthWk/BQ7AGoOjQ4wOCbS57xEEkmEOe3uQhhNt5ss5pOKZeSRl6hhqG\nAdGY\r\n=yBa+\r\n-----END PGP SIGNATURE-----\r\n", "size": 3850, "noattachment": false}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_3.0.6_1637254547191_0.37843656420736105"}, "_hasShrinkwrap": false, "publish_time": 1637254547372, "_cnpm_publish_time": 1637254547372, "_cnpmcore_publish_time": "2021-12-13T10:29:50.380Z"}, "3.0.5": {"name": "signal-exit", "version": "3.0.5", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "tap --timeout=240 ./test/*.js --cov", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^3.1.1", "nyc": "^15.1.0", "standard-version": "^9.3.1", "tap": "^15.0.10"}, "gitHead": "7b4436f0070df3c50187177cb690445b1aac6f59", "_id": "signal-exit@3.0.5", "_nodeVersion": "16.5.0", "_npmVersion": "7.24.1", "dist": {"shasum": "9e3e8cc0c75a99472b44321033a7702e7738252f", "size": 3782, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.5.tgz", "integrity": "sha512-KWcOiKeQj6ZyXx7zq4YxSMgHRlod4czeBQZrPb8OKcohcqAXShm7E20kEMle9WBt26hFcAf0qLOcp5zmY7kOqQ=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_3.0.5_1632948314004_0.2165871510185664"}, "_hasShrinkwrap": false, "publish_time": 1632948314203, "_cnpm_publish_time": 1632948314203, "_cnpmcore_publish_time": "2021-12-13T10:29:50.663Z"}, "3.0.4": {"name": "signal-exit", "version": "3.0.4", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "tap --timeout=240 ./test/*.js --cov", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^3.1.1", "nyc": "^15.1.0", "standard-version": "^9.3.1", "tap": "^15.0.9"}, "gitHead": "e98985e48852f258d9b39ed5a08204a012b0a999", "_id": "signal-exit@3.0.4", "_nodeVersion": "16.5.0", "_npmVersion": "7.23.0", "dist": {"shasum": "366a4684d175b9cab2081e3681fda3747b6c51d7", "size": 3785, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.4.tgz", "integrity": "sha512-rqYhcAnZ6d/vTPGghdrw7iumdcbXpsk1b8IG/rz+VWV51DM0p7XCtMoJ3qhPLIbp3tvyt3pKRbaaEMZYpHto8Q=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_3.0.4_1631748610497_0.5113250064842751"}, "_hasShrinkwrap": false, "publish_time": 1631748610675, "_cnpm_publish_time": 1631748610675, "_cnpmcore_publish_time": "2021-12-13T10:29:50.947Z"}, "3.0.3": {"name": "signal-exit", "version": "3.0.3", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"pretest": "standard", "test": "tap --timeout=240 ./test/*.js --cov", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.10", "nyc": "^8.1.0", "standard": "^8.1.0", "standard-version": "^2.3.0", "tap": "^8.0.1"}, "gitHead": "bb32fe5e3126e9bb55acf83168628839d3a81ea6", "_id": "signal-exit@3.0.3", "_nodeVersion": "12.15.0", "_npmVersion": "6.13.4", "dist": {"shasum": "a1410c2edd8f077b08b4e253c8eacfcaf057461c", "size": 4158, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.3.tgz", "integrity": "sha512-VUJ49FC8U1OxwZLxIbTTrDvLnf/6TDgxZcK8wxR8zs13xpx7xbG60ndBlhNrFi2EMuFRoeDoJO7wthSLq42EjA=="}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "isaacs"}], "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_3.0.3_1585251174552_0.5516349483279421"}, "_hasShrinkwrap": false, "publish_time": 1585251174645, "_cnpm_publish_time": 1585251174645, "_cnpmcore_publish_time": "2021-12-13T10:29:51.206Z"}, "3.0.2": {"name": "signal-exit", "version": "3.0.2", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"pretest": "standard", "test": "tap --timeout=240 ./test/*.js --cov", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "files": ["index.js", "signals.js"], "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.10", "nyc": "^8.1.0", "standard": "^7.1.2", "standard-version": "^2.3.0", "tap": "^8.0.1"}, "gitHead": "9c5ad9809fe6135ef22e2623989deaffe2a4fa8a", "_id": "signal-exit@3.0.2", "_shasum": "b5fdc08f1287ea1178628e415e25132b73646c6d", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b5fdc08f1287ea1178628e415e25132b73646c6d", "size": 3967, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.2.tgz", "integrity": "sha512-meQNNykwecVxdu1RlYMKpQx4+wefIYpmxi6gexo/KAbwquJrBUrBmKYJrE8KFkVQAAVWEnwNdu21PgrD77J3xA=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/signal-exit-3.0.2.tgz_1480821660838_0.6809983775019646"}, "directories": {}, "publish_time": 1480821662792, "_hasShrinkwrap": false, "_cnpm_publish_time": 1480821662792, "_cnpmcore_publish_time": "2021-12-13T10:29:51.533Z"}, "3.0.1": {"name": "signal-exit", "version": "3.0.1", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"pretest": "standard", "test": "tap --timeout=240 ./test/*.js --cov", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "files": ["index.js", "signals.js"], "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.10", "nyc": "^8.1.0", "standard": "^7.1.2", "standard-version": "^2.3.0", "tap": "^7.1.0"}, "gitHead": "6859aff54f5198c63fff91baef279b86026bde69", "_id": "signal-exit@3.0.1", "_shasum": "5a4c884992b63a7acd9badb7894c3ee9cfccad81", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "5a4c884992b63a7acd9badb7894c3ee9cfccad81", "size": 3821, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.1.tgz", "integrity": "sha512-jMxoxd0fzr1lrcP3NJqu4d+DG0R41ZvTsnO8HUYmtN0oFzi0WijhuYJfsofAT7NhWcqhJfqa4auDDTTb0I1rYw=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/signal-exit-3.0.1.tgz_1473354783379_0.4592130535747856"}, "directories": {}, "publish_time": 1473354785740, "_hasShrinkwrap": false, "_cnpm_publish_time": 1473354785740, "_cnpmcore_publish_time": "2021-12-13T10:29:51.858Z"}, "3.0.0": {"name": "signal-exit", "version": "3.0.0", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"pretest": "standard", "test": "tap --timeout=240 ./test/*.js --cov", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "files": ["index.js", "signals.js"], "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.2", "nyc": "^6.4.4", "standard": "^7.1.2", "standard-version": "^2.3.0", "tap": "^5.7.2"}, "gitHead": "2bbec4e5d9f9cf1f7529b1c923d1b058e69ccf7f", "_id": "signal-exit@3.0.0", "_shasum": "3c0543b65d7b4fbc60b6cd94593d9bf436739be8", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "3c0543b65d7b4fbc60b6cd94593d9bf436739be8", "size": 3668, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.0.tgz", "integrity": "sha512-Ac0AA11BsZJ3/amrRfKAT8ECmO8qMtxOqMLvG8L5ae0PXrCETs3tpn80exSZe+rW1p4h7yv85PK0SGZdNQY9+A=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/signal-exit-3.0.0.tgz_1465857346813_0.7961636525578797"}, "directories": {}, "publish_time": 1465857349825, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465857349825, "_cnpmcore_publish_time": "2021-12-13T10:29:52.199Z"}, "3.0.0-candidate": {"name": "signal-exit", "version": "3.0.0-candidate", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"pretest": "standard", "test": "tap --timeout=240 ./test/*.js --cov", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "files": ["index.js", "signals.js"], "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.2", "nyc": "^6.4.4", "standard": "^7.1.2", "standard-version": "^2.3.0", "tap": "^5.7.2"}, "gitHead": "0734aaf90b2a56d7e4bdcf69b2f55f4bc21bcfbb", "_id": "signal-exit@3.0.0-candidate", "_shasum": "e5c316b0d56b82a23143460c237da09bb7db58ae", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "e5c316b0d56b82a23143460c237da09bb7db58ae", "size": 3376, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.0-candidate.tgz", "integrity": "sha512-t8Mc3VsxBc8Ol03KRN77yHmuwDuKH5WQRAJMWa8Jpvqnp3/F6fHpDomjd2IcHPQ0cMWQRakIfdFh+FXTOpZNzg=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/signal-exit-3.0.0-candidate.tgz_1465575245318_0.3199180525261909"}, "directories": {}, "publish_time": 1465575247934, "_hasShrinkwrap": false, "_cnpm_publish_time": 1465575247934, "_cnpmcore_publish_time": "2021-12-13T10:29:52.575Z"}, "2.1.2": {"name": "signal-exit", "version": "2.1.2", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "standard && nyc tap --timeout=240 ./test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "coveralls": "^2.11.2", "nyc": "^2.1.2", "standard": "^3.9.0", "tap": "1.0.4"}, "gitHead": "8d50231bda6d0d1c4d39de20fc09d11487eb9951", "_id": "signal-exit@2.1.2", "_shasum": "375879b1f92ebc3b334480d038dc546a6d558564", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "375879b1f92ebc3b334480d038dc546a6d558564", "size": 9247, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-2.1.2.tgz", "integrity": "sha512-Hjt8MofEmj5vFgJ5vnad1V8msp7lJg/gdBP7fOmEnlgeUYkg9ntdQEzBPMc0sjJf6MVkBALNSo/KvfVn34MIwQ=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432548431052, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432548431052, "_cnpmcore_publish_time": "2021-12-13T10:29:52.936Z"}, "2.0.0": {"name": "signal-exit", "version": "2.0.0", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "standard && nyc tap --timeout=240 ./test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "coveralls": "^2.11.2", "nyc": "^2.1.2", "standard": "^3.9.0", "tap": "1.0.4"}, "gitHead": "8799da141591970be4acd1520ddf285c679b402e", "_id": "signal-exit@2.0.0", "_shasum": "ff49a7570adbe39f28ef0c879e1fa519627c7f0f", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "ff49a7570adbe39f28ef0c879e1fa519627c7f0f", "size": 9247, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-2.0.0.tgz", "integrity": "sha512-U+tSV1tdmLwLDTo1nA5LHSmLSCrOsWdo6JI34GGy2NFujkPeekl58d2IlgVwlg/Jrd/1NfYUigWYDJOjTyARxw=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432547611879, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432547611879, "_cnpmcore_publish_time": "2021-12-13T10:29:53.714Z"}, "2.1.1": {"name": "signal-exit", "version": "2.1.1", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "standard && nyc tap --timeout=240 ./test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "coveralls": "^2.11.2", "nyc": "^2.1.1", "standard": "^3.9.0", "tap": "1.0.4"}, "gitHead": "6a35feb2fca13587de78b8580c397f0e320b40f5", "_id": "signal-exit@2.1.1", "_shasum": "c6c74947c23ccf2174f765d19f04d5e50a28ae4e", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.36", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "c6c74947c23ccf2174f765d19f04d5e50a28ae4e", "size": 9249, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-2.1.1.tgz", "integrity": "sha512-tpw2DUpPg9NrmOmrM8CrBn8fgd5urPele/MhihqX4rvXzcpee34pBYZv4jLT5QMq1TTxd34buefhVDz3SI6bRQ=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432533668097, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432533668097, "_cnpmcore_publish_time": "2021-12-13T10:29:54.128Z"}, "2.1.0": {"name": "signal-exit", "version": "2.1.0", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "standard && nyc tap --timeout=240 ./test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "coveralls": "^2.11.2", "nyc": "^2.0.6", "standard": "^3.9.0", "tap": "1.0.4"}, "gitHead": "b2003f133816d4e1fa5dc8f6ddd55854f6de24ec", "_id": "signal-exit@2.1.0", "_shasum": "3307338a7dad7bf0e6952411e3163e6a3a5b171d", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "3307338a7dad7bf0e6952411e3163e6a3a5b171d", "size": 9092, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-2.1.0.tgz", "integrity": "sha512-eEG8Heq1dHjSZDcdNxEkSgk5Di487agWxYK4XGt0gQVRsmkFKmR0z7RmiGCnoEmvLF04JvNeIofe6ihLmakdDg=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432521900437, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432521900437, "_cnpmcore_publish_time": "2021-12-13T10:29:54.550Z"}, "1.3.1": {"name": "signal-exit", "version": "1.3.1", "description": "when you want to fire an event no matter how a process exits.", "main": "index.js", "scripts": {"test": "standard && nyc tap ./test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "coveralls": "^2.11.2", "nyc": "^2.0.5", "standard": "^3.9.0", "tap": "^1.0.4"}, "gitHead": "cb1fbb71eb4bdc99346be59e840e1709d404efca", "_id": "signal-exit@1.3.1", "_shasum": "ed2ad7a323526c3738acf2da801716ba7e9d4e63", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.36", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "ed2ad7a323526c3738acf2da801716ba7e9d4e63", "size": 3495, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-1.3.1.tgz", "integrity": "sha512-hxUsHCBGPGGGDm8Og++2xMRCQ7+Tb0ZM4N9GCWdJpJqYKAz3y/6osuu+BnZCkKEx8s+0teSWpv/zGj5aOFj71A=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432136888812, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432136888812, "_cnpmcore_publish_time": "2021-12-13T10:29:55.002Z"}, "1.3.0": {"name": "signal-exit", "version": "1.3.0", "description": "when you want process.on('exit') to fire when a process is killed with a signal.", "main": "index.js", "scripts": {"test": "standard && nyc tap ./test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "nyc": "^2.0.4", "standard": "^3.9.0", "tap": "^1.0.4"}, "gitHead": "709cad913cf8a033ed2f6ef0c942a44e21f00d5c", "_id": "signal-exit@1.3.0", "_shasum": "5e2da9bf25151c69e93092a4984cfead7eea91ae", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "5e2da9bf25151c69e93092a4984cfead7eea91ae", "size": 3261, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-1.3.0.tgz", "integrity": "sha512-9reMUrmYnjR4My8PJ5tMt8T23qJWXb/tI7G5SWYjRJH5ApMEgQ0jQLtzGgHZjctC0q5WtYm6fx8y+jF7Y8hvUg=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432011016726, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432011016726, "_cnpmcore_publish_time": "2021-12-13T10:29:55.418Z"}, "1.2.0": {"name": "signal-exit", "version": "1.2.0", "description": "when you want process.on('exit') to fire when a process is killed with a signal.", "main": "index.js", "scripts": {"test": "nyc tap ./test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "nyc": "^2.0.0", "standard": "^3.9.0", "tap": "^1.0.4"}, "gitHead": "a75ae7fb47cb0c85c4727837a7d92f950be2df6a", "_id": "signal-exit@1.2.0", "_shasum": "5ece3781c39ed72a540b63236603b10031c2c9ba", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "5ece3781c39ed72a540b63236603b10031c2c9ba", "size": 2734, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-1.2.0.tgz", "integrity": "sha512-b0ioBBIvTzsbB4JNXptlaP0jim35GH1kGBYBBGyNzflBEAMpZQcHAQfn6AwOVp+WX2dL0fvvL8NmxV4BuG/+jA=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431913501230, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431913501230, "_cnpmcore_publish_time": "2021-12-13T10:29:55.881Z"}, "1.1.0": {"name": "signal-exit", "version": "1.1.0", "description": "when you want process.on('exit') to fire when a process is killed with a signal.", "main": "index.js", "scripts": {"test": "nyc tap ./test/*.js"}, "repository": {"type": "git", "url": "https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "nyc": "^1.3.0", "standard": "^3.9.0", "tap": "^1.0.4"}, "gitHead": "2b1715cb2c125745f563acf4ade61e400c83517c", "_id": "signal-exit@1.1.0", "_shasum": "3a52269649dafaa0a1c4150d8e11535e0b75c834", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.6.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "3a52269649dafaa0a1c4150d8e11535e0b75c834", "size": 2444, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-1.1.0.tgz", "integrity": "sha512-uEHzAHDnqeonqUeFeaN88OSLCai76QBBut6bXZZBF4IwcmKbXJGiQvIcDrHAQ9gnVry/2vXT20+VYXQT21q9pQ=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431804017735, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431804017735, "_cnpmcore_publish_time": "2021-12-13T10:29:56.354Z"}, "1.0.1": {"name": "signal-exit", "version": "1.0.1", "description": "when you want process.on('exit') to fire when a process is killed with a signal.", "main": "index.js", "scripts": {"test": "nyc tap ./test/*.js"}, "repository": {"type": "git", "url": "https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "nyc": "^1.3.0", "standard": "^3.9.0", "tap": "^1.0.4"}, "gitHead": "fa46d6f1a87d46e5c35a5665d407d95db25ebae8", "_id": "signal-exit@1.0.1", "_shasum": "71b2022c08ab28e19b44067ad855914be0d4579b", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.6.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "71b2022c08ab28e19b44067ad855914be0d4579b", "size": 2190, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-1.0.1.tgz", "integrity": "sha512-vBkaloi/Yzyn7S9YYKh3Uq7tChonCE5H/7d0O8gf8Q6/Hlq8q3vaXvZVWOufLGzndkP6RmGjl8qL8PvfD4l8Dw=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431760010535, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431760010535, "_cnpmcore_publish_time": "2021-12-13T10:29:56.758Z"}, "1.0.0": {"name": "signal-exit", "version": "1.0.0", "description": "when you want process.on('exit') to fire when a process is killed with a signal.", "main": "index.js", "scripts": {"test": "nyc tap ./test/*.js"}, "repository": {"type": "git", "url": "https://github.com/bcoe/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/bcoe/signal-exit/issues"}, "homepage": "https://github.com/bcoe/signal-exit", "devDependencies": {"chai": "^2.3.0", "nyc": "^1.3.0", "standard": "^3.9.0", "tap": "^1.0.4"}, "gitHead": "f27efb6117f139de8259dd4c36d60ffe8f187eb1", "_id": "signal-exit@1.0.0", "_shasum": "5d37a251b4b63701db283d8c22367e19541ca214", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.6.2", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "5d37a251b4b63701db283d8c22367e19541ca214", "size": 2191, "noattachment": false, "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-1.0.0.tgz", "integrity": "sha512-+iRQVQV+Doo4P6cNYVBkR5J8t94YD6AX/poEsigWt4e6Sg245sX3ZOhgNPnqETUW/WfFPXctOYuauzgjPaJuyQ=="}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431758754221, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431758754221, "_cnpmcore_publish_time": "2021-12-13T10:29:57.296Z"}, "4.0.0": {"name": "signal-exit", "version": "4.0.0", "description": "when you want to fire an event no matter how a process exits.", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "browser": "./dist/mjs/browser.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./signals": {"import": {"types": "./dist/mjs/signals.d.ts", "default": "./dist/mjs/signals.js"}, "require": {"types": "./dist/cjs/signals.d.ts", "default": "./dist/cjs/signals.js"}}, "./browser": {"import": {"types": "./dist/mjs/browser.d.ts", "default": "./dist/mjs/browser.js"}, "require": {"types": "./dist/cjs/browser.d.ts", "default": "./dist/cjs/browser.js"}}}, "engines": {"node": ">=14"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "devDependencies": {"@types/cross-spawn": "^6.0.2", "@types/node": "^18.15.11", "@types/signal-exit": "^3.0.1", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.8.6", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.23.28", "typescript": "^5.0.2"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"coverage": false, "jobs": 1, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "c791d0367e1f1fa46e808cde1138f256aa488b7c", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit#readme", "_id": "signal-exit@4.0.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-oxKGTwaoWKDSTLlmztzePHuQB4nh/IFSsCkIWy/7sY6Y7mVXR/jZ74Cp3foTr01syWHhjxKVfw8bdJSmPOZJuQ==", "shasum": "8663cb954e194eec1fcfd8c60ad9ca94b0ae394e", "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.0.0.tgz", "fileCount": 29, "unpackedSize": 69298, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHrC/S5rnbVlGPOK3PSmSPtS/bzeq+V1iyKWYp/fUkVYAiAQvwTms5ja8cGbhvgBHZdWx7ChIR9K+j8A4O7M8m+zlQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO0I1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnuxAApSP1+AMXn2/X0tRS9bgzOzVZqu6Xz+NbH+o6IbgM15jzwBml\r\nVhxhQPu3KUNPWzP5og+q0tY/l0gohWl5uWI0fQu0QhFXSv2N02YdCp0JwsWR\r\ngPaI++pqIdttWXA+Fx5x2NCHzE+ZvnopC1AKhgOknbDFlNcNtMo74802WuLE\r\nvRAMjNQ0N51nRcB3q6RP3/JeCVYhQ7NwWUQTW/P/Z2xPIbGZF9a6oEACtBO6\r\nAkAm4vpkxBgcVGRGMZonU4no7UxKDHLR4u/yzg/DbA871D9shwtvvhSorI9+\r\nZ45a/cuT+67YP+7HLb70w2XbB0BDfy4tt4MGcnXLBCqeLI7tW8AbiKKjNvfx\r\nptqp92in+lnD7EhvHaXoaR9Df43dq3xPCzVnLyWIBaLImVllO4GUVKAo3yLD\r\nmjosNPf4wZcFLUW4yy2TB7+/+CQp50Dts2kE47ogEqvB5HdPAgdWmmbFBJWb\r\n37BxeaskUhjf027BhivOIUJBrmplmSfXIkz50M81HPxEp5JfT74AW2gMrITK\r\nQ4hbWPcr+y7T9YFpCTSwPA2PRuw0Z0zWa3oTXA7Tf6CaAavrobK3pv8L4v68\r\n1OlEFf7iNGtn6xjdY21uivKfVInX4ifz8e/FTh5rWrCdVxGMRqX+0+bVSy2I\r\n1RW1+6fHw+cvJ2cRri0tIonhffSEsToXpNs=\r\n=ZdtG\r\n-----END PGP SIGNATURE-----\r\n", "size": 11954}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_4.0.0_1681605173661_0.16283449413033124"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-16T00:32:53.803Z", "publish_time": 1681605173803}, "4.0.1": {"name": "signal-exit", "version": "4.0.1", "description": "when you want to fire an event no matter how a process exits.", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "browser": "./dist/mjs/browser.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./signals": {"import": {"types": "./dist/mjs/signals.d.ts", "default": "./dist/mjs/signals.js"}, "require": {"types": "./dist/cjs/signals.d.ts", "default": "./dist/cjs/signals.js"}}, "./browser": {"import": {"types": "./dist/mjs/browser.d.ts", "default": "./dist/mjs/browser.js"}, "require": {"types": "./dist/cjs/browser.d.ts", "default": "./dist/cjs/browser.js"}}}, "engines": {"node": ">=14"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "devDependencies": {"@types/cross-spawn": "^6.0.2", "@types/node": "^18.15.11", "@types/signal-exit": "^3.0.1", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.8.6", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.23.28", "typescript": "^5.0.2"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"coverage": false, "jobs": 1, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "9600c5532ac69846c5eb84592b23ad33aba87c87", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit#readme", "_id": "signal-exit@4.0.1", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-uUWsN4aOxJAS8KOuf3QMyFtgm1pkb6I+KRZbRF/ghdf5T7sM+B1lLLzPDxswUjkmHyxQAVzEgG35E3NzDM9GVw==", "shasum": "96a61033896120ec9335d96851d902cc98f0ba2a", "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.0.1.tgz", "fileCount": 29, "unpackedSize": 72314, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNVTrkGX6XgVClpZ1BHjypdfTtoJUgJmtIjGRTGMDZFgIhALN3c2Hf8gcJhmjl1hTSLGSrOfe+iI2aRBadrunt0QTc"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO5JbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCuhAAiAA2evP9cKX/5sM1STb5z0QUvFgZXqxO2qoeHovQSR5Fq1uK\r\n/PXQ09478gpfqjPDGhr6YZRakCM6JirxpH3y5/KCaLIcfyXhkpgkzSRJz/9+\r\n1pDKf0yMLABmnBRfjFNPE8mDPhDwK1T8vFtClst9mlktIsw/8Hw4LhA/fna2\r\nxpV0TQcR59UJ8//btQOuHViQy33yTQvdrXEEWniRD+HL/XUUFC8SvyBWje+w\r\nT2rbeUbRs/abfLQtqdhLxXXLb85vrcxTzV1eJD8HoZPrJV/KnndnydYGTU+J\r\nlW7tZeYEWp+3QgN19G1MDNW/w1SZfTyCheT7gytZKCJ4ET2RYH4eEFr9flOK\r\nDtPGjF9xYT0QcwSsPRaIfl4po6F7ScM/4bfY4QFvrtVbPpG8Ov2Z2yf+m062\r\nL7D5xhmwgKEQ1dFn2u2ubSX4mbKTbX9OM43P8jrgFdTkwea62sPX0m2xZBBv\r\nruszDOucKo4b5cHyafUfT3ZyL2kwGshWiioVn7KjU7KKdqPsOrWljUCSzxyc\r\ni1gst0jIVZBhbjKjzRRyqy9OrqljvF1P8x20O6AdLPv3rKcsxCyyjS/t/RyJ\r\nrgqpEUVEUzXyQkDj9iNCTbP47qCfaNoLjy3bsUixptIt4Lm1/imAZQ1EHjCf\r\np1Ig3nWgbqnuS6rF+sXJabF0uwQeum51GG0=\r\n=G5MY\r\n-----END PGP SIGNATURE-----\r\n", "size": 12424}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_4.0.1_1681625691257_0.9851898016656047"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-16T06:14:51.490Z", "publish_time": 1681625691490}, "4.0.2": {"name": "signal-exit", "version": "4.0.2", "description": "when you want to fire an event no matter how a process exits.", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "browser": "./dist/mjs/browser.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./signals": {"import": {"types": "./dist/mjs/signals.d.ts", "default": "./dist/mjs/signals.js"}, "require": {"types": "./dist/cjs/signals.d.ts", "default": "./dist/cjs/signals.js"}}, "./browser": {"import": {"types": "./dist/mjs/browser.d.ts", "default": "./dist/mjs/browser.js"}, "require": {"types": "./dist/cjs/browser.d.ts", "default": "./dist/cjs/browser.js"}}}, "engines": {"node": ">=14"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "devDependencies": {"@types/cross-spawn": "^6.0.2", "@types/node": "^18.15.11", "@types/signal-exit": "^3.0.1", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.8.6", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.23.28", "typescript": "^5.0.2"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"coverage": false, "jobs": 1, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "8fa7fc9a9c63f559af43d292b7eb727901775507", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit#readme", "_id": "signal-exit@4.0.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-MY2/qGx4enyjprQnFaZsHib3Yadh3IXyV2C321GY0pjGfVBu4un0uDJkwgdxqO+Rdx8JMT8IfJIRwbYVz3Ob3Q==", "shasum": "ff55bb1d9ff2114c13b400688fa544ac63c36967", "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.0.2.tgz", "fileCount": 29, "unpackedSize": 72028, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPF6Nj/cqN/W3vvMogEYocnGrrvnBvZGjqJ0VjlAvQPAiBUSEZP5+COAc0MUKwdrXVt3LkhiBkIfxlGuT6OzWNwdQ=="}], "size": 12371}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_4.0.2_1683749797898_0.9421542243156267"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-10T20:16:38.095Z", "publish_time": 1683749798095, "_source_registry_name": "default"}, "4.0.3": {"name": "signal-exit", "version": "4.0.3", "description": "when you want to fire an event no matter how a process exits.", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "browser": "./dist/mjs/browser.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./signals": {"import": {"types": "./dist/mjs/signals.d.ts", "default": "./dist/mjs/signals.js"}, "require": {"types": "./dist/cjs/signals.d.ts", "default": "./dist/cjs/signals.js"}}, "./browser": {"import": {"types": "./dist/mjs/browser.d.ts", "default": "./dist/mjs/browser.js"}, "require": {"types": "./dist/cjs/browser.d.ts", "default": "./dist/cjs/browser.js"}}}, "engines": {"node": ">=14"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "devDependencies": {"@types/cross-spawn": "^6.0.2", "@types/node": "^18.15.11", "@types/signal-exit": "^3.0.1", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.8.6", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.23.28", "typescript": "^5.0.2"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"coverage": false, "jobs": 1, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "bae4850282e36eba85291b0bbb30db92ad87413f", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit#readme", "_id": "signal-exit@4.0.3", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-U97H1k7QQ8OQJ18ryc5lSI16ouK1a43nSNRkXz16OMcc5dTVz5TlQxgf2NbX+cF0luukRuy3/womPZqfpIucbw==", "shasum": "6a11e3663c031d6196860374d710afda97875944", "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.0.3.tgz", "fileCount": 29, "unpackedSize": 73260, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFR0DZKzc/cy5x3yEzEo+r6Tr7nlr0AFFk1fj3ounRrQIhAK2vvG4e8YU2O94lC/FJjLxoo1t0CpPXXpREj1i50yl0"}], "size": 12528}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_4.0.3_1690590453587_0.6178433709368925"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-29T00:27:33.736Z", "publish_time": 1690590453736, "_source_registry_name": "default"}, "4.1.0": {"name": "signal-exit", "version": "4.1.0", "description": "when you want to fire an event no matter how a process exits.", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "browser": "./dist/mjs/browser.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./signals": {"import": {"types": "./dist/mjs/signals.d.ts", "default": "./dist/mjs/signals.js"}, "require": {"types": "./dist/cjs/signals.d.ts", "default": "./dist/cjs/signals.js"}}, "./browser": {"import": {"types": "./dist/mjs/browser.d.ts", "default": "./dist/mjs/browser.js"}, "require": {"types": "./dist/cjs/browser.d.ts", "default": "./dist/cjs/browser.js"}}}, "engines": {"node": ">=14"}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "keywords": ["signal", "exit"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "devDependencies": {"@types/cross-spawn": "^6.0.2", "@types/node": "^18.15.11", "@types/signal-exit": "^3.0.1", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.8.6", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.23.28", "typescript": "^5.0.2"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"coverage": false, "jobs": 1, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "458776d9cf8be89712aa1f7b45bb2163ce15ef4a", "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit#readme", "_id": "signal-exit@4.1.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==", "shasum": "952188c1cbd546070e2dd20d0f41c0ae0530cb04", "tarball": "https://registry.npmmirror.com/signal-exit/-/signal-exit-4.1.0.tgz", "fileCount": 29, "unpackedSize": 76966, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC17YSNmo/hSXVVI2cchqvs3la7twHltiehlUoPq/9VGgIgD2Hb18tHfjmWx8vendx1mWOOOYvu7+XRLUj+wwxejPE="}], "size": 13369}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/signal-exit_4.1.0_1690609496546_0.23985049542845926"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-29T05:44:56.721Z", "publish_time": 1690609496721, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "homepage": "https://github.com/tapjs/signal-exit#readme", "keywords": ["signal", "exit"], "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "_source_registry_name": "default"}