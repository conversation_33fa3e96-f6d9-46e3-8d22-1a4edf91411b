{"_attachments": {}, "_id": "emoji-regex", "_rev": "1875-61f147eb963ca28f5ee4178e", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "dist-tags": {"latest": "10.4.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "name": "emoji-regex", "readme": "# emoji-regex [![Build status](https://github.com/mathiasbynens/emoji-regex/actions/workflows/main.yml/badge.svg)](https://github.com/mathiasbynens/emoji-regex/actions/workflows/main.yml) [![emoji-regex on npm](https://img.shields.io/npm/v/emoji-regex)](https://www.npmjs.com/package/emoji-regex)\n\n_emoji-regex_ offers a regular expression to match all emoji symbols and sequences (including textual representations of emoji) as per the Unicode Standard. It’s based on [_emoji-test-regex-pattern_](https://github.com/mathiasbynens/emoji-test-regex-pattern), which generates (at build time) the regular expression pattern based on the Unicode Standard. As a result, _emoji-regex_ can easily be updated whenever new emoji are added to Unicode.\n\nSince each version of _emoji-regex_ is tied to the latest Unicode version at the time of release, results are deterministic. This is important for use cases like image replacement, where you want to guarantee that an image asset is available for every possibly matched emoji. If you don’t need a deterministic regex, a lighter-weight, general emoji pattern is available via the [_emoji-regex-xs_](https://github.com/slevithan/emoji-regex-xs) package that follows the same API.\n\n## Installation\n\nVia [npm](https://www.npmjs.com/):\n\n```bash\nnpm install emoji-regex\n```\n\nIn [Node.js](https://nodejs.org/):\n\n```js\nconst emojiRegex = require('emoji-regex');\n// Note: because the regular expression has the global flag set, this module\n// exports a function that returns the regex rather than exporting the regular\n// expression itself, to make it impossible to (accidentally) mutate the\n// original regular expression.\n\nconst text = `\n\\u{231A}: ⌚ default emoji presentation character (Emoji_Presentation)\n\\u{2194}\\u{FE0F}: ↔️ default text presentation character rendered as emoji\n\\u{1F469}: 👩 emoji modifier base (Emoji_Modifier_Base)\n\\u{1F469}\\u{1F3FF}: 👩🏿 emoji modifier base followed by a modifier\n`;\n\nconst regex = emojiRegex();\nfor (const match of text.matchAll(regex)) {\n  const emoji = match[0];\n  console.log(`Matched sequence ${ emoji } — code points: ${ [...emoji].length }`);\n}\n```\n\nConsole output:\n\n```\nMatched sequence ⌚ — code points: 1\nMatched sequence ⌚ — code points: 1\nMatched sequence ↔️ — code points: 2\nMatched sequence ↔️ — code points: 2\nMatched sequence 👩 — code points: 1\nMatched sequence 👩 — code points: 1\nMatched sequence 👩🏿 — code points: 2\nMatched sequence 👩🏿 — code points: 2\n```\n\n## For maintainers\n\n### How to update emoji-regex after new Unicode Standard releases\n\n1. [Update _emoji-test-regex-pattern_ as described in its repository](https://github.com/mathiasbynens/emoji-test-regex-pattern#how-to-update-emoji-test-regex-pattern-after-new-uts51-releases).\n\n1. Bump the _emoji-test-regex-pattern_ dependency to the latest version.\n\n1. Update the Unicode data dependency in `package.json` by running the following commands:\n\n     ```sh\n     # Example: updating from Unicode v13 to Unicode v14.\n     npm uninstall @unicode/unicode-13.0.0\n     npm install @unicode/unicode-14.0.0 --save-dev\n     ````\n\n 1. Generate the new output:\n\n     ```sh\n     npm run build\n     ```\n\n 1. Verify that tests still pass:\n\n     ```sh\n     npm test\n     ```\n\n### How to publish a new release\n\n1. On the `main` branch, bump the emoji-regex version number in `package.json`:\n\n    ```sh\n    npm version patch -m 'Release v%s'\n    ```\n\n    Instead of `patch`, use `minor` or `major` [as needed](https://semver.org/).\n\n    Note that this produces a Git commit + tag.\n\n1. Push the release commit and tag:\n\n    ```sh\n    git push && git push --tags\n    ```\n\n    Our CI then automatically publishes the new release to npm.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_emoji-regex_ is available under the [MIT](https://mths.be/mit) license.\n", "time": {"created": "2022-01-26T13:08:59.336Z", "modified": "2024-08-26T20:34:37.423Z", "10.0.0": "2021-09-27T14:01:27.346Z", "9.2.2": "2021-03-02T10:58:09.319Z", "9.2.1": "2021-01-29T09:10:03.378Z", "9.2.0": "2020-10-20T17:12:40.635Z", "9.1.1": "2020-10-13T13:21:55.541Z", "9.0.0": "2020-04-10T09:35:09.769Z", "8.0.0": "2019-03-05T18:58:23.040Z", "7.0.3": "2018-12-21T10:26:45.903Z", "7.0.2": "2018-12-20T11:08:57.200Z", "7.0.1": "2018-08-31T19:24:00.344Z", "7.0.0": "2018-06-08T09:12:15.142Z", "6.5.1": "2017-07-26T13:42:07.910Z", "6.5.0": "2017-07-15T22:10:04.552Z", "6.4.3": "2017-07-02T08:55:21.949Z", "6.4.2": "2017-04-06T11:37:02.494Z", "6.4.1": "2017-03-15T21:05:09.685Z", "6.4.0": "2017-03-15T20:45:40.423Z", "6.3.0": "2017-03-15T19:54:04.590Z", "6.2.0": "2017-03-15T12:43:50.440Z", "6.1.3": "2017-03-09T12:59:14.686Z", "6.1.1": "2017-03-08T19:30:43.274Z", "6.1.0": "2016-12-21T19:26:57.851Z", "6.0.0": "2015-07-04T11:57:13.288Z", "5.0.0": "2015-02-13T22:46:20.711Z", "4.0.0": "2014-12-17T23:29:54.222Z", "3.0.0": "2014-11-27T11:49:34.181Z", "2.0.0": "2014-11-04T06:29:44.152Z", "1.0.1": "2014-09-29T15:38:43.645Z", "1.0.0": "2014-09-28T11:10:47.034Z", "10.0.1": "2022-03-07T10:23:49.050Z", "10.1.0": "2022-04-01T10:46:34.281Z", "10.2.0": "2022-09-29T14:08:27.887Z", "10.2.1": "2022-09-29T18:27:35.963Z", "10.3.0": "2023-10-17T09:02:29.400Z", "10.4.0": "2024-08-26T12:41:07.335Z"}, "versions": {"10.0.0": {"name": "emoji-regex", "version": "10.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "node script/build.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@unicode/unicode-14.0.0": "^1.2.1", "emoji-test-regex-pattern": "^1.5.0", "mocha": "^9.1.2"}, "gitHead": "4b351ff8138215a963fe617d020492bd4766f690", "_id": "emoji-regex@10.0.0", "_nodeVersion": "14.17.6", "_npmVersion": "6.14.15", "dist": {"shasum": "96559e19f82231b436403e059571241d627c42b8", "size": 5181, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-10.0.0.tgz", "integrity": "sha512-KmJa8l6uHi1HrBI34udwlzZY1jOEuID/ft4d8BSSEdRyap7PwBEt910453PJa5MuGvxkLqlt4Uvhu7tttFHViw=="}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_10.0.0_1632751287205_0.7117707241122113"}, "_hasShrinkwrap": false, "publish_time": 1632751287346, "_cnpm_publish_time": 1632751287346, "_cnpmcore_publish_time": "2021-12-13T11:29:08.574Z"}, "9.2.2": {"name": "emoji-regex", "version": "9.2.2", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src es2015_types -D -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/preset-env": "^7.4.4", "@unicode/unicode-13.0.0": "^1.0.3", "mocha": "^6.1.4", "regexgen": "^1.3.0"}, "gitHead": "0ffa466d7ab65af304d03dddd3a92a2d8268e7ce", "_id": "emoji-regex@9.2.2", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"shasum": "840c8803b0d8047f4ff0cf963176b32d4ef3ed72", "size": 11118, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz", "integrity": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg=="}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_9.2.2_1614682689140_0.39715833161311176"}, "_hasShrinkwrap": false, "publish_time": 1614682689319, "_cnpm_publish_time": 1614682689319, "_cnpmcore_publish_time": "2021-12-13T11:29:08.879Z"}, "9.2.1": {"name": "emoji-regex", "version": "9.2.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; cp src/RGI_Emoji-pattern.txt .; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/preset-env": "^7.4.4", "@unicode/unicode-13.0.0": "^1.0.3", "mocha": "^6.1.4", "regexgen": "^1.3.0"}, "gitHead": "8f0033fc230c4cb7945b5e3cda7e6902e178c747", "_id": "emoji-regex@9.2.1", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.11", "dist": {"shasum": "c9b25604256bb3428964bead3ab63069d736f7ee", "size": 11029, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.1.tgz", "integrity": "sha512-117l1H6U4X3Krn+MrzYrL57d5H7siRHWraBs7s+LjRuFK7Fe7hJqnJ0skWlinqsycVLU5YAo6L8CsEYQ0V5prg=="}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_9.2.1_1611911403238_0.23326161744932872"}, "_hasShrinkwrap": false, "publish_time": 1611911403378, "_cnpm_publish_time": 1611911403378, "_cnpmcore_publish_time": "2021-12-13T11:29:09.161Z"}, "9.2.0": {"name": "emoji-regex", "version": "9.2.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/preset-env": "^7.4.4", "@unicode/unicode-13.0.0": "^1.0.1", "mocha": "^6.1.4", "regexgen": "^1.3.0"}, "gitHead": "acc45a26fdcf85bb9ed03d0ef0a6c5e1c17177e9", "_id": "emoji-regex@9.2.0", "_nodeVersion": "12.19.0", "_npmVersion": "6.14.8", "dist": {"shasum": "a26da8e832b16a9753309f25e35e3c0efb9a066a", "size": 9300, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.0.tgz", "integrity": "sha512-DNc3KFPK18bPdElMJnf/Pkv5TXhxFU3YFDEuGLDRtPmV4rkmCjBkCSEp22u6rBHdSN9Vlp/GK7k98prmE1Jgug=="}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_9.2.0_1603213960455_0.49571742283457265"}, "_hasShrinkwrap": false, "publish_time": 1603213960635, "_cnpm_publish_time": 1603213960635, "_cnpmcore_publish_time": "2021-12-13T11:29:09.468Z"}, "9.1.1": {"name": "emoji-regex", "version": "9.1.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/preset-env": "^7.4.4", "@unicode/unicode-13.0.0": "^1.0.1", "mocha": "^6.1.4", "regexgen": "^1.3.0"}, "gitHead": "0a00b98dc1532e15d2011638127b8b2c663a3732", "_id": "emoji-regex@9.1.1", "_nodeVersion": "12.18.4", "_npmVersion": "6.14.8", "dist": {"shasum": "1d5ffce26d8191e6c3f3a9d27987b1c5bba7d20a", "size": 8139, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.1.1.tgz", "integrity": "sha512-AaWyDiNO9rbtMIcGl7tdxMcNu8SOLaDLxmQEFT5JhgKufOJzPPkYmgN2QwqTgw4doWMZZQttC6sUWVQjb+1VdA=="}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_9.1.1_1602595315386_0.32293751978809326"}, "_hasShrinkwrap": false, "publish_time": 1602595315541, "_cnpm_publish_time": 1602595315541, "_cnpmcore_publish_time": "2021-12-13T11:29:09.790Z"}, "9.0.0": {"name": "emoji-regex", "version": "9.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/preset-env": "^7.4.4", "mocha": "^6.1.4", "regexgen": "^1.3.0", "unicode-13.0.0": "^0.8.0"}, "gitHead": "cf70015c33262fdddf731cf5cda16588bb1583dc", "_id": "emoji-regex@9.0.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.13.6", "dist": {"shasum": "48a2309cc8a1d2e9d23bc6a67c39b63032e76ea4", "size": 6769, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.0.0.tgz", "integrity": "sha512-6p1NII1Vm62wni/VR/cUMauVQoxmLVb9csqQlvLz+hO2gk8U2UYDfXHQSUYIBKmZwAKz867IDqG7B+u0mj+M6w=="}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_9.0.0_1586511309638_0.7386618611323867"}, "_hasShrinkwrap": false, "publish_time": 1586511309769, "_cnpm_publish_time": 1586511309769, "_cnpmcore_publish_time": "2021-12-13T11:29:10.157Z"}, "8.0.0": {"name": "emoji-regex", "version": "8.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/plugin-proposal-unicode-property-regex": "^7.2.0", "@babel/preset-env": "^7.3.4", "mocha": "^6.0.2", "regexgen": "^1.3.0", "unicode-12.0.0": "^0.7.9"}, "gitHead": "a9f2e514523d4c0931974aff5059052da10c52c5", "_id": "emoji-regex@8.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"shasum": "e818fd69ce5ccfcb404594f842963bf53164cc37", "size": 6664, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_8.0.0_1551812302894_0.8728129094317747"}, "_hasShrinkwrap": false, "publish_time": 1551812303040, "_cnpm_publish_time": 1551812303040, "_cnpmcore_publish_time": "2021-12-13T11:29:10.452Z"}, "7.0.3": {"name": "emoji-regex", "version": "7.0.3", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0", "@babel/preset-env": "^7.0.0", "mocha": "^5.2.0", "regexgen": "^1.3.0", "unicode-11.0.0": "^0.7.7", "unicode-tr51": "^9.0.1"}, "gitHead": "3cbaf44e18f4996464ce97f1484ef425bbe8864a", "_id": "emoji-regex@7.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.4.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"shasum": "933a04052860c85e83c122479c4748a8e4c72156", "size": 6001, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-7.0.3.tgz", "integrity": "sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA=="}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_7.0.3_1545388005752_0.10144054418270465"}, "_hasShrinkwrap": false, "publish_time": 1545388005903, "_cnpm_publish_time": 1545388005903, "_cnpmcore_publish_time": "2021-12-13T11:29:10.821Z"}, "7.0.2": {"name": "emoji-regex", "version": "7.0.2", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "./src/index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0", "@babel/preset-env": "^7.0.0", "mocha": "^5.2.0", "regexgen": "^1.3.0", "unicode-11.0.0": "^0.7.7", "unicode-tr51": "^9.0.1"}, "gitHead": "880a35684329a35296a80c133acb765d351b0f55", "_id": "emoji-regex@7.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.4.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"shasum": "7e08c8797b2f7ecf422fe62714b6214f0256be42", "size": 5947, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-7.0.2.tgz", "integrity": "sha512-I+SKlWNCfHZZakO0+3HOM8RdrfByyKb4HGf+QfpQq2qwyThq2wyEIyZnt9OINAe8ug2PvoqJBXqDM7/ApjuXBw=="}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_7.0.2_1545304137084_0.3727673379301799"}, "_hasShrinkwrap": false, "publish_time": 1545304137200, "_cnpm_publish_time": 1545304137200, "_cnpmcore_publish_time": "2021-12-13T11:29:11.193Z"}, "7.0.1": {"name": "emoji-regex", "version": "7.0.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0", "@babel/preset-env": "^7.0.0", "mocha": "^5.2.0", "regexgen": "^1.3.0", "unicode-11.0.0": "^0.7.7", "unicode-tr51": "^9.0.1"}, "gitHead": "cf33819f8c6be57a48518acdb04af9158b910f98", "_id": "emoji-regex@7.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"shasum": "5a132b28ebf84a289ba692862f7d4206ebcd32d0", "size": 5940, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-7.0.1.tgz", "integrity": "sha512-cjx7oFbFIyZMpmWaEBnKeJXWAVzjXwK6yHiz/5X73A2Ww4pnabw+4ZaA/MxLroIQQrB3dL6XzEz8P3aZsSdj8Q=="}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_7.0.1_1535743440114_0.7775245713596617"}, "_hasShrinkwrap": false, "publish_time": 1535743440344, "_cnpm_publish_time": 1535743440344, "_cnpmcore_publish_time": "2021-12-13T11:29:11.634Z"}, "7.0.0": {"name": "emoji-regex", "version": "7.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js", "text.js", "es2015/index.js", "es2015/text.js"], "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.49", "@babel/core": "^7.0.0-beta.49", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0-beta.49", "@babel/preset-env": "^7.0.0-beta.49", "mocha": "^5.2.0", "regexgen": "^1.3.0", "unicode-11.0.0": "^0.7.7", "unicode-tr51": "^9.0.1"}, "gitHead": "7e67d4038143c27bc94bfaf175364d765d641c24", "_id": "emoji-regex@7.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"shasum": "7c25d9bb46480f9a5a80ab6a2a12935bdde6cb6c", "size": 5940, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-7.0.0.tgz", "integrity": "sha512-lnvttkzAlYW8WpFPiStPWyd/YdS02cFsYwXwWqnbKY43fMgUeUx+vzW1Zaozu34n4Fm7sxygi8+SEL6dcks/hQ=="}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_7.0.0_1528449135041_0.5583032630325226"}, "_hasShrinkwrap": false, "publish_time": 1528449135142, "_cnpm_publish_time": 1528449135142, "_cnpmcore_publish_time": "2021-12-13T11:29:12.042Z"}, "6.5.1": {"name": "emoji-regex", "version": "6.5.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js", "text.js", "es2015/index.js", "es2015/text.js"], "scripts": {"build": "babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "clean": "rm -rf -- index.js text.js es2015", "prepublish": "npm run clean && npm run build", "test": "npm run build && mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-core": "^6.25.0", "babel-plugin-transform-unicode-property-regex": "^2.0.2", "babel-preset-env": "^1.6.0", "mocha": "^3.4.2", "regexgen": "^1.3.0", "unicode-tr51": "^8.1.2"}, "gitHead": "ebba6b6eb6da0d2b3bb1b4be383e4be6bd53fc3a", "_id": "emoji-regex@6.5.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "9baea929b155565c11ea41c6626eaa65cef992c2", "size": 5896, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.5.1.tgz", "integrity": "sha512-PAHp6TxrCy7MGMFidro8uikr+zlJJKJ/Q6mm2ExZ7HwkyR9lSVFfE3kt36qcwa24BQL7y0G9axycGjK1A/0uNQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex-6.5.1.tgz_1501076526981_0.06240403279662132"}, "directories": {}, "publish_time": 1501076527910, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501076527910, "_cnpmcore_publish_time": "2021-12-13T11:29:12.407Z"}, "6.5.0": {"name": "emoji-regex", "version": "6.5.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js", "text.js", "es2015/index.js", "es2015/text.js"], "scripts": {"build": "babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "clean": "rm -rf -- index.js text.js es2015", "prepublish": "npm run clean && npm run build", "test": "npm run build && mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-core": "^6.25.0", "babel-plugin-transform-unicode-property-regex": "^2.0.2", "babel-preset-env": "^1.5.2", "mocha": "^3.4.2", "regexgen": "^1.3.0", "unicode-tr51": "^8.1.2"}, "gitHead": "3233d998ef33161dd43dbdefe340767b80ce4712", "_id": "emoji-regex@6.5.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.4", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "c1699e17f34154f7922219ea712ea76a2619c77b", "size": 5198, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.5.0.tgz", "integrity": "sha512-Vja85njef5T0kGfRUFkyl0etU9+49L1LNKR5oE41wAGRtJR64/a+JX3I8YCIur/uXj4Kt4cNe5i8bfd58ilgKQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex-6.5.0.tgz_1500156603081_0.8509252779185772"}, "directories": {}, "publish_time": 1500156604552, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500156604552, "_cnpmcore_publish_time": "2021-12-13T11:29:12.883Z"}, "6.4.3": {"name": "emoji-regex", "version": "6.4.3", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js", "text.js"], "scripts": {"build": "babel src -d .; node script/inject-sequences.js", "clean": "rm -f -- index.js text.js", "prepublish": "npm run clean && npm run build", "test": "npm run build && mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-core": "^6.25.0", "babel-plugin-transform-unicode-property-regex": "^2.0.2", "babel-preset-env": "^1.5.2", "mocha": "^3.4.2", "regexgen": "^1.2.4", "unicode-tr51": "^8.1.2"}, "gitHead": "e784cef2e3c18855d521a37f4a9e8a813870f0a6", "_id": "emoji-regex@6.4.3", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "6ac2ac58d4b78def5e39b33fcbf395688af3076c", "size": 4295, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.4.3.tgz", "integrity": "sha512-VyrhS7duUGY+mBDRobQtGYP+wArX2HU0FHcP/JrGUtVPf824eCDrUTe7ZQ7iWIkzdL772ZWU0NUig/pDaVeIVQ=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex-6.4.3.tgz_1498985721035_0.18083568243309855"}, "directories": {}, "publish_time": 1498985721949, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498985721949, "_cnpmcore_publish_time": "2021-12-13T11:29:13.286Z"}, "6.4.2": {"name": "emoji-regex", "version": "6.4.2", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js", "text.js"], "scripts": {"build": "babel src -d .; node script/inject-sequences.js", "clean": "rm -f -- index.js text.js", "prepublish": "npm run clean && npm run build", "test": "npm run build && mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.23.0", "babel-core": "^6.18.2", "babel-plugin-transform-unicode-property-regex": "^2.0.1", "babel-preset-env": "^1.2.2", "mocha": "^3.2.0", "regexgen": "^1.2.3", "unicode-tr51": "^8.1.1"}, "gitHead": "9f282109a58a1a1c80c1c5f75d51042a8c038647", "_id": "emoji-regex@6.4.2", "_shasum": "a30b6fee353d406d96cfb9fa765bdc82897eff6e", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "a30b6fee353d406d96cfb9fa765bdc82897eff6e", "size": 4325, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.4.2.tgz", "integrity": "sha512-/Q5UgaKxL7NZNZpZdgODUlJsadqcPCE2tAysGf2rHxW4j67rMozQUFimuj/G3r8KDeNVzNZv+N3NTh4kcAK0qA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/emoji-regex-6.4.2.tgz_1491478620524_0.9190256621222943"}, "directories": {}, "publish_time": 1491478622494, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491478622494, "_cnpmcore_publish_time": "2021-12-13T11:29:13.702Z"}, "6.4.1": {"name": "emoji-regex", "version": "6.4.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js", "text.js"], "scripts": {"build": "babel src -d .; node script/inject-sequences.js", "clean": "rm -f -- index.js text.js", "prepublish": "npm run clean && npm run build", "test": "npm run build && mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.23.0", "babel-core": "^6.18.2", "babel-plugin-transform-unicode-property-regex": "^2.0.1", "babel-preset-es2015": "^6.18.0", "mocha": "^3.2.0", "regexgen": "github:mathiasbynens/regexgen#sort-alternation", "unicode-tr51": "^8.1.1"}, "gitHead": "69b18a5c676998964dda0fc7aa1e402e820900cd", "_id": "emoji-regex@6.4.1", "_shasum": "77486fe9cd45421d260a6238b88d721e2fad2050", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "77486fe9cd45421d260a6238b88d721e2fad2050", "size": 4328, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.4.1.tgz", "integrity": "sha512-AvXfY9oj55pWmuSGBihF5oF//GtmR3EZ/3nKVMGQdddawEp1gncpf1KyQvdP5960dqwN+x5iTnUX6iexin2RlA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/emoji-regex-6.4.1.tgz_1489611909449_0.7590553292538971"}, "directories": {}, "publish_time": 1489611909685, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489611909685, "_cnpmcore_publish_time": "2021-12-13T11:29:14.138Z"}, "6.4.0": {"name": "emoji-regex", "version": "6.4.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js", "text.js"], "scripts": {"build": "babel src -d .; node script/inject-sequences.js", "clean": "rm -- index.js text.js", "prepublish": "npm run clean && npm run build", "test": "npm run build && mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.23.0", "babel-core": "^6.18.2", "babel-plugin-transform-unicode-property-regex": "^2.0.1", "babel-preset-es2015": "^6.18.0", "mocha": "^3.2.0", "regexgen": "github:mathiasbynens/regexgen#sort-alternation", "unicode-tr51": "^8.1.1"}, "gitHead": "2cf425da0f89be32a1a6c2ddd66de09592822657", "_id": "emoji-regex@6.4.0", "_shasum": "1685ef6ad557ae3f630990348cbe45889e7de9d6", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "1685ef6ad557ae3f630990348cbe45889e7de9d6", "size": 4322, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.4.0.tgz", "integrity": "sha512-Gao1K4r0OWnNMYUjJdwKPGmIMzUVygjtO6EsgmwXQ6bH5q4VPTDd/jS9+DKlvvRRwhmjCdIAm7l6rUwwibpM5g=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/emoji-regex-6.4.0.tgz_1489610740180_0.6046377806924284"}, "directories": {}, "publish_time": 1489610740423, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489610740423, "_cnpmcore_publish_time": "2021-12-13T11:29:14.611Z"}, "6.3.0": {"name": "emoji-regex", "version": "6.3.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "dist/index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "dist/index.js", "dist/text.js"], "scripts": {"build": "babel src -d dist; node script/inject-sequences.js", "clean": "rm -rf dist", "prepublish": "npm run clean && npm run build", "test": "npm run build && mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.23.0", "babel-core": "^6.18.2", "babel-plugin-transform-unicode-property-regex": "^2.0.1", "babel-preset-es2015": "^6.18.0", "mocha": "^3.2.0", "regexgen": "github:mathiasbynens/regexgen#sort-alternation", "unicode-tr51": "^8.1.1"}, "gitHead": "2bbbc81f5659c69e3630575db226a726ea99d740", "_id": "emoji-regex@6.3.0", "_shasum": "8cfd2eba5d469a308329d44ec28ec50cc22cbe50", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "8cfd2eba5d469a308329d44ec28ec50cc22cbe50", "size": 4294, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.3.0.tgz", "integrity": "sha512-7CVFaOfQvuHtazkTKy50bml9ph18GV6aTb8YoKKAxgrK6+Wfy+VGptSUIiVaGuoZHQNLaWa2361sQ2CRglAFzw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/emoji-regex-6.3.0.tgz_1489607644347_0.6887907045893371"}, "directories": {}, "publish_time": 1489607644590, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489607644590, "_cnpmcore_publish_time": "2021-12-13T11:29:15.112Z"}, "6.2.0": {"name": "emoji-regex", "version": "6.2.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "dist/index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "dist/index.js"], "scripts": {"build": "babel src -d dist; node script/inject-sequences.js", "clean": "rm -rf dist", "prepublish": "npm run clean && npm run build", "test": "npm run build && mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.23.0", "babel-core": "^6.18.2", "babel-plugin-transform-unicode-property-regex": "^2.0.1", "babel-preset-es2015": "^6.18.0", "mocha": "^3.2.0", "regexgen": "github:mathiasbynens/regexgen#sort-alternation", "unicode-tr51": "^8.1.1"}, "gitHead": "48431ce1778eef2451da34dfc6c30255f13bcec2", "_id": "emoji-regex@6.2.0", "_shasum": "6e2889a6803d85141ce01d189ec6b88022ea2122", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "6e2889a6803d85141ce01d189ec6b88022ea2122", "size": 4044, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.2.0.tgz", "integrity": "sha512-Cmoz7J4sU0/ACl6qhmp1VeBLkwi1nu0E7LBH2AcVizzdTKVar7Y9szOd8PD5ApjpmAQudwLLFCDcv8TixSdE3w=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/emoji-regex-6.2.0.tgz_1489581830190_0.2853828819934279"}, "directories": {}, "publish_time": 1489581830440, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489581830440, "_cnpmcore_publish_time": "2021-12-13T11:29:15.652Z"}, "6.1.3": {"name": "emoji-regex", "version": "6.1.3", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "dist/index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "dist/index.js"], "scripts": {"build": "babel src -d dist", "clean": "rm -rf dist", "prepublish": "npm run clean && npm run build", "test": "mocha --compilers js:babel-register", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.23.0", "babel-core": "^6.18.2", "babel-plugin-transform-unicode-property-regex": "^2.0.1", "babel-preset-es2015": "^6.18.0", "mocha": "^3.2.0", "unicode-tr51": "^8.0.1"}, "gitHead": "b8a4f39b8e5afdbe6f594f5fd1f0b7a259a1b1e8", "_id": "emoji-regex@6.1.3", "_shasum": "ec79a3969b02d2ecf2b72254279bf99bc7a83932", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "ec79a3969b02d2ecf2b72254279bf99bc7a83932", "size": 3235, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.1.3.tgz", "integrity": "sha512-73/zxHTjP2N2FQf0J5ngNjxP9LqG2krUshxYaowI8HxZQsiL2pYJc3k9/O93fc5/lCSkZv+bQ5Esk6k6msiSvg=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/emoji-regex-6.1.3.tgz_1489064354443_0.5536381872370839"}, "directories": {}, "publish_time": 1489064354686, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489064354686, "_cnpmcore_publish_time": "2021-12-13T11:29:16.210Z"}, "6.1.1": {"name": "emoji-regex", "version": "6.1.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "dist/index.js"], "scripts": {"build": "babel src -d dist", "clean": "rm -rf dist", "prepublish": "npm run clean && npm run build", "test": "mocha --compilers js:babel-register", "test:watch": "npm run test -- --watch"}, "devDependencies": {"babel-cli": "^6.23.0", "babel-core": "^6.18.2", "babel-plugin-transform-unicode-property-regex": "^2.0.1", "babel-preset-es2015": "^6.18.0", "mocha": "^3.2.0", "unicode-tr51": "^8.0.1"}, "gitHead": "7d00562619d478b133f442cc6681068438448ba0", "_id": "emoji-regex@6.1.1", "_shasum": "c6cd0ec1b0642e2a3c67a1137efc5e796da4f88e", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "c6cd0ec1b0642e2a3c67a1137efc5e796da4f88e", "size": 3729, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.1.1.tgz", "integrity": "sha512-WfVwM9e+M9B/4Qjh9SRnPX2A74Tom3WlVfWF9QWJ8f2BPa1u+/q4aEp1tizZ3vBKAZTg7B6yxn3t9iMjT+dv4w=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/emoji-regex-6.1.1.tgz_1489001442680_0.72080272086896"}, "directories": {}, "publish_time": 1489001443274, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489001443274, "_cnpmcore_publish_time": "2021-12-13T11:29:16.800Z"}, "6.1.0": {"name": "emoji-regex", "version": "6.1.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js"], "directories": {"test": "tests"}, "scripts": {"build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec", "test": "mocha tests"}, "devDependencies": {"coveralls": "^2.11.1", "istanbul": "^0.3.5", "lodash.template": "^3.2.0", "mocha": "^2.1.0", "regexgen": "^1.0.0", "string.fromcodepoint": "^0.2.1", "unicode-tr51": "^6.0.0"}, "gitHead": "3954a7729054e6f7ff64e168eb509a90ab21e2ff", "_id": "emoji-regex@6.1.0", "_shasum": "d14ef743a7dfa6eaf436882bd1920a4aed84dd94", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "d14ef743a7dfa6eaf436882bd1920a4aed84dd94", "size": 3032, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.1.0.tgz", "integrity": "sha512-NY6VHzna/d/SQ3ZKLdRBNOrc+hEoD8IkmfsPj5QDyL3HsbIgnzEv3lTO5xURIPHJ2B/wDJaSYsdOswkYnH91gg=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/emoji-regex-6.1.0.tgz_1482348417211_0.31900757434777915"}, "publish_time": 1482348417851, "_hasShrinkwrap": false, "_cnpm_publish_time": 1482348417851, "_cnpmcore_publish_time": "2021-12-13T11:29:17.341Z"}, "6.0.0": {"name": "emoji-regex", "version": "6.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js"], "directories": {"test": "tests"}, "scripts": {"build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec", "test": "mocha tests"}, "devDependencies": {"coveralls": "^2.11.1", "istanbul": "^0.3.5", "jsesc": "^0.5.0", "lodash.template": "^3.2.0", "mocha": "^2.1.0", "regenerate": "^1.2.1", "regex-trie": "^1.0.4", "string.fromcodepoint": "^0.2.1", "unicode-tr51": "^6.0.0"}, "gitHead": "3eb1bb312226bec134540902b0679b583e14ff50", "_id": "emoji-regex@6.0.0", "_shasum": "df6d704ae667c7d3af89b37dc664b3048996024d", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "df6d704ae667c7d3af89b37dc664b3048996024d", "size": 3099, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-6.0.0.tgz", "integrity": "sha512-wNzOhBSfdzYagAwIaW0mFGLxZL4KF1o5ivxoxvesRhzyoNaQkO7KAm3YqIrZciTct/yNz2I5ju4pMfW00p0tqA=="}, "publish_time": 1436011033288, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436011033288, "_cnpmcore_publish_time": "2021-12-13T11:29:17.964Z"}, "5.0.0": {"name": "emoji-regex", "version": "5.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js"], "directories": {"test": "tests"}, "scripts": {"build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec", "test": "mocha tests"}, "devDependencies": {"coveralls": "^2.11.1", "istanbul": "^0.3.5", "jsesc": "^0.5.0", "lodash.template": "^3.2.0", "mocha": "^2.1.0", "regenerate": "^1.2.1", "regex-trie": "^1.0.4", "string.fromcodepoint": "^0.2.1", "unicode-tr51": "^5.0.0"}, "gitHead": "4657bcb29fe627b16732814394e15ea7f4e75220", "_id": "emoji-regex@5.0.0", "_shasum": "dc077afcaa8f67a11170f0339c57d92867bec829", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.2.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "dc077afcaa8f67a11170f0339c57d92867bec829", "size": 2942, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-5.0.0.tgz", "integrity": "sha512-Ze3ALeZ2efknvbCGXG1YbNYkPtzPRqiB+Co+thwYuN2c5K8YYPV34u2wZb/WADcb3Ft3en4knhVEhmEGxgtACA=="}, "publish_time": 1423867580711, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423867580711, "_cnpmcore_publish_time": "2021-12-13T11:29:18.523Z"}, "4.0.0": {"name": "emoji-regex", "version": "4.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js"], "directories": {"test": "tests"}, "scripts": {"build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec", "test": "mocha tests"}, "devDependencies": {"coveralls": "^2.11.1", "istanbul": "^0.3.5", "jsesc": "^0.5.0", "lodash.template": "^2.4.1", "mocha": "^2.0.1", "regenerate": "^1.0.1", "regex-trie": "^1.0.4", "string.fromcodepoint": "^0.2.1", "unicode-tr51": "^4.0.0"}, "gitHead": "d8d883a9a761156ded461d6b710aa0dce361069f", "_id": "emoji-regex@4.0.0", "_shasum": "f54e520d45f308c9094e15ba401b76ec2d41d839", "_from": ".", "_npmVersion": "2.1.6", "_nodeVersion": "0.10.32", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "f54e520d45f308c9094e15ba401b76ec2d41d839", "size": 2905, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-4.0.0.tgz", "integrity": "sha512-yFgtcAL3geDoppx3Xobh0ui7lCdKMdfiOIoW8PRKwbD04K5YbRzxJ2K/3A+OEJtaISgBAijKq2nPAoc49RIk4w=="}, "publish_time": 1418858994222, "_hasShrinkwrap": false, "_cnpm_publish_time": 1418858994222, "_cnpmcore_publish_time": "2021-12-13T11:29:19.119Z"}, "3.0.0": {"name": "emoji-regex", "version": "3.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js"], "directories": {"test": "tests"}, "scripts": {"build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec", "test": "mocha tests"}, "devDependencies": {"coveralls": "^2.11.1", "istanbul": "^0.3.2", "jsesc": "^0.5.0", "lodash.template": "^2.4.1", "mocha": "^2.0.1", "regenerate": "^1.0.1", "regex-trie": "^1.0.4", "string.fromcodepoint": "^0.2.1", "unicode-tr51": "^3.0.0"}, "gitHead": "2322260dd72b062566859fa398f2bd31d43890b5", "_id": "emoji-regex@3.0.0", "_shasum": "d89af9ee660ea0780a5d8b09a415872f2a9d3add", "_from": ".", "_npmVersion": "2.1.6", "_nodeVersion": "0.10.32", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "d89af9ee660ea0780a5d8b09a415872f2a9d3add", "size": 2906, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-3.0.0.tgz", "integrity": "sha512-p29WyNdX+v/q7b75S8nInYQx3wZLYdIZ3/HzehylXtMEFutYaYIEnucOWwH+k16NVAF8vIKsZ6E/mAzb0J70Bw=="}, "publish_time": 1417088974181, "_hasShrinkwrap": false, "_cnpm_publish_time": 1417088974181, "_cnpmcore_publish_time": "2021-12-13T11:29:19.648Z"}, "2.0.0": {"name": "emoji-regex", "version": "2.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js"], "directories": {"test": "tests"}, "scripts": {"build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec", "test": "mocha tests"}, "devDependencies": {"coveralls": "^2.11.1", "istanbul": "^0.3.2", "jsesc": "^0.5.0", "lodash.template": "^2.4.1", "mocha": "^2.0.1", "regenerate": "^1.0.1", "regex-trie": "^1.0.4", "string.fromcodepoint": "^0.2.1", "unicode-tr51": "^2.0.0"}, "gitHead": "c57ed120f3619431861519fbf2621b12b966c87d", "_id": "emoji-regex@2.0.0", "_shasum": "07c6304da0ec921f5cdcf01c2f1894402eaed137", "_from": ".", "_npmVersion": "2.1.6", "_nodeVersion": "0.10.32", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "07c6304da0ec921f5cdcf01c2f1894402eaed137", "size": 2900, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-2.0.0.tgz", "integrity": "sha512-QokNpTlvWXnJdOkHlieB9LNECKwQOSW1Yp+otylcg540ejghf33ErARj3zRuOhb/bXbU8iffFlY6bQKzFL+Kvg=="}, "publish_time": 1415082584152, "_hasShrinkwrap": false, "_cnpm_publish_time": 1415082584152, "_cnpmcore_publish_time": "2021-12-13T11:29:20.202Z"}, "1.0.1": {"name": "emoji-regex", "version": "1.0.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js"], "directories": {"test": "tests"}, "scripts": {"build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec", "test": "mocha tests"}, "devDependencies": {"coveralls": "^2.11.1", "istanbul": "^0.3.2", "jsesc": "^0.5.0", "lodash.template": "^2.4.1", "mocha": "^1.21.4", "regenerate": "^1.0.0", "regex-trie": "^1.0.3", "string.fromcodepoint": "^0.2.1", "unicode-tr51": "^1.0.0"}, "gitHead": "971ea0284a72ed083572e8dfc7e0096ac76c9eb8", "_id": "emoji-regex@1.0.1", "_shasum": "665d27b543bf8fb66bd127d98c6939a0e723ad3f", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "665d27b543bf8fb66bd127d98c6939a0e723ad3f", "size": 2748, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-1.0.1.tgz", "integrity": "sha512-OtS7fAIqlXc81A1aK9vXBoKTmqE3L6CqePgEUCE96v3FA3GOJMevbbehEZLtTTIROEmIXkEnu3EQAC4uJrYUdQ=="}, "publish_time": 1412005123645, "_hasShrinkwrap": false, "_cnpm_publish_time": 1412005123645, "_cnpmcore_publish_time": "2021-12-13T11:29:20.853Z"}, "1.0.0": {"name": "emoji-regex", "version": "1.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "files": ["LICENSE-MIT.txt", "index.js"], "directories": {"test": "tests"}, "scripts": {"build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec", "test": "mocha tests"}, "devDependencies": {"coveralls": "^2.11.1", "istanbul": "^0.3.2", "jsesc": "^0.5.0", "lodash.template": "^2.4.1", "mocha": "^1.21.4", "string.fromcodepoint": "^0.2.1", "unicode-tr51": "^1.0.0"}, "gitHead": "2915f75504a7ff51d0bb487fe11ee7d35a461d5d", "_id": "emoji-regex@1.0.0", "_shasum": "50698ade6de03754e726f33b51ef609c18b7c449", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "dist": {"shasum": "50698ade6de03754e726f33b51ef609c18b7c449", "size": 3127, "noattachment": false, "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-1.0.0.tgz", "integrity": "sha512-DEI01motGwoeyaiAh4+ndb7cyB1RSdIt+9+5m8acTihMdDuMdGVhg3NdBazziLgumWrfNk5az3fwtjvqQUXH3A=="}, "publish_time": 1411902647034, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411902647034, "_cnpmcore_publish_time": "2021-12-13T11:29:21.478Z"}, "10.0.1": {"name": "emoji-regex", "version": "10.0.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "node script/build.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@unicode/unicode-14.0.0": "^1.2.1", "emoji-test-regex-pattern": "^1.7.2", "mocha": "^9.1.2"}, "gitHead": "be333b33e32e21cb085c8a190f855f9037c0bcf2", "_id": "emoji-regex@10.0.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-cLuZzriSWVE+rllzeq4zpUEoCPfYEbQ6ZVhIq+ed6ynWST7Bw9XnOr+bKWgCup4paq72DI21gw9M3aaFkm4HAw==", "shasum": "77180edb279b99510a21b79b19e1dc283d8f3991", "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-10.0.1.tgz", "fileCount": 5, "unpackedSize": 22371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJd01ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+hw//X2lhQ+YdxYTABgLnV+afUZeDtFru4q/U19EGZc8snCYvO/0m\r\nuJFDIX12nearl08jWJk7o0ZY94AzQ70AmLHkRw2z1MaEVswK3lE8RftlILjp\r\nmVN1ahkRo9I73sxdcnB5B9m4Zvfy8jSxdsDL9NYZQkTzA1JTPnPVuXSSNJEz\r\n61hvT8X1LPmRcRc8n+dF6ECjZWn89xQq/Wy/Izv/I5EqPSQLDoICW1azRkMR\r\neuHOQbbswoN64D9BU43aKzn1WflODhi4mEnRUAL79l3CTBfH7NW4xPKAa8cJ\r\noNyH8UI26/5xcJLZ3Fg4sLpUu251tKvzGnguoxOtZAWxpiamd/aGEidoVt6K\r\nVW122E7RjRBbdKG8htQPkiq19UvTygOuyY+6l6rbT/ZolTiBZAsy4kYMOhaW\r\nBaHukfzxI7g9qTrE0TpuRduHBcffF+iWNO17kmqIaDxTKhTHa/PX5kcNAmsp\r\njnb7ADL/A9FPcLG0nSAMuPJDQOMwHKkR2uacLiX6jnJrlLNuNqdYCMbjlIy8\r\nuGoo0E9CS5Qh4u5wa17ymmYSTqQB1gxve0Den8kzt3oVrlPuGsvWYOEEhmph\r\nY96IqMtg6AfHuALNzJYH1Fg6FQUJcz1KpUvKVcHDFGx98GCRnq7gw6PBL5K1\r\nYBkIjjdK8rboYDcskR5s2sdbDBGDbTvXNnU=\r\n=OHD7\r\n-----END PGP SIGNATURE-----\r\n", "size": 5150}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_10.0.1_1646648628924_0.9085361564769059"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-07T10:36:11.049Z"}, "10.1.0": {"name": "emoji-regex", "version": "10.1.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "node script/build.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@unicode/unicode-14.0.0": "^1.2.1", "emoji-test-regex-pattern": "^2.0.0", "mocha": "^9.2.2"}, "gitHead": "d342252ff3ff2e8912a686a1e77b0cb672530844", "_id": "emoji-regex@10.1.0", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-xAEnNCT3w2Tg6MA7ly6QqYJvEoY1tm9iIjJ3yMKK9JPlWuRHAMoe5iETwQnx3M9TVbFMfsrBgWKR+IsmswwNjg==", "shasum": "d50e383743c0f7a5945c47087295afc112e3cf66", "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-10.1.0.tgz", "fileCount": 5, "unpackedSize": 17432, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE9bKulpuIaGofthOg6rhMtX8n4HYnkrTVdOj05py1TZAiEAkXfB8g5fsXFnJgzIm/DCrGqePZQAG1CkVAOt5b/Qu0g="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRtgKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpS9g//QRVxHimrZXwYOvMrIlc67eFBNsYBWaJR3CMx/TaLHW0G+dmt\r\nzedh0khhO+PgO4EE7TeOr/aI1E43XjECrNwpX0VcxufVFzy42J0kBt7Gh67N\r\nojUxuVIpwaBapN66jYJKoAcUM4cuilIYAbryyIyGLx2fsDiO/T41QMl8UKyo\r\nUYJxKmyo6WOyEwL1NC26FyB1Vo7LS8greqwEw4RRaLbxxY6ACmF2akH3EXvl\r\n+C471Xb3CaLsFrq9BdrdP8MI01JxhRXQvFViZVdhF5+/xYYoP3Xaom9O49hB\r\nkazJbZ69jnNtVTBmKItNbOSJIWKgO2Ax3FB1y+9Xq15AGSGn9PXtGcnwi+W3\r\n3a9ekgcmgyaa0r02jwIs6F60m3yP4TqLTI7oP6pqenEKy2obasTHLDTvA+Ji\r\nBoZ8rxuVFnNDOzvzuSgW0U3R6z4sotibJIMFQcUe9Fkf9kPex57hbU1doXMc\r\ndL7zZV9MZ1MDywMCfmaRKsx7eS7QFoRB/ZSV8RIxrfZTQmXnFjACz/YF0i5e\r\nu2mWcrn5eYkEwdAQgGb/4GjisDV8UhOTIHTCOhA42Ma3E1X67Ssw9Kpik7Qw\r\nWdO5RwVh4XWK/T5EfdATuSsAnB//lxFL4Oqp90dtbimGL/zQj2q23sfZSMdq\r\nGdr3HVs1mjdwUp6QdpcYU65SKuRYV5Gb32E=\r\n=E9Bn\r\n-----END PGP SIGNATURE-----\r\n", "size": 4866}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_10.1.0_1648809994129_0.7557956790845277"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-01T10:52:24.165Z"}, "10.2.0": {"name": "emoji-regex", "version": "10.2.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "node script/build.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@unicode/unicode-15.0.0": "^1.3.1", "emoji-test-regex-pattern": "^2.0.2", "mocha": "^10.0.0"}, "gitHead": "a5207f2484d2ac9ac1975fbcb3d2a1f9e2cd76bc", "_id": "emoji-regex@10.2.0", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-vum/WOT0ZbG61R3bmfZ+Yw+MsOC7maogn5mjBiY9m4Ga9d/jEEr2hB+DXM+/T/jEpWQAgkB+cfYOrcbRRxltVQ==", "shasum": "a027bae7f5624a726f41c219783a4401e6a73f4b", "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-10.2.0.tgz", "fileCount": 5, "unpackedSize": 17440, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVzK/zNgYK002RdNXrrylzEl/WSf4uXTp7icCcuHbYYAIhAPBRLWcifvAdH8hTSE/LO/aBbZJXSMOKgwce3TjB9u2x"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNabbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoWA/+P4r3oxbVKpMdHjxCFjmKyFW1W9qbJVHZl6mwDFpzXlsuMOmW\r\nw+rfOI0AUsLafg5Llf1//2/5kc1aQSPgmnrvDHXV1pDuZstZT6FLsS3CJ6t9\r\nwEkHKu/wIdvBytFnRUWN/u33Sb3BlNerfYQm9M+5gm5kUuu2xNlpOU8eE8qb\r\nDEyxpkLMiP1gvYRwPKCG10MYgAsfFDXqpWxlyAezLoiI8WnM59xmbnezuNnY\r\nfn6RrNWGbuBPTd4FHLC5Zu7Wl1RCk2a7xwChXuP9CQ620dn9HCUor+vxmG3a\r\nZkPDnrR09G8K0Gr4rl+GsaNA2cUA9mjzrxsTZG5r9qSZlOzQateXpeP3jRsU\r\nSDCFs/WwLgLidyCgT4TpbQDgeU7qAk7RakwuW29wHL0sO7a5L34X4JOq6US5\r\nbIXZXl2eUbt9VhC82nenwN4dprFu/AOXjHiRAXc6VFIHKQQCjayzfeSjnyV7\r\n/yRs80q1yrqPykThyeLqw56wxffSWTcfkB58bUJNoLfdaGiLNRbe5uu01bI3\r\nDSQz90ndQJIrcsI6oIl7uF6m/cjnSE9308tdoT5U9T8cJSLDs05tON/AFN+J\r\nyF89ItQA+UXnbQ5/9sjvwM3vU06zd6cYagwENeLRKkvpvDe7wP1RmyCxFvnE\r\nNTiE5s+xksGOaUvmRhtrZAtP4nyx+Cal3Dk=\r\n=8LY+\r\n-----END PGP SIGNATURE-----\r\n", "size": 4880}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_10.2.0_1664460507693_0.6045529408307782"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-29T14:16:37.662Z"}, "10.2.1": {"name": "emoji-regex", "version": "10.2.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "node script/build.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@unicode/unicode-15.0.0": "^1.3.1", "emoji-test-regex-pattern": "^2.0.2", "mocha": "^10.0.0"}, "gitHead": "6acf03a84f4ceb0728643057fd03ea8f0f970555", "_id": "emoji-regex@10.2.1", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-97g6QgOk8zlDRdgq1WxwgTMgEWGVAQvB5Fdpgc1MkNy56la5SKP9GsMXKDOdqwn90/41a8yPwIGk1Y6WVbeMQA==", "shasum": "a41c330d957191efd3d9dfe6e1e8e1e9ab048b3f", "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-10.2.1.tgz", "fileCount": 6, "unpackedSize": 29148, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGqitw9fvjFFGoiIlAH782O+mOLNOmbUlojHExFRXK26AiEAmasLPGr5rFf493M8MxGagYTLgXniN6U9y3TZly3dpa4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNeOXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmruiw//VmrdABpnOgWpe+LzUgqyz3Wuf3Jt9wxT8vFgnfBJumqt2Jht\r\ngEuCdJFVgpCfh1e7ju/vmriPHn1LHjOA9A0rFznTpWb6oK0klNpDvRbezSku\r\n94j/A57b7xHCigX6L6gIZBba40u00dVwMzpsH7zJ2hwjZ2lth755SFuSJVlh\r\n+x7WLK+lzBGaemo1qJyt1lmmcrUdpm0hpEkqD9L1mowAPAGvyYEjMhKFY6nj\r\nXejtXzSA7MkiiePsr3gkk96Bzu856TtgSjgIE4k6yHM8b/Ek3ko2+J4ggRWm\r\nF1XlIRQIf35F4mrsqyaWNGGHjgxKmiaJFsNgFBPFNY6rgTyNn0ryhY1BNL/e\r\nTBvh+eJ03zzN9a5ARYR8v6VJ65I8tOc6TQBWQnzljqGvvBt61Vc2WMvzcRyG\r\n+tEtIuY2lZdJ8FqXTD5x+dFgkg9TkLIzon5SCcE1CHwlKU/3GpLfps07jGiz\r\nAnlypXuu1XoXvWmjECeKRbKNO7J4lYuwC9xNT1KF7uIHbIzaTlVHLpo34qTn\r\nasqhYGrz4KaTvIj+HDbm0xaOz44W3rS3Vsf2QZYFLeZ5K8pIoqym+u7JMwz+\r\nAacXIE4cZv/26Foj1hmwI327JQb1Y6NOtWnGrcu3gkH+E+a9ewxSzYh8Tmo1\r\n5X9G4HOCLt2sKwict4E37nW+xCjh6S4ymBU=\r\n=8Wcx\r\n-----END PGP SIGNATURE-----\r\n", "size": 5071}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_10.2.1_1664476055777_0.26730326447221886"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-29T22:56:48.736Z"}, "10.3.0": {"name": "emoji-regex", "version": "10.3.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "node script/build.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@unicode/unicode-15.1.0": "^1.5.2", "emoji-test-regex-pattern": "^2.1.0", "mocha": "^10.0.0"}, "_id": "emoji-regex@10.3.0", "gitHead": "3a5f0fbf4448826eb5ac91283b504e1c68fc928e", "_nodeVersion": "18.18.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-QpLs9D9v9kArv4lfDEgg1X/gN5XLnf/A6l9cs8SPZLRZR3ZkY9+kwIQTxm+fsSej5UMYGE8fdoaZVIBlqG0XTw==", "shasum": "76998b9268409eb3dae3de989254d456e70cfe23", "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-10.3.0.tgz", "fileCount": 6, "unpackedSize": 31536, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBcULqmEAytnHuqH+u6WFFDhHHz1+ytqaTFdeibVLeRcAiEAnd/b374LWR/ah+eKZppCpNcbnLtu9tKpBPLWDnO6uTk="}], "size": 5191}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_10.3.0_1697533349188_0.044973383042357096"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-17T09:02:29.400Z", "publish_time": 1697533349400, "_source_registry_name": "default"}, "10.4.0": {"name": "emoji-regex", "version": "10.4.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "node script/build.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@unicode/unicode-16.0.0": "^1.0.0", "emoji-test-regex-pattern": "^2.2.0", "mocha": "^10.7.3"}, "_id": "emoji-regex@10.4.0", "gitHead": "8a6871a787a9c9441ed5a341951dcdb1fb3c1d1f", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==", "shasum": "03553afea80b3975749cfcb36f776ca268e413d4", "tarball": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-10.4.0.tgz", "fileCount": 6, "unpackedSize": 31990, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAM9EoNtjw4nz2NrmpElyoVGH5PkB1fRuQJbUBVH0HmiAiBvlrSf30DraJsF9AZ2Nj6xQQLs5QMm0UaV00DwzMdM+Q=="}], "size": 5463}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_10.4.0_1724676067217_0.16435619762092735"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-26T12:41:07.335Z", "publish_time": 1724676067335, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "homepage": "https://mths.be/emoji-regex", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "_source_registry_name": "default"}