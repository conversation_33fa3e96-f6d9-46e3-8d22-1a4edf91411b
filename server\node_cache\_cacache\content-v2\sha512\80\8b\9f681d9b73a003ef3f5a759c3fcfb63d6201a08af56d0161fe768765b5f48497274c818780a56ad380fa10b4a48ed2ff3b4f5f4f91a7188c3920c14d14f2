{"_attachments": {}, "_id": "make-dir", "_rev": "2289-61f148b2a920628a7b6f2765", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "description": "Make a directory and its parents if needed - Think `mkdir -p`", "dist-tags": {"latest": "5.0.0"}, "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "name": "make-dir", "readme": "# make-dir\n\n> Make a directory and its parents if needed - Think `mkdir -p`\n\n> [!TIP]\n> You probably want the built-in [`fsPromises.mkdir('…', {recursive: true})`](https://nodejs.org/api/fs.html#fspromisesmkdirpath-options) instead.\n\n### Advantages over `fsPromises.mkdir('…', {recursive: true})`\n\n- Supports a custom `fs` implementation.\n\n### Advantages over [`mkdirp`](https://github.com/substack/node-mkdirp)\n\n- Promise API *(Async/await ready!)*\n- Fixes many `mkdirp` issues: [#96](https://github.com/substack/node-mkdirp/pull/96) [#70](https://github.com/substack/node-mkdirp/issues/70) [#66](https://github.com/substack/node-mkdirp/issues/66)\n- CI-tested on macOS, Linux, and Windows\n- Actively maintained\n- Doesn't bundle a CLI\n- Uses the native `fs.mkdir/mkdirSync` [`recursive` option](https://nodejs.org/dist/latest/docs/api/fs.html#fs_fs_mkdir_path_options_callback) in Node.js unless [overridden](#fs)\n\n## Install\n\n```sh\nnpm install make-dir\n```\n\n## Usage\n\n```console\n$ pwd\n/Users/<USER>/fun\n$ tree\n.\n```\n\n```js\nimport {makeDirectory} from 'make-dir';\n\nconst path = await makeDirectory('unicorn/rainbow/cake');\n\nconsole.log(path);\n//=> '/Users/<USER>/fun/unicorn/rainbow/cake'\n```\n\n```console\n$ tree\n.\n└── unicorn\n    └── rainbow\n        └── cake\n```\n\nMultiple directories:\n\n```js\nimport {makeDirectory} from 'make-dir';\n\nconst paths = await Promise.all([\n\tmakeDirectory('unicorn/rainbow'),\n\tmakeDirectory('foo/bar')\n]);\n\nconsole.log(paths);\n/*\n[\n\t'/Users/<USER>/fun/unicorn/rainbow',\n\t'/Users/<USER>/fun/foo/bar'\n]\n*/\n```\n\n## API\n\n### makeDirectory(path, options?)\n\nReturns a `Promise` for the path to the created directory.\n\n### makeDirectorySync(path, options?)\n\nReturns the path to the created directory.\n\n#### path\n\nType: `string`\n\nThe directory to create.\n\n#### options\n\nType: `object`\n\n##### mode\n\nType: `integer`\\\nDefault: `0o777`\n\nThe directory [permissions](https://x-team.com/blog/file-system-permissions-umask-node-js/).\n\n##### fs\n\nType: `object`\\\nDefault: `import fs from 'node:fs'`\n\nUse a custom `fs` implementation. For example [`graceful-fs`](https://github.com/isaacs/node-graceful-fs).\n\nUsing a custom `fs` implementation will block the use of the native `recursive` option if `fs.mkdir` or `fs.mkdirSync` is not the native function.\n\n## Related\n\n- [make-dir-cli](https://github.com/sindresorhus/make-dir-cli) - CLI for this module\n- [del](https://github.com/sindresorhus/del) - Delete files and directories\n- [globby](https://github.com/sindresorhus/globby) - User-friendly glob matching\n- [cpy](https://github.com/sindresorhus/cpy) - Copy files\n- [cpy-cli](https://github.com/sindresorhus/cpy-cli) - Copy files on the command-line\n- [move-file](https://github.com/sindresorhus/move-file) - Move a file\n", "time": {"created": "2022-01-26T13:12:18.584Z", "modified": "2024-05-28T16:22:29.773Z", "3.1.0": "2020-04-22T14:59:14.524Z", "3.0.2": "2020-02-12T20:11:01.785Z", "3.0.1": "2020-02-12T17:39:49.297Z", "3.0.0": "2019-04-01T09:27:30.052Z", "2.1.0": "2019-03-03T19:40:20.829Z", "2.0.0": "2019-02-04T05:37:29.131Z", "1.3.0": "2018-05-10T13:52:55.437Z", "1.2.0": "2018-02-19T07:26:44.932Z", "1.1.0": "2017-10-22T06:21:08.477Z", "1.0.0": "2017-05-09T18:34:33.547Z", "4.0.0": "2023-06-23T11:54:08.292Z", "5.0.0": "2024-05-02T16:08:54.358Z"}, "versions": {"3.1.0": {"name": "make-dir", "version": "3.1.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^6.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^13.7.1", "ava": "^1.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^0.2.1", "tsd": "^0.11.0", "xo": "^0.25.4"}, "gitHead": "6d029fe1f75f1a02fcdd7a67f34eadf0941a424f", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@3.1.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "415e967046b3a7f1d185277d84aa58203726a13f", "size": 3793, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_3.1.0_1587567554383_0.7728968177603428"}, "_hasShrinkwrap": false, "publish_time": 1587567554524, "_cnpm_publish_time": 1587567554524, "_cnpmcore_publish_time": "2021-12-13T11:52:09.949Z"}, "3.0.2": {"name": "make-dir", "version": "3.0.2", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^6.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^13.7.1", "ava": "^1.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^0.2.1", "tsd": "^0.11.0", "xo": "^0.25.4"}, "gitHead": "66ba0d9def1b32054bbb94eaeb27a345e35abdd7", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@3.0.2", "_nodeVersion": "10.18.1", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "04a1acbf22221e1d6ef43559f43e05a90dbb4392", "size": 3812, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-3.0.2.tgz", "integrity": "sha512-rYKABKutXa6vXTXhoV18cBE7PaewPXHe/Bdq4v+ZLMhxbWApkFFplT0LcbMW+6BbjnQXzZ/sAvSE/JdguApG5w=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_3.0.2_1581538261609_0.9143599805378171"}, "_hasShrinkwrap": false, "publish_time": 1581538261785, "_cnpm_publish_time": 1581538261785, "_cnpmcore_publish_time": "2021-12-13T11:52:10.220Z"}, "3.0.1": {"name": "make-dir", "version": "3.0.1", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^6.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^13.7.1", "ava": "^1.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^0.2.1", "tsd": "^0.11.0", "xo": "^0.25.4"}, "gitHead": "cde3270aa23b6bf094feeb90a34779e1da4880db", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@3.0.1", "_nodeVersion": "10.18.1", "_npmVersion": "6.13.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "584a4ee258a41b8bdd56665e5acd888f5168afed", "size": 3808, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-3.0.1.tgz", "integrity": "sha512-NFCymdWS4z/T+3AQ6tjsknt/+4hikbOe6zdq9ZBoPFfoX1S3fthRUbQWzZhI4WZBOM0kQ0GkhdCdfdan3HK+wg=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_3.0.1_1581529189182_0.6326104602385865"}, "_hasShrinkwrap": false, "publish_time": 1581529189297, "_cnpm_publish_time": 1581529189297, "_cnpmcore_publish_time": "2021-12-13T11:52:10.538Z"}, "3.0.0": {"name": "make-dir", "version": "3.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^6.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^11.12.2", "ava": "^1.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^13.3.0", "path-type": "^4.0.0", "tempy": "^0.2.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "b13a97d67d92b0948803bdbb9b6bfb84c21079d9", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@3.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1b5f39f6b9270ed33f9f054c5c0f84304989f801", "size": 3550, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-3.0.0.tgz", "integrity": "sha512-grNJDhb8b1Jm1qeqW5R/O63wUo4UXo2v2HMic6YT9i/HBlF93S8jkMgH7yugvY9ABDShH4VZMn8I+U8+fCNegw=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_3.0.0_1554110849895_0.2988523279475934"}, "_hasShrinkwrap": false, "publish_time": 1554110850052, "_cnpm_publish_time": 1554110850052, "_cnpmcore_publish_time": "2021-12-13T11:52:10.814Z"}, "2.1.0": {"name": "make-dir", "version": "2.1.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava && tsd-check"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^11.10.4", "ava": "^1.2.0", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^13.1.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "1c36213da8b2e13306ab4704ea861742bbbe7a40", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@2.1.0", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5f0310e18b8be898cc07009295a30ae41e91e6f5", "size": 3579, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-2.1.0.tgz", "integrity": "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_2.1.0_1551642020600_0.43649614195121145"}, "_hasShrinkwrap": false, "publish_time": 1551642020829, "_cnpm_publish_time": 1551642020829, "_cnpmcore_publish_time": "2021-12-13T11:52:11.105Z"}, "2.0.0": {"name": "make-dir", "version": "2.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "devDependencies": {"ava": "^1.2.0", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^13.1.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "^0.24.0"}, "gitHead": "c1294d718f3d82fd238e91ccbda08c9b6ad474e0", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@2.0.0", "_npmVersion": "6.5.0", "_nodeVersion": "10.15.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "648a648c64fe460621461997f7ae2b40d1f65c7e", "size": 3287, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-2.0.0.tgz", "integrity": "sha512-DCZvJtCxpfY3a0Onp57Jm0PY9ggZENfVtBMsPdXFZDrMSHU5kYCMJkJesLr0/UrFdJKuDUYoGxCpc93n4F3Z8g=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_2.0.0_1549258649050_0.2079142967111085"}, "_hasShrinkwrap": false, "publish_time": 1549258649131, "_cnpm_publish_time": 1549258649131, "_cnpmcore_publish_time": "2021-12-13T11:52:11.437Z"}, "1.3.0": {"name": "make-dir", "version": "1.3.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^3.0.0"}, "devDependencies": {"ava": "*", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^11.3.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "^0.20.0"}, "gitHead": "e962394d01198a961d42272f1e849ab601aaef1e", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@1.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "79c1033b80515bd6d24ec9933e860ca75ee27f0c", "size": 2901, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-1.3.0.tgz", "integrity": "sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_1.3.0_1525960375355_0.21501361126034468"}, "_hasShrinkwrap": false, "publish_time": 1525960375437, "_cnpm_publish_time": 1525960375437, "_cnpmcore_publish_time": "2021-12-13T11:52:11.724Z"}, "1.2.0": {"name": "make-dir", "version": "1.2.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^3.0.0"}, "devDependencies": {"ava": "*", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^11.3.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "*"}, "gitHead": "7f854b4728c2ca45fdc015cc7099dae23bcfe738", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@1.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6d6a49eead4aae296c53bbf3a1a008bd6c89469b", "size": 2900, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-1.2.0.tgz", "integrity": "sha512-aNUAa4UMg/UougV25bbrU4ZaaKNjJ/3/xnvg/twpmKROPdKZPZ9wGgI0opdZzO8q/zUFawoUuixuOv33eZ61Iw=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_1.2.0_1519025204776_0.3757507708266983"}, "_hasShrinkwrap": false, "publish_time": 1519025204932, "_cnpm_publish_time": 1519025204932, "_cnpmcore_publish_time": "2021-12-13T11:52:12.038Z"}, "1.1.0": {"name": "make-dir", "version": "1.1.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^3.0.0"}, "devDependencies": {"ava": "*", "codecov": "^2.1.0", "graceful-fs": "^4.1.11", "nyc": "^10.2.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "*"}, "gitHead": "de4a32fd806f4da0b880a8f2b3962d0ac34d7244", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@1.1.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "19b4369fe48c116f53c2af95ad102c0e39e85d51", "size": 2834, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-1.1.0.tgz", "integrity": "sha512-0Pkui4wLJ7rxvmfUvs87skoEaxmu0hCUApF8nonzpl7q//FWp9zu8W61Scz4sd/kUiqDxvUhtoam2efDyiBzcA=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir-1.1.0.tgz_1508653268298_0.4698842391371727"}, "directories": {}, "publish_time": 1508653268477, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508653268477, "_cnpmcore_publish_time": "2021-12-13T11:52:12.394Z"}, "1.0.0": {"name": "make-dir", "version": "1.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^2.3.0"}, "devDependencies": {"ava": "*", "coveralls": "^2.13.0", "graceful-fs": "^4.1.11", "nyc": "^10.2.0", "path-type": "^2.0.0", "tempy": "^0.1.0", "xo": "*"}, "gitHead": "5fea1cde511edf07f0ac8101b376269de3f4c98a", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@1.0.0", "_shasum": "97a011751e91dd87cfadef58832ebb04936de978", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "97a011751e91dd87cfadef58832ebb04936de978", "size": 2868, "noattachment": false, "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-1.0.0.tgz", "integrity": "sha512-d4rXDWLFad/RwLfoORm8T/Vtm8A9h7S8tobJ4WRo9DEJg5KjWNVFbVpzLgpX9OfzD8JiXIa7fq/p9z6HT1J9uA=="}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/make-dir-1.0.0.tgz_1494354871618_0.5163482388015836"}, "directories": {}, "publish_time": 1494354873547, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494354873547, "_cnpmcore_publish_time": "2021-12-13T11:52:12.749Z"}, "4.0.0": {"name": "make-dir", "version": "4.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^7.5.3"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^14.14.6", "ava": "^2.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^1.0.0", "tsd": "^0.13.1", "xo": "^0.34.2"}, "nyc": {"reporter": ["text", "lcov"]}, "types": "./index.d.ts", "gitHead": "54612e856661a0c2c61b8414f066ca43e407fe77", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_id": "make-dir@4.0.0", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==", "shasum": "c3c2307a771277cd9638305f915c29ae741b614e", "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-4.0.0.tgz", "fileCount": 5, "unpackedSize": 9913, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyavAwzgHVhOOZvRVjh/L5wLuYCAJ6jGlklwgtVTbMDAIgBn+Srk8Wt8KVb9Cttai1QNKT0+GD46iP7/odXoHJu/M="}], "size": 3743}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_4.0.0_1687521248134_0.053928984077646236"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-23T11:54:08.292Z", "publish_time": 1687521248292, "_source_registry_name": "default"}, "5.0.0": {"name": "make-dir", "version": "5.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["mkdir", "mkdirp", "make", "directories", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "devDependencies": {"@types/graceful-fs": "^4.1.9", "@types/node": "^20.12.8", "ava": "^6.1.2", "graceful-fs": "^4.2.11", "path-type": "^5.0.0", "tempy": "^3.1.0", "tsd": "^0.31.0", "xo": "^0.58.0"}, "ava": {"workerThreads": false}, "_id": "make-dir@5.0.0", "gitHead": "9b6308ca97bcb73e1a0d24a9c6173f21832bcc69", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-G0yBotnlWVonPClw+tq+xi4K7DZC9n96HjGTBDdHkstAVsDkfZhi1sTvZypXLpyQTbISBkDtK0E5XlUqDsShQg==", "shasum": "39b41251426eb8cff94de63bbef62e66ccbb538f", "tarball": "https://registry.npmmirror.com/make-dir/-/make-dir-5.0.0.tgz", "fileCount": 5, "unpackedSize": 9498, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbJP7p++j2JgGwk9MJ0FsdB35qc2G2A3p23nWeovUBLwIhAJPdYAQR7YEmQuUKd6iz2ybA6HmJhAvbBJljYQ0Wu5H9"}], "size": 3427}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/make-dir_5.0.0_1714666134176_0.9038903094648874"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-02T16:08:54.358Z", "publish_time": 1714666134358, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "homepage": "https://github.com/sindresorhus/make-dir#readme", "keywords": ["mkdir", "mkdirp", "make", "directories", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "_source_registry_name": "default"}