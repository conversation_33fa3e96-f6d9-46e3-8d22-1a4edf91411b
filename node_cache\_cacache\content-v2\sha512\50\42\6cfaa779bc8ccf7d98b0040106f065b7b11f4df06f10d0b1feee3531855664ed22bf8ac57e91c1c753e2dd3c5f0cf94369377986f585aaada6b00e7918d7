{"_attachments": {}, "_id": "cmd-shim", "_rev": "2516-61f1494823990e8a812f69dc", "description": "Used in npm for command line application support", "dist-tags": {"latest": "7.0.0"}, "license": "ISC", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "name": "cmd-shim", "readme": "# cmd-shim\n\nThe cmd-shim used in npm to create executable scripts on Windows,\nsince symlinks are not suitable for this purpose there.\n\nOn Unix systems, you should use a symbolic link instead.\n\n[![Build Status](https://img.shields.io/github/actions/workflow/status/npm/cmd-shim/ci.yml?branch=main)](https://github.com/npm/cmd-shim)\n[![Dependency Status](https://img.shields.io/librariesio/github/npm/cmd-shim)](https://libraries.io/npm/cmd-shim)\n[![npm version](https://img.shields.io/npm/v/cmd-shim.svg)](https://www.npmjs.com/package/cmd-shim)\n\n## Installation\n\n```\nnpm install cmd-shim\n```\n\n## API\n\n### cmdShim(from, to) -> Promise\n\nCreate a cmd shim at `to` for the command line program at `from`.\ne.g.\n\n```javascript\nvar cmdShim = require('cmd-shim');\ncmdShim(__dirname + '/cli.js', '/usr/bin/command-name').then(() => {\n  // shims are created!\n})\n```\n\n### cmdShim.ifExists(from, to) -> Promise\n\nThe same as above, but will just continue if the file does not exist.\n", "time": {"created": "2022-01-26T13:14:48.364Z", "modified": "2025-05-14T20:10:26.166Z", "4.1.0": "2021-02-05T20:45:58.188Z", "4.0.2": "2020-07-11T00:16:43.346Z", "4.0.1": "2020-01-27T21:36:13.315Z", "4.0.0": "2020-01-27T07:15:28.093Z", "3.0.3": "2019-08-21T21:03:14.693Z", "3.0.2": "2019-08-21T00:05:38.181Z", "3.0.1": "2019-08-20T19:51:23.346Z", "3.0.0": "2019-08-14T19:41:18.055Z", "2.1.0": "2019-08-12T19:09:04.312Z", "2.0.2": "2016-02-10T15:02:50.083Z", "2.0.1": "2014-09-05T16:55:49.181Z", "2.0.0": "2014-08-02T01:42:19.433Z", "1.1.2": "2014-07-11T13:26:43.248Z", "1.1.1": "2013-08-25T16:27:13.538Z", "1.0.1": "2013-07-22T16:26:42.053Z", "1.1.0": "2013-04-03T10:48:28.936Z", "1.0.0": "2013-03-31T18:09:52.437Z", "5.0.0": "2022-04-05T17:51:48.706Z", "6.0.0": "2022-10-11T18:48:45.520Z", "6.0.1": "2022-12-13T22:30:26.898Z", "6.0.2": "2023-10-18T18:25:33.406Z", "6.0.3": "2024-05-04T01:09:48.406Z", "7.0.0": "2024-08-26T22:13:29.463Z"}, "versions": {"4.1.0": {"name": "cmd-shim", "version": "4.1.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"rimraf": "~2.2.8", "tap": "^14.10.6"}, "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true}, "engines": {"node": ">=10"}, "gitHead": "c5118da34126e6639361fe9706a5ff07e726ed45", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@4.1.0", "_nodeVersion": "15.3.0", "_npmVersion": "7.5.2", "dist": {"shasum": "b3a904a6743e9fede4148c6f3800bf2a08135bdd", "size": 4274, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-4.1.0.tgz", "integrity": "sha512-lb9L7EM4I/ZRVuljLPEtUJOP+xiQVknZ4ZMpMgEp4JzNldPb27HU03hi6K1/6CoIuit/Zm/LQXySErFeXxDprw=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_4.1.0_1612557958077_0.5882411286379068"}, "_hasShrinkwrap": false, "publish_time": 1612557958188, "_cnpm_publish_time": 1612557958188, "_cnpmcore_publish_time": "2021-12-13T18:14:51.152Z"}, "4.0.2": {"name": "cmd-shim", "version": "4.0.2", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"rimraf": "~2.2.8", "tap": "^14.10.6"}, "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true}, "engines": {"node": ">=10"}, "gitHead": "92c0a16d3c8a8c53ade76b5eae8efae01dd7f41f", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@4.0.2", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.5", "dist": {"shasum": "600c8b5962eea0cfd8cb809826b9584a59380c01", "size": 4196, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-4.0.2.tgz", "integrity": "sha512-yuOHOon6oFX6kcxVl2jIkvPJsQ/yiKp9fd2dnuoBRZB9GEJ3USWAFCIqfB4xmFou93C3MjjhAprcDwrw+O29VA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_4.0.2_1594426603241_0.30368722643976276"}, "_hasShrinkwrap": false, "publish_time": 1594426603346, "_cnpm_publish_time": 1594426603346, "_cnpmcore_publish_time": "2021-12-13T18:14:51.378Z"}, "4.0.1": {"name": "cmd-shim", "version": "4.0.1", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp-infer-owner": "^1.0.2"}, "devDependencies": {"rimraf": "~2.2.8", "tap": "^14.10.6"}, "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true}, "engines": {"node": ">=10"}, "gitHead": "4a799fbd4358dd1ae73e1bb2aa51811654743949", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@4.0.1", "_nodeVersion": "13.4.0", "_npmVersion": "6.13.6", "dist": {"shasum": "8853812cd7033fae6bae2763d2721231fc784424", "size": 4196, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-4.0.1.tgz", "integrity": "sha512-exU/B+ts37psdPUgBhYZsHTGZ6kmZuy0i3L6+TG1BzvrQCfgc4VPpjnY4WxCz7tdMtgtCwXUIu7wLsTZ1LsIRg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_4.0.1_1580160973210_0.05277634182343771"}, "_hasShrinkwrap": false, "publish_time": 1580160973315, "_cnpm_publish_time": 1580160973315, "_cnpmcore_publish_time": "2021-12-13T18:14:51.580Z"}, "4.0.0": {"name": "cmd-shim", "version": "4.0.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "^1.0.3"}, "devDependencies": {"rimraf": "~2.2.8", "tap": "^14.10.6"}, "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true}, "engines": {"node": ">=10"}, "gitHead": "f35dcd50f8427e32e9622f3f64bb01d917f15ffa", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@4.0.0", "_nodeVersion": "13.4.0", "_npmVersion": "6.13.6", "dist": {"shasum": "807b82c415d77bfe05bc1026fe417c2b33a45051", "size": 4187, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-4.0.0.tgz", "integrity": "sha512-K3lQ3YlkNJTBcglJ9ubBqkk23WoaiLN0GytOUt7L/E3MzKTjaf33H3b2HHCiNA01SvTnDoamPpTmczrvOMPhWA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_4.0.0_1580109327958_0.8777105251047086"}, "_hasShrinkwrap": false, "publish_time": 1580109328093, "_cnpm_publish_time": 1580109328093, "_cnpmcore_publish_time": "2021-12-13T18:14:51.780Z"}, "3.0.3": {"name": "cmd-shim", "version": "3.0.3", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js --100", "snap": "TAP_SNAPSHOT=1 tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}, "devDependencies": {"rimraf": "~2.2.8", "tap": "^12.7.0"}, "gitHead": "bf5df1156ff856571011d0b4b8ce78fe19441a57", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@3.0.3", "_nodeVersion": "12.6.0", "_npmVersion": "6.11.1", "dist": {"shasum": "2c35238d3df37d98ecdd7d5f6b8dc6b21cadc7cb", "size": 4213, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-3.0.3.tgz", "integrity": "sha512-DtGg+0xiFhQIntSBRzL2fRQBnmtAVwXIDo4Qq46HPpObYquxMaZS4sb82U9nH91qJrlosC1wa9gwr0QyL/HypA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_3.0.3_1566421394401_0.9316181722794517"}, "_hasShrinkwrap": false, "publish_time": 1566421394693, "_cnpm_publish_time": 1566421394693, "_cnpmcore_publish_time": "2021-12-13T18:14:51.949Z"}, "3.0.2": {"name": "cmd-shim", "version": "3.0.2", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js --100", "snap": "TAP_SNAPSHOT=1 tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}, "devDependencies": {"rimraf": "~2.2.8", "tap": "^12.7.0"}, "gitHead": "c380c2ab26f1eaea1fd15cb868d5fc9ea3847076", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@3.0.2", "_nodeVersion": "12.6.0", "_npmVersion": "6.11.0", "dist": {"shasum": "159a20602ac4295ec0997243a40ac9b2df54f5e3", "size": 4200, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-3.0.2.tgz", "integrity": "sha512-x/2sXAaA1lUgUCZ9zaeOGzSkXOoN2DFxY61kgfhnIIUe/2QK8DFDnezT2drSZa/u8SKQT3vRFwTFPMMgQmH8kg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_3.0.2_1566345938047_0.8904793888273503"}, "_hasShrinkwrap": false, "publish_time": 1566345938181, "_cnpm_publish_time": 1566345938181, "_cnpmcore_publish_time": "2021-12-13T18:14:52.144Z"}, "3.0.1": {"name": "cmd-shim", "version": "3.0.1", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js --100", "snap": "TAP_SNAPSHOT=1 tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}, "devDependencies": {"rimraf": "~2.2.8", "tap": "^12.7.0"}, "gitHead": "a9ea3f8e772da68b38ceb9d55e1374d3b1d43b03", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@3.0.1", "_nodeVersion": "12.6.0", "_npmVersion": "6.11.0", "dist": {"shasum": "7b34dfbd503898b9953928a9eed1ef251fe19f88", "size": 4204, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-3.0.1.tgz", "integrity": "sha512-C5t8o5glT2rDJkfRXZXweu5+obqcCjA5OSKSFQeYBDGZo+crirMh35Uhm9Lmb1wRSinKBXOFsCN7XNyLGTve8A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_3.0.1_1566330683267_0.29721216609804335"}, "_hasShrinkwrap": false, "publish_time": 1566330683346, "_cnpm_publish_time": 1566330683346, "_cnpmcore_publish_time": "2021-12-13T18:14:52.341Z"}, "3.0.0": {"name": "cmd-shim", "version": "3.0.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js --100", "snap": "TAP_SNAPSHOT=1 tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}, "devDependencies": {"rimraf": "~2.2.8", "tap": "^12.7.0"}, "gitHead": "a8247738b11e27f5668a07374a9928445b00ebf4", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@3.0.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.3", "dist": {"shasum": "738b3532187238c4b69682345c2ad930e99b7750", "size": 6523, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-3.0.0.tgz", "integrity": "sha512-9cl9KcmjzrOZhA/mLl2qwhFaPLr6HpbbuPjMWPdDQ7EVrIOQJsI1JvzMur87mgaeCAtwRb5WNS2SBnC9uZDWwg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_3.0.0_1565811677907_0.4344263968534181"}, "_hasShrinkwrap": false, "publish_time": 1565811678055, "_cnpm_publish_time": 1565811678055, "_cnpmcore_publish_time": "2021-12-13T18:14:52.543Z"}, "2.1.0": {"name": "cmd-shim", "version": "2.1.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}, "devDependencies": {"rimraf": "~2.2.8", "tap": "^1.2.0"}, "gitHead": "37dd1e4681bcf75fd686e9f0665448f162f74790", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@2.1.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.3", "dist": {"shasum": "e59a08d4248dda3bb502044083a4db4ac890579a", "size": 12039, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-2.1.0.tgz", "integrity": "sha512-A5C0Cyf2H8sKsHqX0tvIWRXw5/PK++3Dc0lDbsugr90nOECLLuSPahVQBG8pgmgiXgm/TzBWMqI2rWdZwHduAw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_2.1.0_1565636944140_0.3777399950082776"}, "_hasShrinkwrap": false, "publish_time": 1565636944312, "_cnpm_publish_time": 1565636944312, "_cnpmcore_publish_time": "2021-12-13T18:14:52.732Z"}, "2.0.2": {"name": "cmd-shim", "version": "2.0.2", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/cmd-shim.git"}, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}, "devDependencies": {"tap": "~0.4.11", "rimraf": "~2.2.8"}, "gitHead": "8492e2a92b5062bb02a9eec509e57eea94b110a7", "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "homepage": "https://github.com/ForbesLindesay/cmd-shim", "_id": "cmd-shim@2.0.2", "_shasum": "6fcbda99483a8fd15d7d30a196ca69d688a2efdb", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6fcbda99483a8fd15d7d30a196ca69d688a2efdb", "size": 5075, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-2.0.2.tgz", "integrity": "sha512-NLt0ntM0kvuSNrToO0RTFiNRHdioWsLW+OgDAEVDvIivsYwR+AjlzvLaMJ2Z+SNRpV3vdsDrHp1WI00eetDYzw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/cmd-shim-2.0.2.tgz_1455116566936_0.7582207734230906"}, "directories": {}, "publish_time": 1455116570083, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455116570083, "_cnpmcore_publish_time": "2021-12-13T18:14:52.961Z"}, "2.0.1": {"name": "cmd-shim", "version": "2.0.1", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/cmd-shim.git"}, "license": "BSD", "dependencies": {"graceful-fs": ">3.0.1 <4.0.0-0", "mkdirp": "~0.5.0"}, "devDependencies": {"tap": "~0.4.11", "rimraf": "~2.2.8"}, "gitHead": "6f53d506be590fe9ac20c9801512cd1a3aad5974", "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "homepage": "https://github.com/ForbesLindesay/cmd-shim", "_id": "cmd-shim@2.0.1", "_shasum": "4512a373d2391679aec51ad1d4733559e9b85d4a", "_from": ".", "_npmVersion": "1.5.0-alpha-4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "4512a373d2391679aec51ad1d4733559e9b85d4a", "size": 4195, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-2.0.1.tgz", "integrity": "sha512-jMTDyjr2QTbQ2d7JPjHgG9qsIJd4Qh4EAjfb70CaRD3AIRgd3vpAmnYRnkyHJC+t/ItP3l3IhAOwJOfp2hmLxA=="}, "directories": {}, "publish_time": 1409936149181, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409936149181, "_cnpmcore_publish_time": "2021-12-13T18:14:53.185Z"}, "2.0.0": {"name": "cmd-shim", "version": "2.0.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/cmd-shim.git"}, "license": "BSD", "optionalDependencies": {"graceful-fs": "2"}, "dependencies": {"graceful-fs": "2", "mkdirp": "~0.5.0"}, "devDependencies": {"tap": "~0.4.11", "rimraf": "~2.2.8"}, "gitHead": "10bd1362edd0dddec30168b2c3cd915d89d4c0ef", "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "homepage": "https://github.com/ForbesLindesay/cmd-shim", "_id": "cmd-shim@2.0.0", "_shasum": "34e0cd2ede0505cd8b154667eee9054ee24006b4", "_from": ".", "_npmVersion": "1.5.0-alpha-4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "34e0cd2ede0505cd8b154667eee9054ee24006b4", "size": 4214, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-2.0.0.tgz", "integrity": "sha512-YeFz1Zc8GrE+gvED3s7PUjOy5eZfC+fkyocHO3i3x3pGxGh+rlB4FcB2PwxAGzM/TeKNAmkRV/64CCIFzMBvgA=="}, "directories": {}, "publish_time": 1406943739433, "_hasShrinkwrap": false, "_cnpm_publish_time": 1406943739433, "_cnpmcore_publish_time": "2021-12-13T18:14:53.389Z"}, "1.1.2": {"name": "cmd-shim", "version": "1.1.2", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/cmd-shim.git"}, "license": "BSD", "optionalDependencies": {"graceful-fs": "2"}, "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": "2"}, "devDependencies": {"tap": "~0.4.11", "rimraf": "~2.2.8"}, "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "homepage": "https://github.com/ForbesLindesay/cmd-shim", "_id": "cmd-shim@1.1.2", "dist": {"shasum": "e4f9198802e361e8eb43b591959ef4dc6cdb6754", "size": 4233, "noattachment": false, "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-1.1.2.tgz", "integrity": "sha512-ik9TmR1DTtmh30JdCqJ/KeuA+Dib47dVLGCAZTgB/1l8fLxrTSDWxdB6yPIeIk6fEDEny4Pn8ZfEckfMJHivmQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1405085203248, "_hasShrinkwrap": false, "_cnpm_publish_time": 1405085203248, "_cnpmcore_publish_time": "2021-12-13T18:14:53.589Z"}, "1.1.1": {"name": "cmd-shim", "version": "1.1.1", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/cmd-shim.git"}, "license": "BSD", "optionalDependencies": {"graceful-fs": "2"}, "dependencies": {"mkdirp": "~0.3.3", "graceful-fs": "2"}, "devDependencies": {"tap": "~0.4.1", "rimraf": "~2.1.4"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "_id": "cmd-shim@1.1.1", "dist": {"tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-1.1.1.tgz", "shasum": "348b292db32ed74c8283fcf6c48549b84c6658a7", "size": 4124, "noattachment": false, "integrity": "sha512-SNnCQQ8PntplOH5uN8Mtzd2at6OolhLOwCfC3XfJ8oEr3Nk4vzOIIU9HJa+F34hz1c2C4a9T6nmxGdG1+l/TkA=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377448033538, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377448033538, "_cnpmcore_publish_time": "2021-12-13T18:14:53.804Z"}, "1.0.1": {"name": "cmd-shim", "version": "1.0.1", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/cmd-shim.git"}, "license": "BSD", "optionalDependencies": {"graceful-fs": "2"}, "dependencies": {"mkdirp": "~0.3.3", "graceful-fs": "2"}, "devDependencies": {"tap": "~0.4.1", "rimraf": "~2.1.4"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "_id": "cmd-shim@1.0.1", "dist": {"tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-1.0.1.tgz", "shasum": "75e917c2185240854718c686346770640083d7bc", "size": 4007, "noattachment": false, "integrity": "sha512-tBFdo1ZLoJpak+/YFfBr1wp/scQmh3EwcqWV6/CoCvfoBlA+QzVGgaAFvQb34Nv+Bd+8JxrByulIa1D2J6+p5w=="}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1374510402053, "_hasShrinkwrap": false, "_cnpm_publish_time": 1374510402053, "_cnpmcore_publish_time": "2021-12-13T18:14:54.042Z"}, "1.1.0": {"name": "cmd-shim", "version": "1.1.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/cmd-shim.git"}, "license": "BSD", "optionalDependencies": {"graceful-fs": "1.2"}, "dependencies": {"mkdirp": "~0.3.3", "graceful-fs": "1.2"}, "devDependencies": {"tap": "~0.4.1", "rimraf": "~2.1.4"}, "readmeFilename": "README.md", "_id": "cmd-shim@1.1.0", "dist": {"tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-1.1.0.tgz", "shasum": "e69fe26e9a8b9040c7b61dc8ad6b04d7dbabe767", "size": 4131, "noattachment": false, "integrity": "sha512-K9heLLKmdwstkHM2g7KnLckB5hmj9ow1qGlbcXZHJUJMs5RQGszmHHrT+w2ailhK5u1AscVEI5/GrHkyng7ntQ=="}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1364986108936, "_hasShrinkwrap": false, "_cnpm_publish_time": 1364986108936, "_cnpmcore_publish_time": "2021-12-13T18:14:54.267Z"}, "1.0.0": {"name": "cmd-shim", "version": "1.0.0", "description": "Used in npm for command line application support", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/cmd-shim.git"}, "license": "MIT", "dependencies": {"graceful-fs": "~1.2.0", "rimraf": "2", "mkdirp": "~0.3.3", "slide": "1", "npmlog": "0"}, "readmeFilename": "README.md", "_id": "cmd-shim@1.0.0", "dist": {"tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-1.0.0.tgz", "shasum": "8a126dd25d7656afa12b6de343699ccfa5924dca", "size": 2432, "noattachment": false, "integrity": "sha512-+0HBwpuycH6oWFq/YhCm7l8CDh+nQOS9iJFdOnKIRHT/cu65MEu8KnyNJT+cKFptjkPov2um5CC9pg51PRrJKw=="}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1364753392437, "_hasShrinkwrap": false, "_cnpm_publish_time": 1364753392437, "_cnpmcore_publish_time": "2021-12-13T18:14:54.450Z"}, "5.0.0": {"name": "cmd-shim", "version": "5.0.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "prepublishOnly": "git push origin --follow-tags", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "dependencies": {"mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "main": "lib/index.js", "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.2"}, "gitHead": "c55b9a9a4cb3f321a9abad7d6d45e2aac2f82b08", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@5.0.0", "_nodeVersion": "16.14.2", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-qkCtZ59BidfEwHltnJwkyVZn+XQojdAySM1D1gSeh11Z4pW1Kpolkyo53L5noc0nrxmIvyFwTmJRo4xs7FFLPw==", "shasum": "8d0aaa1a6b0708630694c4dbde070ed94c707724", "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-5.0.0.tgz", "fileCount": 5, "unpackedSize": 12101, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVxNILfg0aAetw0qcMd0+qoN3s5P35UPjypGBCMyxbbwIgKa0AaNUDNIOqUtvEi7Q+MHzr+8V1p5yTHSSS7Nbxg40="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTIG0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUhQ//YoDMAPPuRtd3ih0Onr9ERPltuLgGjMtnsDfQ1pK3MhF0TAPF\r\n72KVyqHl0IJExjfBOPSxUwU4LWErUso5RDfpU6MfyyYWaB4r33DOIcn80mvR\r\nPxYTJ5NR29DRg0d7bXl77FOIyJcMCVw33pv8EOQfMLqcpLQXqZiv8Sq5Bk5F\r\nYKnkUF5Ky/qncYmamaDJkoGABcj33ItXlWWgBD4nu6dCLUPtvkNj7iNWRi3w\r\nGAadR+Jh/8UzJdn7n+NHtMjUeau68vKR/BG+GwnjKGjXr6GgbA5kYICBZ7H/\r\nmw6SmoL0TdUM6bTO5r+YNWycg8bFFFDyM5WUpimNlqrK3V1sWpAiNtkJ4JtC\r\nxIcGVaXni1rGKYR3L7e4wUft0yh8ByA421Agxf23FV0fkTV9xLIaIvsKw0+C\r\nPaioaDGQBQ+rHT0QfMkrkHc84n48UhPoxK0ToSo/Sjsw1aEDi7VE+eohsJ9L\r\nixgcnpoy9a9hqXORUl33lMQ3YINXU32znLAawiEoZ/irz2O/coGBN7K6yv/9\r\nYuJ5NN2Zg03o6MDc6e2vbdUXK2NdcElWXbJBtZ0jzL9I/4NxpGU2L5gMA/Vu\r\nT8DUZJIfTTp97FRYh3vPz8xzloUc3WXBinKqje/77uDn4SezRHGmQ+XaBD5H\r\noiVlMjpW6U+MXvjRXcEt8Rcm/pAGLUAen+A=\r\n=QaBj\r\n-----END PGP SIGNATURE-----\r\n", "size": 4462}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_5.0.0_1649181108588_0.1611595224579978"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-05T19:57:59.275Z"}, "6.0.0": {"name": "cmd-shim", "version": "6.0.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "4.5.1", "tap": "^16.0.1"}, "main": "lib/index.js", "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.5.1"}, "gitHead": "25724bfea794ebec14432278b1a4d223cc158f83", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@6.0.0", "_nodeVersion": "16.16.0", "_npmVersion": "9.0.0-pre.4", "dist": {"integrity": "sha512-wx+RWLgiSU6SCDzMtxG0Dv1lsuOcEfqq5SbqAViezaJIkR5sbveKzFU31YnWhqrJx3o3Iu3H0Rq8R00OS3oI+Q==", "shasum": "6c4eb6437defacf0ae8e9d281d5b06749730a65b", "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-6.0.0.tgz", "fileCount": 5, "unpackedSize": 11740, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDl4/hVp/AHazDbS3FNPmvQBxvW/wmTSRou5VCVPRyG5AIhAI0Psqi/vkXpbwg/CW6Mh6HDer+14bY9E6FFWj0EKMjH"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRbqNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs5A/+NkuYyQB2DkdBJ1pjPg6WfqYUf6TzJquW2FunGCF8D6fEonmK\r\n83t3OYhfRm/HlQ0zEI3JKb6/j0ozppIeAKWHnZWvVEEwBF5uSftcaOqm+IB6\r\nzMVqDTaEk5mdOJbU/zsXSVYRYBHp8bmkpoJZUYSsel2fHmMdrkSJJLOyZv2L\r\nI8OEtdFVJuxGOj82YvusfIkv8qeU052dc0CSX0qrx0BFNIYzfe02RYBC8UsT\r\nrGRsPxqaYZZOBT077EoC+qT49hD753i4CbFdtSN3MDa4FNdOCq1+FtA80t5H\r\nacy15NXqzq5WfFb3RAQYpMQaKQzNpUlYjRjQc4smtbxnZ8GN/tIxbpju5Ydg\r\ns4ENleufVKdkYsXQiCa6iELUiZhbGDvq/oi8dUoABkVAiaF2OOdsXpf38zP9\r\nA8FB54AqrZdiqLzIbSt2+qdDk/NH5xsYmMJ2feWPjYZH38u/MpspfuPhMC+V\r\n/D3zWb9uEBKKatAwyqq7ogu2VVuqXV6TJIot8w0A4NllzNhBCXnlVt5XSD6X\r\n30TC8vRnmv34h2/jAX3A5ffxVqUhfJmhf0uAtXhNIFFxQ1v86IcbNY1WMNTQ\r\nhgA5/Q6oDiJgTznAyHLYKHZoERijF5/w9DPC1m34nwoj0Typ8Go3fXawtMFh\r\n0f3BxDhgLNkfVBvHmfltma2ynu2Wo4Wk6zs=\r\n=PRZJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 4399}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_6.0.0_1665514125294_0.24720610067870985"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-14T16:57:18.095Z"}, "6.0.1": {"name": "cmd-shim", "version": "6.0.1", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "tap": "^16.0.1"}, "main": "lib/index.js", "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0"}, "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_id": "cmd-shim@6.0.1", "_nodeVersion": "16.16.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-S9iI9y0nKR4hwEQsVWpyxld/6kRfGepGfzff83FcaiEBpmvlbA2nnGe7Cylgrx2f/p1P5S5wpRm9oL8z1PbS3Q==", "shasum": "a65878080548e1dca760b3aea1e21ed05194da9d", "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-6.0.1.tgz", "fileCount": 5, "unpackedSize": 11842, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2xMeuWK/xm9Ew2UVIBiX2FTuWGfIjA7GGdfvuqDvbrgIgIfGTrJypvvfhAE8isJ+gMUQJz7PJEB6XUYxIg10wrXg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmP0CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5lQ/+NHDPk8KAioVKH06Uy2LodvcjZxA27vkibYw2QV/gSLWNODM9\r\nzBbaKN8WkjtbBUedWijIYnHzOY/TKQRZ7tNON0PPmhgwl2k/KGqVhR0Z4ZW9\r\nyCgZe93J6oKicf2TkinBHojd8PN2fK2Q9euIVHYbtv10G+7geURG43leeWjv\r\n+1mCh89awPN+Vc5+t+FfTiyWqO7C/ODM/YdJLVKxrF6+PPd5+lkuWGup+OMr\r\nyUtzuBhviPSyQQzxmqcx4+cWADiQn5YX+Dg5AUHtmMtFm/entboH88D7Wsb5\r\nTYYAlN7s0+K4SujZbQOsxSyCQPVnMIzYSCw24KFlZY1eMr5KoT6mhzD75rRz\r\nRkSaXuxph0qSVXcJXM51jueaOdnZ3TGKLnJwtYJb9j7DB7thDdsQXlwJNI1t\r\nzGxXQ1FXh0YD3MOoUni6P34qTXVDf0Jp4sJFUT3drcqMM++ETA9QX5W1GwD4\r\nmB+z8ooSlT38mDffseRlap42B5iX5L42ojO2qTj6eumwBfiozqwNVFm4CxqD\r\nJQodYorJksspzPsIcvbvS8G9A0fuE2JofvTxxcZpHSPNPfu4x95mKVoq/BPQ\r\nB7mBdbYzoc00eO3/Gk/CZZCxvt2LThk0SG2mC4nvHeqyGAAXWotKqKfX1cUI\r\nDMQkDCgHyMuoDASr6lECA7FuZuI/O1Bic0s=\r\n=dyNH\r\n-----END PGP SIGNATURE-----\r\n", "size": 4453}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_6.0.1_1670970626685_0.3301687872715562"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-13T22:35:33.616Z"}, "6.0.2": {"name": "cmd-shim", "version": "6.0.2", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.19.0", "tap": "^16.0.1"}, "main": "lib/index.js", "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.19.0", "publish": true}, "_id": "cmd-shim@6.0.2", "gitHead": "e5327c0db02e434228e3f14b01adca5e48c7ecfb", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_nodeVersion": "18.18.2", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-+FFYbB0YLaAkhkcrjkyNLYDiOsFSfRjwjY19LXk/psmMx1z00xlCv7hhQoTGXXIKi+YXHL/iiFo8NqMVQX9nOw==", "shasum": "435fd9e5c95340e61715e19f90209ed6fcd9e0a4", "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-6.0.2.tgz", "fileCount": 5, "unpackedSize": 12087, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cmd-shim@6.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+EY/0UXnXRf2Dy4YOntzlfIi5PPuF+4cqXGmWhdZQcAIhANd7Y93v8GpCt7pc7KDk6T4ByaNb5ufpWTsHs60eiq3E"}], "size": 4523}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_6.0.2_1697653533209_0.900791846305252"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-18T18:25:33.406Z", "publish_time": 1697653533406, "_source_registry_name": "default"}, "6.0.3": {"name": "cmd-shim", "version": "6.0.3", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "tap": "^16.0.1"}, "main": "lib/index.js", "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "publish": true}, "_id": "cmd-shim@6.0.3", "gitHead": "6283211cbd16527bbd575f6d1ce12b92333d696b", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_nodeVersion": "22.1.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-FMabTRlc5t5zjdenF6mS0MBeFZm0XqHqeOkcskKFb/LYCcRQ5fVgLOHVc4Lq9CqABd9zhjwPjMBCJvMCziSVtA==", "shasum": "c491e9656594ba17ac83c4bd931590a9d6e26033", "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-6.0.3.tgz", "fileCount": 5, "unpackedSize": 12150, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cmd-shim@6.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwvId1BlBKaKRHKCau3S2wGTAAKvJ2fmzcOXaBH3JQRQIhAO3HF9nzvkK6nu/U8vKJcIevmkKGoiC39wnxb5Nov+mF"}], "size": 4588}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_6.0.3_1714784988262_0.9440454992305478"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-04T01:09:48.406Z", "publish_time": 1714784988406, "_source_registry_name": "default"}, "7.0.0": {"name": "cmd-shim", "version": "7.0.0", "description": "Used in npm for command line application support", "scripts": {"test": "tap", "snap": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.1", "tap": "^16.0.1"}, "main": "lib/index.js", "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.1", "publish": true}, "_id": "cmd-shim@7.0.0", "gitHead": "693079b3c46fb7fec87cb5d976e21d266462fcfd", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "_nodeVersion": "22.7.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-rtpaCbr164TPPh+zFdkWpCyZuKkjpAzODfaZCf/SVJZzJN+4bHQb/LP3Jzq5/+84um3XXY8r548XiWKSborwVw==", "shasum": "23bcbf69fff52172f7e7c02374e18fb215826d95", "tarball": "https://registry.npmmirror.com/cmd-shim/-/cmd-shim-7.0.0.tgz", "fileCount": 5, "unpackedSize": 12172, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cmd-shim@7.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC1x4Ogdscd7KH7Gy3M/jJNC9umewuYs0Y8OIBjU3uuJAiA40ozBaWwaP2dJnWD7bDsMtfKgZkC5J8/3KGjtYSOF+g=="}], "size": 4588}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cmd-shim_7.0.0_1724710409275_0.9781473268099024"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-26T22:13:29.463Z", "publish_time": 1724710409463, "_source_registry_name": "default"}}, "author": {"name": "GitHub Inc."}, "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "homepage": "https://github.com/npm/cmd-shim#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/cmd-shim.git"}, "_source_registry_name": "default"}