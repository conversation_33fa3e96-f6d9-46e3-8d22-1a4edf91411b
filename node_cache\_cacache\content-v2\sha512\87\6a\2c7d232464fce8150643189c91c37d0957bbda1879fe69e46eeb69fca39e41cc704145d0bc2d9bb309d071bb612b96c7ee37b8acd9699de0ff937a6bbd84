{"_attachments": {}, "_id": "bin-links", "_rev": "2802-61f14a1b963ca28f5ee465c0", "description": "JavaScript package binary linker", "dist-tags": {"latest": "5.0.0", "legacy": "1.1.8"}, "license": "ISC", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "name": "bin-links", "readme": "# bin-links [![npm version](https://img.shields.io/npm/v/bin-links.svg)](https://npm.im/bin-links) [![license](https://img.shields.io/npm/l/bin-links.svg)](https://npm.im/bin-links) [![Travis](https://img.shields.io/travis/npm/bin-links.svg)](https://travis-ci.org/npm/bin-links) [![AppVeyor](https://ci.appveyor.com/api/projects/status/github/npm/bin-links?svg=true)](https://ci.appveyor.com/project/npm/bin-links) [![Coverage Status](https://coveralls.io/repos/github/npm/bin-links/badge.svg?branch=latest)](https://coveralls.io/github/npm/bin-links?branch=latest)\n\n[`bin-links`](https://github.com/npm/bin-links) is a standalone library that links\nbinaries and man pages for JavaScript packages\n\n## Install\n\n`$ npm install bin-links`\n\n## Table of Contents\n\n* [Example](#example)\n* [Features](#features)\n* [Contributing](#contributing)\n* [API](#api)\n  * [`binLinks`](#binLinks)\n  * [`binLinks.getPaths()`](#getPaths)\n  * [`binLinks.checkBins()`](#checkBins)\n\n### Example\n\n```javascript\nconst binLinks = require('bin-links')\nconst readPackageJson = require('read-package-json-fast')\nbinLinks({\n  path: '/path/to/node_modules/some-package',\n  pkg: readPackageJson('/path/to/node_modules/some-package/package.json'),\n\n  // true if it's a global install, false for local.  default: false\n  global: true,\n\n  // true if it's the top level package being installed, false otherwise\n  top: true,\n\n  // true if you'd like to recklessly overwrite files.\n  force: true,\n})\n```\n\n### Features\n\n* Links bin files listed under the `bin` property of pkg to the\n  `node_modules/.bin` directory of the installing environment.  (Or\n  `${prefix}/bin` for top level global packages on unix, and `${prefix}`\n  for top level global packages on Windows.)\n* Links man files listed under the `man` property of pkg to the share/man\n  directory.  (This is only done for top-level global packages on Unix\n  systems.)\n\n### Contributing\n\nThe npm team enthusiastically welcomes contributions and project participation!\nThere's a bunch of things you can do if you want to contribute! The [Contributor\nGuide](CONTRIBUTING.md) has all the information you need for everything from\nreporting bugs to contributing entire new features. Please don't hesitate to\njump in if you'd like to, or even ask us questions if something isn't clear.\n\n### API\n\n#### <a name=\"binLinks\"></a> `> binLinks({path, pkg, force, global, top})`\n\nReturns a Promise that resolves when the requisite things have been linked.\n\n#### <a name=\"getPaths\"></a> `> binLinks.getPaths({path, pkg, global, top })`\n\nReturns an array of all the paths of links and shims that _might_ be\ncreated (assuming that they exist!) for the package at the specified path.\n\nDoes not touch the filesystem.\n\n#### <a name=\"checkBins\"></a> `> binLinks.checkBins({path, pkg, global, top, force })`\n\nChecks if there are any conflicting bins which will prevent the linking of\nbins for the given package.  Returns a Promise that resolves with no value\nif the way is clear, and rejects if there's something in the way.\n\nAlways returns successfully if `global` or `top` are false, or if `force`\nis true, or if the `pkg` object does not contain any bins to link.\n\nNote that changes to the file system _may_ still cause the `binLinks`\nmethod to fail even if this method succeeds.  Does not check for\nconflicting `man` links.\n\nReads from the filesystem but does not make any changes.\n\n##### Example\n\n```javascript\nbinLinks({path, pkg, force, global, top}).then(() => console.log('bins linked!'))\n```\n", "time": {"created": "2022-01-26T13:18:19.897Z", "modified": "2025-06-03T07:41:15.614Z", "2.3.0": "2021-10-14T13:45:31.703Z", "2.2.1": "2020-09-30T18:12:44.386Z", "2.2.0": "2020-09-30T17:06:19.352Z", "2.1.4": "2020-09-16T23:36:33.559Z", "2.1.3": "2020-08-03T23:20:45.796Z", "1.1.8": "2020-03-24T00:17:55.754Z", "2.1.2": "2020-02-22T21:50:05.479Z", "2.1.1": "2020-02-22T18:49:09.891Z", "2.1.0": "2020-02-22T18:48:02.487Z", "2.0.0": "2020-01-31T04:12:43.897Z", "1.1.7": "2019-12-26T02:59:32.065Z", "1.1.6": "2019-12-11T18:49:05.771Z", "1.1.5": "2019-12-10T00:29:26.416Z", "1.1.4": "2019-12-09T23:08:57.998Z", "1.1.3": "2019-08-14T19:44:58.753Z", "1.1.2": "2018-03-22T23:43:53.505Z", "1.1.1": "2018-03-07T21:55:11.916Z", "1.1.0": "2017-11-20T23:58:40.904Z", "1.0.0": "2017-10-07T01:39:25.999Z", "3.0.0": "2022-01-18T22:05:48.230Z", "3.0.1": "2022-04-05T19:11:45.974Z", "3.0.2": "2022-08-11T18:57:45.068Z", "3.0.3": "2022-08-23T19:54:44.929Z", "4.0.0": "2022-10-13T18:56:46.530Z", "4.0.1": "2022-10-17T19:35:27.798Z", "4.0.2": "2023-07-11T15:57:37.751Z", "4.0.3": "2023-10-12T16:22:47.134Z", "4.0.4": "2024-05-04T01:10:10.982Z", "5.0.0": "2024-09-26T15:18:26.181Z"}, "versions": {"2.3.0": {"name": "bin-links", "version": "2.3.0", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "snap": "tap", "test": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^3.0.3"}, "devDependencies": {"mkdirp": "^1.0.3", "require-inject": "^1.4.4", "tap": "^15.0.10"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "f6506626f20019a255dcffa14e6093a5342440b2", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@2.3.0", "_nodeVersion": "14.18.0", "_npmVersion": "8.0.0", "dist": {"shasum": "1ff241c86d2c29b24ae52f49544db5d78a4eb967", "size": 7357, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-2.3.0.tgz", "integrity": "sha512-JzrOLHLwX2zMqKdyYZjkDgQGT+kHDkIhv2/IK2lJ00qLxV4TmFoHi8drDBb6H5Zrz1YfgHkai4e2MGPqnoUhqA=="}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_2.3.0_1634219131576_0.37895621427131365"}, "_hasShrinkwrap": false, "publish_time": 1634219131703, "_cnpm_publish_time": 1634219131703, "_cnpmcore_publish_time": "2021-12-13T18:14:40.188Z"}, "2.2.1": {"name": "bin-links", "version": "2.2.1", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "snap": "tap", "test": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp": "^1.0.3", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^3.0.3"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "47e3e535d595efb9cdd110f5e6936b68f34c4f60", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@2.2.1", "_nodeVersion": "14.12.0", "_npmVersion": "7.0.0-beta.12", "dist": {"shasum": "347d9dbb48f7d60e6c11fe68b77a424bee14d61b", "size": 8076, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-2.2.1.tgz", "integrity": "sha512-wFzVTqavpgCCYAh8SVBdnZdiQMxTkGR+T3b14CNpBXIBe2neJWaMGAZ55XWWHELJJ89dscuq0VCBqcVaIOgCMg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "ruyadorno", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_2.2.1_1601489564236_0.20081205462657192"}, "_hasShrinkwrap": false, "publish_time": 1601489564386, "_cnpm_publish_time": 1601489564386, "_cnpmcore_publish_time": "2021-12-13T18:14:40.582Z"}, "2.2.0": {"name": "bin-links", "version": "2.2.0", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "snap": "tap", "test": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp": "^1.0.3", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "6e100749521643409416406bfa913e481fec27c5", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@2.2.0", "_nodeVersion": "14.8.0", "_npmVersion": "7.0.0-beta.13", "dist": {"shasum": "1d8478e51a51cc00c1ff342565c4d697aafaa442", "size": 8077, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-2.2.0.tgz", "integrity": "sha512-SmANP2yFm/fWCW/MSLXS2sFKjmQakc0gyQbUbgpf1jyr3qg+ep0FUgfJpcDeiXxTZ3R2eGAzcTf/mCsIes8+OA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_2.2.0_1601485579189_0.9860213653509886"}, "_hasShrinkwrap": false, "publish_time": 1601485579352, "_cnpm_publish_time": 1601485579352, "_cnpmcore_publish_time": "2021-12-13T18:14:40.836Z"}, "2.1.4": {"name": "bin-links", "version": "2.1.4", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "snap": "tap", "test": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp": "^1.0.3", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "6c8b04413e4b29de1f5cf86e2d74ca5bc472ce7f", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@2.1.4", "_nodeVersion": "14.8.0", "_npmVersion": "7.0.0-beta.11", "dist": {"shasum": "f2fb5c9f232c1e3e301aabf5337f9d7eafa95d16", "size": 7237, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-2.1.4.tgz", "integrity": "sha512-NIi7OWd1FelUfgNERBLpgSaRbbm6+wkMRfURuvfui31N0i9jmQYFXhPxL0d8rcnnbUB2Rw+DiCY4nU1Egdrugg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_2.1.4_1600299393442_0.10872363729624479"}, "_hasShrinkwrap": false, "publish_time": 1600299393559, "_cnpm_publish_time": 1600299393559, "_cnpmcore_publish_time": "2021-12-13T18:14:41.029Z"}, "2.1.3": {"name": "bin-links", "version": "2.1.3", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "snap": "tap", "test": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp": "^1.0.3", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "cf229973bbbaaa766bfa5484a3584473f9061885", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@2.1.3", "_nodeVersion": "14.2.0", "_npmVersion": "7.0.0-beta", "dist": {"shasum": "ec32f2335f2cbce716744137af1cdb6b0b1290fd", "size": 6944, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-2.1.3.tgz", "integrity": "sha512-SPLS96y6+liCdMYz7Jpfe/zhxyPZmk6MqOUThXJLhe2/F3rnJGehG8zjphqV3ZShbpudGms4X92Ue94gUW4M1w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_2.1.3_1596496845623_0.7291094317213467"}, "_hasShrinkwrap": false, "publish_time": 1596496845796, "_cnpm_publish_time": 1596496845796, "_cnpmcore_publish_time": "2021-12-13T18:14:41.285Z"}, "1.1.8": {"name": "bin-links", "version": "1.1.8", "publishConfig": {"tag": "legacy"}, "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "posttest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js --100"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.3.0", "graceful-fs": "^4.1.15", "npm-normalize-package-bin": "^1.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"mkdirp": "^0.5.1", "nyc": "^13.1.0", "rimraf": "^2.6.3", "standard": "^10.0.3", "standard-version": "^4.4.0", "tap": "^12.1.3"}, "readmeFilename": "README.md", "gitHead": "961e3bd65c75ec9a2df05c9204963f115fa7e2f9", "_id": "bin-links@1.1.8", "_nodeVersion": "13.10.1", "_npmVersion": "6.13.7", "dist": {"shasum": "bd39aadab5dc4bdac222a07df5baf1af745b2228", "size": 7776, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.1.8.tgz", "integrity": "sha512-KgmVfx+QqggqP9dA3iIc5pA4T1qEEEL+hOhOhNPaUm77OTrJoOXE/C05SJLNJe6m/2wUK7F1tDSou7n5TfCDzQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_1.1.8_1585009075582_0.17092406550651673"}, "_hasShrinkwrap": false, "publish_time": 1585009075754, "_cnpm_publish_time": 1585009075754, "_cnpmcore_publish_time": "2021-12-13T18:14:41.542Z"}, "2.1.2": {"name": "bin-links", "version": "2.1.2", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "snap": "tap", "test": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp": "^1.0.3", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "26f70cf508c3c1130d9eed31711d90ff6cf291fd", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@2.1.2", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"shasum": "110f82e5563caf66c4483c1eb5d84043ef571660", "size": 6886, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-2.1.2.tgz", "integrity": "sha512-mtspzc/umzMiZkIRIVm93WgyT7fFVXX302qswWwlHErBVM8BgAVvB0EVOdzmtrNzs1zIKEE0dE4RR5PwtBfy7g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_2.1.2_1582408205351_0.6082970235146452"}, "_hasShrinkwrap": false, "publish_time": 1582408205479, "_cnpm_publish_time": 1582408205479, "_cnpmcore_publish_time": "2021-12-13T18:14:41.770Z"}, "2.1.1": {"name": "bin-links", "version": "2.1.1", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "snap": "tap", "test": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp": "^1.0.3", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "57a9c9c90e9cb10ceaeceb201e1766f896161656", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@2.1.1", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"shasum": "22b354faf96b2a312eebb2ac7fe509ec0fd1de04", "size": 6889, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-2.1.1.tgz", "integrity": "sha512-CmKACOnH4T2T0Ja8kd7nbGyU6MqmK1IJoeywgqV8j2FAzOPrmUpDD4GNm0rPqoj409uxHtkP+puujN3Lfu+1Ng=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_2.1.1_1582397349783_0.06977994899571205"}, "_hasShrinkwrap": false, "publish_time": 1582397349891, "_cnpm_publish_time": 1582397349891, "_cnpmcore_publish_time": "2021-12-13T18:14:42.008Z"}, "2.1.0": {"name": "bin-links", "version": "2.1.0", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "snap": "tap", "test": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp": "^1.0.3", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "ae52cdc6d64c284b00365d996c0b5599ebaf0a1d", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@2.1.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"shasum": "b26bd6c0eac564ed7cc8e26199aaef2b070a5549", "size": 7417, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-2.1.0.tgz", "integrity": "sha512-hIZrZte31Aj+dcpOIlIuEbsBGkHHAtB8xV3evHloPkA/JUGW73w1GwG+a+XJlUNk22uS6+twqb8i3+eLp6KgBg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_2.1.0_1582397282382_0.2446218163487821"}, "_hasShrinkwrap": false, "publish_time": 1582397282487, "_cnpm_publish_time": 1582397282487, "_cnpmcore_publish_time": "2021-12-13T18:14:42.202Z"}, "2.0.0": {"name": "bin-links", "version": "2.0.0", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "snap": "tap", "test": "tap"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp": "^1.0.3", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.6"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}, "gitHead": "ca8aad242a50b9ac92393ed17c1a34e048690907", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@2.0.0", "_nodeVersion": "14.0.0-pre", "_npmVersion": "6.13.7", "dist": {"shasum": "7b281e1f6c2238ae809589490c2d61380c027a8b", "size": 6692, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-2.0.0.tgz", "integrity": "sha512-VvBk9YwChMjdn9rs1c9Yt0vRwbZ0SgDBsb9pT07DAmFvuTlxqAuazKtIa4be+6ChBGI+io8gYjT+vq586foYaw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_2.0.0_1580443963761_0.5179872418786688"}, "_hasShrinkwrap": false, "publish_time": 1580443963897, "_cnpm_publish_time": 1580443963897, "_cnpmcore_publish_time": "2021-12-13T18:14:42.424Z"}, "1.1.7": {"name": "bin-links", "version": "1.1.7", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "posttest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js --100", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.3.0", "graceful-fs": "^4.1.15", "npm-normalize-package-bin": "^1.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"mkdirp": "^0.5.1", "nyc": "^13.1.0", "rimraf": "^2.6.3", "standard": "^10.0.3", "standard-version": "^4.4.0", "tap": "^12.1.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "gitHead": "32132c9855f5ad6d7abad970ed13e8c9472007bb", "_id": "bin-links@1.1.7", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.4", "dist": {"shasum": "34b79ea9d0e575d7308afeff0c6b2fc24c793359", "size": 7835, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.1.7.tgz", "integrity": "sha512-/eaLaTu7G7/o7PV04QPy1HRT65zf+1tFkPGv0sPTV0tRwufooYBQO3zrcyGgm+ja+ZtBf2GEuKjDRJ2pPG+yqA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_1.1.7_1577329171959_0.2291732676844116"}, "_hasShrinkwrap": false, "publish_time": 1577329172065, "_cnpm_publish_time": 1577329172065, "_cnpmcore_publish_time": "2021-12-13T18:14:42.604Z"}, "1.1.6": {"name": "bin-links", "version": "1.1.6", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "posttest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js --100", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.3.0", "graceful-fs": "^4.1.15", "npm-normalize-package-bin": "^1.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"mkdirp": "^0.5.1", "nyc": "^13.1.0", "rimraf": "^2.6.3", "standard": "^10.0.3", "standard-version": "^4.4.0", "tap": "^12.1.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "gitHead": "f315830130777dc0cae0c020a743b31b14b598ec", "_id": "bin-links@1.1.6", "_nodeVersion": "12.12.0", "_npmVersion": "6.13.3", "dist": {"shasum": "30d33e810829305e5e61b90cfcb9a3a4f65eb516", "size": 7773, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.1.6.tgz", "integrity": "sha512-b5rV3uVyrlrJWLI3mawUUf5t2f9mCEQm/TqT5zNj6DPYhYDZaNp0AYaYd/CVASkSEklayNDLliZHVdo2J3niPw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_1.1.6_1576090145624_0.9799907445951463"}, "_hasShrinkwrap": false, "publish_time": 1576090145771, "_cnpm_publish_time": 1576090145771, "_cnpmcore_publish_time": "2021-12-13T18:14:42.832Z"}, "1.1.5": {"name": "bin-links", "version": "1.1.5", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.0.1", "graceful-fs": "^4.1.15", "npm-normalize-package-bin": "^1.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"mkdirp": "^0.5.1", "nyc": "^13.1.0", "rimraf": "^2.6.3", "standard": "^10.0.3", "standard-version": "^4.4.0", "tap": "^12.1.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "gitHead": "52e65255ea873d875a6bcef43d288fe2646362f0", "_id": "bin-links@1.1.5", "_nodeVersion": "12.12.0", "_npmVersion": "6.13.2", "dist": {"shasum": "bbbcd1d7a8101105b927d54aa895a4cc75138169", "size": 7570, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.1.5.tgz", "integrity": "sha512-GQuxeiclIjomNfQ7LSaERfIz2RSZrF44c0cDEV+Iu4uVhyYndSSwl1RjjunxHU1dYqh+QCP4S/1/DUsjquNRhQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_1.1.5_1575937766306_0.6836856166200178"}, "_hasShrinkwrap": false, "publish_time": 1575937766416, "_cnpm_publish_time": 1575937766416, "_cnpmcore_publish_time": "2021-12-13T18:14:43.019Z"}, "1.1.4": {"name": "bin-links", "version": "1.1.4", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.0.1", "graceful-fs": "^4.1.15", "npm-normalize-package-bin": "^1.0.0", "write-file-atomic": "^2.3.0"}, "devDependencies": {"mkdirp": "^0.5.1", "nyc": "^13.1.0", "rimraf": "^2.6.3", "standard": "^10.0.3", "standard-version": "^4.4.0", "tap": "^12.1.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "gitHead": "fe39e10e447c395fd1a30b5ce20b1ddf7cc37dd0", "_id": "bin-links@1.1.4", "_nodeVersion": "12.12.0", "_npmVersion": "6.13.2", "dist": {"shasum": "9570fcc0a75fcc214cd1c7972b16b43810b9a739", "size": 7514, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.1.4.tgz", "integrity": "sha512-F3lXH2K06zlFARLYA5rnJQb5i74/JNj8wIW+9UJQbuwyL62TUzPbMlnHHp/JxSAHfxBFx41aeY7SQd7Z5hIirw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_1.1.4_1575932937875_0.5722673211812359"}, "_hasShrinkwrap": false, "publish_time": 1575932937998, "_cnpm_publish_time": 1575932937998, "_cnpmcore_publish_time": "2021-12-13T18:14:43.211Z"}, "1.1.3": {"name": "bin-links", "version": "1.1.3", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.0.1", "graceful-fs": "^4.1.15", "write-file-atomic": "^2.3.0"}, "devDependencies": {"mkdirp": "^0.5.1", "nyc": "^13.1.0", "rimraf": "^2.6.3", "standard": "^10.0.3", "standard-version": "^4.4.0", "tap": "^12.1.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "gitHead": "24324d3e3a8bf3d358b828c13016ba3446ce7fbc", "_id": "bin-links@1.1.3", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.3", "dist": {"shasum": "702fd59552703727313bc624bdbc4c0d3431c2ca", "size": 7265, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.1.3.tgz", "integrity": "sha512-TEwmH4PHU/D009stP+fkkazMJgkBNCv60z01lQ/Mn8E6+ThHoD03svMnBVuCowwXo2nP2qKyKZxKxp58OHRzxw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_1.1.3_1565811898578_0.8996417117421225"}, "_hasShrinkwrap": false, "publish_time": 1565811898753, "_cnpm_publish_time": 1565811898753, "_cnpmcore_publish_time": "2021-12-13T18:14:43.439Z"}, "1.1.2": {"name": "bin-links", "version": "1.1.2", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "write-file-atomic": "^2.3.0"}, "devDependencies": {"mkdirp": "^0.5.1", "nyc": "^11.1.0", "rimraf": "^2.6.2", "standard": "^10.0.3", "standard-version": "^4.2.0", "tap": "^10.7.2", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "files": ["index.js"], "gitHead": "3c7112104c3c4f68563a6b22b8abfee8ecb0707d", "_id": "bin-links@1.1.2", "_npmVersion": "5.8.0-next.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "fb74bd54bae6b7befc6c6221f25322ac830d9757", "size": 7267, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.1.2.tgz", "integrity": "sha512-8eEHVgYP03nILphilltWjeIjMbKyJo3wvp9K816pHbhP301ismzw15mxAAEVQ/USUwcP++1uNrbERbp8lOA6Fg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_1.1.2_1521762233444_0.20200400723633738"}, "_hasShrinkwrap": false, "publish_time": 1521762233505, "_cnpm_publish_time": 1521762233505, "_cnpmcore_publish_time": "2021-12-13T18:14:43.657Z"}, "1.1.1": {"name": "bin-links", "version": "1.1.1", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "write-file-atomic": "^2.3.0"}, "devDependencies": {"mkdirp": "^0.5.1", "nyc": "^11.1.0", "rimraf": "^2.6.2", "standard": "^10.0.3", "standard-version": "^4.2.0", "tap": "^10.7.2", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "files": ["index.js"], "gitHead": "7be31bd8a89b94d1180a35e33453fac5d6113f24", "_id": "bin-links@1.1.1", "_npmVersion": "5.7.1", "_nodeVersion": "8.9.4", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "374cd1635265efe884a2d00cf51b080045c01c75", "size": 7213, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.1.1.tgz", "integrity": "sha512-a0tIA2SEZeim5yjKZ5EJa6afsDtBLPFs+fer1fFfPjAyzXU/ZnLnlYIjAVzUxxjiowpHl0GiFBzK/wPf8QHjXA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_1.1.1_1520459711797_0.025525801835520223"}, "_hasShrinkwrap": false, "publish_time": 1520459711916, "_cnpm_publish_time": 1520459711916, "_cnpmcore_publish_time": "2021-12-13T18:14:43.833Z"}, "1.1.0": {"name": "bin-links", "version": "1.1.0", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "fs-write-stream-atomic": "^1.0.10", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "slide": "^1.1.6"}, "devDependencies": {"nyc": "^11.1.0", "standard": "^10.0.3", "standard-version": "^4.2.0", "tap": "^10.7.2", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "files": ["index.js"], "gitHead": "553e04318b40d2be342a9651a25ce6e7488c06e0", "_id": "bin-links@1.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "e0a92cb57f01c4dc1088bca2bae6be110b9f64f9", "size": 7362, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.1.0.tgz", "integrity": "sha512-3desjIEoSt86s+BRZlkLpBPPcHhr4vyUPL/+X1cQuE96NIlkELqnb4Yq+I5gZe47gHsZztA6cm38uMrT9+FWpA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links-1.1.0.tgz_1511222320787_0.32946322369389236"}, "directories": {}, "publish_time": 1511222320904, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511222320904, "_cnpmcore_publish_time": "2021-12-13T18:14:44.055Z"}, "1.0.0": {"name": "bin-links", "version": "1.0.0", "description": "JavaScript package binary linker", "main": "index.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --nyc-arg=--all --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "dependencies": {"bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "fs-write-stream-atomic": "^1.0.10", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "slide": "^1.1.6"}, "devDependencies": {"nyc": "^11.1.0", "standard": "^10.0.3", "standard-version": "^4.2.0", "tap": "^10.7.2", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "files": ["index.js"], "gitHead": "e99da7ad177e677f48b218521df5b494bd2ed110", "_id": "bin-links@1.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f71f48b2e3bbbe79865e68a9e8eef6017840130b", "size": 7246, "noattachment": false, "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-1.0.0.tgz", "integrity": "sha512-eRrGL/i19rIGpu8jMNz+C4YkFHSUTJu6Z7sCXj91dWJeC0yeeEEnmIvNpCw3wlDgXsH9tPIjk9tZc88yHSiHdQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links-1.0.0.tgz_1507340365047_0.9850793110672385"}, "directories": {}, "publish_time": 1507340365999, "_hasShrinkwrap": false, "_cnpm_publish_time": 1507340365999, "_cnpmcore_publish_time": "2021-12-13T18:14:44.269Z"}, "3.0.0": {"name": "bin-links", "version": "3.0.0", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "test": "tap", "lint": "eslint '**/*.js'", "postlint": "npm-template-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^4.0.1", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^2.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^4.0.0"}, "devDependencies": {"@npmcli/template-oss": "^2.5.0", "mkdirp": "^1.0.3", "require-inject": "^1.4.4", "tap": "^15.0.10"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"windowsCI": false, "version": "2.5.0"}, "gitHead": "674756242f36ff981d0979680c57dcafa59bc0d8", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@3.0.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-fC7kPWcEkAWBgCKxmAMqZldlIeHsXwQy9JXzrppAVQiukGiDKxmYesJcBKWu6UMwx/5GOfo10wtK/4zy+Xt/mg==", "shasum": "8273063638919f6ba4fbe890de9438c1b3adf0b7", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-3.0.0.tgz", "fileCount": 18, "unpackedSize": 21213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5zm8CRA9TVsSAnZWagAAnqsP/0gOriNjxKeUcmRblhtC\n/Rau3OBydO6pRWJ9I7rhQTOpMi+bS8eqjTegXunR06HN/t+LLHSoc+ks7Whk\n9Zgf/JUHZwP4QfsALi3llgXC3UGofWVRJUzD0RzujgysWxv/WGZnIHnIXysy\nKvdFKpPoJhEu5+SInL7JAtVXTk8Fc892a4rQ/ObCpbsJNaTbDkUU885tNS8i\nrfpIG4XQIQruN38a5LI/qFukvkgIpIF5wno/uRYPiiM51ycncLjqAyNcUz9O\nqAQ9VkoKLu8rKKkHFk5nZ1DFCQGY3B28BXoKCCMznMCC2bSB/muNzWsdSGtE\n+T1turxEjj5Eid8wIeprye/XwzLn3lju5bu1jb/qDQX8+bI93sh1WEN+fDfG\ncNHvSxJR2FI7OHSnjHIJ1gp4KFbQUt6VOVBbZh0/hnivg0I0xmGDC8M96D10\nv0LR30H3qGh4HxhqjP/lZwSvhkNupIQ2/dNbDodT2m1eMbVqeP0ZyNT7BFZ5\nyuipQvLIWc3u6Q0MAuxaQHDSNEysoGLxUwFe314xUnvuyCg/MVo221mQT6DJ\n7yARQt1Sb34XI1kI9jlRJBMndQrjAUko+2rApnRbX0zM5Jx5SO8O7P58+luD\nYA6j06lYCatl+gpFu8Nm1JaQl7f5ZjByrIfm6ybEh0p7n+x1tWFT2Y37PWoo\nKQBf\r\n=2dsk\r\n-----END PGP SIGNATURE-----\r\n", "size": 7458}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_3.0.0_1642543547999_0.9691668007751593"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-18T22:05:54.064Z"}, "3.0.1": {"name": "bin-links", "version": "3.0.1", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^5.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^3.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.2", "mkdirp": "^1.0.3", "require-inject": "^1.4.4", "tap": "^15.0.10"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "3.2.2"}, "gitHead": "492a1be753239ef5ecac379ef916da1eebc18526", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@3.0.1", "_nodeVersion": "16.14.2", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-9vx+ypzVhASvHTS6K+YSGf7nwQdANoz7v6MTC0aCtYnOEZ87YvMf81aY737EZnGZdpbRM3sfWjO9oWkKmuIvyQ==", "shasum": "cc70ffb481988b22c527d3e6e454787876987a49", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-3.0.1.tgz", "fileCount": 18, "unpackedSize": 21189, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiZGk4nR5BCggWVoF4Dcjshty0FYV2R8NYKL2z5CauuQIgLUh7mZP9STQSCGQZO8nvSJsiruG6JNkmSgNNCH50Eyo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTJRxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs1A/9FQowOBGdUhbOEqXc/4+42thNh3agpwEOMx6sFaPkcpL7GitL\r\nu21LoCKcjhPPqU2BBYo14Gn3fClRxT8sWr4TM2JPSMcJF00egbm3zuHU6X0I\r\nPSAblK5f96nzkxxkd43brkz/5ulykcAR0F+xoD7m8iAdpyzBZW7ZIia9hc54\r\nMzKbBdztY/lLLyhCyLyMd5IhHHgtL4G6P1JCD4fhv3LQT/S7eSf1X27TtX2o\r\nPbyoCn+zI2U0ULP5BlJrPZv9/sZB4xo/sdNukxxXYYixdg31qqfUDTZ6JWwn\r\nB5OPNu9GQ20xPoC5B5eG/Tfy09pgaf1bVXZcswrsyUtLuUFsi2IIX44etSZQ\r\nJEmJTvsiJdZiPGeYWYud84tmTzX4ZHqC8r2OhuNyXUbrD5TgUqi2aFX5rWAv\r\nXNcs8SkpfnwsTGBLMzxMoFac9lUzay8Te+KnfJoR3TBLwtUArconvmdhrlDn\r\n7iYh0oiI0CNshiIoINIPhJuP3ywrd3FxJG5gGl5hXwwKt35Ee7IANqjBHNrG\r\nOA8ga3szQFfkb0C58BnuE/qVR9TzStKUFGSEdGT5i/0yhEybK0NTtupjsd4u\r\nyaTR5QgO7L2+8wPNq++Mmn49npmJ49YP5gAQXopKY/CYUPhkA0ylLVNi7Em6\r\n2ce1P4lcH5VqfSylBFg8NYqHwnWv6oiwrHA=\r\n=stTN\r\n-----END PGP SIGNATURE-----\r\n", "size": 7534}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_3.0.1_1649185905845_0.6300105181506597"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-05T20:39:01.167Z"}, "3.0.2": {"name": "bin-links", "version": "3.0.2", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^5.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^1.0.0", "read-cmd-shim": "^3.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "mkdirp": "^1.0.3", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "3.5.0"}, "gitHead": "a9e00099099ee1de40f62b859a733b16c7defc6c", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@3.0.2", "_nodeVersion": "18.6.0", "_npmVersion": "8.17.0", "dist": {"integrity": "sha512-+oSWBdbCUK6X4LOCSrU36fWRzZNaK7/evX7GozR9xwl2dyiVi3UOUwTyyOVYI1FstgugfsM9QESRrWo7gjCYbg==", "shasum": "5c40f14b0742faa2ae952caa76b4a29090befcbb", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-3.0.2.tgz", "fileCount": 18, "unpackedSize": 21280, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAK+MsnA3t4CL5gB4UgKJWtPvEYDZT8jtUvQCGQfH7weAiAQXQpzDi2gGLrY78gG4bUXVl0AjDThndiDXHJ5b37Ojw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9VEpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfTw//Q46BLuPF1Xm1EsdG20Xgh3Ad68TIoaBzouoQftBKkYq1wpu+\r\nbLZnZ+aAy4agjevgfNa2M0i350JcIzQ3FbXGodIt63BZbmdQaeFDZ6WM0Qa2\r\nJR/rGE7xr9s5vUirhUMn/Dozr8CKDjOC5KS7xbLnvo/NTbzwI8vrCqFJQ2BI\r\niGqRA4d33rtKN+DkNJaBoKvRL1mQv/ckEdVDRP+A2lqrEt8vDDiOliNemUUv\r\n6ChlesBsQAjxrqw19jHK1aeWpqYa1zgpDF7onVzC/dF5dMeQ1urlEcKqhjGa\r\nrkaP5mWdB0sqdgwQXoBnQq2a5FpN2o6mIe61whk7QvDJOeVWp3Pw1kAUsubp\r\nclCfLV3qkOox2X7u+niJj29aGxeX7dWZL2P/ktI9EWyd3VC+f6lvqJjOd5cO\r\ndfIasP6e8Rz9aNJcQEf+EqtUgYQYkDuq+z3Q+NZsebxdVGF7bJ4WavxB7RaW\r\n7pXCFGO1wmvdU09pl+OgW6eH6PGwlhSSn0yRh/tddkhRkNGBHEVrlVzJukln\r\nvBhIvTc88Gni8jdKf3TfXRv0CTDYKIDlO4nZvhtqE4+wVKXueJlZEp7jQqP0\r\nhfmyinZ5rJlXmlt3u0ajDecmjDpkm8y4K95rHwl3ERXLecTbevfd3LqPGzoK\r\nnpbVrRa+R1XtjwaCN4XlTTPwzq80EQU5wYs=\r\n=60o2\r\n-----END PGP SIGNATURE-----\r\n", "size": 7555}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_3.0.2_1660244264856_0.908511027796804"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-11T18:57:50.557Z"}, "3.0.3": {"name": "bin-links", "version": "3.0.3", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^5.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^2.0.0", "read-cmd-shim": "^3.0.0", "rimraf": "^3.0.0", "write-file-atomic": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "mkdirp": "^1.0.3", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "3.5.0"}, "gitHead": "947697c41abfc2aef68a5d7aa3cc8db3f80fe43e", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@3.0.3", "_nodeVersion": "18.7.0", "_npmVersion": "8.18.0", "dist": {"integrity": "sha512-zKdnMPWEdh4F5INR07/eBrodC7QrF5JKvqskjz/ZZRXg5YSAZIbn8zGhbhUrElzHBZ2fvEQdOU59RHcTG3GiwA==", "shasum": "3842711ef3db2cd9f16a5f404a996a12db355a6e", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-3.0.3.tgz", "fileCount": 18, "unpackedSize": 21280, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBVumPzvgztJXC4fvWBcamGJvyK0UCiq2h+JVXbUdedgAiBHvza7sPE//NBxVhWC2GlGfTuweMj6U3XZuYgo/8XLZg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBTCEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqG5w//UpAHhU6L3+dqr0yZWKgK0ElpQQgN/SgaYkOwkfveV/MX5hUR\r\nI/J3VGDUKQNV6iz7DjWNWpoiZmFam6yVctAdUxveW59MMXMyc45PTEtyD3Ba\r\nYGPUoOjui7eCRyMbfPff4iRRi0NNgOE86PMjNgQnq7c31ROmtHlYewnLubaV\r\n3C3UZx2tB8kdONSSQkS1hJo7Buh7Qlhp6cEeZNP5SAT40qkSS+Y4PfalcO84\r\nafhD/V5i6jF1kpbGlsauPFr4tGz7cDm0rJ1fcDGDRwdRFgbPZGegSRm/J4tT\r\n3IAftJ17tIokcliqU4MaIfnOZc6orGvqLcod/4JWQgwJsWyCMuzB1i+bJSOk\r\n4Bc8a65D2cv/czzjIKsMLHKwjgQNjoMj288uzRX9qXS3YUF8X6PKEm+t3Yh1\r\nuwWvj9DMz3ucaqrMRIod/***************************+/zkzZ1CbkKX\r\nztd0XHHucWaC+TbJgViH4oQ48yg7hDzEwWfSTSq/vPKfcNZVMyaqS+AZLvaY\r\nWyUMpEmD6JHaeGo5MJlUMSTLq5QiDUeb8RuSQhWZbEvgFa6fzeurQg5bAKgj\r\nf7C34YT+WeAq2+TaqQHPzSnNLhAltGtphHsW0SdMheIg8K0qOuLfM2MbECaO\r\nn9rpRa551YfclXKeL3xY5f3PJxDklTxiNmA=\r\n=m4Hi\r\n-----END PGP SIGNATURE-----\r\n", "size": 7554}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_3.0.3_1661284484752_0.30233725552549195"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-23T19:54:48.225Z"}, "4.0.0": {"name": "bin-links", "version": "4.0.0", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"snap": "tap", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^6.0.0", "npm-normalize-package-bin": "^2.0.0", "read-cmd-shim": "^3.0.0", "write-file-atomic": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "4.5.1", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.5.1"}, "gitHead": "928858911af48d98c2dbde8dd6dbf0496e72b1b8", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@4.0.0", "_nodeVersion": "16.16.0", "_npmVersion": "9.0.0-pre.4", "dist": {"integrity": "sha512-myOlk+Sj0GCKAB+47BeuR08cv3YPtLnmDPFNpTuqjYh/2qOft42q+i0Alvfk+Lj3900iljb6jgDGl50oFN078A==", "shasum": "f9122f8d0eddca07d014d6ced2a6021de7b4da1f", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-4.0.0.tgz", "fileCount": 18, "unpackedSize": 20679, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGMCKiHcO1A50bb8SLwilXOk8KSbb/02YszM+VfRxbsYAiEAuPi5l7vaUdvR857MFtO/IIQ4erENHqCGSTDk2JoYJoQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSF9uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojZw//SdJ2xyg/yEyl4DRVoUIn5CejW1q6mha7O2ArH1poWUQSbJ07\r\nn/MIxDRQ0CVyzRFw9M5fRwjD8siV6vetA6O8Hwiu4wmqMw+9drHfskGyzVWq\r\n83wgpdrxnYwcI0Jymii9s3BVrExT+J2oHRyUC2atamafloVoxOYYoFfwyRSz\r\noQvRpwHZk08nP7r5jpfzXggLC9/qUj3WxP5R9dEFPBkUIGztg0cHUBH/33Yn\r\nhACn4bEYao1zViI0RInVtgfHhBTirPwfZEKxm3cLJiSFyb3spbEaNJJZ+2YX\r\nKB+BE5FyDem9dEhqAylUfXlmsBJVEpGmX8gp2dR0WC/bqTTPvQ05rmDyBunv\r\nrPmJ0ofOg4ALOnVKVSqZdMva8vt0Di2mHerAKJjcLbJxG21f9ZxuDEF/lPFS\r\nkBKp0qkvuR2y4pHBbYdwFNRTf+mkz3ac6M/LbA2i1GGEM2ag9WxbMC7g8RtE\r\nPxmGdh84wJQ3uu3bTa1LRSTtCEngh+/a6pj2X0wIsEF6ptP5Ulw/eAlwlXU6\r\nvUHkYOrMwlUeTHlHqI6EFL3+4VJcncJcIlU+w4ro0bObeB5nlclojqYmotit\r\nm2jMdLHWN48P61nSORQdsHP4VoYSOyR70e7luDnMn00gBC62QGh1JpYmHRJr\r\noIImD6n9i4SQIbb7V4+HFkEpRhNDh57rzI8=\r\n=BPia\r\n-----END PGP SIGNATURE-----\r\n", "size": 7447}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_4.0.0_1665687406360_0.*********3957751"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-14T19:22:19.503Z"}, "4.0.1": {"name": "bin-links", "version": "4.0.1", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"snap": "tap", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^6.0.0", "npm-normalize-package-bin": "^3.0.0", "read-cmd-shim": "^4.0.0", "write-file-atomic": "^5.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.5.1", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.5.1"}, "gitHead": "9f424c46dbba96fcf659a252e67b59fb026db61d", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_id": "bin-links@4.0.1", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-bmFEM39CyX336ZGGRsGPlc6jZHriIoHacOQcTt72MktIjpPhZoP4te2jOyUXF3BLILmJ8aNLncoPVeIIFlrDeA==", "shasum": "afeb0549e642f61ff889b58ea2f8dca78fb9d8d3", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-4.0.1.tgz", "fileCount": 18, "unpackedSize": 20679, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvcaigXRbW5q7m/LLGZU59WWsfcs/GpUVUZL6cd53eXQIgZdbevWYCG14RtGpWNcVmb8Rvh6xIwfJiNDx+RACANfg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTa5/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCDA//aMd2kbrR29+083YLVlU62kuID476dAv5uwiYtwodZRRVfgls\r\n9pzsNmSoAhNJt0jMW8RLLow9Gw/enqrtJUePDRORp0wtRivMsdiWt8OWBZfr\r\nksppO+7QMbTa1sf558qVONJJ+FvNV0GgCL9XsFCNGmonFfp/N+mnOuTt+DM4\r\nLQDeEGw2926xUTqBxSPhaODoa2fG2K0GFHUhmINM4QE4sbyhKHdvuR686U17\r\n3lsJRkUcxZAYNJ2JP28LksB4ngQPUJToG1hKJiTjEu0Bkc2ByVXboUPVSB+J\r\n1uTR/XJ9eynZojJuw+YGNDPsvr4v+WJVj+p7WafHbS4kV56L2XF7BtkvdgQu\r\nT+Zq/oa2nvr7bvCxAa5CM3wTfsCNFea/6lbVJJTlJmhF1CRjUehCZkBXsDxZ\r\ntI+PAbF4zUZD9PJl6tK5anp3caTsODOr34Z7E69Dwck/SomQ1Rklx4o8adG7\r\nYqBv+P91NoUXWczQkhWFfzPkuIBUC3gLF9KSKN9AKBcVMHrPQAUzZEUH8WJL\r\ncuUc37pwm776LtEwn8myxA8q4MlhdinSNE4C8GeSnrgYmvFmdDHQKMYKGO6d\r\nDST7pdKj3UOkPxIHM7aSzbnGHacWDJ7zBkKiKjEtQ2zIrFJPJ4HFy/AXkhOZ\r\n5yifCayy2WvykDsHloHX+0Kb9m7uuopb/MU=\r\n=vTYK\r\n-----END PGP SIGNATURE-----\r\n", "size": 7448}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_4.0.1_1666035327573_0.07367852163957989"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-19T21:41:44.987Z"}, "4.0.2": {"name": "bin-links", "version": "4.0.2", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"snap": "tap", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^6.0.0", "npm-normalize-package-bin": "^3.0.0", "read-cmd-shim": "^4.0.0", "write-file-atomic": "^5.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.15.1", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.15.1", "publish": true}, "_id": "bin-links@4.0.2", "gitHead": "0816cc0c1f5b8844496119f97cbd341a22465f04", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_nodeVersion": "18.16.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-jxJ0PbXR8eQyPlExCvCs3JFnikvs1Yp4gUJt6nmgathdOwvur+q22KWC3h20gvWl4T/14DXKj2IlkJwwZkZPOw==", "shasum": "13321472ea157e9530caded2b7281496d698665b", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-4.0.2.tgz", "fileCount": 18, "unpackedSize": 20703, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/bin-links@4.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB8N8KpE43yr9qu0Bk1Fnyp7c2Et2nBaeDJaNdL3zZVmAiBZ69tgyI88/5ut2Swlo2i8pvfgh8Ivs6xRSjzdmvuzzQ=="}], "size": 7455}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_4.0.2_1689091057542_0.747121462709939"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-11T15:57:37.751Z", "publish_time": 1689091057751, "_source_registry_name": "default"}, "4.0.3": {"name": "bin-links", "version": "4.0.3", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"snap": "tap", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^6.0.0", "npm-normalize-package-bin": "^3.0.0", "read-cmd-shim": "^4.0.0", "write-file-atomic": "^5.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.19.0", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.19.0", "publish": true}, "_id": "bin-links@4.0.3", "gitHead": "d4ccdb05cd9a1787f25f69cf2458b4926cbe65c8", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_nodeVersion": "18.18.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-obsRaULtJurnfox/MDwgq6Yo9kzbv1CPTk/1/s7Z/61Lezc8IKkFCOXNeVLXz0456WRzBQmSsDWlai2tIhBsfA==", "shasum": "9e4a3c5900830aee3d7f52178b65e01dcdde64a5", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-4.0.3.tgz", "fileCount": 18, "unpackedSize": 20646, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/bin-links@4.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZ+uyV/rPGBMDcgwkiJHBnyhMBq8v0Nrct+6x7lW5kKAIhAPZb8vN2Hy3FUnaWiXVYsiZ52Y/fDz77+m7+j88JhKcn"}], "size": 7440}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_4.0.3_1697127766918_0.3816962899566816"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-10-12T16:22:47.134Z", "publish_time": 1697127767134, "_source_registry_name": "default"}, "4.0.4": {"name": "bin-links", "version": "4.0.4", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"snap": "tap", "test": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^6.0.0", "npm-normalize-package-bin": "^3.0.0", "read-cmd-shim": "^4.0.0", "write-file-atomic": "^5.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.22.0", "publish": true}, "_id": "bin-links@4.0.4", "gitHead": "0b83a9de0816f85dbfe2f9be49b6bd444b3c1d03", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_nodeVersion": "22.1.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-cMtq4W5ZsEwcutJrVId+a/tjt8GSbS+h0oNkdl6+6rBuEv8Ot33Bevj5KPm40t309zuhVic8NjpuL42QCiJWWA==", "shasum": "c3565832b8e287c85f109a02a17027d152a58a63", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-4.0.4.tgz", "fileCount": 18, "unpackedSize": 20665, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/bin-links@4.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqXyqZtaEtILbs2bLrhEp+Yo1QoeqNM6RxThp6OKsygAiBOc7e24SbE7cqEavFLygvXA6uhpBztto1tLW93HRoMzg=="}], "size": 7476}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_4.0.4_1714785010814_0.08736990214927287"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-04T01:10:10.982Z", "publish_time": 1714785010982, "_source_registry_name": "default"}, "5.0.0": {"name": "bin-links", "version": "5.0.0", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"snap": "tap", "test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^7.0.0", "npm-normalize-package-bin": "^4.0.0", "proc-log": "^5.0.0", "read-cmd-shim": "^5.0.0", "write-file-atomic": "^6.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "author": {"name": "GitHub Inc."}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.23.3", "publish": true}, "_id": "bin-links@5.0.0", "gitHead": "21614a8d3f0479136a76648e2f9364aad3ffdd55", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-sdleLVfCjBtgO5cNjA2HVRvWBJAHs4zwenaCPMNJAJU0yNxpzj80IpjOIimkpkr+mhlA+how5poQtt53PygbHA==", "shasum": "2b0605b62dd5e1ddab3b92a3c4e24221cae06cca", "tarball": "https://registry.npmmirror.com/bin-links/-/bin-links-5.0.0.tgz", "fileCount": 18, "unpackedSize": 20855, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/bin-links@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBSKqLVgF/9BwRf3JsqR4ID/iKYgwsDBefgY+3uQHGUwAiATwAk/o2N8xa1sxTAjsCYuZKxm/8xs+yAuJ6QOh4AuYA=="}], "size": 7552}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bin-links_5.0.0_1727363905995_0.617415631733452"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-26T15:18:26.181Z", "publish_time": 1727363906181, "_source_registry_name": "default"}}, "author": {"name": "GitHub Inc."}, "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "homepage": "https://github.com/npm/bin-links#readme", "keywords": ["npm", "link", "bins"], "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "_source_registry_name": "default"}