{"_attachments": {}, "_id": "uuid", "_rev": "183-61f14420a920628a7b6df587", "description": "RFC9562 UUIDs", "dist-tags": {"latest": "11.1.0"}, "license": "MIT", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "name": "uuid", "readme": "<!--\n  -- This file is auto-generated from README_js.md. Changes should be made there.\n  -->\n\n# uuid [![CI](https://github.com/uuidjs/uuid/workflows/CI/badge.svg)](https://github.com/uuidjs/uuid/actions?query=workflow%3ACI) [![Browser](https://github.com/uuidjs/uuid/workflows/Browser/badge.svg)](https://github.com/uuidjs/uuid/actions/workflows/browser.yml)\n\nFor the creation of [RFC9562](https://www.rfc-editor.org/rfc/rfc9562.html) (formerly [RFC4122](https://www.rfc-editor.org/rfc/rfc4122.html)) UUIDs\n\n- **Complete** - Support for all RFC9562 UUID versions\n- **Cross-platform** - Support for...\n  - ESM & Common JS\n  - [Typescript](#support)\n  - [Chrome, Safari, Firefox, and Edge](#support)\n  - [NodeJS](#support)\n  - [React Native / Expo](#react-native--expo)\n- **Secure** - Uses modern `crypto` API for random values\n- **Compact** - Zero-dependency, [tree-shakable](https://developer.mozilla.org/en-US/docs/Glossary/Tree_shaking)\n- **CLI** - [`uuid` command line](#command-line) utility\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> `uuid@11` is now available:  See the [CHANGELOG](./CHANGELOG.md) for details. TL;DR:\n> * TypeScript support is now included  (remove `@types/uuid` from your dependencies)\n> * Subtle changes to how the `options` arg is interpreted for `v1()`, `v6()`, and `v7()`. [See details](#options-handling-for-timestamp-uuids)\n> * Binary UUIDs are now `Uint8Array`s.  (May impact callers of `parse()`, `stringify()`,  or that pass an `option#buf` argument to `v1()`-`v7()`.)\n\n## Quickstart\n\n**1. Install**\n\n```shell\nnpm install uuid\n```\n\n**2. Create a UUID**\n\nESM-syntax (must use named exports):\n\n```javascript\nimport { v4 as uuidv4 } from 'uuid';\nuuidv4(); // ⇨ '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d'\n```\n\n... CommonJS:\n\n```javascript\nconst { v4: uuidv4 } = require('uuid');\nuuidv4(); // ⇨ '1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed'\n```\n\nFor timestamp UUIDs, namespace UUIDs, and other options read on ...\n\n## API Summary\n\n|  |  |  |\n| --- | --- | --- |\n| [`uuid.NIL`](#uuidnil) | The nil UUID string (all zeros) | New in `uuid@8.3` |\n| [`uuid.MAX`](#uuidmax) | The max UUID string (all ones) | New in `uuid@9.1` |\n| [`uuid.parse()`](#uuidparsestr) | Convert UUID string to array of bytes | New in `uuid@8.3` |\n| [`uuid.stringify()`](#uuidstringifyarr-offset) | Convert array of bytes to UUID string | New in `uuid@8.3` |\n| [`uuid.v1()`](#uuidv1options-buffer-offset) | Create a version 1 (timestamp) UUID |  |\n| [`uuid.v1ToV6()`](#uuidv1tov6uuid) | Create a version 6 UUID from a version 1 UUID | New in `uuid@10` |\n| [`uuid.v3()`](#uuidv3name-namespace-buffer-offset) | Create a version 3 (namespace w/ MD5) UUID |  |\n| [`uuid.v4()`](#uuidv4options-buffer-offset) | Create a version 4 (random) UUID |  |\n| [`uuid.v5()`](#uuidv5name-namespace-buffer-offset) | Create a version 5 (namespace w/ SHA-1) UUID |  |\n| [`uuid.v6()`](#uuidv6options-buffer-offset) | Create a version 6 (timestamp, reordered) UUID | New in `uuid@10` |\n| [`uuid.v6ToV1()`](#uuidv6tov1uuid) | Create a version 1 UUID from a version 6 UUID | New in `uuid@10` |\n| [`uuid.v7()`](#uuidv7options-buffer-offset) | Create a version 7 (Unix Epoch time-based) UUID | New in `uuid@10` |\n| ~~[`uuid.v8()`](#uuidv8)~~ | \"Intentionally left blank\" |  |\n| [`uuid.validate()`](#uuidvalidatestr) | Test a string to see if it is a valid UUID | New in `uuid@8.3` |\n| [`uuid.version()`](#uuidversionstr) | Detect RFC version of a UUID | New in `uuid@8.3` |\n\n## API\n\n### uuid.NIL\n\nThe nil UUID string (all zeros).\n\nExample:\n\n```javascript\nimport { NIL as NIL_UUID } from 'uuid';\n\nNIL_UUID; // ⇨ '00000000-0000-0000-0000-000000000000'\n```\n\n### uuid.MAX\n\nThe max UUID string (all ones).\n\nExample:\n\n```javascript\nimport { MAX as MAX_UUID } from 'uuid';\n\nMAX_UUID; // ⇨ 'ffffffff-ffff-ffff-ffff-ffffffffffff'\n```\n\n### uuid.parse(str)\n\nConvert UUID string to array of bytes\n\n|           |                                          |\n| --------- | ---------------------------------------- |\n| `str`     | A valid UUID `String`                    |\n| _returns_ | `Uint8Array[16]`                         |\n| _throws_  | `TypeError` if `str` is not a valid UUID |\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> Ordering of values in the byte arrays used by `parse()` and `stringify()` follows the left &Rarr; right order of hex-pairs in UUID strings. As shown in the example below.\n\nExample:\n\n```javascript\nimport { parse as uuidParse } from 'uuid';\n\n// Parse a UUID\nuuidParse('6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b'); // ⇨\n// Uint8Array(16) [\n//   110, 192, 189, 127,  17,\n//   192,  67, 218, 151,  94,\n//    42, 138, 217, 235, 174,\n//    11\n// ]\n```\n\n### uuid.stringify(arr[, offset])\n\nConvert array of bytes to UUID string\n\n|                |                                                                              |\n| -------------- | ---------------------------------------------------------------------------- |\n| `arr`          | `Array`-like collection of 16 values (starting from `offset`) between 0-255. |\n| [`offset` = 0] | `Number` Starting index in the Array                                         |\n| _returns_      | `String`                                                                     |\n| _throws_       | `TypeError` if a valid UUID string cannot be generated                       |\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> Ordering of values in the byte arrays used by `parse()` and `stringify()` follows the left &Rarr; right order of hex-pairs in UUID strings. As shown in the example below.\n\nExample:\n\n```javascript\nimport { stringify as uuidStringify } from 'uuid';\n\nconst uuidBytes = Uint8Array.of(\n  0x6e,\n  0xc0,\n  0xbd,\n  0x7f,\n  0x11,\n  0xc0,\n  0x43,\n  0xda,\n  0x97,\n  0x5e,\n  0x2a,\n  0x8a,\n  0xd9,\n  0xeb,\n  0xae,\n  0x0b\n);\n\nuuidStringify(uuidBytes); // ⇨ '6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b'\n```\n\n### uuid.v1([options[, buffer[, offset]]])\n\nCreate an RFC version 1 (timestamp) UUID\n\n|  |  |\n| --- | --- |\n| [`options`] | `Object` with one or more of the following properties: |\n| [`options.node = (random)` ] | RFC \"node\" field as an `Array[6]` of byte values (per 4.1.6) |\n| [`options.clockseq = (random)`] | RFC \"clock sequence\" as a `Number` between 0 - 0x3fff |\n| [`options.msecs = (current time)`] | RFC \"timestamp\" field (`Number` of milliseconds, unix epoch) |\n| [`options.nsecs = 0`] | RFC \"timestamp\" field (`Number` of nanoseconds to add to `msecs`, should be 0-10,000) |\n| [`options.random = (random)`] | `Array` of 16 random bytes (0-255) used to generate other fields, above |\n| [`options.rng`] | Alternative to `options.random`, a `Function` that returns an `Array` of 16 random bytes (0-255) |\n| [`buffer`] | `Uint8Array` or `Uint8Array` subtype (e.g. Node.js `Buffer`). If provided, binary UUID is written into the array, starting at `offset` |\n| [`offset` = 0] | `Number` Index to start writing UUID bytes in `buffer` |\n| _returns_ | UUID `String` if no `buffer` is specified, otherwise returns `buffer` |\n| _throws_ | `Error` if more than 10M UUIDs/sec are requested |\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> The default [node id](https://datatracker.ietf.org/doc/html/rfc9562#section-5.1) (the last 12 digits in the UUID) is generated once, randomly, on process startup, and then remains unchanged for the duration of the process.\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> `options.random` and `options.rng` are only meaningful on the very first call to `v1()`, where they may be passed to initialize the internal `node` and `clockseq` fields.\n\nExample:\n\n```javascript\nimport { v1 as uuidv1 } from 'uuid';\n\nuuidv1(); // ⇨ '2c5ea4c0-4067-11e9-9bdd-2b0d7b3dcb6d'\n```\n\nExample using `options`:\n\n```javascript\nimport { v1 as uuidv1 } from 'uuid';\n\nconst options = {\n  node: Uint8Array.of(0x01, 0x23, 0x45, 0x67, 0x89, 0xab),\n  clockseq: 0x1234,\n  msecs: new Date('2011-11-01').getTime(),\n  nsecs: 5678,\n};\nuuidv1(options); // ⇨ '710b962e-041c-11e1-9234-0123456789ab'\n```\n\n### uuid.v1ToV6(uuid)\n\nConvert a UUID from version 1 to version 6\n\n```javascript\nimport { v1ToV6 } from 'uuid';\n\nv1ToV6('92f62d9e-22c4-11ef-97e9-325096b39f47'); // ⇨ '1ef22c49-2f62-6d9e-97e9-325096b39f47'\n```\n\n### uuid.v3(name, namespace[, buffer[, offset]])\n\nCreate an RFC version 3 (namespace w/ MD5) UUID\n\nAPI is identical to `v5()`, but uses \"v3\" instead.\n\n<!-- prettier-ignore -->\n> [!IMPORTANT]\n> Per the RFC, \"_If backward compatibility is not an issue, SHA-1 [Version 5] is preferred_.\"\n\n### uuid.v4([options[, buffer[, offset]]])\n\nCreate an RFC version 4 (random) UUID\n\n|  |  |\n| --- | --- |\n| [`options`] | `Object` with one or more of the following properties: |\n| [`options.random`] | `Array` of 16 random bytes (0-255) |\n| [`options.rng`] | Alternative to `options.random`, a `Function` that returns an `Array` of 16 random bytes (0-255) |\n| [`buffer`] | `Uint8Array` or `Uint8Array` subtype (e.g. Node.js `Buffer`). If provided, binary UUID is written into the array, starting at `offset` |\n| [`offset` = 0] | `Number` Index to start writing UUID bytes in `buffer` |\n| _returns_ | UUID `String` if no `buffer` is specified, otherwise returns `buffer` |\n\nExample:\n\n```javascript\nimport { v4 as uuidv4 } from 'uuid';\n\nuuidv4(); // ⇨ '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d'\n```\n\nExample using predefined `random` values:\n\n```javascript\nimport { v4 as uuidv4 } from 'uuid';\n\nconst v4options = {\n  random: Uint8Array.of(\n    0x10,\n    0x91,\n    0x56,\n    0xbe,\n    0xc4,\n    0xfb,\n    0xc1,\n    0xea,\n    0x71,\n    0xb4,\n    0xef,\n    0xe1,\n    0x67,\n    0x1c,\n    0x58,\n    0x36\n  ),\n};\nuuidv4(v4options); // ⇨ '109156be-c4fb-41ea-b1b4-efe1671c5836'\n```\n\n### uuid.v5(name, namespace[, buffer[, offset]])\n\nCreate an RFC version 5 (namespace w/ SHA-1) UUID\n\n|  |  |\n| --- | --- |\n| `name` | `String \\| Array` |\n| `namespace` | `String \\| Array[16]` Namespace UUID |\n| [`buffer`] | `Uint8Array` or `Uint8Array` subtype (e.g. Node.js `Buffer`). If provided, binary UUID is written into the array, starting at `offset` |\n| [`offset` = 0] | `Number` Index to start writing UUID bytes in `buffer` |\n| _returns_ | UUID `String` if no `buffer` is specified, otherwise returns `buffer` |\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> The RFC `DNS` and `URL` namespaces are available as `v5.DNS` and `v5.URL`.\n\nExample with custom namespace:\n\n```javascript\nimport { v5 as uuidv5 } from 'uuid';\n\n// Define a custom namespace.  Readers, create your own using something like\n// https://www.uuidgenerator.net/\nconst MY_NAMESPACE = '1b671a64-40d5-491e-99b0-da01ff1f3341';\n\nuuidv5('Hello, World!', MY_NAMESPACE); // ⇨ '630eb68f-e0fa-5ecc-887a-7c7a62614681'\n```\n\nExample with RFC `URL` namespace:\n\n```javascript\nimport { v5 as uuidv5 } from 'uuid';\n\nuuidv5('https://www.w3.org/', uuidv5.URL); // ⇨ 'c106a26a-21bb-5538-8bf2-57095d1976c1'\n```\n\n### uuid.v6([options[, buffer[, offset]]])\n\nCreate an RFC version 6 (timestamp, reordered) UUID\n\nThis method takes the same arguments as uuid.v1().\n\n```javascript\nimport { v6 as uuidv6 } from 'uuid';\n\nuuidv6(); // ⇨ '1e940672-c5ea-64c0-9b5d-ab8dfbbd4bed'\n```\n\nExample using `options`:\n\n```javascript\nimport { v6 as uuidv6 } from 'uuid';\n\nconst options = {\n  node: [0x01, 0x23, 0x45, 0x67, 0x89, 0xab],\n  clockseq: 0x1234,\n  msecs: new Date('2011-11-01').getTime(),\n  nsecs: 5678,\n};\nuuidv6(options); // ⇨ '1e1041c7-10b9-662e-9234-0123456789ab'\n```\n\n### uuid.v6ToV1(uuid)\n\nConvert a UUID from version 6 to version 1\n\n```javascript\nimport { v6ToV1 } from 'uuid';\n\nv6ToV1('1ef22c49-2f62-6d9e-97e9-325096b39f47'); // ⇨ '92f62d9e-22c4-11ef-97e9-325096b39f47'\n```\n\n### uuid.v7([options[, buffer[, offset]]])\n\nCreate an RFC version 7 (random) UUID\n\n|  |  |\n| --- | --- |\n| [`options`] | `Object` with one or more of the following properties: |\n| [`options.msecs = (current time)`] | RFC \"timestamp\" field (`Number` of milliseconds, unix epoch) |\n| [`options.random = (random)`] | `Array` of 16 random bytes (0-255) used to generate other fields, above |\n| [`options.rng`] | Alternative to `options.random`, a `Function` that returns an `Array` of 16 random bytes (0-255) |\n| [`options.seq = (random)`] | 32-bit sequence `Number` between 0 - 0xffffffff. This may be provided to help ensure uniqueness for UUIDs generated within the same millisecond time interval. Default = random value. |\n| [`buffer`] | `Uint8Array` or `Uint8Array` subtype (e.g. Node.js `Buffer`). If provided, binary UUID is written into the array, starting at `offset` |\n| [`offset` = 0] | `Number` Index to start writing UUID bytes in `buffer` |\n| _returns_ | UUID `String` if no `buffer` is specified, otherwise returns `buffer` |\n\nExample:\n\n```javascript\nimport { v7 as uuidv7 } from 'uuid';\n\nuuidv7(); // ⇨ '01695553-c90c-705a-b56d-778dfbbd4bed'\n```\n\n### ~~uuid.v8()~~\n\n**_\"Intentionally left blank\"_**\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> Version 8 (experimental) UUIDs are \"[for experimental or vendor-specific use cases](https://www.rfc-editor.org/rfc/rfc9562.html#name-uuid-version-8)\".  The RFC does not define a creation algorithm for them, which is why this package does not offer a `v8()` method.  The `validate()` and `version()` methods do work with such UUIDs, however.\n\n### uuid.validate(str)\n\nTest a string to see if it is a valid UUID\n\n|           |                                                     |\n| --------- | --------------------------------------------------- |\n| `str`     | `String` to validate                                |\n| _returns_ | `true` if string is a valid UUID, `false` otherwise |\n\nExample:\n\n```javascript\nimport { validate as uuidValidate } from 'uuid';\n\nuuidValidate('not a UUID'); // ⇨ false\nuuidValidate('6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b'); // ⇨ true\n```\n\nUsing `validate` and `version` together it is possible to do per-version validation, e.g. validate for only v4 UUIds.\n\n```javascript\nimport { version as uuidVersion } from 'uuid';\nimport { validate as uuidValidate } from 'uuid';\n\nfunction uuidValidateV4(uuid) {\n  return uuidValidate(uuid) && uuidVersion(uuid) === 4;\n}\n\nconst v1Uuid = 'd9428888-122b-11e1-b85c-61cd3cbb3210';\nconst v4Uuid = '109156be-c4fb-41ea-b1b4-efe1671c5836';\n\nuuidValidateV4(v4Uuid); // ⇨ true\nuuidValidateV4(v1Uuid); // ⇨ false\n```\n\n### uuid.version(str)\n\nDetect RFC version of a UUID\n\n|           |                                          |\n| --------- | ---------------------------------------- |\n| `str`     | A valid UUID `String`                    |\n| _returns_ | `Number` The RFC version of the UUID     |\n| _throws_  | `TypeError` if `str` is not a valid UUID |\n\nExample:\n\n```javascript\nimport { version as uuidVersion } from 'uuid';\n\nuuidVersion('45637ec4-c85f-11ea-87d0-0242ac130003'); // ⇨ 1\nuuidVersion('6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b'); // ⇨ 4\n```\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> This method returns `0` for the `NIL` UUID, and `15` for the `MAX` UUID.\n\n## Command Line\n\nUUIDs can be generated from the command line using `uuid`.\n\n```shell\n$ npx uuid\nddeb27fb-d9a0-4624-be4d-4615062daed4\n```\n\nThe default is to generate version 4 UUIDS, however the other versions are supported. Type `uuid --help` for details:\n\n```shell\n$ npx uuid --help\n\nUsage:\n  uuid\n  uuid v1\n  uuid v3 <name> <namespace uuid>\n  uuid v4\n  uuid v5 <name> <namespace uuid>\n  uuid v7\n  uuid --help\n\nNote: <namespace uuid> may be \"URL\" or \"DNS\" to use the corresponding UUIDs\ndefined by RFC9562\n```\n\n## `options` Handling for Timestamp UUIDs\n\nPrior to `uuid@11`, it was possible for `options` state to interfere with the internal state used to ensure uniqueness of timestamp-based UUIDs (the `v1()`, `v6()`, and `v7()` methods). Starting with `uuid@11`, this issue has been addressed by using the presence of the `options` argument as a flag to select between two possible behaviors:\n\n- Without `options`: Internal state is utilized to improve UUID uniqueness.\n- With `options`: Internal state is **NOT** used and, instead, appropriate defaults are applied as needed.\n\n## Support\n\n**Browsers**: `uuid` [builds are tested](/uuidjs/uuid/blob/main/wdio.conf.js) against the latest version of desktop Chrome, Safari, Firefox, and Edge. Mobile versions of these same browsers are expected to work but aren't currently tested.\n\n**Node**: `uuid` [builds are tested](https://github.com/uuidjs/uuid/blob/main/.github/workflows/ci.yml#L26-L27) against node ([LTS releases](https://github.com/nodejs/Release)), plus one prior. E.g. `node@18` is in maintainence mode, and `node@22` is the current LTS release. So `uuid` supports `node@16`-`node@22`.\n\n**Typescript**: TS versions released within the past two years are supported. [source](https://github.com/microsoft/TypeScript/issues/49088#issuecomment-2468723715)\n\n## Known issues\n\n<!-- This header is referenced as an anchor in src/rng-browser.ts -->\n\n### \"getRandomValues() not supported\"\n\nThis error occurs in environments where the standard [`crypto.getRandomValues()`](https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues) API is not supported. This issue can be resolved by adding an appropriate polyfill:\n\n#### React Native / Expo\n\n1. Install [`react-native-get-random-values`](https://github.com/LinusU/react-native-get-random-values#readme)\n1. Import it _before_ `uuid`. Since `uuid` might also appear as a transitive dependency of some other imports it's safest to just import `react-native-get-random-values` as the very first thing in your entry point:\n\n```javascript\nimport 'react-native-get-random-values';\nimport { v4 as uuidv4 } from 'uuid';\n```\n\n---\n\nMarkdown generated from [README_js.md](README_js.md) by <a href=\"https://github.com/broofa/runmd\"><image height=\"13\" src=\"https://camo.githubusercontent.com/5c7c603cd1e6a43370b0a5063d457e0dabb74cf317adc7baba183acb686ee8d0/687474703a2f2f692e696d6775722e636f6d2f634a4b6f3662552e706e67\" /></a>\n", "time": {"created": "2022-01-26T12:52:48.108Z", "modified": "2025-02-19T18:27:41.760Z", "8.3.2": "2020-12-08T20:38:36.233Z", "8.3.2-beta.0": "2020-11-21T13:10:15.605Z", "8.3.1": "2020-10-04T15:46:46.276Z", "8.3.0": "2020-07-27T19:01:36.217Z", "8.3.0-beta.0": "2020-07-22T22:28:57.880Z", "8.2.0": "2020-06-23T20:56:14.285Z", "8.2.0-beta.0": "2020-06-23T07:38:42.240Z", "8.1.0": "2020-05-20T18:52:38.248Z", "8.0.0": "2020-04-29T20:42:26.823Z", "8.0.0-beta.0": "2020-04-29T20:21:52.783Z", "7.0.3": "2020-03-31T19:41:24.836Z", "7.0.2": "2020-03-04T12:49:00.666Z", "7.0.2-beta.0": "2020-03-02T20:31:08.402Z", "7.0.1": "2020-02-25T20:26:35.800Z", "7.0.0": "2020-02-24T13:23:46.712Z", "7.0.0-beta.0": "2020-02-17T11:25:25.965Z", "3.4.0": "2020-01-16T21:05:38.628Z", "3.3.3": "2019-08-19T13:26:25.072Z", "3.3.2": "2018-06-28T21:29:01.853Z", "3.3.0": "2018-06-26T13:23:39.426Z", "3.2.1": "2018-01-16T17:44:57.871Z", "3.2.0": "2018-01-16T14:44:50.639Z", "3.1.0": "2017-06-16T17:54:51.877Z", "3.0.1": "2016-11-29T07:18:07.016Z", "3.0.0": "2016-11-18T05:55:37.542Z", "2.0.3": "2016-09-18T21:03:38.127Z", "2.0.2": "2016-04-13T07:37:23.788Z", "2.0.1": "2014-09-29T09:16:55.228Z", "2.0.0": "2014-09-29T09:03:24.612Z", "1.4.2": "2014-09-25T09:03:34.083Z", "1.4.1": "2013-03-14T05:25:04.735Z", "1.4.0": "2013-02-19T22:28:10.376Z", "0.0.2": "2011-03-31T08:12:51.801Z", "0.0.1": "2011-03-31T08:12:51.801Z", "9.0.0-beta.0": "2022-08-05T10:12:36.295Z", "9.0.0": "2022-09-05T20:03:54.869Z", "9.0.1": "2023-09-12T08:56:35.205Z", "10.0.0": "2024-06-09T13:42:04.366Z", "11.0.0-0": "2024-09-05T21:08:26.805Z", "11.0.0": "2024-10-27T14:35:13.696Z", "11.0.1": "2024-10-27T21:40:24.728Z", "11.0.2": "2024-10-28T17:27:12.069Z", "11.0.3": "2024-11-10T21:25:54.278Z", "11.0.4": "2025-01-05T15:16:52.451Z", "11.0.5": "2025-01-09T22:40:18.873Z", "11.1.0": "2025-02-19T18:16:11.602Z"}, "versions": {"8.3.2": {"name": "uuid", "version": "8.3.2", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.11.6", "@babel/core": "7.11.6", "@babel/preset-env": "7.11.5", "@commitlint/cli": "11.0.0", "@commitlint/config-conventional": "11.0.0", "@rollup/plugin-node-resolve": "9.0.0", "babel-eslint": "10.1.0", "bundlewatch": "0.3.1", "eslint": "7.10.0", "eslint-config-prettier": "6.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "husky": "4.3.0", "jest": "25.5.4", "lint-staged": "10.4.0", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "2.1.2", "random-seed": "0.3.0", "rollup": "2.28.2", "rollup-plugin-terser": "7.0.2", "runmd": "1.3.2", "standard-version": "9.0.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "6.4.0", "@wdio/cli": "6.4.0", "@wdio/jasmine-framework": "6.4.0", "@wdio/local-runner": "6.4.0", "@wdio/spec-reporter": "6.4.0", "@wdio/static-server-service": "6.4.0", "@wdio/sync": "6.4.0"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "ed3240154759b748f6a3b7d545f3b10759ee4ba7", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.3.2", "_nodeVersion": "12.19.1", "_npmVersion": "6.14.8", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "80d5b5ced271bb9af6c445f21a1a04c606cefbe2", "size": 27977, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="}, "directories": {}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.3.2_1607459915862_0.45108061870390803"}, "_hasShrinkwrap": false, "publish_time": 1607459916233, "_cnpm_publish_time": 1607459916233, "_cnpmcore_publish_time": "2021-12-13T12:08:46.856Z"}, "8.3.2-beta.0": {"name": "uuid", "version": "8.3.2-beta.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.11.6", "@babel/core": "7.11.6", "@babel/preset-env": "7.11.5", "@commitlint/cli": "11.0.0", "@commitlint/config-conventional": "11.0.0", "@rollup/plugin-node-resolve": "9.0.0", "babel-eslint": "10.1.0", "bundlewatch": "0.3.1", "eslint": "7.10.0", "eslint-config-prettier": "6.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "husky": "4.3.0", "jest": "25.5.4", "lint-staged": "10.4.0", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "2.1.2", "random-seed": "0.3.0", "rollup": "2.28.2", "rollup-plugin-terser": "7.0.2", "runmd": "1.3.2", "standard-version": "9.0.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "6.4.0", "@wdio/cli": "6.4.0", "@wdio/jasmine-framework": "6.4.0", "@wdio/local-runner": "6.4.0", "@wdio/spec-reporter": "6.4.0", "@wdio/static-server-service": "6.4.0", "@wdio/sync": "6.4.0"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "gitHead": "334ef62c330d92f8ca376d09087e8ee9abc5cc12", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.3.2-beta.0", "_nodeVersion": "12.18.4", "_npmVersion": "6.14.6", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "ecd8091258ce05be1dfb1fa7330481aaf90509a4", "size": 27758, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.3.2-beta.0.tgz", "integrity": "sha512-V2GewaGescJgTw3WDBz3xC6is44S1eCavBX6Kjou1+yLjHnMWA4rxfV1cmGTw2HMGO91AO0+8DvvQyRzWyu2iA=="}, "directories": {}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.3.2-beta.0_1605964215452_0.17989725484763142"}, "_hasShrinkwrap": false, "publish_time": 1605964215605, "_cnpm_publish_time": 1605964215605, "_cnpmcore_publish_time": "2021-12-13T12:08:47.207Z"}, "8.3.1": {"name": "uuid", "version": "8.3.1", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.11.6", "@babel/core": "7.11.6", "@babel/preset-env": "7.11.5", "@commitlint/cli": "11.0.0", "@commitlint/config-conventional": "11.0.0", "@rollup/plugin-node-resolve": "9.0.0", "babel-eslint": "10.1.0", "bundlewatch": "0.3.1", "eslint": "7.10.0", "eslint-config-prettier": "6.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "husky": "4.3.0", "jest": "25.5.4", "lint-staged": "10.4.0", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "2.1.2", "random-seed": "0.3.0", "rollup": "2.28.2", "rollup-plugin-terser": "7.0.2", "runmd": "1.3.2", "standard-version": "9.0.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "6.4.0", "@wdio/cli": "6.4.0", "@wdio/jasmine-framework": "6.4.0", "@wdio/local-runner": "6.4.0", "@wdio/spec-reporter": "6.4.0", "@wdio/static-server-service": "6.4.0", "@wdio/sync": "6.4.0"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "252ebcfb33b2d056963a530fc02067ae3de6095a", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.3.1", "_nodeVersion": "12.18.4", "_npmVersion": "6.14.6", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "2ba2e6ca000da60fce5a196954ab241131e05a31", "size": 27668, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.3.1.tgz", "integrity": "sha512-FOmRr+FmWEIG8uhZv6C2bTgEVXsHk08kE7mPlrBbEe+c3r9pjceVPgupIfNIhc4yx55H69OXANrUaSuu9eInKg=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.3.1_1601826406124_0.8839671601486778"}, "_hasShrinkwrap": false, "publish_time": 1601826406276, "_cnpm_publish_time": 1601826406276, "_cnpmcore_publish_time": "2021-12-13T12:08:47.592Z"}, "8.3.0": {"name": "uuid", "version": "8.3.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.10.3", "@babel/core": "7.10.3", "@babel/preset-env": "7.10.3", "@commitlint/cli": "9.1.2", "@commitlint/config-conventional": "9.0.1", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/browserstack-service": "6.1.15", "@wdio/cli": "6.1.20", "@wdio/jasmine-framework": "6.1.17", "@wdio/local-runner": "6.1.20", "@wdio/spec-reporter": "6.1.14", "@wdio/static-server-service": "6.1.14", "@wdio/sync": "6.1.14", "babel-eslint": "10.1.0", "bundlewatch": "0.2.7", "eslint": "7.3.0", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.21.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "husky": "4.2.5", "jest": "25.5.4", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "prettier": "2.0.5", "random-seed": "0.3.0", "rollup": "2.18.0", "rollup-plugin-terser": "6.1.0", "runmd": "1.3.2", "standard-version": "8.0.2"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "a91f78d9e593b8a9af1cfebdf33b771b82c42475", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.3.0", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "ab738085ca22dc9a8c92725e459b1d507df5d6ea", "size": 27105, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.3.0.tgz", "integrity": "sha512-fX6Z5o4m6XsXBdli9g7DtWgAx+osMsRRZFKma1mIUsLCz6vRvv+pz5VNbyu9UEDzpMWulZfvpgb/cmDXVulYFQ=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.3.0_1595876496032_0.44563664352850596"}, "_hasShrinkwrap": false, "publish_time": 1595876496217, "_cnpm_publish_time": 1595876496217, "_cnpmcore_publish_time": "2021-12-13T12:08:48.000Z"}, "8.3.0-beta.0": {"name": "uuid", "version": "8.3.0-beta.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.10.3", "@babel/core": "7.10.3", "@babel/preset-env": "7.10.3", "@commitlint/cli": "9.1.2", "@commitlint/config-conventional": "9.0.1", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/browserstack-service": "6.1.15", "@wdio/cli": "6.1.20", "@wdio/jasmine-framework": "6.1.17", "@wdio/local-runner": "6.1.20", "@wdio/spec-reporter": "6.1.14", "@wdio/static-server-service": "6.1.14", "@wdio/sync": "6.1.14", "babel-eslint": "10.1.0", "bundlewatch": "0.2.7", "eslint": "7.3.0", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.21.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "husky": "4.2.5", "jest": "25.5.4", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "prettier": "2.0.5", "random-seed": "0.3.0", "rollup": "2.18.0", "rollup-plugin-terser": "6.1.0", "runmd": "1.3.2", "standard-version": "8.0.2"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "gitHead": "d6c185161b80f277ff03fc151e0f4bfb848a4570", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.3.0-beta.0", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "fbe17ffc7b6214d36a52c89f9e19c82dcc0f294a", "size": 26953, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.3.0-beta.0.tgz", "integrity": "sha512-YX12mJFOtrnE7o7GIbtYIoTrRN+5DTKLJXiUMnucohXeBPY0UYIaK2HrteuarIrWrjNvZ7FROqXMRNPKQz8wMg=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.3.0-beta.0_1595456937747_0.8708052138920852"}, "_hasShrinkwrap": false, "publish_time": 1595456937880, "_cnpm_publish_time": 1595456937880, "_cnpmcore_publish_time": "2021-12-13T12:08:48.394Z"}, "8.2.0": {"name": "uuid", "version": "8.2.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.10.3", "@babel/core": "7.10.3", "@babel/preset-env": "7.10.3", "@commitlint/cli": "9.0.1", "@commitlint/config-conventional": "9.0.1", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/browserstack-service": "6.1.15", "@wdio/cli": "6.1.20", "@wdio/jasmine-framework": "6.1.17", "@wdio/local-runner": "6.1.20", "@wdio/spec-reporter": "6.1.14", "@wdio/static-server-service": "6.1.14", "@wdio/sync": "6.1.14", "babel-eslint": "10.1.0", "bundlewatch": "0.2.7", "eslint": "7.3.0", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.21.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "husky": "4.2.5", "jest": "25.5.4", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "prettier": "2.0.5", "rollup": "2.18.0", "rollup-plugin-terser": "6.1.0", "runmd": "1.3.2", "standard-version": "8.0.0"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "b51f172b22580dabd382861c1a6316173244f51b", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.2.0", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "cb10dd6b118e2dada7d0cd9730ba7417c93d920e", "size": 24252, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.2.0.tgz", "integrity": "sha512-CYpGiFTUrmI6OBMkAdjSDM0k5h8SkkiTP4WAjQgDgNB1S3Ou9VBEvr6q0Kv2H1mMk7IWfxYGpMH5sd5AvcIV2Q=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.2.0_1592945774107_0.25456791127041134"}, "_hasShrinkwrap": false, "publish_time": 1592945774285, "_cnpm_publish_time": 1592945774285, "_cnpmcore_publish_time": "2021-12-13T12:08:48.777Z"}, "8.2.0-beta.0": {"name": "uuid", "version": "8.2.0-beta.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.10.3", "@babel/core": "7.10.3", "@babel/preset-env": "7.10.3", "@commitlint/cli": "9.0.1", "@commitlint/config-conventional": "9.0.1", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/browserstack-service": "6.1.15", "@wdio/cli": "6.1.20", "@wdio/jasmine-framework": "6.1.17", "@wdio/local-runner": "6.1.20", "@wdio/spec-reporter": "6.1.14", "@wdio/static-server-service": "6.1.14", "@wdio/sync": "6.1.14", "babel-eslint": "10.1.0", "bundlewatch": "0.2.7", "eslint": "7.3.0", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.21.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "husky": "4.2.5", "jest": "25.5.4", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "prettier": "2.0.5", "rollup": "2.18.0", "rollup-plugin-terser": "6.1.0", "runmd": "1.3.2", "standard-version": "8.0.0"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "gitHead": "e5075c871ad571b7e08a300b855e5a308b021aef", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.2.0-beta.0", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "bbab71018e75e556381986bbaaf849648d0665c5", "size": 23887, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.2.0-beta.0.tgz", "integrity": "sha512-hwW39cDgHKtopHIRUBDMrGIpPTWOYEid17+nr9uQcuijDGMLZ9NgBZOFOfgn/UISN+3NVbXpvytwDR8WlU7RrQ=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.2.0-beta.0_1592897922131_0.21824069661026968"}, "_hasShrinkwrap": false, "publish_time": 1592897922240, "_cnpm_publish_time": 1592897922240, "_cnpmcore_publish_time": "2021-12-13T12:08:49.227Z"}, "8.1.0": {"name": "uuid", "version": "8.1.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {"./package.json": "./package.json", ".": {"require": "./dist/index.js", "import": "./wrapper.mjs"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.9.0", "@babel/preset-env": "7.9.5", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/browserstack-service": "6.0.12", "@wdio/cli": "6.0.15", "@wdio/jasmine-framework": "6.0.15", "@wdio/local-runner": "6.0.15", "@wdio/spec-reporter": "6.0.14", "@wdio/static-server-service": "6.0.13", "@wdio/sync": "6.0.15", "babel-eslint": "10.1.0", "bundlewatch": "0.2.6", "eslint": "6.8.0", "eslint-config-prettier": "6.10.1", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.20.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.3", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "husky": "4.2.5", "jest": "25.3.0", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "prettier": "2.0.4", "rollup": "2.6.1", "rollup-plugin-terser": "5.3.0", "runmd": "1.3.2", "standard-version": "7.1.0"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "4124ec75f032da7b405b870b68219cda8495297f", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.1.0", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "6f1536eb43249f473abc6bd58ff983da1ca30d8d", "size": 23886, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.1.0.tgz", "integrity": "sha512-CI18flHDznR0lq54xBycOVmphdCYnQLKn8abKn7PXUiKUGdEd+/l9LWNJmugXel4hXq7S+RMNl34ecyC9TntWg=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.1.0_1590000758059_0.9915391136867249"}, "_hasShrinkwrap": false, "publish_time": 1590000758248, "_cnpm_publish_time": 1590000758248, "_cnpmcore_publish_time": "2021-12-13T12:08:49.628Z"}, "8.0.0": {"name": "uuid", "version": "8.0.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {"require": "./dist/index.js", "import": "./wrapper.mjs"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.9.0", "@babel/preset-env": "7.9.5", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/browserstack-service": "6.0.12", "@wdio/cli": "6.0.15", "@wdio/jasmine-framework": "6.0.15", "@wdio/local-runner": "6.0.15", "@wdio/spec-reporter": "6.0.14", "@wdio/static-server-service": "6.0.13", "@wdio/sync": "6.0.15", "babel-eslint": "10.1.0", "bundlewatch": "0.2.6", "eslint": "6.8.0", "eslint-config-prettier": "6.10.1", "eslint-plugin-prettier": "3.1.3", "husky": "4.2.5", "jest": "25.3.0", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "prettier": "2.0.4", "rollup": "2.6.1", "rollup-plugin-terser": "5.3.0", "runmd": "1.3.2", "standard-version": "7.1.0"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "a7d3d517382490eda6dd21158e5413261c45ccdd", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.0.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "bc6ccf91b5ff0ac07bbcdbf1c7c4e150db4dbb6c", "size": 22827, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.0.0.tgz", "integrity": "sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.0.0_1588192946684_0.9321919222541137"}, "_hasShrinkwrap": false, "publish_time": 1588192946823, "_cnpm_publish_time": 1588192946823, "_cnpmcore_publish_time": "2021-12-13T12:08:50.029Z"}, "8.0.0-beta.0": {"name": "uuid", "version": "8.0.0-beta.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {"require": "./dist/index.js", "import": "./wrapper.mjs"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.9.0", "@babel/preset-env": "7.9.5", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/browserstack-service": "6.0.12", "@wdio/cli": "6.0.15", "@wdio/jasmine-framework": "6.0.15", "@wdio/local-runner": "6.0.15", "@wdio/spec-reporter": "6.0.14", "@wdio/static-server-service": "6.0.13", "@wdio/sync": "6.0.15", "babel-eslint": "10.1.0", "bundlewatch": "0.2.6", "eslint": "6.8.0", "eslint-config-prettier": "6.10.1", "eslint-plugin-prettier": "3.1.3", "husky": "4.2.5", "jest": "25.3.0", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "prettier": "2.0.4", "rollup": "2.6.1", "rollup-plugin-terser": "5.3.0", "runmd": "1.3.2", "standard-version": "7.1.0"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "gitHead": "99d65afdc42b98377842756c0743bc38dcb03570", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@8.0.0-beta.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "93e7d8e269022a2fa2027d6a77d4129de877fadb", "size": 22383, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-8.0.0-beta.0.tgz", "integrity": "sha512-Ql2iMiWxJYtI3biUynCFc1J1XS6rOWhv8zN60bWh0hHwJsYZQ4jM0Z2614qS6cyPRRDOu6NVz+cLmCLt6G8eMw=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_8.0.0-beta.0_1588191712623_0.4997152339873798"}, "_hasShrinkwrap": false, "publish_time": 1588191712783, "_cnpm_publish_time": 1588191712783, "_cnpmcore_publish_time": "2021-12-13T12:08:50.461Z"}, "7.0.3": {"name": "uuid", "version": "7.0.3", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "dist/index.js", "module": "dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.8.7", "@babel/preset-env": "7.8.7", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-node-resolve": "7.1.1", "@wdio/browserstack-service": "5.18.7", "@wdio/cli": "5.18.7", "@wdio/jasmine-framework": "5.18.6", "@wdio/local-runner": "5.18.7", "@wdio/spec-reporter": "5.18.7", "@wdio/static-server-service": "5.16.10", "@wdio/sync": "5.18.7", "babel-eslint": "10.1.0", "babel-plugin-add-module-exports": "1.0.2", "bundlewatch": "0.2.6", "eslint": "6.8.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "husky": "3.0.9", "jest": "25.1.0", "lint-staged": "10.0.8", "npm-run-all": "4.1.5", "prettier": "1.19.1", "rollup": "1.32.0", "rollup-plugin-terser": "5.2.0", "runmd": "1.3.2", "standard-version": "7.1.0"}, "scripts": {"examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && bundlewatch --config bundlewatch.config.json )", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "build": "./scripts/build.sh", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md", "postcommit": "npm run build"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "4fcd881246f102239fa386f958ec0e5f83b53bbe", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@7.0.3", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "c5c9f2c8cf25dc0a372c4df1441c41f5bd0c680b", "size": 22660, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-7.0.3.tgz", "integrity": "sha512-DPSke0pXhTZgoF/d+WSt2QaKMCFSfx7QegxEWT+JOuHF5aWrKEn0G+ztjuJg/gG8/ItK+rbPCD/yNv8yyih6Cg=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_7.0.3_1585683684668_0.7417149445973905"}, "_hasShrinkwrap": false, "publish_time": 1585683684836, "_cnpm_publish_time": 1585683684836, "_cnpmcore_publish_time": "2021-12-13T12:08:50.887Z"}, "7.0.2": {"name": "uuid", "version": "7.0.2", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "dist/index.js", "module": "dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.8.4", "@babel/preset-env": "7.8.4", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-node-resolve": "7.1.1", "babel-eslint": "10.0.3", "babel-plugin-add-module-exports": "1.0.2", "browserstack-local": "1.4.5", "bundlewatch": "0.2.5", "eslint": "6.8.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "esm": "3.2.25", "http-server": "0.12.1", "husky": "3.0.9", "jest": "25.1.0", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "prettier": "1.19.1", "rollup": "1.31.1", "rollup-plugin-terser": "5.2.0", "runmd": "1.3.2", "selenium-webdriver": "3.6.0", "standard-version": "7.1.0"}, "scripts": {"examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "build": "./scripts/build.sh", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md", "postcommit": "npm run build"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "4b61be05c840ba4d6fadf89cc8d4e1bbba7b9b1a", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@7.0.2", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "7ff5c203467e91f5e0d85cfcbaaf7d2ebbca9be6", "size": 22431, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-7.0.2.tgz", "integrity": "sha512-vy9V/+pKG+5ZTYKf+VcphF5Oc6EFiu3W8Nv3P3zIh0EqVI80ZxOzuPfe9EHjkFNvf8+xuTHVeei4Drydlx4zjw=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_7.0.2_1583326140504_0.43396107706675835"}, "_hasShrinkwrap": false, "publish_time": 1583326140666, "_cnpm_publish_time": 1583326140666, "_cnpmcore_publish_time": "2021-12-13T12:08:51.371Z"}, "7.0.2-beta.0": {"name": "uuid", "version": "7.0.2-beta.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "dist/index.js", "module": "dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.8.4", "@babel/preset-env": "7.8.4", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-node-resolve": "7.1.1", "babel-eslint": "10.0.3", "babel-plugin-add-module-exports": "1.0.2", "browserstack-local": "1.4.5", "bundlewatch": "0.2.5", "eslint": "6.8.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "esm": "3.2.25", "http-server": "0.12.1", "husky": "3.0.9", "jest": "25.1.0", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "prettier": "1.19.1", "rollup": "1.31.1", "rollup-plugin-terser": "5.2.0", "runmd": "1.3.2", "selenium-webdriver": "3.6.0", "standard-version": "7.1.0"}, "scripts": {"examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "build": "./scripts/build.sh", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md", "postcommit": "npm run build"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "gitHead": "0da6efbc703a93236087ff55cd9087d2dcdff155", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@7.0.2-beta.0", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "8cc5d456c79a9dcb527108a799639cbf2e764bab", "size": 22280, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-7.0.2-beta.0.tgz", "integrity": "sha512-nWN1O0baX9+xv8DxMl6O5ERk0R/4xAuCkosXBNB68m1PReciNwzBoO9xzf8wX4HrXpfbcu9Mpo5mmgLcsDTZlA=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_7.0.2-beta.0_1583181068234_0.11125894339297981"}, "_hasShrinkwrap": false, "publish_time": 1583181068402, "_cnpm_publish_time": 1583181068402, "_cnpmcore_publish_time": "2021-12-13T12:08:51.883Z"}, "7.0.1": {"name": "uuid", "version": "7.0.1", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "dist/index.js", "module": "dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.8.4", "@babel/preset-env": "7.8.4", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-node-resolve": "7.1.1", "babel-eslint": "10.0.3", "babel-plugin-add-module-exports": "1.0.2", "browserstack-local": "1.4.5", "bundlewatch": "0.2.5", "eslint": "6.8.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "esm": "3.2.25", "http-server": "0.12.1", "husky": "3.0.9", "jest": "25.1.0", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "prettier": "1.19.1", "rollup": "1.31.1", "rollup-plugin-terser": "5.2.0", "runmd": "1.3.2", "selenium-webdriver": "3.6.0", "standard-version": "7.1.0"}, "scripts": {"examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "build": "./scripts/build.sh", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md", "postcommit": "npm run build"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "3f78220564ff2abcb06cbf5ea173513a0e0cba43", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@7.0.1", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "95ed6ff3d8c881cbf85f0f05cc3915ef994818ef", "size": 21987, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-7.0.1.tgz", "integrity": "sha512-yqjRXZzSJm9Dbl84H2VDHpM3zMjzSJQ+hn6C4zqd5ilW+7P4ZmLEEqwho9LjP+tGuZlF4xrHQXT0h9QZUS/pWA=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_7.0.1_1582662395609_0.09015471273563103"}, "_hasShrinkwrap": false, "publish_time": 1582662395800, "_cnpm_publish_time": 1582662395800, "_cnpmcore_publish_time": "2021-12-13T12:08:52.432Z"}, "7.0.0": {"name": "uuid", "version": "7.0.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "dist/index.js", "module": "dist/esm-browser/index.js", "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.8.4", "@babel/preset-env": "7.8.4", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "babel-eslint": "10.0.3", "babel-plugin-add-module-exports": "1.0.2", "browserstack-local": "1.4.5", "bundlewatch": "0.2.5", "eslint": "6.8.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "esm": "3.2.25", "http-server": "0.12.1", "husky": "3.0.9", "jest": "25.1.0", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "prettier": "1.19.1", "rollup": "1.31.1", "rollup-plugin-terser": "5.2.0", "runmd": "1.3.2", "selenium-webdriver": "3.6.0", "standard-version": "7.1.0"}, "scripts": {"examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "build": "./scripts/build.sh", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md", "postcommit": "npm run build"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "63e5702d7629303a5193c4dd31825376f240f888", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@7.0.0", "_nodeVersion": "12.16.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "1833d4b9ce50b732bfaa271f9cb74e974d303c79", "size": 21015, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-7.0.0.tgz", "integrity": "sha512-LNUrNsXdI/fUsypJbWM8Jt4DgQdFAZh41p9C7WE9Cn+CULOEkoG2lgQyH68v3wnIy5K3fN4jdSt270K6IFA3MQ=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_7.0.0_1582550626542_0.483157796354976"}, "_hasShrinkwrap": false, "publish_time": 1582550626712, "_cnpm_publish_time": 1582550626712, "_cnpmcore_publish_time": "2021-12-13T12:08:52.934Z"}, "7.0.0-beta.0": {"name": "uuid", "version": "7.0.0-beta.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "dist/index.js", "module": "dist/esm-browser/index.js", "devDependencies": {"@babel/cli": "7.8.3", "@babel/core": "7.8.3", "@babel/preset-env": "7.8.3", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "babel-eslint": "10.0.3", "babel-plugin-add-module-exports": "1.0.2", "browserstack-local": "1.4.4", "bundlewatch": "0.2.5", "eslint": "6.8.0", "eslint-config-prettier": "6.9.0", "eslint-plugin-prettier": "3.1.2", "esm": "3.2.25", "http-server": "0.12.1", "husky": "3.0.9", "jest": "24.9.0", "lint-staged": "10.0.1", "npm-run-all": "4.1.5", "prettier": "1.19.1", "rollup": "1.30.0", "rollup-plugin-terser": "5.2.0", "runmd": "1.3.2", "selenium-webdriver": "3.6.0", "standard-version": "7.0.1"}, "scripts": {"examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "build": "./scripts/build.sh", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md", "postcommit": "npm run build"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "522e44c055871493303ec0158dac75d7ce1eba5b", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@7.0.0-beta.0", "_nodeVersion": "12.15.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "7f2bd91cf896f8f3d8d911d995edd7d66d4c2651", "size": 19637, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-7.0.0-beta.0.tgz", "integrity": "sha512-Am22LVM3UXB0DTzQAeDSsZwP5eyqjIhmff330hqkxGvIxX8RRrUYLtKJ0eYxiBgjeQdUaMONpBZbJachMShxBw=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_7.0.0-beta.0_1581938725845_0.7896196061229703"}, "_hasShrinkwrap": false, "publish_time": 1581938725965, "_cnpm_publish_time": 1581938725965, "_cnpmcore_publish_time": "2021-12-13T12:08:53.519Z"}, "3.4.0": {"name": "uuid", "version": "3.4.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"@commitlint/cli": "~8.2.0", "@commitlint/config-conventional": "~8.2.0", "eslint": "~6.4.0", "husky": "~3.0.5", "mocha": "6.2.0", "runmd": "1.2.1", "standard-version": "7.0.0"}, "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/test.js", "md": "runmd --watch --output=README.md README_js.md", "release": "standard-version", "prepare": "runmd --output=README.md README_js.md"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "3df73a98f07c0a38a94bcaf1ecde0e384dc3b126", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@3.4.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "dist": {"shasum": "b23e4358afa8a202fe7a100af1f5f883f02007ee", "size": 12156, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-3.4.0.tgz", "integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_3.4.0_1579208738423_0.4772515028870088"}, "_hasShrinkwrap": false, "publish_time": 1579208738628, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1579208738628, "_cnpmcore_publish_time": "2021-12-13T12:08:53.958Z"}, "3.3.3": {"name": "uuid", "version": "3.3.3", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"@commitlint/cli": "8.1.0", "@commitlint/config-conventional": "8.1.0", "eslint": "6.2.0", "husky": "3.0.4", "mocha": "6.2.0", "runmd": "1.2.1", "standard-version": "7.0.0"}, "scripts": {"commitmsg": "commitlint -E HUSKY_GIT_PARAMS", "test": "mocha test/test.js", "md": "runmd --watch --output=README.md README_js.md", "release": "standard-version", "prepare": "runmd --output=README.md README_js.md"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "ab33c8a77df180de1a12fede423490b3c2de9d07", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "_id": "uuid@3.3.3", "_nodeVersion": "8.15.1", "_npmVersion": "6.9.0", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "dist": {"shasum": "4568f0216e78760ee1dbf3a4d2cf53e224112866", "size": 12119, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-3.3.3.tgz", "integrity": "sha512-pW0No1RGHgzlpHJO1nsVrHKpOEIxkGg1xB+v0ZmdNH5OAeAwzAVrCnI2/6Mtx+Uys6iaylxa+D3g4j63IKKjSQ=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_3.3.3_1566221184938_0.9358725731017152"}, "_hasShrinkwrap": false, "publish_time": 1566221185072, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1566221185072, "_cnpmcore_publish_time": "2021-12-13T12:08:54.448Z"}, "3.3.2": {"name": "uuid", "version": "3.3.2", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"@commitlint/cli": "7.0.0", "@commitlint/config-conventional": "7.0.1", "eslint": "4.19.1", "husky": "0.14.3", "mocha": "5.2.0", "runmd": "1.0.1", "standard-version": "4.4.0"}, "scripts": {"commitmsg": "commitlint -E GIT_PARAMS", "test": "mocha test/test.js", "md": "runmd --watch --output=README.md README_js.md", "release": "standard-version", "prepare": "runmd --output=README.md README_js.md"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "fe4ae79c55af2c7cbf2bb39d3bcb6716d5367091", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "_id": "uuid@3.3.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "dist": {"shasum": "1b4af4955eb3077c501c23872fc6513811587131", "size": 12909, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-3.3.2.tgz", "integrity": "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_3.3.2_1530221341707_0.5404045638057293"}, "_hasShrinkwrap": false, "publish_time": 1530221341853, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1530221341853, "_cnpmcore_publish_time": "2021-12-13T12:08:54.934Z"}, "3.3.0": {"name": "uuid", "version": "3.3.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"@commitlint/cli": "7.0.0", "@commitlint/config-conventional": "7.0.1", "eslint": "4.19.1", "husky": "0.14.3", "mocha": "5.2.0", "runmd": "1.0.1", "standard-version": "4.4.0"}, "scripts": {"commitmsg": "commitlint -E GIT_PARAMS", "test": "mocha test/test.js", "md": "runmd --watch --output=README.md README_js.md", "release": "standard-version", "prepare": "runmd --output=README.md README_js.md"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "1cb9826260b46352db19ccbfb36dfdbc323c35c9", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "_id": "uuid@3.3.0", "_npmVersion": "6.0.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "dist": {"shasum": "b237147804881d7b86f40a7ff8f590f15c37de32", "size": 12884, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-3.3.0.tgz", "integrity": "sha512-ijO9N2xY/YaOqQ5yz5c4sy2ZjWmA6AR6zASb/gdpeKZ8+948CxwfMW9RrKVk5may6ev8c0/Xguu32e2Llelpqw=="}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_3.3.0_1530019419356_0.9632179204070574"}, "_hasShrinkwrap": false, "publish_time": 1530019419426, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1530019419426, "_cnpmcore_publish_time": "2021-12-13T12:08:55.524Z"}, "3.2.1": {"name": "uuid", "version": "3.2.1", "description": "RFC4122 (v1, v4, and v5) UUIDs", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"eslint": "4.5.0", "mocha": "3.1.2", "runmd": "1.0.1", "standard-version": "4.2.0"}, "scripts": {"test": "mocha test/test.js", "md": "runmd --watch --output=README.md README_js.md", "release": "standard-version", "prepare": "runmd --output=README.md README_js.md"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "dependencies": {}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "ce7d3176fc927ab83b4e29b8d5a37b7243ce3360", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "_id": "uuid@3.2.1", "_npmVersion": "5.5.1", "_nodeVersion": "7.10.1", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "dist": {"shasum": "12c528bb9d58d0b9265d9a2f6f0fe8be17ff1f14", "size": 12286, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-3.2.1.tgz", "integrity": "sha512-jZnMwlb9Iku/O3smGWvZhauCf6cvvpKi4BKRiliS3cxnI+Gz9j5MEpTz2UFuXiKPJocb7gnsLHwiS05ige5BEA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid-3.2.1.tgz_1516124697652_0.3172094284091145"}, "directories": {}, "publish_time": 1516124697871, "_hasShrinkwrap": false, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1516124697871, "_cnpmcore_publish_time": "2021-12-13T12:08:56.021Z"}, "3.2.0": {"name": "uuid", "version": "3.2.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"eslint": "4.5.0", "mocha": "3.1.2", "runmd": "1.0.1", "standard-version": "4.2.0"}, "scripts": {"test": "mocha test/test.js", "md": "runmd --watch --output=README.md README_js.md", "release": "standard-version", "prepare": "runmd --output=README.md README_js.md"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "dependencies": {}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "c0d44fda8abeac5eb1444f03a135f8a6353854da", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "_id": "uuid@3.2.0", "_npmVersion": "5.5.1", "_nodeVersion": "7.10.1", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "dist": {"shasum": "19a63e22b3b32a0ba23984a4f384836465e24949", "size": 12203, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-3.2.0.tgz", "integrity": "sha512-qC0vMFX6q6ee8JaoTF2Om1uL8KAV1ATUjVaHRxLiPJkIsp8JZl6ZjG0MIB+twZFLbi1vXj30rqj4zlqYiOS9xg=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid-3.2.0.tgz_1516113890512_0.8254034700803459"}, "directories": {}, "publish_time": 1516113890639, "_hasShrinkwrap": false, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1516113890639, "_cnpmcore_publish_time": "2021-12-13T12:08:56.577Z"}, "3.1.0": {"name": "uuid", "version": "3.1.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"mocha": "3.1.2"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "c50ac88f098ecfbff9a940816c8e6825ffd7e05a", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "_id": "uuid@3.1.0", "_npmVersion": "5.0.3", "_nodeVersion": "7.10.0", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "dist": {"shasum": "3dd3d3e790abc24d7b0d3a034ffababe28ebbc04", "size": 8471, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-3.1.0.tgz", "integrity": "sha512-DIWtzUkw04M4k3bf1IcpS2tngXEL26YUD2M0tMDUpnUrz2hgzUBlD55a4FjdLGPvfHxS6uluGWvaVEqgBcVa+g=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid-3.1.0.tgz_1497635691778_0.6424044836312532"}, "directories": {}, "publish_time": 1497635691877, "_hasShrinkwrap": false, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1497635691877, "_cnpmcore_publish_time": "2021-12-13T12:08:57.191Z"}, "3.0.1": {"name": "uuid", "version": "3.0.1", "description": "RFC4122 (v1 and v4) generator", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"mocha": "3.1.2"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "374de826de71d8997f71b4641f65552e48956ced", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "_id": "uuid@3.0.1", "_shasum": "6544bba2dfda8c1cf17e629a3a305e2bb1fee6c1", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "6544bba2dfda8c1cf17e629a3a305e2bb1fee6c1", "size": 7195, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-3.0.1.tgz", "integrity": "sha512-tyhM7iisckwwmyHVFcjTzISz/R1ss/bRudNgHFYsgeu7j4JbhRvjE+Hbcpr9y5xh+b+HxeFjuToDT4i9kQNrtA=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/uuid-3.0.1.tgz_1480403886767_0.2584113120101392"}, "directories": {}, "publish_time": 1480403887016, "_hasShrinkwrap": false, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1480403887016, "_cnpmcore_publish_time": "2021-12-13T12:08:57.783Z"}, "3.0.0": {"name": "uuid", "version": "3.0.0", "description": "RFC4122 (v1 and v4) generator", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "main": "./uuid.js", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"mocha": "3.1.2"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "923fe4a7893c2057b608c0c74743ad5512599072", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "_id": "uuid@3.0.0", "_shasum": "6728fc0459c450d796a99c31837569bdf672d728", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "6728fc0459c450d796a99c31837569bdf672d728", "size": 6878, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-3.0.0.tgz", "integrity": "sha512-rqE1LoOVLv3QrZMjb4NkF5UWlkurCfPyItVnFPNKDDGkHw4dQUdE4zMcLqx28+0Kcf3+bnUk4PisaiRJT4aiaQ=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/uuid-3.0.0.tgz_1479448535568_0.9578766466584057"}, "directories": {}, "publish_time": 1479448537542, "_hasShrinkwrap": false, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1479448537542, "_cnpmcore_publish_time": "2021-12-13T12:08:58.514Z"}, "2.0.3": {"name": "uuid", "version": "2.0.3", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "main": "./uuid.js", "devDependencies": {"mocha": "1.8.0"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./rng.js": "./rng-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/defunctzombie/node-uuid.git"}, "testling": {"browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"], "harness": "mocha-tdd", "files": "test/*.js"}, "gitHead": "3f44acd0e722e965c14af816e2f658361a6b15f9", "bugs": {"url": "https://github.com/defunctzombie/node-uuid/issues"}, "homepage": "https://github.com/defunctzombie/node-uuid#readme", "_id": "uuid@2.0.3", "_shasum": "67e2e863797215530dff318e5bf9dcebfd47b21a", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.2.0", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "67e2e863797215530dff318e5bf9dcebfd47b21a", "size": 12162, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-2.0.3.tgz", "integrity": "sha512-FULf7fayPdpASncVy4DLh3xydlXEJJpvIELjYjNeQWYUZ9pclcpvCZSr2gkmN2FrrGcI7G/cJsIEwk5/8vfXpg=="}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/uuid-2.0.3.tgz_1474232617862_0.6578061426989734"}, "directories": {}, "publish_time": 1474232618127, "_hasShrinkwrap": false, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1474232618127, "_cnpmcore_publish_time": "2021-12-13T12:08:59.132Z"}, "2.0.2": {"name": "uuid", "version": "2.0.2", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "main": "./uuid.js", "devDependencies": {"mocha": "1.8.0"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./rng.js": "./rng-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/shtylman/node-uuid.git"}, "testling": {"browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"], "harness": "mocha-tdd", "files": "test/*.js"}, "gitHead": "6e95855ff4b79881aa95c5502478314adc6719dc", "bugs": {"url": "https://github.com/shtylman/node-uuid/issues"}, "homepage": "https://github.com/shtylman/node-uuid#readme", "_id": "uuid@2.0.2", "_shasum": "48bd5698f0677e3c7901a1c46ef15b1643794726", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "vvo", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "dist": {"shasum": "48bd5698f0677e3c7901a1c46ef15b1643794726", "size": 12142, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-2.0.2.tgz", "integrity": "sha512-BooSif/UQWXwaQme+4z32duvmtUUz/nlHsyGrrSCgsGf6snMrp9q/n1nGHwQzU12kaCeceODmAiRZA8TCK06jA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/uuid-2.0.2.tgz_1460533041436_0.07706069457344711"}, "directories": {}, "publish_time": 1460533043788, "_hasShrinkwrap": false, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1460533043788, "_cnpmcore_publish_time": "2021-12-13T12:08:59.780Z"}, "2.0.1": {"name": "uuid", "version": "2.0.1", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "main": "./uuid.js", "devDependencies": {"mocha": "1.8.0"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./rng.js": "./rng-browser.js"}, "repository": {"type": "git", "url": "https://github.com/shtylman/node-uuid.git"}, "testling": {"browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"], "harness": "mocha-tdd", "files": "test/*.js"}, "gitHead": "ddaf90942095f26ee8c1961b4346f093b3e7eb5b", "bugs": {"url": "https://github.com/shtylman/node-uuid/issues"}, "homepage": "https://github.com/shtylman/node-uuid", "_id": "uuid@2.0.1", "_shasum": "c2a30dedb3e535d72ccf82e343941a50ba8533ac", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "vvo", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "dist": {"shasum": "c2a30dedb3e535d72ccf82e343941a50ba8533ac", "size": 12187, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-2.0.1.tgz", "integrity": "sha512-nWg9+Oa3qD2CQzHIP4qKUqwNfzKn8P0LtFhotaCTFchsV7ZfDhAybeip/HZVeMIpZi9JgY1E3nUlwaCmZT1sEg=="}, "directories": {}, "publish_time": 1411982215228, "_hasShrinkwrap": false, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1411982215228, "_cnpmcore_publish_time": "2021-12-13T12:09:00.446Z"}, "2.0.0": {"name": "uuid", "version": "2.0.0", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "main": "./uuid.js", "devDependencies": {"mocha": "1.8.0"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./rng.js": "./rng-browser.js"}, "repository": {"type": "git", "url": "https://github.com/shtylman/node-uuid.git"}, "testling": {"browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"], "harness": "mocha-tdd", "files": "test/*.js"}, "gitHead": "3c007a5748d6fcbd0916ea8b6d18539958972932", "bugs": {"url": "https://github.com/shtylman/node-uuid/issues"}, "homepage": "https://github.com/shtylman/node-uuid", "_id": "uuid@2.0.0", "_shasum": "377ab4417736dba5ce379ff0a0c1a539921ebb74", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "vvo", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "dist": {"shasum": "377ab4417736dba5ce379ff0a0c1a539921ebb74", "size": 12186, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-2.0.0.tgz", "integrity": "sha512-MgCjmgHKiEVlRQ24qLbInOkKOrg1g8VhoXlzFHWY5dXfT/HLfcUomFyoQPIpp7YZ3ymtteUJBYhcYISFmmnsHw=="}, "directories": {}, "publish_time": 1411981404612, "_hasShrinkwrap": false, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1411981404612, "_cnpmcore_publish_time": "2021-12-13T12:09:01.058Z"}, "1.4.2": {"name": "uuid", "version": "1.4.2", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "main": "./uuid.js", "devDependencies": {"mocha": "1.8.0"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./rng.js": "./rng-browser.js", "./buffer.js": "./buffer-browser.js"}, "repository": {"type": "git", "url": "https://github.com/shtylman/node-uuid.git"}, "testling": {"browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"], "harness": "mocha-tdd", "files": "test/*.js"}, "gitHead": "688730efe3ec3ab7c12c6b92db2aa2826a50ed14", "bugs": {"url": "https://github.com/shtylman/node-uuid/issues"}, "homepage": "https://github.com/shtylman/node-uuid", "_id": "uuid@1.4.2", "_shasum": "453019f686966a6df83cdc5244e7c990ecc332fc", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "vvo", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "dist": {"shasum": "453019f686966a6df83cdc5244e7c990ecc332fc", "size": 12420, "noattachment": false, "tarball": "https://registry.npmmirror.com/uuid/-/uuid-1.4.2.tgz", "integrity": "sha512-woV5Ei+GBJyrqMXt0mJ9p8/I+47LYKp/4urH76FNTMjl22EhLPz1tNrQufTsrFf/PYV/7ctSZYAK7fKPWQKg+Q=="}, "directories": {}, "publish_time": 1411635814083, "_hasShrinkwrap": false, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1411635814083, "_cnpmcore_publish_time": "2021-12-13T12:09:01.728Z"}, "1.4.1": {"name": "uuid", "version": "1.4.1", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "main": "./uuid.js", "devDependencies": {"mocha": "1.8.0"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./rng.js": "./rng-browser.js"}, "repository": {"type": "git", "url": "https://github.com/shtylman/node-uuid.git"}, "testling": {"browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"], "harness": "mocha-tdd", "files": "test/*.js"}, "readmeFilename": "README.md", "_id": "uuid@1.4.1", "dist": {"tarball": "https://registry.npmmirror.com/uuid/-/uuid-1.4.1.tgz", "shasum": "a337828580d426e375b8ee11bd2bf901a596e0b8", "size": 12136, "noattachment": false, "integrity": "sha512-VvxWRJy+jqowMX1wClasj2BIORh82/X3wkRNNpXDOh1tUxmVAbdEWRUM+yRVg30a+XBmf+duDVtMgvRiuGq0qw=="}, "_from": ".", "_npmVersion": "1.2.11", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1363238704735, "_hasShrinkwrap": false, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1363238704735, "_cnpmcore_publish_time": "2021-12-13T12:09:02.459Z"}, "1.4.0": {"name": "uuid", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "url": "http://github.com/broofa/node-uuid", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "lib": ".", "main": "./uuid.js", "repository": {"type": "git", "url": "https://github.com/broofa/node-uuid.git"}, "version": "1.4.0", "readmeFilename": "README.md", "_id": "uuid@1.4.0", "dist": {"tarball": "https://registry.npmmirror.com/uuid/-/uuid-1.4.0.tgz", "shasum": "d0d3b84ab56902e99ff952f2a17aa3986d44d36f", "size": 12204, "noattachment": false, "integrity": "sha512-IzR48RgxTHa2bbD4KtzkfO11HrwBBpN536a3D1NRBBNKMCMbHjGHQRzhCuS1cMMptTagWTLIMVYCG5SP0UyEfg=="}, "_npmVersion": "1.1.66", "_npmUser": {"name": "tim-smart", "email": "<EMAIL>"}, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1361312890376, "_hasShrinkwrap": false, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1361312890376, "_cnpmcore_publish_time": "2021-12-13T12:09:03.275Z"}, "0.0.2": {"name": "uuid", "description": "Simple libuuid bindings to allow UUIDs to be generated from JS.", "version": "0.0.2", "author": {"name": "<PERSON><PERSON>"}, "repository": {"type": "hg", "url": "http://bitbucket.org/nikhilm/uuidjs"}, "engine": ["node >=0.1.103"], "scripts": {"preinstall": "node-waf configure && node-waf build"}, "main": "build/default/uuid", "_id": "uuid@0.0.2", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/uuid/-/uuid-0.0.2.tgz", "shasum": "3171f2c4f58895b8b307692a335fb2349ddf6733", "size": 2547, "noattachment": false, "integrity": "sha512-3h/4V/B5W+7FmanZTk1bQMDDoNstFk/2xy0W2W1s1WX8NPU2Sgrfi3GXZQvhqVZZiQAA7A7uUgOB4xzy0ngraA=="}, "directories": {}, "publish_time": 1301559171801, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1301559171801, "_cnpmcore_publish_time": "2021-12-13T12:09:04.002Z", "hasInstallScript": true}, "0.0.1": {"name": "uuid", "description": "Simple libuuid bindings to allow UUIDs to be generated from JS.", "version": "0.0.1", "author": {"name": "<PERSON><PERSON>"}, "repository": {"type": "hg", "url": "http://bitbucket.org/nikhilm/uuidjs"}, "engine": ["node >=0.1.103"], "scripts": {"preinstall": "node-waf configure && node-waf build"}, "main": "build/default/uuid", "_id": "uuid@0.0.1", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/uuid/-/uuid-0.0.1.tgz", "shasum": "5b43a6840d25914b5a76a0664d71a51601ddec79", "size": 2499, "noattachment": false, "integrity": "sha512-x3aIUBw/J5WMm+mfHLh5b7OelhczIY5/wr/b6JapW/SYdU4Yy7mW8AQ6vxecnRjy/qqe14mLV5vdA3c+4QCO/w=="}, "directories": {}, "publish_time": 1301559171801, "maintainers": [{"name": "vvo", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "_cnpm_publish_time": 1301559171801, "_cnpmcore_publish_time": "2021-12-13T12:09:04.694Z", "hasInstallScript": true}, "9.0.0-beta.0": {"name": "uuid", "version": "9.0.0-beta.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/commonjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/native.js": "./dist/native-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.18.10", "@babel/core": "7.18.10", "@babel/eslint-parser": "7.18.9", "@babel/preset-env": "7.18.10", "@commitlint/cli": "17.0.3", "@commitlint/config-conventional": "17.0.3", "bundlewatch": "0.3.3", "eslint": "8.21.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-promise": "6.0.0", "husky": "8.0.1", "jest": "28.1.3", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "2.7.1", "random-seed": "0.3.0", "runmd": "1.3.6", "standard-version": "9.5.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "7.16.10", "@wdio/cli": "7.16.10", "@wdio/jasmine-framework": "7.16.6", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/static-server-service": "7.16.6"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "prepare": "cd $( git rev-parse --show-toplevel ) && husky install", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v16' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readmeFilename": "README.md", "gitHead": "ee8a9c1edab8d6d6cc81cbb51416b87753fd0ed5", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@9.0.0-beta.0", "_nodeVersion": "16.14.1", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-FYW2Ry9thUvDKQKekvKvQhGifh6X4FYAkbN56sYD6l4Zh8EG3GyIiqRKEq9UuIEPO/I1u/grfQxlRjTgnLxvMw==", "shasum": "164a23bd9bc422462a1274e64d34ab7a8dc3f31b", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-9.0.0-beta.0.tgz", "fileCount": 76, "unpackedSize": 119295, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDnMHDyBT0Gs8Tq9un0ZtOBm1BkO+JxBU3YULBBgj/B4AiAE9OrxsVoz7zg4nl1bPgsQmgFkbNL/7HG3MM8o0Gx2xA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7O0UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodMhAAo6mAI1wYveeyPvV4CUL8q5w1SZ66SW4xERBGaUTHj4DaxW/S\r\ntVSVJ/px+4yfCoIDb6mVN95Gvo37Q4QezE+BALzymMVdKSnlRr9e38qXfqHG\r\nb3v/MaLOYyG6zNEHVUVJN00X87PEIFMv6llJh624ghMFFn/3WwBG3ZMeQ0Qe\r\nLWdKBnENr6A7Dw51n9T9wGY7dKWs4fc2BkytMGIpH1jfYhV3S6M6LthLKOFM\r\nsGMGEagSm/3dxXwARnCRUUJWtTuQV+BlDEogq/VQCCrKcM7QwqMmf/FWZXBm\r\nCLqKqur0whw5sY3flmQ5Z66jtIQFWiYerrgl6NW+huQ0BhxXL5E95zcGfbTk\r\nrVz/iUeW3XBW99oq63+yaSS+pIuHV46XIU44w1i7BkxUOHGbnIU7LG3vwsaE\r\n+ZU3zDwIal32It/P3xL7wMht56Bo6iRZL4PMpWBoOVWJicEOOX/dmVjfuBpK\r\nNcfpG38aEBq2PBXs16UZ6NoLa07m2aEytzWMzvZhHQeqyciBUaRDI0FeCj/c\r\nq0pj/yYIfP/YAmcOv7R0EZWRkVgBsV89U564X9T7IuN7AbrSajlQB5MW5Lm/\r\nQfqEu561+nqyaDEmZfGT5YHwiJH/0ZuYD235Lu4kKRaNJY9IPTsaTxIH7l5G\r\nW0YmHAu+1hRvCTWC3dn4xLqDwtUVsDT3ba0=\r\n=+yYu\r\n-----END PGP SIGNATURE-----\r\n", "size": 22066}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_9.0.0-beta.0_1659694356059_0.631985123339952"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-08-05T10:48:39.896Z"}, "9.0.0": {"name": "uuid", "version": "9.0.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/commonjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/native.js": "./dist/native-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.18.10", "@babel/core": "7.18.10", "@babel/eslint-parser": "7.18.9", "@babel/preset-env": "7.18.10", "@commitlint/cli": "17.0.3", "@commitlint/config-conventional": "17.0.3", "bundlewatch": "0.3.3", "eslint": "8.21.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-promise": "6.0.0", "husky": "8.0.1", "jest": "28.1.3", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "2.7.1", "random-seed": "0.3.0", "runmd": "1.3.6", "standard-version": "9.5.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "7.16.10", "@wdio/cli": "7.16.10", "@wdio/jasmine-framework": "7.16.6", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/static-server-service": "7.16.6"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "prepare": "cd $( git rev-parse --show-toplevel ) && husky install", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v16' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "4cf24c018cead5ebe48cb4da232b57a2345d9fb5", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@9.0.0", "_nodeVersion": "16.14.1", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-MXcSTerfPa4uqyzStbRoTgt5XIe3x5+42+q1sDuy3R5MDk66URdLMOZe5aPX/SQd+kuYAh0FdP/pO28IkQyTeg==", "shasum": "592f550650024a38ceb0c562f2f6aa435761efb5", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-9.0.0.tgz", "fileCount": 76, "unpackedSize": 122687, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmKbDr6WecVwMEbEwyKBiUaPniMBvoQeVW4/TgnB4XzwIhAPnKsWjySkMnuquSAkRxJl8MsKPm3S8so3Z19XguF1NJ"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFlYqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9Ig//av6W4HOYtB3r5tW8Ie10yCvrJIAs8DqTN0hTvmuDvepR+iQM\r\ngEd/T/yL1UoWLE789CMNbsq4rz/LAhDzES52rjevFhE4VPC9P1ZqMvwLnQnS\r\n4BSsDUoJ58VoyCH+oRRR5YiBu/BJBHfGsiAgaIrEyq4hliC9FXs5CZCebJpd\r\nU/E2JX/G9yF5OR/UO5vuHRX7AORQh6WTnsT6qaCJK325vzA8POvMR3TDowAF\r\nltyDq8nlYDeDLEh84Qvv7QSJuQAQ3i1qBVPoXyuTTKS6w8vl+m1wUrxkEblJ\r\nVlxdjGvwlgq7MFL/pkiKgtaaK8ccQcqHn8wEJjqR/i9OpQmC2jFr9qeVrG1A\r\naJy6N8OTSd1iI4P6AprE8VnhXj/OZnZUR6p9oDfwGZAuiMjK3pSvEKXiaQqz\r\n31VRwq2mp+7Kq4AnIbRBkFWKm3L3c9lTa1ltNAW02585x9jxLPABMxd7vLip\r\n4VANifKwtfuTVcEwDGO8kdFbyeuiNRxf8igL041KWbINzQRsb/ElZg3jk//u\r\nFjZBMviIf9CK1IOCO1gX4YoEj4pFvwGkuuFPL145jfyKiGr/4UMw2/OjCJOo\r\no35Xf4yc4YUloK4D3auxXtbTn8jKq8cIxsLo3pITudQc7zWaYs8HkrRca3A/\r\nbbcDTamFyh736XTsJR3P9Cb6BT3uGWJ0uPc=\r\n=Ju3Q\r\n-----END PGP SIGNATURE-----\r\n", "size": 23235}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_9.0.0_1662408234545_0.8259576626529794"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-09-05T20:04:24.793Z"}, "9.0.1": {"name": "uuid", "version": "9.0.1", "description": "RFC4122 (v1, v4, and v5) UUIDs", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/commonjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/native.js": "./dist/native-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "devDependencies": {"@babel/cli": "7.18.10", "@babel/core": "7.18.10", "@babel/eslint-parser": "7.18.9", "@babel/preset-env": "7.18.10", "@commitlint/cli": "17.0.3", "@commitlint/config-conventional": "17.0.3", "bundlewatch": "0.3.3", "eslint": "8.21.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-promise": "6.0.0", "husky": "8.0.1", "jest": "28.1.3", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "2.7.1", "random-seed": "0.3.0", "runmd": "1.3.9", "standard-version": "9.5.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "7.16.10", "@wdio/cli": "7.16.10", "@wdio/jasmine-framework": "7.16.6", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/static-server-service": "7.16.6"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "prepare": "cd $( git rev-parse --show-toplevel ) && husky install", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && npm install && npm test", "prettier:check": "prettier --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v18' ) && ( npm run build && npx runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "ca1d39d58a6308d5311bcb356a931aa818ec0ded", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_id": "uuid@9.0.1", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==", "shasum": "e188d4c8853cc722220392c424cd637f32293f30", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-9.0.1.tgz", "fileCount": 76, "unpackedSize": 123288, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvscJxv7mXEiTE+Ykp+FxcDL4/XiNCg4qDG+EPH07gaQIhAPzoirDjELJCA3SqLrbV0nJFhheyCg+AGW6BJ2QolPKT"}], "size": 23487}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_9.0.1_1694508995051_0.21952707353933043"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-12T08:56:35.205Z", "publish_time": 1694508995205, "_source_registry_name": "default"}, "10.0.0": {"name": "uuid", "version": "10.0.0", "description": "RFC9562 UUIDs", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {".": {"node": {"module": "./dist/esm-node/index.js", "require": "./dist/index.js", "import": "./wrapper.mjs"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/commonjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/esm-node/index.js": "./dist/esm-browser/index.js", "./dist/md5.js": "./dist/md5-browser.js", "./dist/native.js": "./dist/native-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js"}, "devDependencies": {"@babel/cli": "7.24.6", "@babel/core": "7.24.6", "@babel/eslint-parser": "7.24.6", "@babel/plugin-syntax-import-attributes": "7.24.6", "@babel/preset-env": "7.24.6", "@commitlint/cli": "19.3.0", "@commitlint/config-conventional": "19.2.2", "@wdio/browserstack-service": "7.16.10", "@wdio/cli": "7.16.10", "@wdio/jasmine-framework": "7.16.6", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/static-server-service": "7.16.6", "bundlewatch": "0.3.3", "eslint": "9.4.0", "eslint-plugin-prettier": "5.1.3", "globals": "15.3.0", "husky": "9.0.11", "jest": "29.7.0", "lint-staged": "15.2.5", "neostandard": "0.5.1", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "3.3.0", "random-seed": "0.3.0", "runmd": "1.3.9", "standard-version": "9.5.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "7.16.10", "@wdio/cli": "7.16.10", "@wdio/jasmine-framework": "7.16.6", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/static-server-service": "7.16.6"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "prepare": "husky install", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "npm run build", "test": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/", "test:matching": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/ -t", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "pretest:benchmark": "npm run build", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@10.8.1+sha256.b8807aebb9656758e2872fa6e7c564b506aa2faa9297439a478d471d2fe32483", "_id": "uuid@10.0.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "5388bbb03bbb426ade0bb32ad0b0f6a2e8d69042", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==", "shasum": "5a95aa454e6e002725c79055fd42aaba30ca6294", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-10.0.0.tgz", "fileCount": 96, "unpackedSize": 168173, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCL55oFsbUUreujw9RQl+8DxybBLO+ccjIG4q3U5SV8ngIhAPV/TPJWjej3tuO1uwAw8Y/DWwpB/uu/aDvKTJWH/QUA"}], "size": 29328}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_10.0.0_1717940524111_0.5936782976152961"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-09T13:42:04.366Z", "publish_time": 1717940524366, "_source_registry_name": "default"}, "11.0.0-0": {"name": "uuid", "version": "11.0.0-0", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "_id": "uuid@11.0.0-0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "b9d151889aa116aceaa21c1a1a5e0d44a7fcbccc", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-11.0.0-0.tgz", "fileCount": 281, "integrity": "sha512-gPhXpKFuxFX0BvpbLtzvYQf+aqKWDGL0mpjrIg6k/DgG/VrOdZ4+RbmSeP89UVLsgGxecQ2n7aE6OESwYYnCpg==", "signatures": [{"sig": "MEQCIG9krhIVIlTZAM4Q/Pxl0+PAxhUUbWG1s6akuBOFRI9zAiAOVUs0qUkJyIaA1zuGIVOjkdjgCdz1ir71sLs7CkSSMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650868, "size": 67306}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.js", "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "f7fd0bd544ee3dc4fe08d1c9e98ffee682ed73c4", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "lint": "npm run eslint:check && npm run prettier:check", "test": "node --test --enable-source-maps dist/esm/test", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "husky", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "test:watch": "node --test --enable-source-maps --watch dist/esm/test", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "pretest:node": "npm run build", "prettier:fix": "prettier --write .", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check .", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "RFC9562 UUIDs", "directories": {}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "sideEffects": false, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "packageManager": "npm@10.8.2+sha256.c8c61ba0fa0ab3b5120efd5ba97fdaf0e0b495eef647a97c4413919eda0a878b", "readmeFilename": "README.md", "devDependencies": {"jest": "29.7.0", "husky": "9.1.1", "runmd": "1.3.9", "eslint": "9.7.0", "globals": "15.8.0", "prettier": "3.3.3", "@wdio/cli": "9.0.9", "@eslint/js": "9.7.0", "typescript": "5.5.3", "bundlewatch": "0.3.3", "lint-staged": "15.2.7", "neostandard": "0.11.1", "npm-run-all": "4.1.5", "@commitlint/cli": "19.3.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.0.0-alpha.30", "@wdio/local-runner": "9.0.9", "@wdio/spec-reporter": "9.0.8", "@babel/eslint-parser": "7.24.8", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "@wdio/jasmine-framework": "9.0.9", "optional-dev-dependency": "2.0.1", "@wdio/browserstack-service": "9.0.9", "@wdio/static-server-service": "9.0.8", "@commitlint/config-conventional": "19.2.2"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_11.0.0-0_1725570506587_0.7037416393141083", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "9.0.9", "@wdio/local-runner": "9.0.9", "@wdio/spec-reporter": "9.0.8", "@wdio/jasmine-framework": "9.0.9", "@wdio/browserstack-service": "9.0.9", "@wdio/static-server-service": "9.0.8"}, "_cnpmcore_publish_time": "2024-09-05T21:08:26.805Z", "publish_time": 1725570506805, "_source_registry_name": "default"}, "11.0.0": {"name": "uuid", "version": "11.0.0", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}, "sideEffects": false, "main": "./dist/cjs/index.js", "exports": {".": {"node": {"module": "./dist/esm/index.js", "require": "./dist/cjs/index.js", "import": "./wrapper.mjs"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm/index.js", "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@eslint/js": "9.13.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "eslint": "9.13.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.11.0", "husky": "9.1.6", "jest": "29.7.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "3.3.3", "release-please": "16.14.3", "runmd": "1.3.9", "standard-version": "9.5.0", "typescript": "5.6.3", "typescript-eslint": "8.11.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build", "prepare": "husky", "pretest:benchmark": "npm run build", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "test": "node --test --enable-source-maps dist/esm/test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@10.9.0", "_id": "uuid@11.0.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "964be22f812c89ee2e1c1fb335eea3b2163f45b3", "types": "./dist/cjs/index.d.ts", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_nodeVersion": "20.18.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-iE8Fa5fgBY4rN5GvNUJ8TSwO1QG7TzdPfhrJczf6XJ6mZUxh/GX433N70fCiJL9h8EKP5ayEIo0Q6EBQGWHFqA==", "shasum": "01c1f5492ed10ad2c0fba1ae1f6d542e6b568d0c", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-11.0.0.tgz", "fileCount": 281, "unpackedSize": 667676, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWrOJWNatNXcL1pMUOznnkHDsGU1CRtzT36hSfM0rCVwIhAMDFbImN0yh7LmQzMyhwTfRdAAgreWUl2qtrBvuEjlEL"}], "size": 68505}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_11.0.0_1730039713404_0.7280415691476416"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-27T14:35:13.696Z", "publish_time": 1730039713696, "_source_registry_name": "default"}, "11.0.1": {"name": "uuid", "version": "11.0.1", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}, "sideEffects": false, "main": "./dist/cjs/index.js", "exports": {".": {"node": {"module": "./dist/esm/index.js", "require": "./dist/cjs/index.js", "import": "./wrapper.mjs"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm/index.js", "browser": {"./dist/esm/index.js": "./dist/esm-browser/index.js", "./dist/cjs/index.js": "./dist/cjs-browser/index.js"}, "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@eslint/js": "9.13.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "eslint": "9.13.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.11.0", "husky": "9.1.6", "jest": "29.7.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "3.3.3", "release-please": "16.14.3", "runmd": "1.3.9", "standard-version": "9.5.0", "typescript": "5.6.3", "typescript-eslint": "8.11.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build", "prepare": "husky", "pretest:benchmark": "npm run build", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "test": "node --test --enable-source-maps dist/esm/test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@10.9.0", "_id": "uuid@11.0.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "50dc0cee4b5879f1b77698b0103f38aac902939d", "types": "./dist/cjs/index.d.ts", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_nodeVersion": "20.16.0", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-wt9UB5EcLhnboy1UvA1mvGPXkIIrHSu+3FmUksARfdVw9tuPf3CH/CohxO0Su1ApoKAeT6BVzAJIvjTuQVSmuQ==", "shasum": "a527e188c4c11a7ff5d139e59f229a9f90440669", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-11.0.1.tgz", "fileCount": 281, "unpackedSize": 650767, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBRJbGueTPd1gtkU1IoPh1dEhkFvJvWNEVfHXseVOiulAiEAl4LyNbDMXqgaJmZynfQpT5txlVbn5fYtKCGMCaGKHy0="}], "size": 67439}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_11.0.1_1730065224431_0.0730012896207195"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-27T21:40:24.728Z", "publish_time": 1730065224728, "_source_registry_name": "default"}, "11.0.2": {"name": "uuid", "version": "11.0.2", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}, "sideEffects": false, "main": "./dist/cjs/index.js", "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm/index.js", "browser": {"./dist/esm/index.js": "./dist/esm-browser/index.js", "./dist/cjs/index.js": "./dist/cjs-browser/index.js"}, "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@eslint/js": "9.13.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "eslint": "9.13.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.11.0", "husky": "9.1.6", "jest": "29.7.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "optional-dev-dependency": "2.0.1", "prettier": "3.3.3", "release-please": "16.14.3", "runmd": "1.3.9", "standard-version": "9.5.0", "typescript": "5.6.3", "typescript-eslint": "8.11.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build", "prepare": "husky", "pretest:benchmark": "npm run build", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "test": "node --test --enable-source-maps dist/esm/test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@10.9.0", "_id": "uuid@11.0.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "36f2369af6479a46db1eb4fdffb73cbbf6108cf4", "types": "./dist/cjs/index.d.ts", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_nodeVersion": "20.18.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-14FfcOJmqdjbBPdDjFQyk/SdT4NySW4eM0zcG+HqbHP5jzuH56xO3J1DGhgs/cEMCfwYi3HQI1gnTO62iaG+tQ==", "shasum": "a8d68ba7347d051e7ea716cc8dcbbab634d66875", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-11.0.2.tgz", "fileCount": 280, "unpackedSize": 650519, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDF728NgEqz+WQIJbzy741TEBqKjasQ9TbpEEOQahTgYQIhAIJTpCpkp5opLTipKoL1yFxnb2EHYn/lJ/EokNGg1jGf"}], "size": 67371}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_11.0.2_1730136431873_0.18215081892152507"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-10-28T17:27:12.069Z", "publish_time": 1730136432069, "_source_registry_name": "default"}, "11.0.3": {"name": "uuid", "version": "11.0.3", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}, "sideEffects": false, "main": "./dist/cjs/index.js", "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm/index.js", "browser": {"./dist/esm/index.js": "./dist/esm-browser/index.js", "./dist/cjs/index.js": "./dist/cjs-browser/index.js"}, "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@eslint/js": "9.13.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "commander": "12.1.0", "eslint": "9.13.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.11.0", "husky": "9.1.6", "jest": "29.7.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "prettier": "3.3.3", "release-please": "16.14.3", "runmd": "1.3.9", "standard-version": "9.5.0", "typescript": "5.6.3", "typescript-eslint": "8.11.0"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build", "prepare": "husky", "pretest:benchmark": "npm run build", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "test": "node --test --enable-source-maps dist/esm/test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@10.9.0", "_id": "uuid@11.0.3", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "1370497eecb5c4a570da3d76aa1b47b86448470f", "types": "./dist/cjs/index.d.ts", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_nodeVersion": "20.18.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-d0z310fCWv5dJwnX1Y/MncBAqGMKEzlBb1AOf7z9K8ALnd0utBX/msg/fA0+sbyN1ihbMsLhrBlnl1ak7Wa0rg==", "shasum": "248451cac9d1a4a4128033e765d137e2b2c49a3d", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-11.0.3.tgz", "fileCount": 280, "unpackedSize": 293890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlhgS+bpevg+LIumEE6aJc+nlUQbPIQoXIIaxgS1W0FgIgaiuAjF9FIkiT1PKAYtCESbbz2IsTcMH2F98bsW4F/PE="}], "size": 37399}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid_11.0.3_1731273954060_0.9488239716412279"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-11-10T21:25:54.278Z", "publish_time": 1731273954278, "_source_registry_name": "default"}, "11.0.4": {"name": "uuid", "version": "11.0.4", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}, "sideEffects": false, "main": "./dist/cjs/index.js", "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm/index.js", "browser": {"./dist/esm/index.js": "./dist/esm-browser/index.js", "./dist/cjs/index.js": "./dist/cjs-browser/index.js"}, "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.6.1", "@commitlint/config-conventional": "19.6.0", "@eslint/js": "9.17.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "commander": "12.1.0", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "prettier": "3.4.2", "release-please": "16.15.0", "runmd": "1.4.1", "standard-version": "9.5.0", "typescript": "5.7.2", "typescript-eslint": "8.18.2"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build", "prepare": "husky", "pretest:benchmark": "npm run build", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "test": "node --test --enable-source-maps dist/esm/test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@11.0.0", "_id": "uuid@11.0.4", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "050cd5b9df5aa73097a1677b9e7c3482eb4367fc", "types": "./dist/cjs/index.d.ts", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-IzL6VtTTYcAhA/oghbFJ1Dkmqev+FpQWnCBaKq/gUluLxliWvO8DPFWfIviRmYbtaavtSQe4WBL++rFjdcGWEg==", "shasum": "37943977894ef806d2919a7ca3f89d6e23c60bac", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-11.0.4.tgz", "fileCount": 190, "unpackedSize": 131611, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD73ZGLsRD8FEQs09uxY7bdiLKinaBl8TpN1QaBqDH6ywIhANBgX8TG4FpbgQK3LTRin7rhkx/XWDeFbxeetZ5P2DXC"}], "size": 19808}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/uuid_11.0.4_1736090212294_0.85428434872553"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-05T15:16:52.451Z", "publish_time": 1736090212451, "_source_registry_name": "default"}, "11.0.5": {"name": "uuid", "version": "11.0.5", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}, "sideEffects": false, "main": "./dist/cjs/index.js", "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm/index.js", "browser": {"./dist/esm/index.js": "./dist/esm-browser/index.js", "./dist/cjs/index.js": "./dist/cjs-browser/index.js"}, "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.6.1", "@commitlint/config-conventional": "19.6.0", "@eslint/js": "9.17.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "commander": "12.1.0", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "prettier": "3.4.2", "release-please": "16.15.0", "runmd": "1.4.1", "standard-version": "9.5.0", "typescript": "5.0.4", "typescript-eslint": "8.18.2"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm test", "examples:node:jest:test": "cd examples/node-jest && npm test", "examples:node:typescript:test": "cd examples/typescript && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build -- --no-pack", "prepare": "husky", "prepublishOnly": "npm run build", "pretest:benchmark": "npm run build", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "test": "node --test --enable-source-maps dist/esm/test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@11.0.0", "_id": "uuid@11.0.5", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "46ada3cbc4acdc907dd6924eaedcc2c53dc6095a", "types": "./dist/cjs/index.d.ts", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-508e6IcKLrhxKdBbcA2b4KQZlLVp2+J5UwQ6F7Drckkc5N9ZJwFa4TgWtsww9UG8fGHbm6gbV19TdM5pQ4GaIA==", "shasum": "07b46bdfa6310c92c3fb3953a8720f170427fc62", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-11.0.5.tgz", "fileCount": 190, "unpackedSize": 131959, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlJZBK51EKTUbNwOmydZJHMg+rMDbwD5oTQxWZu4nXTAIgdhSo9LABv0rnkX14aqTWCnDrPGbUAFacYTf91fkNuVs="}], "size": 19888}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/uuid_11.0.5_1736462418706_0.8467112808117361"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-09T22:40:18.873Z", "publish_time": 1736462418873, "_source_registry_name": "default"}, "11.1.0": {"name": "uuid", "version": "11.1.0", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}, "sideEffects": false, "main": "./dist/cjs/index.js", "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm/index.js", "browser": {"./dist/esm/index.js": "./dist/esm-browser/index.js", "./dist/cjs/index.js": "./dist/cjs-browser/index.js"}, "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.6.1", "@commitlint/config-conventional": "19.6.0", "@eslint/js": "9.17.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "commander": "12.1.0", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "prettier": "3.4.2", "release-please": "16.15.0", "runmd": "1.4.1", "standard-version": "9.5.0", "typescript": "5.0.4", "typescript-eslint": "8.18.2"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm test", "examples:node:jest:test": "cd examples/node-jest && npm test", "examples:node:typescript:test": "cd examples/typescript && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build -- --no-pack", "prepare": "husky", "prepublishOnly": "npm run build", "pretest:benchmark": "npm run build", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "test": "node --test --enable-source-maps dist/esm/test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@11.0.0", "_id": "uuid@11.1.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "3d1eba06be81fb3a02e16d06ef6fe959c9bb5c5c", "types": "./dist/cjs/index.d.ts", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==", "shasum": "9549028be1753bb934fc96e2bca09bb4105ae912", "tarball": "https://registry.npmmirror.com/uuid/-/uuid-11.1.0.tgz", "fileCount": 190, "unpackedSize": 132894, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAwmSSeNa1LZOnX1xHZTmKjAbNtJrVs4X6AGfFtdU6TgAiEA0QQOK2FF4KY5Qv7/xBIx19YeJ8hWbpvdw1M3Q8YSROw="}], "size": 19964}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/uuid_11.1.0_1739988971419_0.4427353848167064"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-19T18:16:11.602Z", "publish_time": 1739988971602, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "_source_registry_name": "default"}