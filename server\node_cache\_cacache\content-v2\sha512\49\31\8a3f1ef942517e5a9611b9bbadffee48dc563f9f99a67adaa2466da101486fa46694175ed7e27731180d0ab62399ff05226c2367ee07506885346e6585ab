{"_attachments": {}, "_id": "tar", "_rev": "2525-61f1494da920628a7b6f472d", "author": {"name": "<PERSON>"}, "description": "tar for node", "dist-tags": {"latest": "7.4.3", "v3-legacy": "3.2.3", "v4-legacy": "4.4.19", "v5-legacy": "5.0.11"}, "license": "ISC", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "tar", "readme": "# node-tar\n\nFast and full-featured Tar for Node.js\n\nThe API is designed to mimic the behavior of `tar(1)` on unix systems.\nIf you are familiar with how tar works, most of this will hopefully be\nstraightforward for you. If not, then hopefully this module can teach\nyou useful unix skills that may come in handy someday :)\n\n## Background\n\nA \"tar file\" or \"tarball\" is an archive of file system entries\n(directories, files, links, etc.) The name comes from \"tape archive\".\nIf you run `man tar` on almost any Unix command line, you'll learn\nquite a bit about what it can do, and its history.\n\nTar has 5 main top-level commands:\n\n- `c` Create an archive\n- `r` Replace entries within an archive\n- `u` Update entries within an archive (ie, replace if they're newer)\n- `t` List out the contents of an archive\n- `x` Extract an archive to disk\n\nThe other flags and options modify how this top level function works.\n\n## High-Level API\n\nThese 5 functions are the high-level API. All of them have a\nsingle-character name (for unix nerds familiar with `tar(1)`) as well\nas a long name (for everyone else).\n\nAll the high-level functions take the following arguments, all three\nof which are optional and may be omitted.\n\n1. `options` - An optional object specifying various options\n2. `paths` - An array of paths to add or extract\n3. `callback` - Called when the command is completed, if async. (If\n   sync or no file specified, providing a callback throws a\n   `TypeError`.)\n\nIf the command is sync (ie, if `options.sync=true`), then the\ncallback is not allowed, since the action will be completed immediately.\n\nIf a `file` argument is specified, and the command is async, then a\n`Promise` is returned. In this case, if async, a callback may be\nprovided which is called when the command is completed.\n\nIf a `file` option is not specified, then a stream is returned. For\n`create`, this is a readable stream of the generated archive. For\n`list` and `extract` this is a writable stream that an archive should\nbe written into. If a file is not specified, then a callback is not\nallowed, because you're already getting a stream to work with.\n\n`replace` and `update` only work on existing archives, and so require\na `file` argument.\n\nSync commands without a file argument return a stream that acts on its\ninput immediately in the same tick. For readable streams, this means\nthat all of the data is immediately available by calling\n`stream.read()`. For writable streams, it will be acted upon as soon\nas it is provided, but this can be at any time.\n\n### Warnings and Errors\n\nTar emits warnings and errors for recoverable and unrecoverable situations,\nrespectively. In many cases, a warning only affects a single entry in an\narchive, or is simply informing you that it's modifying an entry to comply\nwith the settings provided.\n\nUnrecoverable warnings will always raise an error (ie, emit `'error'` on\nstreaming actions, throw for non-streaming sync actions, reject the\nreturned Promise for non-streaming async operations, or call a provided\ncallback with an `Error` as the first argument). Recoverable errors will\nraise an error only if `strict: true` is set in the options.\n\nRespond to (recoverable) warnings by listening to the `warn` event.\nHandlers receive 3 arguments:\n\n- `code` String. One of the error codes below. This may not match\n  `data.code`, which preserves the original error code from fs and zlib.\n- `message` String. More details about the error.\n- `data` Metadata about the error. An `Error` object for errors raised by\n  fs and zlib. All fields are attached to errors raisd by tar. Typically\n  contains the following fields, as relevant:\n  - `tarCode` The tar error code.\n  - `code` Either the tar error code, or the error code set by the\n    underlying system.\n  - `file` The archive file being read or written.\n  - `cwd` Working directory for creation and extraction operations.\n  - `entry` The entry object (if it could be created) for `TAR_ENTRY_INFO`,\n    `TAR_ENTRY_INVALID`, and `TAR_ENTRY_ERROR` warnings.\n  - `header` The header object (if it could be created, and the entry could\n    not be created) for `TAR_ENTRY_INFO` and `TAR_ENTRY_INVALID` warnings.\n  - `recoverable` Boolean. If `false`, then the warning will emit an\n    `error`, even in non-strict mode.\n\n#### Error Codes\n\n- `TAR_ENTRY_INFO` An informative error indicating that an entry is being\n  modified, but otherwise processed normally. For example, removing `/` or\n  `C:\\` from absolute paths if `preservePaths` is not set.\n\n- `TAR_ENTRY_INVALID` An indication that a given entry is not a valid tar\n  archive entry, and will be skipped. This occurs when:\n\n  - a checksum fails,\n  - a `linkpath` is missing for a link type, or\n  - a `linkpath` is provided for a non-link type.\n\n  If every entry in a parsed archive raises an `TAR_ENTRY_INVALID` error,\n  then the archive is presumed to be unrecoverably broken, and\n  `TAR_BAD_ARCHIVE` will be raised.\n\n- `TAR_ENTRY_ERROR` The entry appears to be a valid tar archive entry, but\n  encountered an error which prevented it from being unpacked. This occurs\n  when:\n\n  - an unrecoverable fs error happens during unpacking,\n  - an entry is trying to extract into an excessively deep\n    location (by default, limited to 1024 subfolders),\n  - an entry has `..` in the path and `preservePaths` is not set, or\n  - an entry is extracting through a symbolic link, when `preservePaths` is\n    not set.\n\n- `TAR_ENTRY_UNSUPPORTED` An indication that a given entry is\n  a valid archive entry, but of a type that is unsupported, and so will be\n  skipped in archive creation or extracting.\n\n- `TAR_ABORT` When parsing gzipped-encoded archives, the parser will\n  abort the parse process raise a warning for any zlib errors encountered.\n  Aborts are considered unrecoverable for both parsing and unpacking.\n\n- `TAR_BAD_ARCHIVE` The archive file is totally hosed. This can happen for\n  a number of reasons, and always occurs at the end of a parse or extract:\n\n  - An entry body was truncated before seeing the full number of bytes.\n  - The archive contained only invalid entries, indicating that it is\n    likely not an archive, or at least, not an archive this library can\n    parse.\n\n  `TAR_BAD_ARCHIVE` is considered informative for parse operations, but\n  unrecoverable for extraction. Note that, if encountered at the end of an\n  extraction, tar WILL still have extracted as much it could from the\n  archive, so there may be some garbage files to clean up.\n\nErrors that occur deeper in the system (ie, either the filesystem or zlib)\nwill have their error codes left intact, and a `tarCode` matching one of\nthe above will be added to the warning metadata or the raised error object.\n\nErrors generated by tar will have one of the above codes set as the\n`error.code` field as well, but since errors originating in zlib or fs will\nhave their original codes, it's better to read `error.tarCode` if you wish\nto see how tar is handling the issue.\n\n### Examples\n\nThe API mimics the `tar(1)` command line functionality, with aliases\nfor more human-readable option and function names. The goal is that\nif you know how to use `tar(1)` in Unix, then you know how to use\n`import('tar')` in JavaScript.\n\nTo replicate `tar czf my-tarball.tgz files and folders`, you'd do:\n\n```js\nimport { create } from 'tar'\ncreate(\n  {\n    gzip: <true|gzip options>,\n    file: 'my-tarball.tgz'\n  },\n  ['some', 'files', 'and', 'folders']\n).then(_ => { .. tarball has been created .. })\n```\n\nTo replicate `tar cz files and folders > my-tarball.tgz`, you'd do:\n\n```js\n// if you're familiar with the tar(1) cli flags, this can be nice\nimport * as tar from 'tar'\ntar.c(\n  {\n    // 'z' is alias for 'gzip' option\n    z: <true|gzip options>\n  },\n  ['some', 'files', 'and', 'folders']\n).pipe(fs.createWriteStream('my-tarball.tgz'))\n```\n\nTo replicate `tar xf my-tarball.tgz` you'd do:\n\n```js\ntar.x( // or `tar.extract`\n  {\n    // or `file:`\n    f: 'my-tarball.tgz'\n  }\n).then(_=> { .. tarball has been dumped in cwd .. })\n```\n\nTo replicate `cat my-tarball.tgz | tar x -C some-dir --strip=1`:\n\n```js\nfs.createReadStream('my-tarball.tgz').pipe(\n  tar.x({\n    strip: 1,\n    C: 'some-dir', // alias for cwd:'some-dir', also ok\n  }),\n)\n```\n\nTo replicate `tar tf my-tarball.tgz`, do this:\n\n```js\ntar.t({\n  file: 'my-tarball.tgz',\n  onReadEntry: entry => { .. do whatever with it .. }\n})\n```\n\nFor example, to just get the list of filenames from an archive:\n\n```js\nconst getEntryFilenames = async tarballFilename => {\n  const filenames = []\n  await tar.t({\n    file: tarballFilename,\n    onReadEntry: entry => filenames.push(entry.path),\n  })\n  return filenames\n}\n```\n\nTo replicate `cat my-tarball.tgz | tar t` do:\n\n```js\nfs.createReadStream('my-tarball.tgz')\n  .pipe(tar.t())\n  .on('entry', entry => { .. do whatever with it .. })\n```\n\nTo do anything synchronous, add `sync: true` to the options. Note\nthat sync functions don't take a callback and don't return a promise.\nWhen the function returns, it's already done. Sync methods without a\nfile argument return a sync stream, which flushes immediately. But,\nof course, it still won't be done until you `.end()` it.\n\n```js\nconst getEntryFilenamesSync = tarballFilename => {\n  const filenames = []\n  tar.t({\n    file: tarballFilename,\n    onReadEntry: entry => filenames.push(entry.path),\n    sync: true,\n  })\n  return filenames\n}\n```\n\nTo filter entries, add `filter: <function>` to the options.\nTar-creating methods call the filter with `filter(path, stat)`.\nTar-reading methods (including extraction) call the filter with\n`filter(path, entry)`. The filter is called in the `this`-context of\nthe `Pack` or `Unpack` stream object.\n\nThe arguments list to `tar t` and `tar x` specify a list of filenames\nto extract or list, so they're equivalent to a filter that tests if\nthe file is in the list.\n\nFor those who _aren't_ fans of tar's single-character command names:\n\n```\ntar.c === tar.create\ntar.r === tar.replace (appends to archive, file is required)\ntar.u === tar.update (appends if newer, file is required)\ntar.x === tar.extract\ntar.t === tar.list\n```\n\nKeep reading for all the command descriptions and options, as well as\nthe low-level API that they are built on.\n\n### tar.c(options, fileList, callback) [alias: tar.create]\n\nCreate a tarball archive.\n\nThe `fileList` is an array of paths to add to the tarball. Adding a\ndirectory also adds its children recursively.\n\nAn entry in `fileList` that starts with an `@` symbol is a tar archive\nwhose entries will be added. To add a file that starts with `@`,\nprepend it with `./`.\n\nThe following options are supported:\n\n- `file` Write the tarball archive to the specified filename. If this\n  is specified, then the callback will be fired when the file has been\n  written, and a promise will be returned that resolves when the file\n  is written. If a filename is not specified, then a Readable Stream\n  will be returned which will emit the file data. [Alias: `f`]\n- `sync` Act synchronously. If this is set, then any provided file\n  will be fully written after the call to `tar.c`. If this is set,\n  and a file is not provided, then the resulting stream will already\n  have the data ready to `read` or `emit('data')` as soon as you\n  request it.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `cwd` The current working directory for creating the archive.\n  Defaults to `process.cwd()`. [Alias: `C`]\n- `prefix` A path portion to prefix onto the entries in the archive.\n- `gzip` Set to any truthy value to create a gzipped archive, or an\n  object with settings for `zlib.Gzip()` [Alias: `z`]\n- `filter` A function that gets called with `(path, stat)` for each\n  entry being added. Return `true` to add the entry to the archive,\n  or `false` to omit it.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths. [Alias: `P`]\n- `mode` The mode to set on the created file archive\n- `noDirRecurse` Do not recursively archive the contents of\n  directories. [Alias: `n`]\n- `follow` Set to true to pack the targets of symbolic links. Without\n  this option, symbolic links are archived as such. [Alias: `L`, `h`]\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n  [Alias: `m`, `no-mtime`]\n- `mtime` Set to a `Date` object to force a specific `mtime` for\n  everything added to the archive. Overridden by `noMtime`.\n- `onWriteEntry` Called with each `WriteEntry` or\n  `WriteEntrySync` that is created in the course of writing the\n  archive.\n\nThe following options are mostly internal, but can be modified in some\nadvanced use cases, such as re-using caches between runs.\n\n- `linkCache` A Map object containing the device and inode value for\n  any file whose nlink is > 1, to identify hard links.\n- `statCache` A Map object that caches calls `lstat`.\n- `readdirCache` A Map object that caches calls to `readdir`.\n- `jobs` A number specifying how many concurrent jobs to run.\n  Defaults to 4.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n\n### tar.x(options, fileList, callback) [alias: tar.extract]\n\nExtract a tarball archive.\n\nThe `fileList` is an array of paths to extract from the tarball. If\nno paths are provided, then all the entries are extracted.\n\nIf the archive is gzipped, then tar will detect this and unzip it.\n\nNote that all directories that are created will be forced to be\nwritable, readable, and listable by their owner, to avoid cases where\na directory prevents extraction of child entries by virtue of its\nmode.\n\nMost extraction errors will cause a `warn` event to be emitted. If\nthe `cwd` is missing, or not a directory, then the extraction will\nfail completely.\n\nThe following options are supported:\n\n- `cwd` Extract files relative to the specified directory. Defaults\n  to `process.cwd()`. If provided, this must exist and must be a\n  directory. [Alias: `C`]\n- `file` The archive file to extract. If not specified, then a\n  Writable stream is returned where the archive data should be\n  written. [Alias: `f`]\n- `sync` Create files and directories synchronously.\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `filter` A function that gets called with `(path, entry)` for each\n  entry being unpacked. Return `true` to unpack the entry from the\n  archive, or `false` to skip it.\n- `newer` Set to true to keep the existing file on disk if it's newer\n  than the file in the archive. [Alias: `keep-newer`,\n  `keep-newer-files`]\n- `keep` Do not overwrite existing files. In particular, if a file\n  appears more than once in an archive, later copies will not\n  overwrite earlier copies. [Alias: `k`, `keep-existing`]\n- `preservePaths` Allow absolute paths, paths containing `..`, and\n  extracting through symbolic links. By default, `/` is stripped from\n  absolute paths, `..` paths are not extracted, and any file whose\n  location would be modified by a symbolic link is not extracted.\n  [Alias: `P`]\n- `unlink` Unlink files before creating them. Without this option,\n  tar overwrites existing files, which preserves existing hardlinks.\n  With this option, existing hardlinks will be broken, as will any\n  symlink that would affect the location of an extracted file. [Alias:\n  `U`]\n- `strip` Remove the specified number of leading path elements.\n  Pathnames with fewer elements will be silently skipped. Note that\n  the pathname is edited after applying the filter, but before\n  security checks. [Alias: `strip-components`, `stripComponents`]\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `preserveOwner` If true, tar will set the `uid` and `gid` of\n  extracted entries to the `uid` and `gid` fields in the archive.\n  This defaults to true when run as root, and false otherwise. If\n  false, then files and directories will be set with the owner and\n  group of the user running the process. This is similar to `-p` in\n  `tar(1)`, but ACLs and other system-specific data is never unpacked\n  in this implementation, and modes are set by default already.\n  [Alias: `p`]\n- `uid` Set to a number to force ownership of all extracted files and\n  folders, and all implicitly created directories, to be owned by the\n  specified user id, regardless of the `uid` field in the archive.\n  Cannot be used along with `preserveOwner`. Requires also setting a\n  `gid` option.\n- `gid` Set to a number to force ownership of all extracted files and\n  folders, and all implicitly created directories, to be owned by the\n  specified group id, regardless of the `gid` field in the archive.\n  Cannot be used along with `preserveOwner`. Requires also setting a\n  `uid` option.\n- `noMtime` Set to true to omit writing `mtime` value for extracted\n  entries. [Alias: `m`, `no-mtime`]\n- `transform` Provide a function that takes an `entry` object, and\n  returns a stream, or any falsey value. If a stream is provided,\n  then that stream's data will be written instead of the contents of\n  the archive entry. If a falsey value is provided, then the entry is\n  written to disk as normal. (To exclude items from extraction, use\n  the `filter` option described above.)\n- `onReadEntry` A function that gets called with `(entry)` for each entry\n  that passes the filter.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `chmod` Set to true to call `fs.chmod()` to ensure that the\n  extracted file matches the entry mode. This may necessitate a\n  call to the deprecated and thread-unsafe `process.umask()`\n  method to determine the default umask value, unless a\n  `processUmask` options is also provided. Otherwise tar will\n  extract with whatever mode is provided, and let the process\n  `umask` apply normally.\n- `processUmask` Set to an explicit numeric value to avoid\n  calling `process.umask()` when `chmod: true` is set.\n- `maxDepth` The maximum depth of subfolders to extract into. This\n  defaults to 1024. Anything deeper than the limit will raise a\n  warning and skip the entry. Set to `Infinity` to remove the\n  limitation.\n\nThe following options are mostly internal, but can be modified in some\nadvanced use cases, such as re-using caches between runs.\n\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `umask` Filter the modes of entries like `process.umask()`.\n- `dmode` Default mode for directories\n- `fmode` Default mode for files\n- `dirCache` A Map object of which directories exist.\n- `maxMetaEntrySize` The maximum size of meta entries that is\n  supported. Defaults to 1 MB.\n\nNote that using an asynchronous stream type with the `transform`\noption will cause undefined behavior in sync extractions.\n[MiniPass](http://npm.im/minipass)-based streams are designed for this\nuse case.\n\n### tar.t(options, fileList, callback) [alias: tar.list]\n\nList the contents of a tarball archive.\n\nThe `fileList` is an array of paths to list from the tarball. If\nno paths are provided, then all the entries are listed.\n\nIf the archive is gzipped, then tar will detect this and unzip it.\n\nIf the `file` option is _not_ provided, then returns an event emitter that\nemits `entry` events with `tar.ReadEntry` objects. However, they don't\nemit `'data'` or `'end'` events. (If you want to get actual readable\nentries, use the `tar.Parse` class instead.)\n\nIf a `file` option _is_ provided, then the return value will be a promise\nthat resolves when the file has been fully traversed in async mode, or\n`undefined` if `sync: true` is set. Thus, you _must_ specify an `onReadEntry`\nmethod in order to do anything useful with the data it parses.\n\nThe following options are supported:\n\n- `file` The archive file to list. If not specified, then a\n  Writable stream is returned where the archive data should be\n  written. [Alias: `f`]\n- `sync` Read the specified file synchronously. (This has no effect\n  when a file option isn't specified, because entries are emitted as\n  fast as they are parsed from the stream anyway.)\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `filter` A function that gets called with `(path, entry)` for each\n  entry being listed. Return `true` to emit the entry from the\n  archive, or `false` to skip it.\n- `onReadEntry` A function that gets called with `(entry)` for each entry\n  that passes the filter. This is important for when `file` is set,\n  because there is no other way to do anything useful with this method.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `noResume` By default, `entry` streams are resumed immediately after\n  the call to `onReadEntry`. Set `noResume: true` to suppress this\n  behavior. Note that by opting into this, the stream will never\n  complete until the entry data is consumed.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n\n### tar.u(options, fileList, callback) [alias: tar.update]\n\nAdd files to an archive if they are newer than the entry already in\nthe tarball archive.\n\nThe `fileList` is an array of paths to add to the tarball. Adding a\ndirectory also adds its children recursively.\n\nAn entry in `fileList` that starts with an `@` symbol is a tar archive\nwhose entries will be added. To add a file that starts with `@`,\nprepend it with `./`.\n\nThe following options are supported:\n\n- `file` Required. Write the tarball archive to the specified\n  filename. [Alias: `f`]\n- `sync` Act synchronously. If this is set, then any provided file\n  will be fully written after the call to `tar.c`.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `cwd` The current working directory for adding entries to the\n  archive. Defaults to `process.cwd()`. [Alias: `C`]\n- `prefix` A path portion to prefix onto the entries in the archive.\n- `gzip` Set to any truthy value to create a gzipped archive, or an\n  object with settings for `zlib.Gzip()` [Alias: `z`]\n- `filter` A function that gets called with `(path, stat)` for each\n  entry being added. Return `true` to add the entry to the archive,\n  or `false` to omit it.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths. [Alias: `P`]\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `noDirRecurse` Do not recursively archive the contents of\n  directories. [Alias: `n`]\n- `follow` Set to true to pack the targets of symbolic links. Without\n  this option, symbolic links are archived as such. [Alias: `L`, `h`]\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n  [Alias: `m`, `no-mtime`]\n- `mtime` Set to a `Date` object to force a specific `mtime` for\n  everything added to the archive. Overridden by `noMtime`.\n- `onWriteEntry` Called with each `WriteEntry` or\n  `WriteEntrySync` that is created in the course of writing the\n  archive.\n\n### tar.r(options, fileList, callback) [alias: tar.replace]\n\nAdd files to an existing archive. Because later entries override\nearlier entries, this effectively replaces any existing entries.\n\nThe `fileList` is an array of paths to add to the tarball. Adding a\ndirectory also adds its children recursively.\n\nAn entry in `fileList` that starts with an `@` symbol is a tar archive\nwhose entries will be added. To add a file that starts with `@`,\nprepend it with `./`.\n\nThe following options are supported:\n\n- `file` Required. Write the tarball archive to the specified\n  filename. [Alias: `f`]\n- `sync` Act synchronously. If this is set, then any provided file\n  will be fully written after the call to `tar.c`.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `cwd` The current working directory for adding entries to the\n  archive. Defaults to `process.cwd()`. [Alias: `C`]\n- `prefix` A path portion to prefix onto the entries in the archive.\n- `gzip` Set to any truthy value to create a gzipped archive, or an\n  object with settings for `zlib.Gzip()` [Alias: `z`]\n- `filter` A function that gets called with `(path, stat)` for each\n  entry being added. Return `true` to add the entry to the archive,\n  or `false` to omit it.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths. [Alias: `P`]\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `noDirRecurse` Do not recursively archive the contents of\n  directories. [Alias: `n`]\n- `follow` Set to true to pack the targets of symbolic links. Without\n  this option, symbolic links are archived as such. [Alias: `L`, `h`]\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n  [Alias: `m`, `no-mtime`]\n- `mtime` Set to a `Date` object to force a specific `mtime` for\n  everything added to the archive. Overridden by `noMtime`.\n- `onWriteEntry` Called with each `WriteEntry` or\n  `WriteEntrySync` that is created in the course of writing the\n  archive.\n\n## Low-Level API\n\n### class Pack\n\nA readable tar stream.\n\nHas all the standard readable stream interface stuff. `'data'` and\n`'end'` events, `read()` method, `pause()` and `resume()`, etc.\n\n#### constructor(options)\n\nThe following options are supported:\n\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `cwd` The current working directory for creating the archive.\n  Defaults to `process.cwd()`.\n- `prefix` A path portion to prefix onto the entries in the archive.\n- `gzip` Set to any truthy value to create a gzipped archive, or an\n  object with settings for `zlib.Gzip()`\n- `filter` A function that gets called with `(path, stat)` for each\n  entry being added. Return `true` to add the entry to the archive,\n  or `false` to omit it.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths.\n- `linkCache` A Map object containing the device and inode value for\n  any file whose nlink is > 1, to identify hard links.\n- `statCache` A Map object that caches calls `lstat`.\n- `readdirCache` A Map object that caches calls to `readdir`.\n- `jobs` A number specifying how many concurrent jobs to run.\n  Defaults to 4.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `noDirRecurse` Do not recursively archive the contents of\n  directories.\n- `follow` Set to true to pack the targets of symbolic links. Without\n  this option, symbolic links are archived as such.\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n- `mtime` Set to a `Date` object to force a specific `mtime` for\n  everything added to the archive. Overridden by `noMtime`.\n- `onWriteEntry` Called with each `WriteEntry` or\n  `WriteEntrySync` that is created in the course of writing the\n  archive.\n\n#### add(path)\n\nAdds an entry to the archive. Returns the Pack stream.\n\n#### write(path)\n\nAdds an entry to the archive. Returns true if flushed.\n\n#### end()\n\nFinishes the archive.\n\n### class PackSync\n\nSynchronous version of `Pack`.\n\n### class Unpack\n\nA writable stream that unpacks a tar archive onto the file system.\n\nAll the normal writable stream stuff is supported. `write()` and\n`end()` methods, `'drain'` events, etc.\n\nNote that all directories that are created will be forced to be\nwritable, readable, and listable by their owner, to avoid cases where\na directory prevents extraction of child entries by virtue of its\nmode.\n\n`'close'` is emitted when it's done writing stuff to the file system.\n\nMost unpack errors will cause a `warn` event to be emitted. If the\n`cwd` is missing, or not a directory, then an error will be emitted.\n\n#### constructor(options)\n\n- `cwd` Extract files relative to the specified directory. Defaults\n  to `process.cwd()`. If provided, this must exist and must be a\n  directory.\n- `filter` A function that gets called with `(path, entry)` for each\n  entry being unpacked. Return `true` to unpack the entry from the\n  archive, or `false` to skip it.\n- `newer` Set to true to keep the existing file on disk if it's newer\n  than the file in the archive.\n- `keep` Do not overwrite existing files. In particular, if a file\n  appears more than once in an archive, later copies will not\n  overwrite earlier copies.\n- `preservePaths` Allow absolute paths, paths containing `..`, and\n  extracting through symbolic links. By default, `/` is stripped from\n  absolute paths, `..` paths are not extracted, and any file whose\n  location would be modified by a symbolic link is not extracted.\n- `unlink` Unlink files before creating them. Without this option,\n  tar overwrites existing files, which preserves existing hardlinks.\n  With this option, existing hardlinks will be broken, as will any\n  symlink that would affect the location of an extracted file.\n- `strip` Remove the specified number of leading path elements.\n  Pathnames with fewer elements will be silently skipped. Note that\n  the pathname is edited after applying the filter, but before\n  security checks.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `umask` Filter the modes of entries like `process.umask()`.\n- `dmode` Default mode for directories\n- `fmode` Default mode for files\n- `dirCache` A Map object of which directories exist.\n- `maxMetaEntrySize` The maximum size of meta entries that is\n  supported. Defaults to 1 MB.\n- `preserveOwner` If true, tar will set the `uid` and `gid` of\n  extracted entries to the `uid` and `gid` fields in the archive.\n  This defaults to true when run as root, and false otherwise. If\n  false, then files and directories will be set with the owner and\n  group of the user running the process. This is similar to `-p` in\n  `tar(1)`, but ACLs and other system-specific data is never unpacked\n  in this implementation, and modes are set by default already.\n- `win32` True if on a windows platform. Causes behavior where\n  filenames containing `<|>?` chars are converted to\n  windows-compatible values while being unpacked.\n- `uid` Set to a number to force ownership of all extracted files and\n  folders, and all implicitly created directories, to be owned by the\n  specified user id, regardless of the `uid` field in the archive.\n  Cannot be used along with `preserveOwner`. Requires also setting a\n  `gid` option.\n- `gid` Set to a number to force ownership of all extracted files and\n  folders, and all implicitly created directories, to be owned by the\n  specified group id, regardless of the `gid` field in the archive.\n  Cannot be used along with `preserveOwner`. Requires also setting a\n  `uid` option.\n- `noMtime` Set to true to omit writing `mtime` value for extracted\n  entries.\n- `transform` Provide a function that takes an `entry` object, and\n  returns a stream, or any falsey value. If a stream is provided,\n  then that stream's data will be written instead of the contents of\n  the archive entry. If a falsey value is provided, then the entry is\n  written to disk as normal. (To exclude items from extraction, use\n  the `filter` option described above.)\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `onReadEntry` A function that gets called with `(entry)` for each entry\n  that passes the filter.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `chmod` Set to true to call `fs.chmod()` to ensure that the\n  extracted file matches the entry mode. This may necessitate a\n  call to the deprecated and thread-unsafe `process.umask()`\n  method to determine the default umask value, unless a\n  `processUmask` options is also provided. Otherwise tar will\n  extract with whatever mode is provided, and let the process\n  `umask` apply normally.\n- `processUmask` Set to an explicit numeric value to avoid\n  calling `process.umask()` when `chmod: true` is set.\n- `maxDepth` The maximum depth of subfolders to extract into. This\n  defaults to 1024. Anything deeper than the limit will raise a\n  warning and skip the entry. Set to `Infinity` to remove the\n  limitation.\n\n### class UnpackSync\n\nSynchronous version of `Unpack`.\n\nNote that using an asynchronous stream type with the `transform`\noption will cause undefined behavior in sync unpack streams.\n[MiniPass](http://npm.im/minipass)-based streams are designed for this\nuse case.\n\n### class tar.Parse\n\nA writable stream that parses a tar archive stream. All the standard\nwritable stream stuff is supported.\n\nIf the archive is gzipped, then tar will detect this and unzip it.\n\nEmits `'entry'` events with `tar.ReadEntry` objects, which are\nthemselves readable streams that you can pipe wherever.\n\nEach `entry` will not emit until the one before it is flushed through,\nso make sure to either consume the data (with `on('data', ...)` or\n`.pipe(...)`) or throw it away with `.resume()` to keep the stream\nflowing.\n\n#### constructor(options)\n\nReturns an event emitter that emits `entry` events with\n`tar.ReadEntry` objects.\n\nThe following options are supported:\n\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `filter` A function that gets called with `(path, entry)` for each\n  entry being listed. Return `true` to emit the entry from the\n  archive, or `false` to skip it.\n- `onReadEntry` A function that gets called with `(entry)` for each entry\n  that passes the filter.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n\n#### abort(error)\n\nStop all parsing activities. This is called when there are zlib\nerrors. It also emits an unrecoverable warning with the error provided.\n\n### class tar.ReadEntry extends [MiniPass](http://npm.im/minipass)\n\nA representation of an entry that is being read out of a tar archive.\n\nIt has the following fields:\n\n- `extended` The extended metadata object provided to the constructor.\n- `globalExtended` The global extended metadata object provided to the\n  constructor.\n- `remain` The number of bytes remaining to be written into the\n  stream.\n- `blockRemain` The number of 512-byte blocks remaining to be written\n  into the stream.\n- `ignore` Whether this entry should be ignored.\n- `meta` True if this represents metadata about the next entry, false\n  if it represents a filesystem object.\n- All the fields from the header, extended header, and global extended\n  header are added to the ReadEntry object. So it has `path`, `type`,\n  `size`, `mode`, and so on.\n\n#### constructor(header, extended, globalExtended)\n\nCreate a new ReadEntry object with the specified header, extended\nheader, and global extended header values.\n\n### class tar.WriteEntry extends [MiniPass](http://npm.im/minipass)\n\nA representation of an entry that is being written from the file\nsystem into a tar archive.\n\nEmits data for the Header, and for the Pax Extended Header if one is\nrequired, as well as any body data.\n\nCreating a WriteEntry for a directory does not also create\nWriteEntry objects for all of the directory contents.\n\nIt has the following fields:\n\n- `path` The path field that will be written to the archive. By\n  default, this is also the path from the cwd to the file system\n  object.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `myuid` If supported, the uid of the user running the current\n  process.\n- `myuser` The `env.USER` string if set, or `''`. Set as the entry\n  `uname` field if the file's `uid` matches `this.myuid`.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 1 MB.\n- `linkCache` A Map object containing the device and inode value for\n  any file whose nlink is > 1, to identify hard links.\n- `statCache` A Map object that caches calls `lstat`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths.\n- `cwd` The current working directory for creating the archive.\n  Defaults to `process.cwd()`.\n- `absolute` The absolute path to the entry on the filesystem. By\n  default, this is `path.resolve(this.cwd, this.path)`, but it can be\n  overridden explicitly.\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `win32` True if on a windows platform. Causes behavior where paths\n  replace `\\` with `/` and filenames containing the windows-compatible\n  forms of `<|>?:` characters are converted to actual `<|>?:` characters\n  in the archive.\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n\n#### constructor(path, options)\n\n`path` is the path of the entry as it is written in the archive.\n\nThe following options are supported:\n\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 1 MB.\n- `linkCache` A Map object containing the device and inode value for\n  any file whose nlink is > 1, to identify hard links.\n- `statCache` A Map object that caches calls `lstat`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths.\n- `cwd` The current working directory for creating the archive.\n  Defaults to `process.cwd()`.\n- `absolute` The absolute path to the entry on the filesystem. By\n  default, this is `path.resolve(this.cwd, this.path)`, but it can be\n  overridden explicitly.\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `win32` True if on a windows platform. Causes behavior where paths\n  replace `\\` with `/`.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n- `umask` Set to restrict the modes on the entries in the archive,\n  somewhat like how umask works on file creation. Defaults to\n  `process.umask()` on unix systems, or `0o22` on Windows.\n\n#### warn(message, data)\n\nIf strict, emit an error with the provided message.\n\nOthewise, emit a `'warn'` event with the provided message and data.\n\n### class tar.WriteEntry.Sync\n\nSynchronous version of tar.WriteEntry\n\n### class tar.WriteEntry.Tar\n\nA version of tar.WriteEntry that gets its data from a tar.ReadEntry\ninstead of from the filesystem.\n\n#### constructor(readEntry, options)\n\n`readEntry` is the entry being read out of another archive.\n\nThe following options are supported:\n\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths.\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n\n### class tar.Header\n\nA class for reading and writing header blocks.\n\nIt has the following fields:\n\n- `nullBlock` True if decoding a block which is entirely composed of\n  `0x00` null bytes. (Useful because tar files are terminated by\n  at least 2 null blocks.)\n- `cksumValid` True if the checksum in the header is valid, false\n  otherwise.\n- `needPax` True if the values, as encoded, will require a Pax\n  extended header.\n- `path` The path of the entry.\n- `mode` The 4 lowest-order octal digits of the file mode. That is,\n  read/write/execute permissions for world, group, and owner, and the\n  setuid, setgid, and sticky bits.\n- `uid` Numeric user id of the file owner\n- `gid` Numeric group id of the file owner\n- `size` Size of the file in bytes\n- `mtime` Modified time of the file\n- `cksum` The checksum of the header. This is generated by adding all\n  the bytes of the header block, treating the checksum field itself as\n  all ascii space characters (that is, `0x20`).\n- `type` The human-readable name of the type of entry this represents,\n  or the alphanumeric key if unknown.\n- `typeKey` The alphanumeric key for the type of entry this header\n  represents.\n- `linkpath` The target of Link and SymbolicLink entries.\n- `uname` Human-readable user name of the file owner\n- `gname` Human-readable group name of the file owner\n- `devmaj` The major portion of the device number. Always `0` for\n  files, directories, and links.\n- `devmin` The minor portion of the device number. Always `0` for\n  files, directories, and links.\n- `atime` File access time.\n- `ctime` File change time.\n\n#### constructor(data, [offset=0])\n\n`data` is optional. It is either a Buffer that should be interpreted\nas a tar Header starting at the specified offset and continuing for\n512 bytes, or a data object of keys and values to set on the header\nobject, and eventually encode as a tar Header.\n\n#### decode(block, offset)\n\nDecode the provided buffer starting at the specified offset.\n\nBuffer length must be greater than 512 bytes.\n\n#### set(data)\n\nSet the fields in the data object.\n\n#### encode(buffer, offset)\n\nEncode the header fields into the buffer at the specified offset.\n\nReturns `this.needPax` to indicate whether a Pax Extended Header is\nrequired to properly encode the specified data.\n\n### class tar.Pax\n\nAn object representing a set of key-value pairs in an Pax extended\nheader entry.\n\nIt has the following fields. Where the same name is used, they have\nthe same semantics as the tar.Header field of the same name.\n\n- `global` True if this represents a global extended header, or false\n  if it is for a single entry.\n- `atime`\n- `charset`\n- `comment`\n- `ctime`\n- `gid`\n- `gname`\n- `linkpath`\n- `mtime`\n- `path`\n- `size`\n- `uid`\n- `uname`\n- `dev`\n- `ino`\n- `nlink`\n\n#### constructor(object, global)\n\nSet the fields set in the object. `global` is a boolean that defaults\nto false.\n\n#### encode()\n\nReturn a Buffer containing the header and body for the Pax extended\nheader entry, or `null` if there is nothing to encode.\n\n#### encodeBody()\n\nReturn a string representing the body of the pax extended header\nentry.\n\n#### encodeField(fieldName)\n\nReturn a string representing the key/value encoding for the specified\nfieldName, or `''` if the field is unset.\n\n### tar.Pax.parse(string, extended, global)\n\nReturn a new Pax object created by parsing the contents of the string\nprovided.\n\nIf the `extended` object is set, then also add the fields from that\nobject. (This is necessary because multiple metadata entries can\noccur in sequence.)\n\n### tar.types\n\nA translation table for the `type` field in tar headers.\n\n#### tar.types.name.get(code)\n\nGet the human-readable name for a given alphanumeric code.\n\n#### tar.types.code.get(name)\n\nGet the alphanumeric code for a given human-readable name.\n", "time": {"created": "2022-01-26T13:14:53.040Z", "modified": "2024-07-26T05:13:25.490Z", "6.1.11": "2021-08-26T16:16:13.333Z", "4.4.19": "2021-08-19T02:36:46.121Z", "5.0.11": "2021-08-19T02:36:16.529Z", "6.1.10": "2021-08-19T02:35:54.513Z", "4.4.18": "2021-08-19T02:27:59.112Z", "5.0.10": "2021-08-19T02:25:45.258Z", "6.1.9": "2021-08-19T02:16:18.186Z", "5.0.9": "2021-08-11T19:25:05.518Z", "4.4.17": "2021-08-11T19:24:40.099Z", "6.1.8": "2021-08-11T19:23:50.567Z", "4.4.16": "2021-08-09T23:17:03.494Z", "5.0.8": "2021-08-09T23:15:38.491Z", "6.1.7": "2021-08-09T23:15:21.658Z", "6.1.6": "2021-08-04T07:23:45.335Z", "6.1.5": "2021-08-04T01:06:37.871Z", "6.1.4": "2021-08-03T21:37:19.217Z", "6.1.3": "2021-08-02T17:20:17.578Z", "3.2.3": "2021-07-26T23:22:44.076Z", "4.4.15": "2021-07-26T23:22:34.327Z", "5.0.7": "2021-07-26T23:22:05.063Z", "6.1.2": "2021-07-26T23:11:29.889Z", "6.1.1": "2021-07-23T22:50:46.143Z", "3.2.2": "2021-07-23T22:46:51.627Z", "4.4.14": "2021-07-23T22:45:28.854Z", "5.0.6": "2021-07-23T22:44:40.117Z", "6.1.0": "2021-01-07T19:22:17.608Z", "6.0.5": "2020-08-14T22:44:10.304Z", "6.0.4": "2020-08-14T22:34:07.117Z", "6.0.3": "2020-08-14T22:31:23.092Z", "6.0.2": "2020-04-27T21:02:52.265Z", "6.0.1": "2020-01-29T04:37:54.305Z", "6.0.0": "2020-01-28T01:46:19.890Z", "5.0.5": "2019-10-05T06:54:59.338Z", "5.0.4": "2019-10-05T02:07:36.005Z", "5.0.2": "2019-09-30T21:07:16.078Z", "5.0.1": "2019-09-26T00:42:19.709Z", "5.0.0": "2019-09-25T06:00:08.689Z", "4.4.13": "2019-09-25T04:56:34.833Z", "4.4.12": "2019-09-24T16:24:16.186Z", "4.4.11": "2019-09-17T17:43:11.530Z", "4.4.10": "2019-06-04T19:55:16.494Z", "4.4.9": "2019-06-01T04:48:39.791Z", "2.2.2": "2019-05-15T00:44:22.633Z", "4.4.8": "2018-11-08T23:53:21.416Z", "4.4.7": "2018-11-05T21:14:05.702Z", "4.4.6": "2018-08-02T00:15:20.037Z", "4.4.5": "2018-08-02T00:14:08.433Z", "4.4.4": "2018-05-25T00:58:46.640Z", "4.4.3": "2018-05-23T23:55:05.909Z", "4.4.2": "2018-04-30T18:57:47.535Z", "4.4.1": "2018-03-20T16:59:58.112Z", "4.4.0": "2018-02-20T19:36:59.186Z", "4.3.3": "2018-02-06T20:18:46.562Z", "4.3.2": "2018-02-01T16:36:44.943Z", "4.3.1": "2018-02-01T04:22:44.860Z", "4.3.0": "2018-01-18T01:17:05.740Z", "4.2.0": "2017-12-21T04:27:39.029Z", "4.1.2": "2017-12-21T03:17:05.518Z", "4.1.1": "2017-11-28T19:40:50.614Z", "4.1.0": "2017-11-28T01:07:37.579Z", "4.0.2": "2017-10-18T19:30:06.418Z", "3.2.1": "2017-08-21T01:12:32.637Z", "4.0.1": "2017-08-21T01:10:23.741Z", "4.0.0": "2017-08-18T22:24:34.231Z", "3.2.0": "2017-08-17T00:55:49.757Z", "3.1.15": "2017-08-17T00:50:34.313Z", "3.1.14": "2017-08-16T23:30:17.615Z", "3.1.13": "2017-08-16T00:32:03.399Z", "3.1.12": "2017-08-15T23:45:32.046Z", "3.1.11": "2017-08-15T16:34:05.049Z", "3.1.10": "2017-08-15T07:42:43.039Z", "3.1.9": "2017-08-02T22:48:31.039Z", "3.1.8": "2017-08-01T22:53:09.529Z", "3.1.7": "2017-07-26T18:03:06.054Z", "3.1.6": "2017-07-25T23:03:18.524Z", "3.1.5": "2017-06-03T00:16:58.694Z", "3.1.4": "2017-06-02T23:41:13.115Z", "3.1.3": "2017-05-16T00:56:04.961Z", "3.1.2": "2017-05-12T22:54:32.029Z", "3.1.1": "2017-05-12T17:02:16.466Z", "3.1.0": "2017-05-12T16:43:21.251Z", "3.0.1": "2017-05-10T17:55:58.326Z", "3.0.0": "2017-05-10T01:24:27.621Z", "2.2.1": "2015-09-10T01:49:38.319Z", "2.2.0": "2015-08-27T21:01:29.846Z", "2.1.1": "2015-05-08T00:38:16.661Z", "2.1.0": "2015-04-17T07:03:07.838Z", "2.0.1": "2015-04-09T00:56:16.262Z", "2.0.0": "2015-03-27T01:45:53.078Z", "1.0.3": "2014-11-28T01:50:25.407Z", "1.0.2": "2014-10-28T06:50:17.116Z", "1.0.1": "2014-08-19T19:26:19.807Z", "1.0.0": "2014-07-31T22:28:36.607Z", "0.1.20": "2014-06-24T20:51:47.700Z", "0.1.19": "2013-12-09T17:26:25.554Z", "0.1.18": "2013-07-24T03:35:17.606Z", "0.1.17": "2013-03-20T06:23:09.563Z", "0.1.16": "2013-02-02T19:18:49.559Z", "0.1.15": "2013-02-02T02:38:57.779Z", "0.1.14": "2012-12-03T04:12:29.063Z", "0.1.13": "2012-03-13T00:07:39.934Z", "0.1.12": "2012-01-13T18:05:48.499Z", "0.1.11": "2012-01-06T01:47:30.560Z", "0.1.10": "2012-01-05T00:46:43.197Z", "0.1.9": "2011-12-09T02:01:45.330Z", "0.1.8": "2011-12-03T02:29:20.608Z", "0.1.7": "2011-12-01T03:12:57.060Z", "0.1.6": "2011-11-30T20:50:10.803Z", "0.1.5": "2011-11-30T18:54:34.219Z", "0.1.4": "2011-11-29T03:02:22.216Z", "0.1.3": "2011-11-23T00:44:56.703Z", "0.1.2": "2011-11-21T22:31:19.762Z", "0.1.0": "2011-11-20T07:52:36.525Z", "0.0.1": "2011-11-20T07:11:18.109Z", "6.1.12": "2022-11-01T16:33:12.294Z", "6.1.13": "2022-12-07T20:32:39.620Z", "6.1.14": "2023-05-02T22:46:07.312Z", "6.1.15": "2023-05-17T05:38:51.855Z", "6.2.0": "2023-09-05T05:33:49.378Z", "6.2.1": "2024-03-21T21:13:04.779Z", "7.0.0": "2024-04-10T20:07:24.043Z", "7.0.1": "2024-04-14T21:45:25.236Z", "7.1.0": "2024-05-04T02:07:00.708Z", "7.2.0": "2024-05-30T01:29:04.900Z", "7.3.0": "2024-06-19T02:26:51.845Z", "7.4.0": "2024-06-19T02:37:02.405Z", "7.4.1": "2024-07-22T16:02:39.813Z", "7.4.2": "2024-07-24T23:58:02.238Z", "7.4.3": "2024-07-26T04:33:46.030Z"}, "versions": {"6.1.11": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.11", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "e573aeea19d4d650908b7f6bf0a1ad8dce9f1736", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.11", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.6", "dist": {"shasum": "6760a38f003afa1b2ffd0ffe9e9abbd0eab3d621", "size": 41474, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.11.tgz", "integrity": "sha512-an/KZQzQUkZCkuoAA64hM92X0Urb6VpRhAFllDzz44U2mcD5scmT3zBc4VgVpkugF580+DQn8eAFSyoQt0tznA=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.11_1629994573123_0.2986815842736079"}, "_hasShrinkwrap": false, "publish_time": 1629994573333, "_cnpm_publish_time": 1629994573333, "_cnpmcore_publish_time": "2021-12-13T06:51:22.470Z"}, "4.4.19": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.19", "publishConfig": {"tag": "v4-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.3.3", "mkdirp": "^0.5.5", "safe-buffer": "^5.2.1", "yallist": "^3.1.1"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.4", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "rimraf": "^2.7.1", "tap": "^14.11.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "9a6faa017ca90538840f3ae2ccdb4550ac3f4dcf", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.19", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "2e4d7263df26f2b914dee10c825ab132123742f3", "size": 38931, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.19.tgz", "integrity": "sha512-a20gEsvHnWe0ygBY8JbxoM4w3SJdhc7ZAuxkLqh+nvNQN2IOt0B5lLgM490X5Hl8FF0dl0tOf2ewFYAlIFgzVA=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.19_1629340605951_0.7949247502919905"}, "_hasShrinkwrap": false, "publish_time": 1629340606121, "_cnpm_publish_time": 1629340606121, "_cnpmcore_publish_time": "2021-12-13T06:51:22.801Z"}, "5.0.11": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.11", "publishConfig": {"tag": "v5-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^2.1.0", "minipass": "^3.1.3", "minizlib": "^2.1.2", "mkdirp": "^0.5.5", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.4", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "rimraf": "^2.7.1", "tap": "^14.11.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "c01354990a450c5903919efeaa9e332976e074a3", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.11", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "f6e972e26960f71387c88e4313b202889be09292", "size": 41513, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.11.tgz", "integrity": "sha512-E6q48d5y4XSCD+Xmwc0yc8lXuyDK38E0FB8N4S/drQRtXOMUhfhDxbB0xr2KKDhNfO51CFmoa6Oz00nAkWsjnA=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.11_1629340576343_0.7895363638731205"}, "_hasShrinkwrap": false, "publish_time": 1629340576529, "_cnpm_publish_time": 1629340576529, "_cnpmcore_publish_time": "2021-12-13T06:51:23.187Z"}, "6.1.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.10", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "188baddc1d0e6ef5140c7a788f03fc2a6c3df2ea", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.10", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.6", "dist": {"shasum": "8a320a74475fba54398fa136cd9883aa8ad11175", "size": 41268, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.10.tgz", "integrity": "sha512-kvvfiVvjGMxeUNB6MyYv5z7vhfFRwbwCXJAeL0/lnbrttBVqcMOnpHUf0X42LrPMR8mMpgapkJMchFH4FSHzNA=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.10_1629340554316_0.5902710599713845"}, "_hasShrinkwrap": false, "publish_time": 1629340554513, "_cnpm_publish_time": 1629340554513, "_cnpmcore_publish_time": "2021-12-13T06:51:23.535Z"}, "4.4.18": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.18", "publishConfig": {"tag": "v4-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.3.3", "mkdirp": "^0.5.5", "safe-buffer": "^5.2.1", "yallist": "^3.1.1"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.4", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "rimraf": "^2.7.1", "tap": "^14.11.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "3e35515c09da615ac268254bed85fe43ee71e2f0", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.18", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "a565090fdcf786ee08ed14b1739179451b3cc476", "size": 38921, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.18.tgz", "integrity": "sha512-ZuOtqqmkV9RE1+4odd+MhBpibmCxNP6PJhH/h2OqNuotTX7/XHPZQJv2pKvWMplFH9SIZZhitehh6vBH6LO8Pg=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.18_1629340078966_0.525414223109776"}, "_hasShrinkwrap": false, "publish_time": 1629340079112, "_cnpm_publish_time": 1629340079112, "_cnpmcore_publish_time": "2021-12-13T06:51:23.926Z"}, "5.0.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.10", "publishConfig": {"tag": "v5-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^2.1.0", "minipass": "^3.1.3", "minizlib": "^2.1.2", "mkdirp": "^0.5.5", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.4", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "rimraf": "^2.7.1", "tap": "^14.11.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "df64c4d5d80292e1e043f590de34da237a2ea12c", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.10", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "5bbbb6464472d9703ffc329f3e4de04a858b61f8", "size": 41498, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.10.tgz", "integrity": "sha512-DE64epEYvKcBVi7u5jZpnffbiZMzBZIxJtZpXkb96LoQWpDvCPUgRG6lqHW9P5oF866fYuz4XwmpALndo4T/Xw=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.10_1629339945005_0.4606425075348879"}, "_hasShrinkwrap": false, "publish_time": 1629339945258, "_cnpm_publish_time": 1629339945258, "_cnpmcore_publish_time": "2021-12-13T06:51:24.347Z"}, "6.1.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.9", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "4f1f4a21fca64f3089da0e83ceea775c66b55052", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.9", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.6", "dist": {"shasum": "5646ef51342ac55456b2466e44da810439978db1", "size": 41246, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.9.tgz", "integrity": "sha512-XjLaMNl76o07zqZC/aW4lwegdY07baOH1T8w3AEfrHAdyg/oYO4ctjzEBq9Gy9fEP9oHqLIgvx6zuGDGe+bc8Q=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.9_1629339378004_0.3574697354359857"}, "_hasShrinkwrap": false, "publish_time": 1629339378186, "_cnpm_publish_time": 1629339378186, "_cnpmcore_publish_time": "2021-12-13T06:51:24.737Z"}, "5.0.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.9", "publishConfig": {"tag": "v5-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^2.1.0", "minipass": "^3.1.3", "minizlib": "^2.1.2", "mkdirp": "^0.5.5", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.4", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "rimraf": "^2.7.1", "tap": "^14.11.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "173800ddb104f5c74f241da0967b5992af9d45d7", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.9", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "bbdffcb5949fa61e691dbcac03f248fe08b5828a", "size": 40163, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.9.tgz", "integrity": "sha512-XvvsfjcXKwCmAKTmxRyG5XbyUO0eZ8tyKXkAQd+At0SxOFCN/WvuxWXNAR/UCGXCvoDi+rLi5FV5q7wVhC5X/w=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.9_1628709905098_0.30075042077406366"}, "_hasShrinkwrap": false, "publish_time": 1628709905518, "_cnpm_publish_time": 1628709905518, "_cnpmcore_publish_time": "2021-12-13T06:51:25.151Z"}, "4.4.17": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.17", "publishConfig": {"tag": "v4-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.3.3", "mkdirp": "^0.5.5", "safe-buffer": "^5.2.1", "yallist": "^3.1.1"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.4", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "rimraf": "^2.7.1", "tap": "^14.11.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "9bf70a8cf725c3af5fe2270f1e5d2e06d1559b93", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.17", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "44be5e3fa8353ee1d11db3b1401561223a5c3985", "size": 37541, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.17.tgz", "integrity": "sha512-q7OwXq6NTdcYIa+k58nEMV3j1euhDhGCs/VRw9ymx/PbH0jtIM2+VTgDE/BW3rbLkrBUXs5fzEKgic5oUciu7g=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.17_1628709879947_0.32096013723033967"}, "_hasShrinkwrap": false, "publish_time": 1628709880099, "_cnpm_publish_time": 1628709880099, "_cnpmcore_publish_time": "2021-12-13T06:51:25.538Z"}, "6.1.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.8", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "6a9c51da31a2c9b67d266d8ce7119e1e2c0d1e5d", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.8", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.3", "dist": {"shasum": "4fc50cfe56511c538ce15b71e05eebe66530cbd4", "size": 39904, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.8.tgz", "integrity": "sha512-sb9b0cp855NbkMJcskdSYA7b11Q8JsX4qe4pyUAfHp+Y6jBjJeek2ZVlwEfWayshEIwlIzXx0Fain3QG9JPm2A=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.8_1628709830403_0.21673040450647862"}, "_hasShrinkwrap": false, "publish_time": 1628709830567, "_cnpm_publish_time": 1628709830567, "_cnpmcore_publish_time": "2021-12-13T06:51:25.974Z"}, "4.4.16": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.16", "publishConfig": {"tag": "v4-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.3.3", "mkdirp": "^0.5.5", "safe-buffer": "^5.2.1", "yallist": "^3.1.1"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.4", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "rimraf": "^2.7.1", "tap": "^14.11.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "fd6accba697070560f301604b8f5f7e2995a2a8b", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.16", "_nodeVersion": "14.17.4", "_npmVersion": "6.14.14", "dist": {"shasum": "4a48b3c025e77d9d0c788f038a09b91c594d326d", "size": 37522, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.16.tgz", "integrity": "sha512-gOVUT/KWPkGFZQmCRDVFNUWBl7niIo/PRR7lzrIqtZpit+st54lGROuVjc6zEQM9FhH+dJfQIl+9F0k8GNXg5g=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.16_1628551023337_0.40079804245386286"}, "_hasShrinkwrap": false, "publish_time": 1628551023494, "_cnpm_publish_time": 1628551023494, "_cnpmcore_publish_time": "2021-12-13T06:51:26.363Z"}, "5.0.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.8", "publishConfig": {"tag": "v5-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^2.1.0", "minipass": "^3.1.3", "minizlib": "^2.1.2", "mkdirp": "^0.5.5", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.4", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "rimraf": "^2.7.1", "tap": "^14.11.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "8d2a3168021d12c3a48dbe74b4add954269d16a5", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.8", "_nodeVersion": "14.17.4", "_npmVersion": "6.14.14", "dist": {"shasum": "8cbddead599d7335aa2be551bfb18521dea488a2", "size": 40126, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.8.tgz", "integrity": "sha512-v71KDHvYI+EJ8+o6W44fzuiTDcfHrlVjtvn4ITF1ClH5GbU6H9InNX1XNG07YwZSkloNQQxAJabkjEdHyqjn1g=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.8_1628550938369_0.31359692616754"}, "_hasShrinkwrap": false, "publish_time": 1628550938491, "_cnpm_publish_time": 1628550938491, "_cnpmcore_publish_time": "2021-12-13T06:51:26.811Z"}, "6.1.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.7", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "d61628cb40381d89f119431a16a4aab2fbecb056", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.7", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.3", "dist": {"shasum": "c566d1107d38b09e92983a68db5534fc7f6cab42", "size": 39863, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.7.tgz", "integrity": "sha512-PBoRkOJU0X3lejJ8GaRCsobjXTgFofRDSPdSUhRSdlwJfifRlQBwGXitDItdGFu0/h0XDMCkig0RN1iT7DBxhA=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.7_1628550921534_0.3728176440701534"}, "_hasShrinkwrap": false, "publish_time": 1628550921658, "_cnpm_publish_time": 1628550921658, "_cnpmcore_publish_time": "2021-12-13T06:51:27.342Z"}, "6.1.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.6", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "9bc1729939eec1c822b528385b1cc513b9888835", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.6", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.3", "dist": {"shasum": "c23d797b0a1efe5d479b1490805c5443f3560c5d", "size": 38681, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.6.tgz", "integrity": "sha512-oaWyu5dQbHaYcyZCTfyPpC+VmI62/OM2RTUYavTk1MDr1cwW5Boi3baeYQKiZbY2uSQJGr+iMOzb/JFxLrft+g=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.6_1628061825171_0.8830153421193472"}, "_hasShrinkwrap": false, "publish_time": 1628061825335, "_cnpm_publish_time": 1628061825335, "_cnpmcore_publish_time": "2021-12-13T06:51:27.870Z"}, "6.1.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.5", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "bd4691c90478f41b2649a97048199e34927dc046", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.5", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.3", "dist": {"shasum": "6e25bee1cfda94317aedc3f5d49290ae68361d73", "size": 38586, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.5.tgz", "integrity": "sha512-FiK6MQyyaqd5vHuUjbg/NpO8BuEGeSXcmlH7Pt/JkugWS8s0w8nKybWjHDJiwzCAIKZ66uof4ghm4tBADjcqRA=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.5_1628039197727_0.37752617123503684"}, "_hasShrinkwrap": false, "publish_time": 1628039197871, "_cnpm_publish_time": 1628039197871, "_cnpmcore_publish_time": "2021-12-13T06:51:28.363Z"}, "6.1.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.4", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "bf693837b3dcfeb76878b212310302dc5dc3d3dc", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.4", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.3", "dist": {"shasum": "9f0722b772a5e00dba7d52e1923b37a7ec3799b3", "size": 38079, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.4.tgz", "integrity": "sha512-kcPWrO8S5ABjuZ/v1xQHP8xCEvj1dQ1d9iAb6Qs4jLYzaAIYWwST2IQpz7Ud8VNYRI+fGhFjrnzRKmRggKWg3g=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.4_1628026639011_0.9987201778192802"}, "_hasShrinkwrap": false, "publish_time": 1628026639217, "_cnpm_publish_time": 1628026639217, "_cnpmcore_publish_time": "2021-12-13T06:51:28.857Z"}, "6.1.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.3", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "0b78386c53b00dce422742e19de94f2a4d9389f3", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.3", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.3", "dist": {"shasum": "e44b97ee7d6cc7a4c574e8b01174614538291825", "size": 37812, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.3.tgz", "integrity": "sha512-3r<PERSON><PERSON>wucgVZXTeyJyL2jqtUau8/8r54SioM1xj3AmTX3HnWQdj2AydfJ2qYYayPyIIznSplcvU9mhBb7dR2XF3w=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.3_1627924817188_0.6374361063660521"}, "_hasShrinkwrap": false, "publish_time": 1627924817578, "_cnpm_publish_time": 1627924817578, "_cnpmcore_publish_time": "2021-12-13T06:51:29.324Z"}, "3.2.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.2.3", "publishConfig": {"tag": "v3-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "readmeFilename": "README.md", "gitHead": "0532554701454050693bad82fe0470637715b400", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.2.3", "_nodeVersion": "8.17.0", "_npmVersion": "6.13.4", "dist": {"shasum": "6fa6db1421293ab65655ca9d0eaf858c91e1ee17", "size": 30853, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.2.3.tgz", "integrity": "sha512-dceKyLOOHJCE5NQx9zAS7UjVSVQ0BPrbDc2KN0LI42fBWC8OV9+DP/dS3CMn4SnnNpYKdmEP6crYgdbVf1ZCCg=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_3.2.3_1627341763889_0.9491950608130486"}, "_hasShrinkwrap": false, "publish_time": 1627341764076, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1627341764076, "_cnpmcore_publish_time": "2021-12-13T06:51:29.866Z"}, "4.4.15": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.15", "publishConfig": {"tag": "v4-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.8.6", "minizlib": "^1.2.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.3"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.3", "tap": "^14.6.5", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "843c897e6844f70a34bb115df6c8a9b60112aaf5", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.15", "_nodeVersion": "10.24.1", "_npmVersion": "6.14.12", "dist": {"shasum": "3caced4f39ebd46ddda4d6203d48493a919697f8", "size": 33876, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.15.tgz", "integrity": "sha512-ItbufpujXkry7bHH9NpQyTXPbJ72iTlXgkBAYsAjDXk3Ds8t/3NfO5P4xZGy7u+sYuQUbimgzswX4uQIEeNVOA=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.15_1627341754148_0.822054593561887"}, "_hasShrinkwrap": false, "publish_time": 1627341754327, "_cnpm_publish_time": 1627341754327, "_cnpmcore_publish_time": "2021-12-13T06:51:30.512Z"}, "5.0.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.7", "publishConfig": {"tag": "v5-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.3", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "mkdirp": "^0.5.0", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.6.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "b10856c79362289c483b6bc9c8ced6bd04c2d1b1", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.7", "_nodeVersion": "10.24.1", "_npmVersion": "6.14.12", "dist": {"shasum": "42ff8ca3b731a52f4f2be72cc4cdd7688268f2af", "size": 38025, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.7.tgz", "integrity": "sha512-g0qlHHRtAZAxzkZkJvt0P5C6ODEolw2paouzsSbVqE7l5jKani1m9ogy7VxGp6hEngiKpPCwkh9pX5UH8Wp6QA=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.7_1627341724900_0.3149280160798942"}, "_hasShrinkwrap": false, "publish_time": 1627341725063, "_cnpm_publish_time": 1627341725063, "_cnpmcore_publish_time": "2021-12-13T06:51:31.066Z"}, "6.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.2", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "3f7b20097e0daba10441507becbf5b87c6b83b8b", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.2", "_nodeVersion": "16.5.0", "_npmVersion": "7.19.0", "dist": {"shasum": "1f045a90a6eb23557a603595f41a16c57d47adc6", "size": 37803, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.2.tgz", "integrity": "sha512-EwKEgqJ7nJoS+s8QfLYVGMDmAsj+StbI2AM/RTHeUSsOw6Z8bwNBRv5z3CY0m7laC5qUAqruLX5AhMuc5deY3Q=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.2_1627341089760_0.7159131491758044"}, "_hasShrinkwrap": false, "publish_time": 1627341089889, "_cnpm_publish_time": 1627341089889, "_cnpmcore_publish_time": "2021-12-13T06:51:31.594Z"}, "6.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^15.0.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "1e33534e1e96ca6385b3a4749876aea2cda61cea", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.1", "_nodeVersion": "16.0.0", "_npmVersion": "7.19.0", "dist": {"shasum": "4d7da4b132b334bb8c175ed1de466fe9157ea0eb", "size": 37635, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.1.tgz", "integrity": "sha512-GG0R7yt/CQkvG4fueXDi52Zskqxe2AyRJ+Wm54yqarnBgcX3qRIWh10qLVAAN+mlPFGTfP5UxvD3Fbi11UOTUQ=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.1_1627080645953_0.5865173563945221"}, "_hasShrinkwrap": false, "publish_time": 1627080646143, "_cnpm_publish_time": 1627080646143, "_cnpmcore_publish_time": "2021-12-13T06:51:32.241Z"}, "3.2.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.2.2", "publishConfig": {"tag": "v3-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "readmeFilename": "README.md", "gitHead": "9c393bb04023e58e3445127ab770ed7b0e9e6135", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.2.2", "_nodeVersion": "8.17.0", "_npmVersion": "6.13.4", "dist": {"shasum": "b81363630ce7ef98c6bde6473dc211a0ba24e8ad", "size": 30669, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.2.2.tgz", "integrity": "sha512-D2nGRkPUc4Nsoa8fgmzQmeouUNUutMOYkUOfajmv1POZvTboS/jsgAiUnUkeb5kBExHzCrLgUkg/GZgrgMgwzg=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_3.2.2_1627080411435_0.20706143498776952"}, "_hasShrinkwrap": false, "publish_time": 1627080411627, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1627080411627, "_cnpmcore_publish_time": "2021-12-13T06:51:32.803Z"}, "4.4.14": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.14", "publishConfig": {"tag": "v4-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.8.6", "minizlib": "^1.2.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.3"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.3", "tap": "^14.6.5", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "df3aa4d10253a886be82519acb901b446ca3feeb", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.14", "_nodeVersion": "10.24.1", "_npmVersion": "6.14.12", "dist": {"shasum": "994901b59ce34402be3907ed7c0332a9e38c43c5", "size": 33708, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.14.tgz", "integrity": "sha512-ouN3XcSWYOAHmXZ+P4NEFJvqXL50To9OZBSQNNP30vBUFJFZZ0PLX15fnwupv6azfxMUfUDUr2fhYw4zGAEPcg=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.14_1627080328673_0.7268348147155164"}, "_hasShrinkwrap": false, "publish_time": 1627080328854, "_cnpm_publish_time": 1627080328854, "_cnpmcore_publish_time": "2021-12-13T06:51:33.302Z"}, "5.0.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.6", "publishConfig": {"tag": "v5-legacy"}, "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.3", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "mkdirp": "^0.5.0", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.6.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "fea868401c2860f4c84e17ff070d2317a6f8d349", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.6", "_nodeVersion": "12.22.3", "_npmVersion": "6.14.13", "dist": {"shasum": "e65a7e72ac904b923924b4f5921cd1cf539dcb36", "size": 37856, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.6.tgz", "integrity": "sha512-LDIypi8Avr5l12YdvI9UIKMA+n8ND/E9ZHHD3Zdz9RbgaXLX+AVuu3ivsyd4enPnLLwZHUgrQzBCuyIRvHF1cw=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.6_1627080279937_0.9897126968196139"}, "_hasShrinkwrap": false, "publish_time": 1627080280117, "_cnpm_publish_time": 1627080280117, "_cnpmcore_publish_time": "2021-12-13T06:51:33.850Z"}, "6.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.1.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- test lib", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "eslint": "^7.17.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.9.2", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "259e6494b73936a68dd2b279ad16e2286bdb9344", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.0", "_nodeVersion": "14.12.0", "_npmVersion": "7.3.0", "dist": {"shasum": "d1724e9bcc04b977b18d5c573b333a2207229a83", "size": 38470, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.0.tgz", "integrity": "sha512-DUCttfhsnLCjwoDoFcI+B2iJgYa93vBnDUATYEeRx6sntCTdN01VnqsIuTlALXla/LWooNg0yEGeB+Y8WdFxGA=="}, "_npmUser": {"name": "ruyadorno", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.0_1610047337437_0.7810618101317366"}, "_hasShrinkwrap": false, "publish_time": 1610047337608, "_cnpm_publish_time": 1610047337608, "_cnpmcore_publish_time": "2021-12-13T06:51:34.451Z"}, "6.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.0.5", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.9.2", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "7028aeb3f5bb843bf80af8f5af09c47c3d97503f", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.0.5", "_nodeVersion": "14.8.0", "_npmVersion": "7.0.0-beta.4", "dist": {"shasum": "bde815086e10b39f1dcd298e89d596e1535e200f", "size": 38381, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.0.5.tgz", "integrity": "sha512-0b4HOimQHj9nXNEAA7zWwMM91Zhhba3pspja6sQbgTpynOJf+bkjBnfybNYzbpLbnwXnbyB4LOREvlyXLkCHSg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.0.5_1597445050141_0.9055669008937857"}, "_hasShrinkwrap": false, "publish_time": 1597445050304, "_cnpm_publish_time": 1597445050304, "_cnpmcore_publish_time": "2021-12-13T06:51:35.133Z"}, "6.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.0.4", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.9.2", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "e8fcd639697e07e6ff27d72ceaf542443c5f5434", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.0.4", "_nodeVersion": "14.8.0", "_npmVersion": "7.0.0-beta.4", "dist": {"shasum": "ea653f822aa392df7458b82ae107444eb712be73", "size": 38381, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.0.4.tgz", "integrity": "sha512-pnbXgbXNWgC8qAxAYF8Gz5YkFwUiHq//ddUL8yjbiksGOpXcZvmfMw4JkVYkEvCb0lur/bBX3FK8jrvh6TOTgA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.0.4_1597444447017_0.9090883583113589"}, "_hasShrinkwrap": false, "publish_time": 1597444447117, "_cnpm_publish_time": 1597444447117, "_cnpmcore_publish_time": "2021-12-13T06:51:35.907Z"}, "6.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.0.3", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.9.2", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "12b37e106eca3879cd2e07bd04928633b08cb370", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.0.3", "_nodeVersion": "14.8.0", "_npmVersion": "7.0.0-beta.4", "dist": {"shasum": "ae47d8b2e07b0ee8343145ea17f6457e36795a7d", "size": 38377, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.0.3.tgz", "integrity": "sha512-/BPytVizGJg1AVWmu5lEHMwJ8MygYCl8hGGdtR7ng+dUZDrmmSUZZhB12SmXY/yolTKwZh1YYhkXK7gjU8qRxg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.0.3_1597444282961_0.4625030267175003"}, "_hasShrinkwrap": false, "publish_time": 1597444283092, "_cnpm_publish_time": 1597444283092, "_cnpmcore_publish_time": "2021-12-13T06:51:36.695Z"}, "6.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.0.2", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.9.2", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "0a55ace9898e8456799a0b4a8448fde670d79fa8", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.0.2", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"shasum": "5df17813468a6264ff14f766886c622b84ae2f39", "size": 38379, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.0.2.tgz", "integrity": "sha512-Glo3jkRtPcvpDlAs/0+hozav78yoXKFr+c4wgw62NNMO3oo4AaJdCo21Uu7lcwr55h39W2XD1LMERc64wtbItg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.0.2_1588021372116_0.5634859440969344"}, "_hasShrinkwrap": false, "publish_time": 1588021372265, "_cnpm_publish_time": 1588021372265, "_cnpmcore_publish_time": "2021-12-13T06:51:37.408Z"}, "6.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.0.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.3", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.9.2", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "97a74cde1ee7c3d55ee296ed063630e071e38b57", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.0.1", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"shasum": "7b3bd6c313cb6e0153770108f8d70ac298607efa", "size": 38358, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.0.1.tgz", "integrity": "sha512-bKhKrrz2FJJj5s7wynxy/fyxpE0CmCjmOQ1KV4KkgXFWOgoIT/NbTMnB1n+LFNrNk0SSBVGGxcK5AGsyC+pW5Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.0.1_1580272674209_0.32882047995015373"}, "_hasShrinkwrap": false, "publish_time": 1580272674305, "_cnpm_publish_time": 1580272674305, "_cnpmcore_publish_time": "2021-12-13T06:51:38.123Z"}, "6.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "6.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.3", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.9.2", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 10"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "a069020b2af38f3fddc57e49a870c27beb363fa0", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.0.0", "_nodeVersion": "13.4.0", "_npmVersion": "6.13.6", "dist": {"shasum": "7bc58492f2bdaa068ea3189eae335ce60329eea3", "size": 37651, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-6.0.0.tgz", "integrity": "sha512-yD9H/S7GLhqkObYQneUzN+1GXaCIXbdMwTyo4Cfj0raQRbhdpXIX3WUcTcvheYf7hx/APsbpO7L4cUDGRXSOEA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.0.0_1580175979757_0.34532456490896735"}, "_hasShrinkwrap": false, "publish_time": 1580175979890, "_cnpm_publish_time": 1580175979890, "_cnpmcore_publish_time": "2021-12-13T06:51:38.776Z"}, "5.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.5", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.3", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "mkdirp": "^0.5.0", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.6.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "114efef625f3fe82f7afec838b50982867619130", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.5", "_nodeVersion": "12.11.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "03fcdb7105bc8ea3ce6c86642b9c942495b04f93", "size": 37641, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.5.tgz", "integrity": "sha512-MNIgJddrV2TkuwChwcSNds/5E9VijOiw7kAc1y5hTNJoLDSuIyid2QtLYiCYNnICebpuvjhPQZsXwUL0O3l7OQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.5_1570258499174_0.07767788159909328"}, "_hasShrinkwrap": false, "publish_time": 1570258499338, "_cnpm_publish_time": 1570258499338, "_cnpmcore_publish_time": "2021-12-13T06:51:39.478Z"}, "5.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.4", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.3", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.0.0", "mkdirp": "^0.5.0", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.6.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "58668c6490e840e59b8d45d62299e6ba14ec2d00", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.4", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "b000641deee390eb687b06ad5df6430286c4d6d2", "size": 37594, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.4.tgz", "integrity": "sha512-o+97kfIToDuZV9pKZS2kLyb7nH92AWEj2DkOAvtcqmla13E4666B5eI5j6/1pLvAizbLFIhX5wsiFPKYM7tvBQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.4_1570241255866_0.8184306181475431"}, "_hasShrinkwrap": false, "publish_time": 1570241256005, "_cnpm_publish_time": 1570241256005, "_cnpmcore_publish_time": "2021-12-13T06:51:40.331Z"}, "5.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.2", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.3", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.0.0", "mkdirp": "^0.5.0", "yallist": "^4.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.6.9", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">= 8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "a2f8899feb46830cf71e4876eb88ab56d288d61e", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.2", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "d75a50cf8a37539f8c1b2077bef5c991920d45d2", "size": 37385, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.2.tgz", "integrity": "sha512-s6QnhHIoyEPImp1Y4Tq3UOhPMT6/4kgHwzeZc9fbgq0/+s6RAzezIT43Meepn5RdUFpxdzQkd5x2PkcFdVIEPw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.2_1569877635941_0.939547983387822"}, "_hasShrinkwrap": false, "publish_time": 1569877636078, "_cnpm_publish_time": 1569877636078, "_cnpmcore_publish_time": "2021-12-13T06:51:41.130Z"}, "5.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.3", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.2.2", "mkdirp": "^0.5.0", "yallist": "^3.0.3"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.6.5", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "14dc6f48af4c5476d7613761adc7100c2f8d2a2d", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "88d8cdc9e05e157bac6d0672a34c77b127684e5a", "size": 37387, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.1.tgz", "integrity": "sha512-7YIaS5rgI5NEWrBx7HJmrdZnBxAoQP7ykgBDFX3YLPGVyCzQYxXQtZRtgmJfzcpLLDi5zbDxnDPZ9G0cs3/fKA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.1_1569458539557_0.3475226537774334"}, "_hasShrinkwrap": false, "publish_time": 1569458539709, "_cnpm_publish_time": 1569458539709, "_cnpmcore_publish_time": "2021-12-13T06:51:42.082Z"}, "5.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "5.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.3", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.2.2", "mkdirp": "^0.5.0", "yallist": "^3.0.3"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.7.1", "tap": "^14.6.5", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=8"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "readmeFilename": "README.md", "gitHead": "7bc7c20521154f26529b9ef8ae74c62a7fea0433", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@5.0.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "a1a5f7da2b56f1df50b261f3fc3be8d13d6c1a9d", "size": 36925, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-5.0.0.tgz", "integrity": "sha512-FGgZUEOBsNqREFu/bfuEa70xnOuM6iHAxn8xpEWEHjTrDX5Xgf6boLgXcCV2tXfymiXxu0YZLLSuTumSMk3VAg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_5.0.0_1569391208530_0.8943554666506541"}, "_hasShrinkwrap": false, "publish_time": 1569391208689, "_cnpm_publish_time": 1569391208689, "_cnpmcore_publish_time": "2021-12-13T06:51:42.947Z"}, "4.4.13": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.13", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.8.6", "minizlib": "^1.2.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.3"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.3", "tap": "^14.6.5", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "65edb39114ad5956c06f8d7893365e942042ede1", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.13", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "43b364bc52888d555298637b10d60790254ab525", "size": 33454, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.13.tgz", "integrity": "sha512-w2VwSrBoHa5BsSyH+KxEqeQBAllHhccyMFVHtGtdMpF4W7IRWfZjFiQceJPChOeTsSDVUpER2T8FA93pr0L+QA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.13_1569387394602_0.5681974056976136"}, "_hasShrinkwrap": false, "publish_time": 1569387394833, "_cnpm_publish_time": 1569387394833, "_cnpmcore_publish_time": "2021-12-13T06:51:43.660Z"}, "4.4.12": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.12", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.8.6", "minizlib": "^1.2.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.3"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.3", "tap": "^14.6.5", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "dbd6f52ba9cdfbce2a28d8cd28a016bc3435946a", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.12", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "6a1275a870a782f92828e24d28fa6aa253193af7", "size": 33457, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.12.tgz", "integrity": "sha512-4GwpJwdSjIHlUrWd/1yJrl63UqcqjJyVglgIwn4gcG+Lrp9TXpZ1ZRrGLIRBNqLTUvz6yoPJrX4B/MISxY/Ukg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.12_1569342256046_0.8140056451128697"}, "_hasShrinkwrap": false, "publish_time": 1569342256186, "_cnpm_publish_time": 1569342256186, "_cnpmcore_publish_time": "2021-12-13T06:51:44.429Z"}, "4.4.11": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.11", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.6.4", "minizlib": "^1.2.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.3"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.3", "tap": "^14.2.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "9232b3d7da934c142e3d0ab97ef35ec0ba3917fc", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.11", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "7ac09801445a3cf74445ed27499136b5240ffb73", "size": 33455, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.11.tgz", "integrity": "sha512-iI4zh3ktLJKaDNZKZc+fUONiQrSn9HkCFzamtb7k8FFmVilHVob7QsLX/VySAW8lAviMzMbFw4QtFb4errwgYA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.11_1568742191270_0.9961642803459372"}, "_hasShrinkwrap": false, "publish_time": 1568742191530, "_cnpm_publish_time": 1568742191530, "_cnpmcore_publish_time": "2021-12-13T06:51:45.218Z"}, "4.4.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.10", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.5", "minizlib": "^1.2.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.3"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.3", "tap": "^14.2.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "84ab44d8201e04139f3635685ce7ea2c2e20710a", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.10", "_nodeVersion": "12.3.1", "_npmVersion": "6.9.0", "dist": {"shasum": "946b2810b9a5e0b26140cf78bea6b0b0d689eba1", "size": 35214, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.10.tgz", "integrity": "sha512-g2SVs5QIxvo6OLp0GudTqEf05maawKUxXru104iaayWA09551tFCTI8f1Asb4lPfkBr91k07iL4c11XO3/b0tA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.10_1559678116260_0.05459998121393972"}, "_hasShrinkwrap": false, "publish_time": 1559678116494, "_cnpm_publish_time": 1559678116494, "_cnpmcore_publish_time": "2021-12-13T06:51:46.108Z"}, "4.4.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.9", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.5", "minizlib": "^1.2.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.3"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.3", "tap": "^14.2.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "gitHead": "8f85cab6b71483397fabac1b2aaa2276e3e37a11", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.9", "_nodeVersion": "12.3.1", "_npmVersion": "6.9.0", "dist": {"shasum": "058fbb152f6fc45733e84585a40c39e59302e1b3", "size": 33248, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.9.tgz", "integrity": "sha512-xisFa7Q2i3HOgfn+nmnWLGHD6Tm23hxjkx6wwGmgxkJFr6wxwXnJOdJYcZjL453PSdF0+bemO03+flAzkIdLBQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.9_1559364519667_0.4525932297857702"}, "_hasShrinkwrap": false, "publish_time": 1559364519791, "_cnpm_publish_time": 1559364519791, "_cnpmcore_publish_time": "2021-12-13T06:51:46.951Z"}, "2.2.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "2.2.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.12", "inherits": "2"}, "devDependencies": {"graceful-fs": "^4.1.2", "rimraf": "1.x", "tap": "0.x", "mkdirp": "^0.5.0"}, "license": "ISC", "readmeFilename": "README.md", "gitHead": "523c5c7fef48b10811fccd12b42803c61b6aead8", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_id": "tar@2.2.2", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "0ca8848562c7299b8b446ff6a4d60cdbb23edc40", "size": 216660, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-2.2.2.tgz", "integrity": "sha512-FCEhQ/4rE1zYv9rYXJw/msRqsnmlje5jHP6huWeBZ704jUTy02c5AZyWujpMR1ax6mVw9NyJMfuK2CMDWVIfgA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_2.2.2_1557881062489_0.30195366078850117"}, "_hasShrinkwrap": false, "publish_time": 1557881062633, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1557881062633, "_cnpmcore_publish_time": "2021-12-13T06:51:47.946Z"}, "4.4.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.8", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.4", "minizlib": "^1.1.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^12.0.1", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "gitHead": "074c89b1e639485468706af3c141a68ef8826728", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.8", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b19eec3fde2a96e64666df9fdb40c5ca1bc3747d", "size": 33170, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.8.tgz", "integrity": "sha512-LzHF64s5chPQQS0IYBn9IN5h3i98c12bo4NCO7e0sGM2llXQ3p2FGC5sdENN4cTW48O915Sh+x+EXx7XW96xYQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.8_1541721201200_0.6164252511068276"}, "_hasShrinkwrap": false, "publish_time": 1541721201416, "_cnpm_publish_time": 1541721201416, "_cnpmcore_publish_time": "2021-12-13T06:51:48.892Z"}, "4.4.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.7", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.4", "minizlib": "^1.1.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^12.0.1", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "gitHead": "88d6071c248042e29258f6f72c1b71721904cf22", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.7", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "14df45023ffdcd0c233befa2fc01ebb76ee39e7c", "size": 33131, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.7.tgz", "integrity": "sha512-mR3MzsCdN0IEWjZRuF/J9gaWHnTwOvzjqPTcvi1xXgfKTDQRp39gRETPQEfPByAdEOGmZfx1HrRsn8estaEvtA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.7_1541452445523_0.7630714409712416"}, "_hasShrinkwrap": false, "publish_time": 1541452445702, "_cnpm_publish_time": 1541452445702, "_cnpmcore_publish_time": "2021-12-13T06:51:49.749Z"}, "4.4.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.6", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.3", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^12.0.1", "tar-fs": "^1.16.2", "tar-stream": "^1.6.0"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "8a34ca860e79f4c6d6b38ebd9ec44d95ed8d6df4", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.6", "_npmVersion": "6.2.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "63110f09c00b4e60ac8bcfe1bf3c8660235fbc9b", "size": 33095, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.6.tgz", "integrity": "sha512-tMkTnh9EdzxyfW+6GK6fCahagXsnYk6kE6S9Gr9pjVdys769+laCTbodXDhPAjzVtEBazRgP0gYqOjnk9dQzLg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.6_1533168919920_0.1504720663856911"}, "_hasShrinkwrap": false, "publish_time": 1533168920037, "_cnpm_publish_time": 1533168920037, "_cnpmcore_publish_time": "2021-12-13T06:51:50.568Z"}, "4.4.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.5", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.3", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^12.0.1", "tar-fs": "^1.16.2", "tar-stream": "^1.6.0"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "b40376a9894bf12265e7a0f76a28dabf823bd771", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.5", "_npmVersion": "6.2.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "bd81907a98a0f72d6b5c6586e3ee9d07f72ace09", "size": 42623, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.5.tgz", "integrity": "sha512-caZkmxkASHTDm/GDcc7oVe94p6ZiYiDoyivHFa3L7gwDFDscJbE9dZAkAE1cyvQF97FOc0jffpJsxAF8P+V9TQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.5_1533168848329_0.24223311305749817"}, "_hasShrinkwrap": false, "publish_time": 1533168848433, "_cnpm_publish_time": 1533168848433, "_cnpmcore_publish_time": "2021-12-13T06:51:51.525Z"}, "4.4.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.4", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.3", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^12.0.1", "tar-fs": "^1.16.2", "tar-stream": "^1.6.0"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "362bbc528c15e2d8e454699f8c3964f6b8bed560", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.4", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ec8409fae9f665a4355cc3b4087d0820232bb8cd", "size": 32896, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.4.tgz", "integrity": "sha512-mq9ixIYfNF9SK0IS/h2HKMu8Q2iaCuhDDsZhdEag/FHv8fOaYld4vN7ouMgcSSt5WKZzPs8atclTcJm36OTh4w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.4_1527209926524_0.05577650681719604"}, "_hasShrinkwrap": false, "publish_time": 1527209926640, "_cnpm_publish_time": 1527209926640, "_cnpmcore_publish_time": "2021-12-13T06:51:52.448Z"}, "4.4.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.3", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.3", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^12.0.1", "tar-fs": "^1.16.2", "tar-stream": "^1.6.0"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "2131b0f6a2f672fe17a0ca4bd2f61c831184a7b3", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.3", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "d6bd509dc7f6b5a5d2c13aa0f7d57b03269b8376", "size": 39204, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.3.tgz", "integrity": "sha512-LBw+tcY+/iCCTvF4i3SjqKWIgixSs/dB+Elg3BaY0MXh03D9jWclYskg3BiOkgg414NqpFI3nRgr2Qnw5jJs7Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.3_1527119705592_0.7399063802078791"}, "_hasShrinkwrap": false, "publish_time": 1527119705909, "_cnpm_publish_time": 1527119705909, "_cnpmcore_publish_time": "2021-12-13T06:51:53.312Z"}, "4.4.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.2", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.5", "minipass": "^2.2.4", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.1.4", "tar-fs": "^1.16.2", "tar-stream": "^1.6.0"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "a5f77792dd158603b93cb00e230ef957bc3ce55b", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.2", "_npmVersion": "6.0.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "60685211ba46b38847b1ae7ee1a24d744a2cd462", "size": 32149, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.2.tgz", "integrity": "sha512-BfkE9CciGGgDsATqkikUHrQrraBCO+ke/1f6SFAEMnxyyfN9lxC+nW1NFWMpqH865DhHIy9vQi682gk1X7friw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.2_1525114667418_0.8437672073445661"}, "_hasShrinkwrap": false, "publish_time": 1525114667535, "_cnpm_publish_time": 1525114667535, "_cnpmcore_publish_time": "2021-12-13T06:51:54.184Z"}, "4.4.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.5", "minipass": "^2.2.4", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.1", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.1.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "58a8d437abccd6dc884541b8bf6554901ec592cc", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.1", "_npmVersion": "5.7.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b25d5a8470c976fd7a9a8a350f42c59e9fa81749", "size": 32001, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.1.tgz", "integrity": "sha512-O+v1r9yN4tOsvl90p5HAP4AEqbYhx4036AGMm075fH9F8Qwi3oJ+v4u50FkT/KkvywNGtwkk0zRI+8eYm1X/xg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.1_1521565198053_0.7013183734265185"}, "_hasShrinkwrap": false, "publish_time": 1521565198112, "_cnpm_publish_time": 1521565198112, "_cnpmcore_publish_time": "2021-12-13T06:51:55.250Z"}, "4.4.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.4.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.0.0-rc.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "c1f5de5a621bf03474e546aa5efad5ea7b2cd2ac", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.4.0", "_npmVersion": "5.6.0-canary.11", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "3aaf8c29b6b800a8215f33efb4df1c95ce2ac2f5", "size": 31820, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.4.0.tgz", "integrity": "sha512-gJlTiiErwo96K904FnoYWl+5+FBgS+FimU6GMh66XLdLa55al8+d4jeDfPoGwSNHdtWI5FJP6xurmVqhBuGJpQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_4.4.0_1519155419137_0.8068667000360024"}, "_hasShrinkwrap": false, "publish_time": 1519155419186, "_cnpm_publish_time": 1519155419186, "_cnpmcore_publish_time": "2021-12-13T06:51:56.220Z"}, "4.3.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.3.3", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.0.0-rc.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "9e92533a0724585c695e775d38fc1f64baf8f6ab", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.3.3", "_npmVersion": "5.6.0-canary.8", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e03823dbde4e8060f606fef7d09f92ce06c1064b", "size": 31741, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.3.3.tgz", "integrity": "sha512-v9wjbOXloOIeXifMQGkKhPH3H7tjd+8BubFKOTU+64JpFZ3q2zBfsGlnc7KmyRgl8UxVa1SCRiF3F9tqSOgcaQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.3.3.tgz_1517948326480_0.17111232038587332"}, "directories": {}, "publish_time": 1517948326562, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517948326562, "_cnpmcore_publish_time": "2021-12-13T06:51:57.192Z"}, "4.3.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.3.2", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.0.0-rc.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "5e3dff6e238c6ba74274dda018e9413d504a9249", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.3.2", "_npmVersion": "5.6.0-canary.7", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ef4e813c7cfd2eb261da107753fb8ba15ab8954b", "size": 38396, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.3.2.tgz", "integrity": "sha512-xq2YLcAiE6/THltmw5cmWkwildoat5hv+VoqSX7qqIs1ELzeR4J59QEetSoSI13Bd3RFSdcar4X/pzElEOy9Ow=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.3.2.tgz_1517503004727_0.008722305297851562"}, "directories": {}, "publish_time": 1517503004943, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517503004943, "_cnpmcore_publish_time": "2021-12-13T06:51:58.081Z"}, "4.3.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.3.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.0.0-rc.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "5afba25d097657916abd57cf580657cff818c8c6", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.3.1", "_npmVersion": "5.6.0-canary.7", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "82f711646291f1583934cc5a2118611d4c43ebc0", "size": 37395, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.3.1.tgz", "integrity": "sha512-10Q9Xx7GHaCPH1fFu7fHbq7w70A1aA5rB77bYbOjV79mQVwaK65mFVQwnxbwgYTE4krDqgoWz6XkbUf5iv63uA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.3.1.tgz_1517458964716_0.8735048579983413"}, "directories": {}, "publish_time": 1517458964860, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517458964860, "_cnpmcore_publish_time": "2021-12-13T06:51:59.160Z"}, "4.3.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.3.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.0.0-rc.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "51b6e83761dedcce661404819571779cefd35d04", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "11351be1c7944c59dd197850119c2081d8bc7fe5", "size": 37146, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.3.0.tgz", "integrity": "sha512-Ta5X6BSrA8QHznB156/nbqXUFf1M6A7rXrChKY5+6CkOjoFuOOBcJAj8FvMzDYyYBn7tb1UQNg4vUr7tkSlCUA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.3.0.tgz_1516238225635_0.41157911252230406"}, "directories": {}, "publish_time": 1516238225740, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516238225740, "_cnpmcore_publish_time": "2021-12-13T06:52:00.127Z"}, "4.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.2.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.0.0-rc.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "13bd921a8b4022d518d663fecab63d15c043c8a0", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "7e2bdadf55a4a04bf64a9d2680b4455e7c61d45e", "size": 36591, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.2.0.tgz", "integrity": "sha512-8c4LjonehF+KArauze53Tbx1tfPsWiF94cS8wK8s94wSGTWHwdVMLCRxvqe0u8fzTXfnAjlpkpOAQl240K/anw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.2.0.tgz_1513830458858_0.795999555150047"}, "directories": {}, "publish_time": 1513830459029, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513830459029, "_cnpmcore_publish_time": "2021-12-13T06:52:01.214Z"}, "4.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.1.2", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.0.4", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.0.0-rc.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "a4d8246ab22bd6c00d4d31c67363ac2e4244daa5", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.1.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "40a9da18ae9806d29e54a2633601205b83a9e99f", "size": 31271, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.1.2.tgz", "integrity": "sha512-+yCXsTFPXvZjc7tMUQo/OYPZL10weuLfEod1CMPIYJMfo7ChptNZr4UdCAuVihZ2MHVCSs6IIYTvkXqivL5bDA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.1.2.tgz_1513826225359_0.6972173426765949"}, "directories": {}, "publish_time": 1513826225518, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513826225518, "_cnpmcore_publish_time": "2021-12-13T06:52:02.205Z"}, "4.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.1.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.0.4", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.0.0-rc.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "1bfffcfd65df2b4254d85e34f4121b53d11fa4a5", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.1.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "82fab90e34ac7575f925084de66cc0d2b4a4e040", "size": 36155, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.1.1.tgz", "integrity": "sha512-p2lLtRABOEhuC28GDpMYwxcDYRqAFfiz2AIZiQlI+fhrACPKtQ1mcF/bPY8T1h9aKlpDpd+WE33Y2PJ/hWhm8g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.1.1.tgz_1511898050474_0.540841075591743"}, "directories": {}, "publish_time": 1511898050614, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511898050614, "_cnpmcore_publish_time": "2021-12-13T06:52:03.303Z"}, "4.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.1.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.0.4", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "rimraf": "^2.6.2", "tap": "^11.0.0-rc.3", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "61908e63aec9eb437b15d34a81e7baa956d13a7e", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "3344ed174eb2c6c3749cc1bbee391395962256d9", "size": 31229, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.1.0.tgz", "integrity": "sha512-xQoupzP/LFDSz3llXIS86u6PHAqzc+AUVJLLcaKg898rQ8c71cmklu0G05NQpIKqh8XNF+Pv+EptKySI7rseNw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.1.0.tgz_1511831257451_0.5656173080205917"}, "directories": {}, "publish_time": 1511831257579, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511831257579, "_cnpmcore_publish_time": "2021-12-13T06:52:04.381Z"}, "4.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.0.2", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "minipass": "^2.2.1", "minizlib": "^1.0.4", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "^2.6.2", "tap": "^10.7.2", "tar-fs": "^1.16.0", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "ec864233e717a978553be174dc352f9ca411a8e4", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.0.2", "_npmVersion": "5.5.1-canary.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "e8e22bf3eec330e5c616d415a698395e294e8fad", "size": 30825, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.0.2.tgz", "integrity": "sha512-4lWN4uAEWzw8aHyBUx9HWXvH3vIFEhOyvN22HfBzWpE07HaTBXM8ttSeCQpswRo5On4q3nmmYmk7Tomn0uhUaw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.0.2.tgz_1508355005096_0.7286235468927771"}, "directories": {}, "publish_time": **********418, "_hasShrinkwrap": false, "_cnpm_publish_time": **********418, "_cnpmcore_publish_time": "2021-12-13T06:52:05.394Z"}, "3.2.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.2.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "1dea1df937846192453b8dfd02982c02c556c1d1", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.2.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "9aa8e41c88f09e76c166075bc71f93d5166e61b1", "size": 31037, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.2.1.tgz", "integrity": "sha512-ZSzds1E0IqutvMU8HxjMaU8eB7urw2fGwTq88ukDOVuUIh0656l7/P7LiVPxhO5kS4flcRJQk8USG+cghQbTUQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.2.1.tgz_1503277952302_0.7272206638008356"}, "directories": {}, "publish_time": 1503277952637, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1503277952637, "_cnpmcore_publish_time": "2021-12-13T06:52:06.769Z"}, "4.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.0.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "89a14aa4d681ee4745d5038517c031154c2c8546", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.0.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "3f5b2e5289db30c2abe4c960f43d0d9fff96aaf0", "size": 31511, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.0.1.tgz", "integrity": "sha512-XBpU+/azPOMvE5m2Tn7Sl6U1ahpGfe77LkdrAlFilwrgHZsR+2iy0l8klQtfJNM+DACZO2Xrw10MTyQRB4du5A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.0.1.tgz_1503277822930_0.6976792945060879"}, "directories": {}, "publish_time": 1503277823741, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503277823741, "_cnpmcore_publish_time": "2021-12-13T06:52:07.967Z"}, "4.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "4.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "90ac1e8464b819131f8dbe69374e879f16998253", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@4.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "aa7d58cfb750cc919d9da1d3319f9ccabac339e0", "size": 31571, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-4.0.0.tgz", "integrity": "sha512-oHhf8rlu/1pLR0fEaUvkjTFScTBPWxyH6rxCURo4NWHjM7T0WzPvVaIZ1cw3VfdKc5852QAWavADhR14XeoaEA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-4.0.0.tgz_1503095073992_0.6837309603579342"}, "directories": {}, "publish_time": 1503095074231, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503095074231, "_cnpmcore_publish_time": "2021-12-13T06:52:09.181Z"}, "3.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.2.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.0.1", "minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "6a86ec4963d081e8362081a3a431f5b75a91984a", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "534255ba01befa11f91257cc7fa9cea773596852", "size": 31085, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.2.0.tgz", "integrity": "sha512-YRHTZzumkYuLx/lXsWZVowi9WrYbOQvxbEDtuFjq+LPCtLLGleWLkL/pdCnTwZMA92+Vg3UwVRBjpVQNcg6H3A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.2.0.tgz_1502931349675_0.5638887928798795"}, "directories": {}, "publish_time": 1502931349757, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1502931349757, "_cnpmcore_publish_time": "2021-12-13T06:52:10.265Z"}, "3.1.15": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.15", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "ef33670d429409558879ce616d4347fe4ae0a52d", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.15", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "cccdc35b90917d58e4c3837795d5d022d7a1f46f", "size": 30414, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.15.tgz", "integrity": "sha512-pQNFsg+Wb6VXsrIPUnuQwrHR4wD5ASBR0jRyiT4/AALFA2Nl+CjhkDX5fTmIwCuULRtyQR3Dae2BBnP2EFHscw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.15.tgz_1502931034160_0.11256602685898542"}, "directories": {}, "publish_time": 1502931034313, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1502931034313, "_cnpmcore_publish_time": "2021-12-13T06:52:11.394Z"}, "3.1.14": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.14", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "0594dcf3c8c2d79bf71261f7cd1e462a7af65cf8", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.14", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "fb25515074360457de49f001e2e8a49c10aa3b20", "size": 30414, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.14.tgz", "integrity": "sha512-+WVxPQOhypYpDUymCYQ703cfQaGZKUYT8aE/ebQKBoW3ntsxDFgKMPA36bAH7b8HbB+DMZe138jyrLo2ba8U9A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.14.tgz_1502926217498_0.000836462015286088"}, "directories": {}, "publish_time": 1502926217615, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1502926217615, "_cnpmcore_publish_time": "2021-12-13T06:52:13.011Z"}, "3.1.13": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.13", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "2176335779cc0c7fcd28c778eeaf0b1d8963c254", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.13", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f035a4f5b8b0b51b94530e20080f8bc696e3e320", "size": 30264, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.13.tgz", "integrity": "sha512-jDTf6d8VGRRNdYF6DjsagbRKy7LQvjXs0VuqzoJoSJXRY/wUlVttR8Ciz+sULGzLldKxbkCQAHfi0cpVpPdDmw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.13.tgz_1502843523241_0.4201464627403766"}, "directories": {}, "publish_time": 1502843523399, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1502843523399, "_cnpmcore_publish_time": "2021-12-13T06:52:14.317Z"}, "3.1.12": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.12", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "587a459ce7c72cfd50239188a1d7f6d095c44cb0", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.12", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "7c46610330ca9088af11b961c95e64dcacc31ac8", "size": 30254, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.12.tgz", "integrity": "sha512-mKpGqIzTQSvY9oRBO9NkfF8LjoU38U3JIeL5DD9dfB3Z1Z7bJCQWFkC5OgZGOV8ee38MBb+6KnQylIxVekTF3w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.12.tgz_1502840731877_0.04427452408708632"}, "directories": {}, "publish_time": 1502840732046, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1502840732046, "_cnpmcore_publish_time": "2021-12-13T06:52:15.711Z"}, "3.1.11": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.11", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "474686b31782ae009ac0761b74ec07f91084aefc", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.11", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "5e2506344f17d0bed442c6ee9c2d226d3c905804", "size": 30240, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.11.tgz", "integrity": "sha512-XkLy/D6+cqRED5zUf5KiAR9lAzAG6LBf04L/yiR9gZLgcQmUoNvqj4RcO+wSyxYEYNlAk6ivQsMdZ2B6z3Gy2w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.11.tgz_1502814844953_0.422957036877051"}, "directories": {}, "publish_time": 1502814845049, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1502814845049, "_cnpmcore_publish_time": "2021-12-13T06:52:17.126Z"}, "3.1.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.10", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "gitHead": "9780dc159e080607f24a6bee81277db2ceb0892c", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.10", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "86d006424b2f5cc9a89fb02711f7e68757147582", "size": 30235, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.10.tgz", "integrity": "sha512-7G41HWrzHoujtnxRw5nYWSnA/vNIj6z+MnubIDMdeOrBvXSgIzYy/KcWlLALABd4gEbETJK2ds0ZEYmua33dFQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.10.tgz_1502782962912_0.47463428881019354"}, "directories": {}, "publish_time": 1502782963039, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1502782963039, "_cnpmcore_publish_time": "2021-12-13T06:52:18.432Z"}, "3.1.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.9", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4"}, "files": ["index.js", "lib/"], "gitHead": "747bd164449a20689cba13001922375ffde4223b", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.9", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "2d58fe9fc5dd54652387746e1415223229c9bbeb", "size": 30034, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.9.tgz", "integrity": "sha512-/Aiui1ynEeSz6rr8RhLk3Vp9BEbtYnlIauKHco1ILlP5U6W6SSUWnDtycqTPCovdnnoutKHzzCOchZqEfCcvVw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.9.tgz_1501714110946_0.5878034713678062"}, "directories": {}, "publish_time": 1501714111039, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1501714111039, "_cnpmcore_publish_time": "2021-12-13T06:52:19.786Z"}, "3.1.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.8", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4"}, "files": ["index.js", "lib/"], "gitHead": "4b5fe3469c0280bacfa740a757da88c1ffabcd26", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.8", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "578861772c1dce2133dbbda6cdffc42be39addfa", "size": 29959, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.8.tgz", "integrity": "sha512-Ib0gFf5PaRxDHeUf6w8i9w6M0qV0vK2/RFbD14PSq4e1CDYoRQyV9N2xTJfDw2BfLygJAO9ZBvYIqZTld+fiOg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.8.tgz_1501627989373_0.34951902367174625"}, "directories": {}, "publish_time": 1501627989529, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1501627989529, "_cnpmcore_publish_time": "2021-12-13T06:52:21.327Z"}, "3.1.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.7", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4"}, "files": ["index.js", "lib/"], "gitHead": "18af12da7b639d6ffc3845f910f5e27a5d681ab0", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.7", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ca4a4a6259748dcdaa38f5bfa5c76fe9e4c89fd8", "size": 29775, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.7.tgz", "integrity": "sha512-FWD5niz4Dt/fktgui9NjyDknmGJHQaoooEyLc9uRM72CcJZuFOqIB1GAB3msTrxJ4N9/0QkrN1U8Q5pSpZRJQQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.7.tgz_1501092185757_0.33061009529046714"}, "directories": {}, "publish_time": 1501092186054, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1501092186054, "_cnpmcore_publish_time": "2021-12-13T06:52:22.618Z"}, "3.1.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.6", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4"}, "files": ["index.js", "lib/"], "gitHead": "edc4a0c4644c1378b9aa9dbc259d69d77ab733a4", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.6", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "27af227680177bcff2bd8d690867dce5eb8511b8", "size": 29765, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.6.tgz", "integrity": "sha512-yPMyN763J2vdUrW5omojOzGIEI2rg0m24Yb5OqVA1LQZ1FKe+WSPJqz8rN9vcSpr4OAk4IioFpC7cYPH786a7Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.6.tgz_1501023798446_0.3663195220287889"}, "directories": {}, "publish_time": 1501023798524, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1501023798524, "_cnpmcore_publish_time": "2021-12-13T06:52:23.859Z"}, "3.1.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.5", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.3", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4"}, "files": ["index.js", "lib/"], "gitHead": "fce07b25fae8b42d0f8e594f7d4d52e7077a2596", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.5", "_npmVersion": "5.0.1", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "4981e97ab7bad4cb1d5da9232047c9047a681aef", "size": 29749, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.5.tgz", "integrity": "sha512-TKJKz1fqBOZBaIQ/MGRKU0EnTGmKMLy4ReTRgP10AgtfOWBbj9PBg4MgY80GFpqGbs2EzcIctW5gbwbP4woDYg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.5.tgz_1496449018542_0.8640544661320746"}, "directories": {}, "publish_time": 1496449018694, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1496449018694, "_cnpmcore_publish_time": "2021-12-13T06:52:25.075Z"}, "3.1.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.4", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.1", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "engines": {"node": ">=4"}, "files": ["index.js", "lib/"], "gitHead": "0baac1f2274db222d7615c98bec9317947ee69e8", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.4", "_npmVersion": "5.0.1", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "53afbcdb7140bbe0dc2ada0a1b14e14b3ef541b6", "size": 29801, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.4.tgz", "integrity": "sha512-Y8sSCIdq+mxBbYaEMSjHRnUPGSfTAp45Mb8+QvbJ0/6f6f5qToRW+N7Q+d1+Y/26WleexXiQbTQ8RwjprIuddA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-3.1.4.tgz_1496446872587_0.1321567993145436"}, "directories": {}, "publish_time": 1496446873115, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1496446873115, "_cnpmcore_publish_time": "2021-12-13T06:52:26.622Z"}, "3.1.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.3", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.1", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "files": ["index.js", "lib/"], "gitHead": "a570fadc432ad91f3627ca09cc69c7e38ae39bed", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.3", "_npmVersion": "5.0.0-beta.49", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b4a04aafb5375a969a9fb7cc08e48a8ac3514277", "size": 29720, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.3.tgz", "integrity": "sha512-C7Ets2EikonCWW+JzLJsGxoy5CpYpR4N80EwTRaFw3aMXa9IKmsg2ItcjPT/dTaKTuROmdc/Ki/VBh5v7wNH9Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tar-3.1.3.tgz_1494896164709_0.45167989330366254"}, "directories": {}, "publish_time": 1494896164961, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1494896164961, "_cnpmcore_publish_time": "2021-12-13T06:52:27.997Z"}, "3.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.2", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.1", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "files": ["tar.js", "lib/"], "gitHead": "0525639c2d512791752ae9c4bf0f23eccd7e6833", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.2", "_npmVersion": "5.0.0-beta.44", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b4f3c86dd177822eb51922f8418cd91d4a41b19e", "size": 29565, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.2.tgz", "integrity": "sha512-hBYJ51xunos1Ar3aCsT+ApNvT9GBEG31hwwM4CryTToHcc7gl6cAt9lHW7YCbOz3FFMtVX7c49FqRQYmGrIqSg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tar-3.1.2.tgz_1494629671784_0.9335505347698927"}, "directories": {}, "publish_time": 1494629672029, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1494629672029, "_cnpmcore_publish_time": "2021-12-13T06:52:29.443Z"}, "3.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.1", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "files": ["tar.js", "lib/"], "gitHead": "51f0d2ea7162c2e9f02c0e6845b82cfa94c71cbd", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.1", "_npmVersion": "5.0.0-beta.44", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "3e6fa6949ae903f34d3b30131f825b6f0258d2e1", "size": 29098, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.1.tgz", "integrity": "sha512-UrbClqQLaiYVzR8mMmYDzbgyiexM+zX/KlYktBMu9V6yOvYvShxlbdb00xjJYmdi91sapqnq7EqAtXIKqI+Ktg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tar-3.1.1.tgz_1494608536218_0.847736562602222"}, "directories": {}, "publish_time": 1494608536466, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1494608536466, "_cnpmcore_publish_time": "2021-12-13T06:52:31.105Z"}, "3.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.1.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.1", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "files": ["tar.js", "lib/"], "gitHead": "2ba07e56f7f712cd90c20db9a7d1a99b1fc7aeed", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.1.0", "_npmVersion": "5.0.0-beta.44", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "9d3cc4dafcb96d6786ac802b564ec01de6eec2b0", "size": 29088, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.1.0.tgz", "integrity": "sha512-idZ4x9VQS7gLoxxnS1QCjtl6Xu1GaPnHY6o1d5o9aN1Hk8A/Hk9yNfTprV12Y/8BuSEsSbhe6G4O0GQz5wyYPg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/tar-3.1.0.tgz_1494607398991_0.9328433778136969"}, "directories": {}, "publish_time": 1494607401251, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1494607401251, "_cnpmcore_publish_time": "2021-12-13T06:52:32.649Z"}, "3.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.0.1", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.2", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.1", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "files": ["tar.js", "lib/"], "gitHead": "c3251a725663d014ed84f3a820d5e9e3167996b7", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.0.1", "_npmVersion": "5.0.0-beta.44", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b677a7963caf00fb1cd5ac4d17c5cd22aa46b33a", "size": 27612, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.0.1.tgz", "integrity": "sha512-IqHpYSK33G55aw9qWHIS/0+kO5ZJUBX5hKS5xxAdoUj8nbjO+txqKjwXCXUzJcbIjZKbSqXO9lYBBmorRRaDQA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/tar-3.0.1.tgz_1494438955537_0.8215328669175506"}, "directories": {}, "publish_time": 1494438958326, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1494438958326, "_cnpmcore_publish_time": "2021-12-13T06:52:34.392Z"}, "3.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "3.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"minipass": "^2.0.1", "minizlib": "^1.0.3", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "devDependencies": {"chmodr": "^1.0.2", "events-to-array": "^1.1.2", "mutate-fs": "^1.1.0", "rimraf": "1.x", "tap": "^10.3.1", "tar-fs": "^1.15.2", "tar-stream": "^1.5.2"}, "license": "ISC", "files": ["tar.js", "lib/"], "gitHead": "1098f4b91d2f51aab7301279fdfa104b12eb45e8", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@3.0.0", "_npmVersion": "5.0.0-beta.44", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "cb7b29da4edfcffd06849a1be3d4aeade23daf48", "size": 27592, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-3.0.0.tgz", "integrity": "sha512-Dkr/uUoaelHrifTp4BkVY/jldqgD+Sxo8NOdyT6dkby9R3U9G4gH6Sw4buKgPrGYQgFqs5UK2j0vB4W6nqTpXQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/tar-3.0.0.tgz_1494379464880_0.09705448755994439"}, "directories": {}, "publish_time": 1494379467621, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1494379467621, "_cnpmcore_publish_time": "2021-12-13T06:52:35.715Z"}, "2.2.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "2.2.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "devDependencies": {"graceful-fs": "^4.1.2", "rimraf": "1.x", "tap": "0.x", "mkdirp": "^0.5.0"}, "license": "ISC", "gitHead": "52237e39d2eb68d22a32d9a98f1d762189fe6a3d", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_id": "tar@2.2.1", "_shasum": "8e4d2a256c0e2185c6b18ad694aec968b83cb1d1", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "2.2.2", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "8e4d2a256c0e2185c6b18ad694aec968b83cb1d1", "size": 49619, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-2.2.1.tgz", "integrity": "sha512-2Tw2uNtZqQTSHTIMbKHKFeAPmKcljrNKqKiIN7pu3V/CxYqRgS8DLXvMkFRrbtXlg6mTOQcuTX7DMj18Xi0dtg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1441849778319, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1441849778319, "_cnpmcore_publish_time": "2021-12-13T06:52:37.152Z"}, "2.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "2.2.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "devDependencies": {"graceful-fs": "^3.0.2", "rimraf": "1.x", "tap": "0.x", "mkdirp": "^0.5.0"}, "license": "ISC", "gitHead": "3cab63959c51451a84cc8d1f8ef02d45b8b4f836", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_id": "tar@2.2.0", "_shasum": "527c595940b9673f386c7237759982ab2f274d08", "_from": ".", "_npmVersion": "2.13.4", "_nodeVersion": "0.12.4", "_npmUser": {"name": "soldair", "email": "<EMAIL>"}, "dist": {"shasum": "527c595940b9673f386c7237759982ab2f274d08", "size": 49648, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-2.2.0.tgz", "integrity": "sha512-RMpnyvcR174rI+jivaKUAkyj/GcNnMMdKo+IjQW8QELO5y+hgfJZvJ4HhTiqomzhkA0Dmj0RoQL95j5UHLrhIg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1440709289846, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1440709289846, "_cnpmcore_publish_time": "2021-12-13T06:52:38.539Z"}, "2.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "2.1.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "devDependencies": {"graceful-fs": "^3.0.2", "rimraf": "1.x", "tap": "0.x", "mkdirp": "^0.5.0"}, "license": "ISC", "gitHead": "2cbe6c805fc5d87ce099183ed13c43faba962224", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_id": "tar@2.1.1", "_shasum": "ac0649e135fa4546e430c7698514e1da2e8a7cc4", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "1.8.1", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "dist": {"shasum": "ac0649e135fa4546e430c7698514e1da2e8a7cc4", "size": 49933, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-2.1.1.tgz", "integrity": "sha512-E3Dh0jsaXqrqHOYiUaOEardfoT/FEsq+4i+abbtsroXwdVH5SajgQVaILfXwV5O3UwHKSEOhcvW5YcfF8n2I7Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431045496661, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1431045496661, "_cnpmcore_publish_time": "2021-12-13T06:52:40.075Z"}, "2.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "2.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "devDependencies": {"graceful-fs": "^3.0.2", "rimraf": "1.x", "tap": "0.x", "mkdirp": "^0.5.0"}, "license": "BSD", "gitHead": "b4c03a8e922fa522a3ddadaf2764bc1ab38d484e", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_id": "tar@2.1.0", "_shasum": "d287aad12e947c766e319ac364f3c234900f65ec", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.7.1", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "dist": {"shasum": "d287aad12e947c766e319ac364f3c234900f65ec", "size": 50160, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-2.1.0.tgz", "integrity": "sha512-+2s+I4nV0EtP5geFZCxNkQI3O5jodB2toStlzb9+RJoVHB7MVQ/fHLXhLHhV/pjkb1U7ES4FAZ0N0iXnxDS+5A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1429254187838, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1429254187838, "_cnpmcore_publish_time": "2021-12-13T06:52:41.587Z"}, "2.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "2.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "devDependencies": {"graceful-fs": "^3.0.2", "rimraf": "1.x", "tap": "0.x", "mkdirp": "^0.5.0"}, "license": "BSD", "gitHead": "ce405d0b96f0fe186dd4cc68d666fabb0c59818d", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar", "_id": "tar@2.0.1", "_shasum": "a1537ab0d1ce61462ce87b4eed1cd263fba5fc17", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.4.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "a1537ab0d1ce61462ce87b4eed1cd263fba5fc17", "size": 45016, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-2.0.1.tgz", "integrity": "sha512-bB62AcFkbsdJXLXr1K0mlO5cVcqI7TaUXFwkbHpfwJ7Mzqn7R5DNQZM0b3kuDpdREtu0GGN+ml6QJY1FRV7JMg=="}, "directories": {}, "publish_time": 1428540976262, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1428540976262, "_cnpmcore_publish_time": "2021-12-13T06:52:43.030Z"}, "2.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "devDependencies": {"graceful-fs": "^3.0.2", "rimraf": "1.x", "tap": "0.x", "mkdirp": "^0.5.0"}, "license": "BSD", "gitHead": "9bde260b9ebe6808837a85bedf9c6f7bb04e004f", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar", "_id": "tar@2.0.0", "_shasum": "7cf627bc632167766ce2a5a1f23e4ecb8e3f28bc", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "1.6.2", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "7cf627bc632167766ce2a5a1f23e4ecb8e3f28bc", "size": 44820, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-2.0.0.tgz", "integrity": "sha512-XOCzEegB7fnwSqdn5zsndAg7MHz0JysT1Sz8mDX0c2Y9RbbBfvjPlxOy+XmVg7ePbrQY7mxDx+8jx4VGGBmrHQ=="}, "directories": {}, "publish_time": 1427420753078, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1427420753078, "_cnpmcore_publish_time": "2021-12-13T06:52:44.869Z"}, "1.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "1.0.3", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "devDependencies": {"graceful-fs": "^3.0.2", "rimraf": "1.x", "tap": "0.x", "mkdirp": "^0.5.0"}, "license": "BSD", "gitHead": "f4151128c585da236c6b1e278b762ecaedc20c15", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar", "_id": "tar@1.0.3", "_shasum": "15bcdab244fa4add44e4244a0176edb8aa9a2b44", "_from": ".", "_npmVersion": "2.1.10", "_nodeVersion": "0.10.33", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "15bcdab244fa4add44e4244a0176edb8aa9a2b44", "size": 44071, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-1.0.3.tgz", "integrity": "sha512-/quI12ZS9lw9pGwkxXrSicTSwujEDJWdGLLlzRG6SR7Yih11Efy3paaKof6TUqf1emBIt3z/Ua5qFxpm9JCTbg=="}, "directories": {}, "publish_time": 1417139425407, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1417139425407, "_cnpmcore_publish_time": "2021-12-13T06:52:46.596Z"}, "1.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "1.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "devDependencies": {"graceful-fs": "^3.0.2", "rimraf": "1.x", "tap": "0.x", "mkdirp": "^0.5.0"}, "license": "BSD", "gitHead": "f31811bfa4ed1d1a89c380c65595ac92474dd84d", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar", "_id": "tar@1.0.2", "_shasum": "8b0f6740f9946259de26a3ed9c9a22890dff023f", "_from": ".", "_npmVersion": "2.1.3", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "8b0f6740f9946259de26a3ed9c9a22890dff023f", "size": 44141, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-1.0.2.tgz", "integrity": "sha512-2Rmw++RNkX86qPI/sgS3XFzwecGjSiG1+INHhl46Je9Y8938snM2vIk7niI2cPX4Ic4xDwLHBYW3KJ3ZvZVORw=="}, "directories": {}, "publish_time": 1414479017116, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1414479017116, "_cnpmcore_publish_time": "2021-12-13T06:52:48.208Z"}, "1.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "devDependencies": {"graceful-fs": "^3.0.2", "rimraf": "1.x", "tap": "0.x"}, "license": "BSD", "gitHead": "476bf6f5882b9c33d1cbf66f175d0f25e3981044", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar", "_id": "tar@1.0.1", "_shasum": "6075b5a1f236defe0c7e3756d3d9b3ebdad0f19a", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "6075b5a1f236defe0c7e3756d3d9b3ebdad0f19a", "size": 44046, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-1.0.1.tgz", "integrity": "sha512-v/h4WGLjqGY23MA6/aK9VrLBTriD1qCY9ZwXzWIJdiDVnI9DNV/v++VTSKolbJxA1Y+cf00iydP/MGdgToB2gQ=="}, "directories": {}, "publish_time": 1408476379807, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1408476379807, "_cnpmcore_publish_time": "2021-12-13T06:52:49.742Z"}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "^1.0.0", "inherits": "2"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "license": "BSD", "gitHead": "49979621a55c73c3f668d8e01830eba1ea9df862", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar", "_id": "tar@1.0.0", "_shasum": "36636d76e8ae12b4bc11a940ac606b5ca8a5fe1f", "_from": ".", "_npmVersion": "1.4.22", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "36636d76e8ae12b4bc11a940ac606b5ca8a5fe1f", "size": 43411, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-1.0.0.tgz", "integrity": "sha512-kc/JSzTO1QyzWBwNP2SMv6H8lAZZGkiuqORztYo7qnqjDjFPji3D2rznusngJceuxzXYlMOg6C0xXNPpyxQw+Q=="}, "directories": {}, "publish_time": 1406845716607, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1406845716607, "_cnpmcore_publish_time": "2021-12-13T06:52:51.299Z"}, "0.1.20": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.20", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"block-stream": "*", "fstream": "~0.1.28", "inherits": "2"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "license": "BSD", "gitHead": "b5931010907cd1ef5a186bc947954391050cbcce", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar", "_id": "tar@0.1.20", "_shasum": "42940bae5b5f22c74483699126f9f3f27449cb13", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "42940bae5b5f22c74483699126f9f3f27449cb13", "size": 43409, "noattachment": false, "tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.20.tgz", "integrity": "sha512-RW79YhJI8vSyOcVv0OzOr7DVGkhtFhyz6cufNETPF7dmCJ/kQH+ubpCZkV8Wb7RvQlPuGplQPy0o2ihElzkEZA=="}, "directories": {}, "publish_time": 1403643107700, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1403643107700, "_cnpmcore_publish_time": "2021-12-13T06:52:52.717Z"}, "0.1.19": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.19", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"inherits": "2", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "license": "BSD", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar", "_id": "tar@0.1.19", "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.19.tgz", "shasum": "fe45941799e660ce1ea52d875d37481b4bf13eac", "size": 43239, "noattachment": false, "integrity": "sha512-rIgWlooBwcq8vgE9AbxcCicx9dd1hQw5OOzhtpRrDBVwngKoh3yaE8ICD9RzZUPdOu/nX4Y9qunZghKAoc3GTw=="}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386609985554, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1386609985554, "_cnpmcore_publish_time": "2021-12-13T06:52:54.157Z"}, "0.1.18": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.18", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"inherits": "2", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "license": "BSD", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "_id": "tar@0.1.18", "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.18.tgz", "shasum": "b76c3b23c5e90f9e3e344462f537047c695ba635", "size": 43443, "noattachment": false, "integrity": "sha512-n/G0ARVxLtm08ljk7qO4rmRIssde6+0SO0x9s5h1igiD2lWtjZQeV+MpFIWPwWVsU7DTAGHJ6oyGaN0nXn5P2w=="}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1374636917606, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1374636917606, "_cnpmcore_publish_time": "2021-12-13T06:52:55.845Z"}, "0.1.17": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.17", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "license": "BSD", "readmeFilename": "README.md", "_id": "tar@0.1.17", "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.17.tgz", "shasum": "408c8a95deb8e78a65b59b1a51a333183a32badc", "size": 43466, "noattachment": false, "integrity": "sha512-pmkFvH6JqkYZ+FR62iNrcq9aAM+iGkhc/gaWdlEZFeJ3UoibWdU8e6n++a28VpDJNCxpThmqfsAiHDaXbgA+Ig=="}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1363760589563, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1363760589563, "_cnpmcore_publish_time": "2021-12-13T06:52:57.776Z"}, "0.1.16": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.16", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "license": "BSD", "readmeFilename": "README.md", "_id": "tar@0.1.16", "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.16.tgz", "shasum": "4e0d9cd1fe08c9cfd1e6edb497153d1636617fee", "size": 43490, "noattachment": false, "integrity": "sha512-HALEU6/Td0dY8jOf5yPe/zBGcbunXSCEv1sUEuZRvKNhMSkgEtUMFhmkCCpRyD+BV0TqVGZ4dbSsR5ZZpYS16g=="}, "_from": ".", "_npmVersion": "1.2.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1359832729559, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1359832729559, "_cnpmcore_publish_time": "2021-12-13T06:52:59.792Z"}, "0.1.15": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.15", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "license": "BSD", "readmeFilename": "README.md", "_id": "tar@0.1.15", "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.15.tgz", "shasum": "5c8f42e0c8a4392625274f132df6286a6e62c408", "size": 62709, "noattachment": false, "integrity": "sha512-nb/rhOuuQrW5p5OMXFBGTUk5g6TR/uA2N/jXju3y5VO8B2wYNjNIBOKu34h6tDxnP5XmM2UIxrmHyPxpg9/3Qw=="}, "_from": ".", "_npmVersion": "1.2.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1359772737779, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1359772737779, "_cnpmcore_publish_time": "2021-12-13T06:53:01.509Z"}, "0.1.14": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.14", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "license": "BSD", "readmeFilename": "README.md", "_id": "tar@0.1.14", "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.14.tgz", "shasum": "20fc8516337e9371c82adb143309911e49d78044", "size": 43604, "noattachment": false, "integrity": "sha512-RJRRwqf/GcwLHeKp+umQN6czuK42dqKg85qT84BU0RsaDAjQpK111WPHXQXWbltwrORjxlhJo+gs90Td5xpDOg=="}, "_npmVersion": "1.1.68", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1354507949063, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1354507949063, "_cnpmcore_publish_time": "2021-12-13T06:53:03.174Z"}, "0.1.13": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.13", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.13", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.4", "_nodeVersion": "v0.7.6-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.13.tgz", "shasum": "5e467fcfb4b911ca8881e36eb5c1eb4ab06d9ae5", "size": 44386, "noattachment": false, "integrity": "sha512-hmRuydJ6R1wqVzq2zD6qGArzdQ0Cc5r8ze0ZTZThHCBwxsffYwX8lbSiykFLgslIzw9GSPF1hJdQlB9HAjxtpA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1331597259934, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1331597259934, "_cnpmcore_publish_time": "2021-12-13T06:53:04.824Z"}, "0.1.12": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.12", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.12", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0", "_nodeVersion": "v0.6.8-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.12.tgz", "shasum": "43893a50eeb87e4e604c437ae6a650095506605c", "size": 44267, "noattachment": false, "integrity": "sha512-QeuY+ufsv3KJ+GAaXBuqgGdkc2EpQ6KNLsphyoQuN2mNEiXnhwnRGvm0Nl8B2nSQUwL3k/7PYBl1q8ggAeJxuw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1326477948499, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1326477948499, "_cnpmcore_publish_time": "2021-12-13T06:53:06.488Z"}, "0.1.11": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.11", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.11", "_engineSupported": true, "_npmVersion": "1.1.0-beta-10", "_nodeVersion": "v0.6.7-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.11.tgz", "shasum": "621119ee024da5c5daf4dad6255ce858763a9718", "size": 41694, "noattachment": false, "integrity": "sha512-STR6jTv6l967CVvstdLWVOMPmOqONsbxt8HoyBwkfoDzTHkKh3U17MKa9VY32XYZjJ3yP1K3ESkWwqALR/MQlg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1325814450560, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1325814450560, "_cnpmcore_publish_time": "2021-12-13T06:53:08.201Z"}, "0.1.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.10", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.10", "_engineSupported": true, "_npmVersion": "1.1.0-beta-8", "_nodeVersion": "v0.6.7-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.10.tgz", "shasum": "4bd1ad67043152899df0836c2fc8fe69d8521a55", "size": 41615, "noattachment": false, "integrity": "sha512-ZnnIhQR0b/M/FxakXyI6/q5ojglFEA1VwuTuJYD1yAVOagKNCuXbbEvKGpDFPy33SFXvGuz5u+s0UtB4sAyACg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1325724403197, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1325724403197, "_cnpmcore_publish_time": "2021-12-13T06:53:09.972Z"}, "0.1.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.9", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.8"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.9", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-6", "_nodeVersion": "v0.6.6-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.9.tgz", "shasum": "e68653171937dd505c2b2e57bbcb9e0c780de1fa", "size": 41758, "noattachment": false, "integrity": "sha512-sWlPGnyYhlDFtMBh8Av9Swx6C+wQjHLXY827z8CqcNTwfPDgU+cHuTISIEHDkXVJXc5FMRpNGV51a+gQZAzO+g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1323396105330, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1323396105330, "_cnpmcore_publish_time": "2021-12-13T06:53:11.699Z"}, "0.1.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.8", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.6"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.8", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-6", "_nodeVersion": "v0.6.4", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.8.tgz", "shasum": "39316070df0332271186688061a002c973f6e928", "size": 41740, "noattachment": false, "integrity": "sha512-+XKtVSWnLh0mVOj5/j6uHqOk12jjMIIWVBtvD9yRrJ8jLN4QpGht5/quOYzx1bqPj43lPezgPC6OTdOCPK8hZw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322879360608, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1322879360608, "_cnpmcore_publish_time": "2021-12-13T06:53:13.238Z"}, "0.1.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.7", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.5"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.7", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-3", "_nodeVersion": "v0.6.4-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.7.tgz", "shasum": "33d288cfa1fc62a3b95a265a475a960b0a5721fe", "size": 41730, "noattachment": false, "integrity": "sha512-xoHS5yDx6W0oKUDw3IKfxrbGBrks/7rPGB8JVujBLMIGNAybxcmW1OttPAEeT+b72Sdo5Xmv2fYTMLspOUbNrQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322709177060, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1322709177060, "_cnpmcore_publish_time": "2021-12-13T06:53:15.571Z"}, "0.1.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.6", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.5"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.6", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-2", "_nodeVersion": "v0.6.4-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.6.tgz", "shasum": "ac70abbfac3ab0b1f18775e54fe74d6114b028f8", "size": 41524, "noattachment": false, "integrity": "sha512-0kKXkpdXGPi0+FCzSoxfbMrvk6LIPUtk3oMBVqEFcm60f0d8Gv5GVsPrB7bORlAnAkWVZE6ND1B2O75+eWNpmg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322686210803, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1322686210803, "_cnpmcore_publish_time": "2021-12-13T06:53:17.616Z"}, "0.1.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.5", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.5"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.5", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-2", "_nodeVersion": "v0.6.4-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.5.tgz", "shasum": "190ad20031b539c26268f7d96a079acc1a189b8b", "size": 41148, "noattachment": false, "integrity": "sha512-+dTLL3SNrfk9bue4EdGqAZvnOorsWAmfUirK9LSnZE+T8oGFOQBLlKb0f4QLxXd0Jd/qKRl8afrQF2Gcz27U+w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322679274219, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1322679274219, "_cnpmcore_publish_time": "2021-12-13T06:53:19.611Z"}, "0.1.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.4", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1.3"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.4", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-2", "_nodeVersion": "v0.6.4-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.4.tgz", "shasum": "6af6319b23c830c787bad1b4b2ea2189a776074d", "size": 40845, "noattachment": false, "integrity": "sha512-zDIL5Gx/gqsbEDWfkW9XJMjJ5XmX/feCGT5VcO7T8qvJ/bIVfP57sU3Ub2qXlHA10K1Ys/nbuKM5TFPAwfFaUg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322535742216, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1322535742216, "_cnpmcore_publish_time": "2021-12-13T06:53:21.689Z"}, "0.1.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.3", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "0.1"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.3", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-2", "_nodeVersion": "v0.6.2-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.3.tgz", "shasum": "b40685c0727a74af56df9e03bae44621cbde206f", "size": 48207, "noattachment": false, "integrity": "sha512-QqgKeT9Gjqyyl2K+1brKK76RvqZFe1tXYFLjpsIfR88tARrMP5IZ7DRSBEJF6LQ+S+OXCLvGZL8mpOnf/4+QtA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1322009096703, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1322009096703, "_cnpmcore_publish_time": "2021-12-13T06:53:23.645Z"}, "0.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.2", "_engineSupported": true, "_npmVersion": "1.1.0-alpha", "_nodeVersion": "v0.6.2-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.2.tgz", "shasum": "52cebd0abc1456c9c405d37838d4201d5d186ebe", "size": 48192, "noattachment": false, "integrity": "sha512-sRmod77koebkYDPwd8zfVrH44t4q0etejBlTISTyliknesZmakNGmnAEITwdRiHGY5JkEG+vLxe4Zhvx8qYwuQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1321914679762, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1321914679762, "_cnpmcore_publish_time": "2021-12-13T06:53:25.476Z"}, "0.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "tar", "description": "tar for node", "version": "0.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-tar.git"}, "main": "tar.js", "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "dependencies": {"inherits": "1.x", "block-stream": "*", "fstream": "~0.1"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "tar@0.1.0", "_engineSupported": true, "_npmVersion": "1.1.0-alpha", "_nodeVersion": "v0.6.2-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.1.0.tgz", "shasum": "10fcd1d940ba7330f89c2a35ad5d16f0107a5d67", "size": 48024, "noattachment": false, "integrity": "sha512-TwL/w7JZhsiGRR3tFks+QX1h5vCA72/kamD3DriA5AgFM1nxbyoBdZdRX83fqVAf7Prbi6kQljFHxC5Qw683og=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1321775556525, "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1321775556525, "_cnpmcore_publish_time": "2021-12-13T06:53:27.459Z"}, "0.0.1": {"name": "tar", "version": "0.0.1", "author": {"name": "<PERSON>"}, "directories": {"lib": "./lib"}, "main": "./lib/tar", "_id": "tar@0.0.1", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmmirror.com/tar/-/tar-0.0.1.tgz", "shasum": "2f94ccd48020df33ade32241ccb21ea109b11f56", "size": 8285, "noattachment": false, "integrity": "sha512-vWG/yzh21vuAwt/7vlnhASrOVlHbCtE8pt6zm82/gzev0HIHA+xj//VEqXbAHNHcZ7/c1xcPgdrEbCMni/BGcg=="}, "publish_time": 1321773078109, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_cnpm_publish_time": 1321773078109, "_cnpmcore_publish_time": "2021-12-13T06:53:29.352Z"}, "6.1.12": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "6.1.12", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "template-oss-apply": "template-oss-apply --force", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "test": "tap", "posttest": "npm run lint"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.8.0", "chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "nock": "^13.2.9", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": ">=10"}, "tap": {"coverage-map": "map.js", "timeout": 0, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.8.0", "content": "scripts/template-oss", "engines": ">=10", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"]}, "gitHead": "bec36c741c5a29aa925acd6034a7b27101212716", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.12", "_nodeVersion": "18.12.0", "_npmVersion": "9.0.1", "dist": {"integrity": "sha512-jU4TdemS31uABHd+Lt5WEYJuzn+TJTCBLljvIAHZOz6M9Os5pJ4dD+vRFLxPa/n3T0iEFzpi+0x1UfuDZYbRMw==", "shasum": "3b742fb05669b55671fb769ab67a7791ea1a62e6", "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.12.tgz", "fileCount": 29, "unpackedSize": 163639, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdimIooOADDZSMMowWnrIHjXXdEV24e0mwwZ3vBMtp4gIhAPAQz0afQ4sUV9Vau+DC0Sd7nlJ+J2cyp6NFgAVo2pmY"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYUpIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEGw//cIKPUslx+XEb4iw1FsC1c1WJM7qc2sPfcPTaZ4C/OxoDmuxe\r\nQkuVXQ9if5c/xqkNSVzUodKwa4kXAxhMxZcCIpv9BC0//BDDiyb0q93ifMM0\r\nmaKO8qyNEzyOZy+v/nGjG+e7k+GwfKGgbe4G+hSFQV48Y3ekgo7JctAi+6xG\r\nvsJz9bLYg/WCS+XEaUGP29sQ5N0fl9JFwCFgjynq5c5O4R6xP+fsqghSyaRx\r\ncfJqxFp9upbeuwpqhfhZFSchfJnWFamMs0DZKXKQcVGr3tqCIrkKEsEQ09t7\r\nnfi3BXXYTtDQ+Tz4cg8M/V+DXQl8f1aWOLbK/GCSo/oInKiJF3w2CBSyDPNb\r\nUZ5vWqday0XUlM8KJkcXsG+BuwPhMUlsIgO7P6NbWT/hc4IiXFEXIEhXhFBH\r\nwRSRozYrpYU7R6ICBNJQzmMVA6J1qeiTrSchvSyQykiLjAAxoGw9tOLdMzjl\r\nBp5vz3bIecInd5M/P9yb2oXeGBPPqnmqJw3iSJ+Jo7VQI3ruBlgZNFew0w5j\r\niQXcitJl8V1eOXQx+2oF5v89cNFVMsdXmfHwba1b/QkxLB1L1Cp8j9dP4Q8o\r\nYwn9aLILJiZPdqNf61Ge20r7l2Kh9Lch+OtlFEmTR53uTL/wuWFAkjNHQqtE\r\na//Fcr+OeGtapcsjpBYGQ6p9aoHjxVEazzE=\r\n=yo/O\r\n-----END PGP SIGNATURE-----\r\n", "size": 41840}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.12_1667320392062_0.6486373287121292"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-01T16:36:07.930Z"}, "6.1.13": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "6.1.13", "repository": {"type": "git", "url": "git+https://github.com/npm/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "template-oss-apply": "template-oss-apply --force", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "test": "tap", "posttest": "npm run lint"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^4.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.10.0", "chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.2.9", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": ">=10"}, "tap": {"coverage-map": "map.js", "timeout": 0, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.10.0", "content": "scripts/template-oss", "engines": ">=10", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"]}, "gitHead": "a044a87c6c7fb3ace4ea9bf903c63f0f15965398", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "homepage": "https://github.com/npm/node-tar#readme", "_id": "tar@6.1.13", "_nodeVersion": "18.12.1", "_npmVersion": "9.1.1", "dist": {"integrity": "sha512-jdIBIN6LTIe2jqzay/2vtYLlBHa3JF42ot3h1dW8Q0PaAG4v8rm0cvpVePtau5C6OKXGGcgO9q2AMNSWxiLqKw==", "shasum": "46e22529000f612180601a6fe0680e7da508847b", "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.13.tgz", "fileCount": 29, "unpackedSize": 163641, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsY3hlrt7Ss13tFx/l1Z6E8DqBeKw78Ps0gBhBmy3ZEAIgOjNXw6kcinDoe0N9/gHzQ5AUa7mXPdnjT/WJGh95uW8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkPhnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0Yg/9F9ntQ5GfP4trWX2QRIwPLnyeWW6JC61KEOaVTH0e6deT8170\r\nf9PpoxhimSmCNrJS8ISCntxz6zTnWOhRuQw7KAZBI3FZgUIU6P2/ykthgeqc\r\nNslXEr4j9HL0MltPGO/mXqFJoBCEMxSYwTBOuHtwxu4uN2JnTaG+2eKwsLgH\r\ntmawtLVgVoz/uVK+z4FRLg8K/7htkQ2vbdim0mnRs2e+W61qzyI7OdnStbC2\r\n1VIKwr2Z24A3WCLHKBfbLINxWShL2e9alDKEcGIt3OdM+OfK1bV6ZCWHrSNO\r\nkl99IKzT9TStPvCAQUi0sNf7+OI/kCyYLt6vPGlzeUW+UaAW+xDjHCTD5SYU\r\nJWEBWw2I83yYd8/b1kqcxqm9Og8wgCORyzsVk310g0vr4ZBzUv/0BrRdBGHX\r\nzklW23cc0nMrfWSaeJBgxg8txM+024U+aKvijrYd9pXp1DIGSD1q38kaH158\r\n/bIvAgwrtHmpy//V/qAHvz79FZbP+qSDRllrnsneMj9w40srK6rf42iCcuAP\r\nHpTAeLT9Zo/b8TPR8hJ3Onn0sV+rvvn7+8oaM6jjZ5eS6UTX1mBtn71q0FLB\r\nplYCoeL+t9v8V6ziypEZ6OjFCzUXXbHo7Zef+Y/wnP6vX3Ymcl1H5gpdPNMx\r\n1cCUqXbqd4903y0WUq8gYdpKzXhfz8Y5Acc=\r\n=H8T8\r\n-----END PGP SIGNATURE-----\r\n", "size": 41842}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.13_1670445159418_0.7871782761036956"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-12-07T20:43:50.046Z"}, "6.1.14": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "6.1.14", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "template-oss-apply": "template-oss-apply --force", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "test": "tap", "posttest": "npm run lint"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.2.9", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": ">=10"}, "tap": {"coverage-map": "map.js", "timeout": 0, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0", "content": "scripts/template-oss", "engines": ">=10", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"]}, "gitHead": "4aaffc862f4e991f7965ecf6527072c4423ecb49", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_id": "tar@6.1.14", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-piERznXu0U7/pW7cdSn7hjqySIVTYT6F76icmFk7ptU7dDYlXTm5r9A6K04R2vU3olYgoKeo1Cg3eeu5nhftAw==", "shasum": "e87926bec1cfe7c9e783a77a79f3e81c1cfa3b66", "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.14.tgz", "fileCount": 29, "unpackedSize": 163656, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClA/VrAjP6HiEv37th/ycIMApyaE4wCUPQt8fKjLsvLwIhAOD5wKnsE59xGICpWS0AC9+5Eu1uvBJzB1nmukESoP0P"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUZKvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbERAAoYlEpd7xCq41CemT28lLpyMT+FweYCIAmZWPba+JJgvwSrp7\r\n/y+luD07DhGpKiGfwcHvVIuGZcKugWnPvH04dTi/H7GK2GqxOVQDtAQHDp3L\r\no6CMmT9F4VWxsL4H0UTsbLnuyCa0I9o5PkRjOpPNo28F+4zvlDkEC7BkFsOM\r\n2yi5E/bc1BxfukmKaUO+Az+RvxDLx0aK/nk8iU2o6TtKx6nWTedINzRsiHyp\r\nbrAJy1MNpZoBuLDdnWR3j8Hv7TpAjJOBGg20aOAXFNgVaPKFZcIQHFAzJz0k\r\nNnJyqaw/yN7qV6jFOwZsncXXNpAgaWtTuaNJf153iKhEnLTpR23YlHet7+ZN\r\nbHbyVnkzOlH6NFxKsNEKPvJS33dPGOgljnMCROq5KoxyZ7ZgN/oKLalpAlzr\r\nQo2XWmh0Z/6YYAkviL3rJ31QMcbvfqjs2SLq5o6f/UdNaJY39DGZ9QqPOejx\r\nogp23CXIAXyWplehG+mvvNaDA687WQ2KVAlVtfLrSAbQNlErV76mFzuuj4MR\r\nmZ36hF/G3sxvgT6+35Sf7NViLhnltoUhYhRyiSbaT4dzu5ytj2uPnKASFs54\r\nipt/qoeVf81VV+/CBf5eHHJgwPfI24AeMCOA2rFXn8dHsfFHeaVVZ+0AtYzG\r\nS79tgwkCwgFBRT9I9FV6zKPY894DnirjV80=\r\n=lEWu\r\n-----END PGP SIGNATURE-----\r\n", "size": 41854}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.14_1683067567126_0.8102772200579933"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-02T22:46:07.312Z", "publish_time": 1683067567312, "_source_registry_name": "default"}, "6.1.15": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "6.1.15", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "template-oss-apply": "template-oss-apply --force", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "test": "tap", "posttest": "npm run lint"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.2.9", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": ">=10"}, "tap": {"coverage-map": "map.js", "timeout": 0, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0", "content": "scripts/template-oss", "engines": ">=10", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"]}, "gitHead": "3302cf7330052982ad7d7e9f85e823fa1bb945a4", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_id": "tar@6.1.15", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-/zKt9UyngnxIT/EAGYuxaMYgOIJiP81ab9ZfkILq4oNLPFX50qyYmu7jRj9qeXoxmJHjGlbH0+cm2uy1WCs10A==", "shasum": "c9738b0b98845a3b344d334b8fa3041aaba53a69", "tarball": "https://registry.npmmirror.com/tar/-/tar-6.1.15.tgz", "fileCount": 29, "unpackedSize": 163655, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDwemW47JqdR340tK1p+sJOkh/2yQkHj5OUeZxwy9kfQIgVJYqRwjhbRRgnEvQEXwkTLalC4xCwMmLPg4OTK7OfJI="}], "size": 41854}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.1.15_1684301931697_0.9208356227037398"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-17T05:38:51.855Z", "publish_time": 1684301931855, "_source_registry_name": "default"}, "6.2.0": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "6.2.0", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.2.9", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": ">=10"}, "tap": {"coverage-map": "map.js", "timeout": 0, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0", "content": "scripts/template-oss", "engines": ">=10", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"]}, "_id": "tar@6.2.0", "gitHead": "5bc9d404e88c39870e0fbb55655a53de6fbf0a04", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "18.16.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-/Wo7DcT0u5HUV486xg675HtjNd3BXZ6xDbzsCUZPt5iw8bTQ63bP0Raut3mvro9u+CUyq7YQd8Cx55fsZXxqLQ==", "shasum": "b14ce49a79cb1cd23bc9b016302dea5474493f73", "tarball": "https://registry.npmmirror.com/tar/-/tar-6.2.0.tgz", "fileCount": 29, "unpackedSize": 165629, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHHmm6S/j2Mzqz02plQEPKVkvucfDCfwOYNZCjRSM93ZAiBV5b4KEkjVREUu8MMsQTduB6sRnxbuUEQKdRULw4bGlQ=="}], "size": 42424}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.2.0_1693892029140_0.06913049104751678"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-05T05:33:49.378Z", "publish_time": 1693892029378, "_source_registry_name": "default"}, "6.2.1": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "6.2.1", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.2.9", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": ">=10"}, "tap": {"coverage-map": "map.js", "timeout": 0, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0", "content": "scripts/template-oss", "engines": ">=10", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"]}, "_id": "tar@6.2.1", "gitHead": "c65f76d89a69a4c0d2a1e3ab97c289ce965f6476", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==", "shasum": "717549c541bc3c2af15751bea94b1dd068d4b03a", "tarball": "https://registry.npmmirror.com/tar/-/tar-6.2.1.tgz", "fileCount": 29, "unpackedSize": 166613, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICiN57b6g7ZWCQN3sRJ+KpQedOYvNEmp0rJiwyGiT6ilAiA92/r4ohXAxTc9ATTxiPbhsnqxFx5d2WFp4qzPVMI+cA=="}], "size": 42735}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_6.2.1_1711055584589_0.44397657073835783"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-21T21:13:04.779Z", "publish_time": 1711055584779, "_source_registry_name": "default"}, "7.0.0": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "7.0.0", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap", "pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "tshy", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^5.0.0", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.5.4", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "license": "ISC", "engines": {"node": ">=18"}, "tap": {"coverage-map": "map.js", "timeout": 0}, "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./c": "./src/create.ts", "./create": "./src/create.ts", "./replace": "./src/create.ts", "./r": "./src/create.ts", "./list": "./src/list.ts", "./t": "./src/list.ts", "./update": "./src/update.ts", "./u": "./src/update.ts", "./extract": "./src/extract.ts", "./x": "./src/extract.ts", "./pack": "./src/pack.ts", "./unpack": "./src/unpack.ts", "./parse": "./src/parse.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./header": "./src/header.ts", "./pax": "./src/pax.ts", "./types": "./src/types.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}}, "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "_id": "tar@7.0.0", "gitHead": "b0fbdea4631f9f65f15786fb5333695a9164a536", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-spRiR+tDOVD01YeeWBUbNa6HoQErjztT2BXxZWmxJDgaCVgZMO1RAoeKpybiUbr8FxKsUm/svtiEyIRZeWYhAw==", "shasum": "3ee87a8552666422773acecc8a99ab6ab92539a4", "tarball": "https://registry.npmmirror.com/tar/-/tar-7.0.0.tgz", "fileCount": 229, "unpackedSize": 1191469, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfKQHQwKOt6UgVXzRChr8izyWFR5a/rO1PXwmk9kxrXQIga9Bzhpom5my2rv7N+S0cLNyiiJuq3XhVUj7WuRb9YAA="}], "size": 190859}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_7.0.0_1712779643755_0.7922138040170696"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-10T20:07:24.043Z", "publish_time": 1712779644043, "_source_registry_name": "default"}, "7.0.1": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "7.0.1", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap", "pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "tshy", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^5.0.0", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.5.4", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "license": "ISC", "engines": {"node": ">=18"}, "tap": {"coverage-map": "map.js", "timeout": 0}, "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./c": "./src/create.ts", "./create": "./src/create.ts", "./replace": "./src/create.ts", "./r": "./src/create.ts", "./list": "./src/list.ts", "./t": "./src/list.ts", "./update": "./src/update.ts", "./u": "./src/update.ts", "./extract": "./src/extract.ts", "./x": "./src/extract.ts", "./pack": "./src/pack.ts", "./unpack": "./src/unpack.ts", "./parse": "./src/parse.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./header": "./src/header.ts", "./pax": "./src/pax.ts", "./types": "./src/types.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}}, "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "_id": "tar@7.0.1", "gitHead": "d99fce38ebf5175cce4c6623c53f4b17d6d31157", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-IjMhdQMZFpKsHEQT3woZVxBtCQY+0wk3CVxdRkGXEgyGa0dNS/ehPvOMr2nmfC7x5Zj2N+l6yZUpmICjLGS35w==", "shasum": "8f6ccebcd91b69e9767a6fc4892799e8b0e606d5", "tarball": "https://registry.npmmirror.com/tar/-/tar-7.0.1.tgz", "fileCount": 229, "unpackedSize": 1192165, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIARdoweD8Qlt+yskRd9B9NnWnOqYImwEv1zDc7G6WtOAAiBn8hoLaCxMzAhR8lBkYgSI36BqgpQW5xwLzIZ/DM4muw=="}], "size": 190960}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_7.0.1_1713131124986_0.48876574112094695"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-14T21:45:25.236Z", "publish_time": 1713131125236, "_source_registry_name": "default"}, "7.1.0": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "7.1.0", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap", "pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "tshy", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.0", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.5.4", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "license": "ISC", "engines": {"node": ">=18"}, "tap": {"coverage-map": "map.js", "timeout": 0, "typecheck": true}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./c": "./src/create.ts", "./create": "./src/create.ts", "./replace": "./src/create.ts", "./r": "./src/create.ts", "./list": "./src/list.ts", "./t": "./src/list.ts", "./update": "./src/update.ts", "./u": "./src/update.ts", "./extract": "./src/extract.ts", "./x": "./src/extract.ts", "./pack": "./src/pack.ts", "./unpack": "./src/unpack.ts", "./parse": "./src/parse.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./header": "./src/header.ts", "./pax": "./src/pax.ts", "./types": "./src/types.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}}, "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "_id": "tar@7.1.0", "gitHead": "ce612d0aa818f93a133a5f76e14fe6eef2abb12f", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-ENhg4W6BmjYxl8GTaE7/h99f0aXiSWv4kikRZ9n2/JRxypZniE84ILZqimAhxxX7Zb8Px6pFdheW3EeHfhnXQQ==", "shasum": "c6d4ec5b10ccdffe8bc412b206eaeaf5181f3098", "tarball": "https://registry.npmmirror.com/tar/-/tar-7.1.0.tgz", "fileCount": 229, "unpackedSize": 1213036, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIASesW9W2ouMl20pvVPdBWefdVjBs9mHRQ5mfm3Lo2ZXAiA21X5XEnQCvrbHp7S7hsbUuOE8+F3RvxZhkm01QAmRJg=="}], "size": 194337}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_7.1.0_1714788420470_0.24385213168751663"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-04T02:07:00.708Z", "publish_time": 1714788420708, "_source_registry_name": "default"}, "7.2.0": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "7.2.0", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap", "pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "tshy", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.0", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.5.4", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "license": "ISC", "engines": {"node": ">=18"}, "tap": {"coverage-map": "map.js", "timeout": 0, "typecheck": true}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./c": "./src/create.ts", "./create": "./src/create.ts", "./replace": "./src/create.ts", "./r": "./src/create.ts", "./list": "./src/list.ts", "./t": "./src/list.ts", "./update": "./src/update.ts", "./u": "./src/update.ts", "./extract": "./src/extract.ts", "./x": "./src/extract.ts", "./pack": "./src/pack.ts", "./unpack": "./src/unpack.ts", "./parse": "./src/parse.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./header": "./src/header.ts", "./pax": "./src/pax.ts", "./types": "./src/types.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}}, "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "_id": "tar@7.2.0", "gitHead": "ed17f588f202c598bf74d9cdcda0998e706e86a1", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-hctwP0Nb4AB60bj8WQgRYaMOuJYRAPMGiQUAotms5igN8ppfQM+IvjQ5HcKu1MaZh2Wy2KWVTe563Yj8dfc14w==", "shasum": "f03ae6ecd2e2bab880f2ef33450f502e761d7548", "tarball": "https://registry.npmmirror.com/tar/-/tar-7.2.0.tgz", "fileCount": 237, "unpackedSize": 1228514, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBqNWyIOz+B2I7bOm+cyCp9jshGC/JFYPf5F7EiS0EycAiEA9SdyEF1Y95/Lpc+dSTsVHHQd0FF6o14h1WPpnuR3IfY="}], "size": 195865}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_7.2.0_1717032544676_0.31685659408416456"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-30T01:29:04.900Z", "publish_time": 1717032544900, "_source_registry_name": "default"}, "7.3.0": {"name": "tar", "version": "7.3.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@7.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"timeout": 0, "typecheck": true, "coverage-map": "map.js"}, "dist": {"shasum": "c6f1944f990d2f5821b26eb0ddaba7fd5ea25425", "tarball": "https://registry.npmmirror.com/tar/-/tar-7.3.0.tgz", "fileCount": 237, "integrity": "sha512-ihockMgWX0hnTa1eJf/usF91BXd2R5CwOUiWgAgdcd82Pv12ByK8S9obWnS5NTUrdadg3EpPUlowNqT+4GTE6A==", "signatures": [{"sig": "MEUCIQCJd/KmP2iswaHDvUFpxL9aWbZszsTmUrSfKpU4SPeLAAIgFOy1B/ambxrpCOqvoQbOr83OBRltjfVqGLUwFyKb9g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1233620, "size": 196469}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./c": "./src/create.ts", "./r": "./src/create.ts", "./t": "./src/list.ts", "./u": "./src/update.ts", "./x": "./src/extract.ts", "./pax": "./src/pax.ts", "./list": "./src/list.ts", "./pack": "./src/pack.ts", "./parse": "./src/parse.ts", "./types": "./src/types.ts", "./create": "./src/create.ts", "./header": "./src/header.ts", "./unpack": "./src/unpack.ts", "./update": "./src/update.ts", "./extract": "./src/extract.ts", "./replace": "./src/create.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/commonjs/pax.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/commonjs/pack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/commonjs/parse.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "source": "./src/types.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "source": "./src/types.ts", "default": "./dist/commonjs/types.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "source": "./src/header.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "source": "./src/header.ts", "default": "./dist/commonjs/header.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/commonjs/unpack.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/commonjs/write-entry.js"}}, "./package.json": "./package.json"}, "gitHead": "556a13c7c6733cab8baf29f8d382a3bef3f6fb07", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "tar for node", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"chownr": "^3.0.0", "mkdirp": "^3.0.1", "yallist": "^5.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "@isaacs/fs-minipass": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "nock": "^13.5.4", "tshy": "^1.13.1", "chmodr": "^1.2.0", "rimraf": "^5.0.5", "typedoc": "^0.25.13", "prettier": "^3.2.5", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tar_7.3.0_1718764011606_0.3710217112313847", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-06-19T02:26:51.845Z", "publish_time": 1718764011845, "_source_registry_name": "default"}, "7.4.0": {"author": {"name": "GitHub Inc."}, "name": "tar", "description": "tar for node", "version": "7.4.0", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap", "pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "tshy", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.5.4", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "license": "ISC", "engines": {"node": ">=18"}, "tap": {"coverage-map": "map.js", "timeout": 0, "typecheck": true}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./c": "./src/create.ts", "./create": "./src/create.ts", "./replace": "./src/create.ts", "./r": "./src/create.ts", "./list": "./src/list.ts", "./t": "./src/list.ts", "./update": "./src/update.ts", "./u": "./src/update.ts", "./extract": "./src/extract.ts", "./x": "./src/extract.ts", "./pack": "./src/pack.ts", "./unpack": "./src/unpack.ts", "./parse": "./src/parse.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./header": "./src/header.ts", "./pax": "./src/pax.ts", "./types": "./src/types.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"source": "./src/index.ts", "types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./create": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./replace": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./list": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./t": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./update": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./u": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./x": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pack": {"import": {"source": "./src/pack.ts", "types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"source": "./src/pack.ts", "types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./unpack": {"import": {"source": "./src/unpack.ts", "types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"source": "./src/unpack.ts", "types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./parse": {"import": {"source": "./src/parse.ts", "types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"source": "./src/parse.ts", "types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./read-entry": {"import": {"source": "./src/read-entry.ts", "types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"source": "./src/read-entry.ts", "types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"source": "./src/write-entry.ts", "types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"source": "./src/write-entry.ts", "types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./header": {"import": {"source": "./src/header.ts", "types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"source": "./src/header.ts", "types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./pax": {"import": {"source": "./src/pax.ts", "types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"source": "./src/pax.ts", "types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./types": {"import": {"source": "./src/types.ts", "types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"source": "./src/types.ts", "types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}}, "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "_id": "tar@7.4.0", "gitHead": "02b26879b880df80271cfb1ac98b7153becf9fb0", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-XQs0S8fuAkQWuqhDeCdMlJXDX80D7EOVLDPVFkna9yQfzS+PHKgfxcei0jf6/+QAWcjqrnC8uM3fSAnrQl+XYg==", "shasum": "e513afef5ba20ce250fd99397b4599db07d06443", "tarball": "https://registry.npmmirror.com/tar/-/tar-7.4.0.tgz", "fileCount": 237, "unpackedSize": 1234297, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFg/URgoseRlqvYMJzLZ4Ecr9ND9t1gmeaBlYAmAT0iLAiBRHebnlOXn1J/rQOXSQ8SiLmoEp25liihStrca0HPuEg=="}], "size": 196423}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_7.4.0_1718764622117_0.6934545195773854"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-19T02:37:02.405Z", "publish_time": 1718764622405, "_source_registry_name": "default"}, "7.4.1": {"author": {"name": "<PERSON>"}, "name": "tar", "description": "tar for node", "version": "7.4.1", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap", "pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "tshy", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.5.4", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "license": "ISC", "engines": {"node": ">=18"}, "tap": {"coverage-map": "map.js", "timeout": 0, "typecheck": true}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./c": "./src/create.ts", "./create": "./src/create.ts", "./replace": "./src/create.ts", "./r": "./src/create.ts", "./list": "./src/list.ts", "./t": "./src/list.ts", "./update": "./src/update.ts", "./u": "./src/update.ts", "./extract": "./src/extract.ts", "./x": "./src/extract.ts", "./pack": "./src/pack.ts", "./unpack": "./src/unpack.ts", "./parse": "./src/parse.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./header": "./src/header.ts", "./pax": "./src/pax.ts", "./types": "./src/types.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"source": "./src/index.ts", "types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./create": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./replace": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./list": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./t": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./update": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./u": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./x": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pack": {"import": {"source": "./src/pack.ts", "types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"source": "./src/pack.ts", "types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./unpack": {"import": {"source": "./src/unpack.ts", "types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"source": "./src/unpack.ts", "types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./parse": {"import": {"source": "./src/parse.ts", "types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"source": "./src/parse.ts", "types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./read-entry": {"import": {"source": "./src/read-entry.ts", "types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"source": "./src/read-entry.ts", "types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"source": "./src/write-entry.ts", "types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"source": "./src/write-entry.ts", "types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./header": {"import": {"source": "./src/header.ts", "types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"source": "./src/header.ts", "types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./pax": {"import": {"source": "./src/pax.ts", "types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"source": "./src/pax.ts", "types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./types": {"import": {"source": "./src/types.ts", "types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"source": "./src/types.ts", "types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}}, "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "_id": "tar@7.4.1", "gitHead": "f1d7a4d39bba883cbe31687a1497a728876479e7", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-dDJzpQf7Nud96mCs3wtw+XUiWGpi9WHxytSusrg0lYlj/Kr11DnB5hfw5bNDQNzx52JJ2Vy+7l8AFivp6H7ETA==", "shasum": "e74de34641cc0cb6a1d6a200085a3fa55de64843", "tarball": "https://registry.npmmirror.com/tar/-/tar-7.4.1.tgz", "fileCount": 237, "unpackedSize": 1234420, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPUQf0j5JmuTnL9yEhg7H4enmdqEuwhT3FBKgUBdN6ugIhAPEPZt8O1ZZKpMxXgvviAT2vLN+5Kb70hb6ibLTH4Ioq"}], "size": 196435}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_7.4.1_1721664159522_0.5712520713329583"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-22T16:02:39.813Z", "publish_time": 1721664159813, "_source_registry_name": "default"}, "7.4.2": {"author": {"name": "<PERSON>"}, "name": "tar", "description": "tar for node", "version": "7.4.2", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap", "pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "tshy", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.5.4", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "license": "ISC", "engines": {"node": ">=18"}, "tap": {"coverage-map": "map.js", "timeout": 0, "typecheck": true}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./c": "./src/create.ts", "./create": "./src/create.ts", "./replace": "./src/create.ts", "./r": "./src/create.ts", "./list": "./src/list.ts", "./t": "./src/list.ts", "./update": "./src/update.ts", "./u": "./src/update.ts", "./extract": "./src/extract.ts", "./x": "./src/extract.ts", "./pack": "./src/pack.ts", "./unpack": "./src/unpack.ts", "./parse": "./src/parse.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./header": "./src/header.ts", "./pax": "./src/pax.ts", "./types": "./src/types.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"source": "./src/index.ts", "types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./create": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./replace": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./list": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./t": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./update": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./u": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./x": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pack": {"import": {"source": "./src/pack.ts", "types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"source": "./src/pack.ts", "types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./unpack": {"import": {"source": "./src/unpack.ts", "types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"source": "./src/unpack.ts", "types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./parse": {"import": {"source": "./src/parse.ts", "types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"source": "./src/parse.ts", "types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./read-entry": {"import": {"source": "./src/read-entry.ts", "types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"source": "./src/read-entry.ts", "types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"source": "./src/write-entry.ts", "types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"source": "./src/write-entry.ts", "types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./header": {"import": {"source": "./src/header.ts", "types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"source": "./src/header.ts", "types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./pax": {"import": {"source": "./src/pax.ts", "types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"source": "./src/pax.ts", "types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./types": {"import": {"source": "./src/types.ts", "types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"source": "./src/types.ts", "types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}}, "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "_id": "tar@7.4.2", "gitHead": "68a685b30eadddad71cec56bc136bd276fa7e7f6", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-pP4ToLATHpXOrbxOZudW6rnfyPWJZoyERNrpTpQEtW6LkvpXw+WDnpj2GBQX9tyQ6teUyPESyyiOCFgp6HnGdw==", "shasum": "244859617851fc6dd5413c67b195585d25a7a2f4", "tarball": "https://registry.npmmirror.com/tar/-/tar-7.4.2.tgz", "fileCount": 237, "unpackedSize": 1237853, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCl4TEQVcMGdK3nHkLjudipEVHj4A/Jdtm1VDBhFeq0ogIgLwEsXuOcKpNnujI8iZNkBpi2/o9q5gt2qEc5NnsfzBk="}], "size": 196888}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_7.4.2_1721865482015_0.8905065251417372"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-24T23:58:02.238Z", "publish_time": 1721865482238, "_source_registry_name": "default"}, "7.4.3": {"author": {"name": "<PERSON>"}, "name": "tar", "description": "tar for node", "version": "7.4.3", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap", "pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "tshy", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.5.4", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "license": "ISC", "engines": {"node": ">=18"}, "tap": {"coverage-map": "map.js", "timeout": 0, "typecheck": true}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./c": "./src/create.ts", "./create": "./src/create.ts", "./replace": "./src/create.ts", "./r": "./src/create.ts", "./list": "./src/list.ts", "./t": "./src/list.ts", "./update": "./src/update.ts", "./u": "./src/update.ts", "./extract": "./src/extract.ts", "./x": "./src/extract.ts", "./pack": "./src/pack.ts", "./unpack": "./src/unpack.ts", "./parse": "./src/parse.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./header": "./src/header.ts", "./pax": "./src/pax.ts", "./types": "./src/types.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"source": "./src/index.ts", "types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./create": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./replace": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./list": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./t": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./update": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./u": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./x": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pack": {"import": {"source": "./src/pack.ts", "types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"source": "./src/pack.ts", "types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./unpack": {"import": {"source": "./src/unpack.ts", "types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"source": "./src/unpack.ts", "types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./parse": {"import": {"source": "./src/parse.ts", "types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"source": "./src/parse.ts", "types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./read-entry": {"import": {"source": "./src/read-entry.ts", "types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"source": "./src/read-entry.ts", "types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"source": "./src/write-entry.ts", "types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"source": "./src/write-entry.ts", "types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./header": {"import": {"source": "./src/header.ts", "types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"source": "./src/header.ts", "types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./pax": {"import": {"source": "./src/pax.ts", "types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"source": "./src/pax.ts", "types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./types": {"import": {"source": "./src/types.ts", "types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"source": "./src/types.ts", "types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}}, "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "_id": "tar@7.4.3", "gitHead": "206fcf91b01fae95ae859b8f3254bfd88744602a", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==", "shasum": "88bbe9286a3fcd900e94592cda7a22b192e80571", "tarball": "https://registry.npmmirror.com/tar/-/tar-7.4.3.tgz", "fileCount": 237, "unpackedSize": 1239499, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDasJ6vLVVHfYJ+aYAkE/DlmPOe1m64zpGNEJCOm87+MAIgb0C7vdQ0KmAMLD7+EOL6LQKsYaNghtIxC6wTE+nr7iQ="}], "size": 197115}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_7.4.3_1721968425683_0.9899170882636155"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-26T04:33:46.030Z", "publish_time": 1721968426030, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "_source_registry_name": "default"}