{"_id": "lru.min", "_rev": "4867460-66c86d1e363dc7e414c845b3", "dist-tags": {"latest": "1.1.2"}, "name": "lru.min", "time": {"created": "2024-08-23T11:06:06.069Z", "modified": "2025-03-09T18:30:14.242Z", "0.1.0": "2024-08-23T09:20:15.947Z", "0.2.0": "2024-08-26T09:43:12.504Z", "0.2.1": "2024-08-26T10:15:10.025Z", "0.2.2": "2024-08-26T11:04:41.403Z", "0.2.3": "2024-08-26T11:08:58.377Z", "0.3.0": "2024-08-27T02:58:33.769Z", "0.3.1": "2024-08-27T03:26:01.998Z", "0.3.2": "2024-08-27T09:04:17.991Z", "1.0.0": "2024-08-27T10:00:22.189Z", "1.1.0": "2024-08-28T22:06:16.309Z", "1.1.1": "2024-09-20T02:18:28.006Z", "1.1.2": "2025-03-09T18:29:37.481Z"}, "versions": {"0.1.0": {"name": "lru.min", "version": "0.1.0", "description": "🔥 Extremely fast LRU cache for JavaScript (Browser compatible) — 6.1KB.", "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/wellwelwel/lru.min.git"}, "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "author": {"name": "https://github.com/wellwelwel"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}, "engines": {"node": ">=8.0.0", "bun": ">=1.0.0", "deno": ">=1.30.0"}, "scripts": {"benchmark": "cd benchmark && npm ci && node lib/index.js", "prebuild": "rm -rf ./browser ./lib", "build:browser": "tsx tools/browserfy.ts", "build:cjs": "esbuild src/index.ts --outfile=lib/index.js --platform=node --target=node8 --format=cjs", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "build": "tsc && npm run build:cjs && npm run build:esm && npm run build:browser", "test:node": "poku --node -p", "test:bun": "poku --bun -p", "test:deno": "poku --deno -p", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "update": "pu minor && npm i && npm audit fix", "postupdate": "npm run lint:fix", "size": "ls -lh lib/index.mjs | awk '{print $5}'"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@types/node": "^22.5.0", "esbuild": "^0.23.1", "happy-dom": "^15.0.0", "packages-update": "^2.0.0", "poku": "^2.5.0", "prettier": "^3.3.3", "tsx": "^4.17.0", "typescript": "^5.5.4"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "keywords": ["lru", "mru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "_id": "lru.min@0.1.0", "homepage": "https://github.com/wellwelwel/lru.min#readme", "_nodeVersion": "22.6.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-mT46uWDX25swxbtduLmiyycUCto8u2qQLgZDDq2NR+N9pXzlgJ/ca2eKTc8hWKnAnlxNclgcAVNSc8TnrLA+2w==", "shasum": "9f5aec6544349a22bd24172d546eacf0d5ac19d5", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-0.1.0.tgz", "fileCount": 7, "unpackedSize": 23634, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFuOL+VDXxl37a1hXacnT+CIziOOboaJspuyrDzDNe1hAiEA76ovXxZUdtolxsxTaO/9hugIX0nqttBtSzaVluXUD0M="}], "size": 6043}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lru.min_0.1.0_1724404815757_0.8889774704816504"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-23T09:20:15.947Z", "publish_time": 1724404815947, "_source_registry_name": "default"}, "0.2.0": {"name": "lru.min", "version": "0.2.0", "keywords": ["lru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "lru.min@0.2.0", "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/wellwelwel/lru.min#readme", "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "dist": {"shasum": "cd8013bbe31511cbcf1460b77ebd47f839e9ac44", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-0.2.0.tgz", "fileCount": 7, "integrity": "sha512-ORjTfjYfi98IUX1M8kU7Qd41GXpgPbyXjpY2l0rzQy6t4go2XGPZJYEXiKTxz4vEtPV/gw2oUxb5MD5VjYlILw==", "signatures": [{"sig": "MEUCIGW6EQlBxCPz5vB4sMAIWGhyRnpvLIvD4Qp3yIHNBakxAiEA1CF0cpGjJnrfi/iniJlpnuOAHyhkTVjuO4eTgBipILY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23489, "size": 6316}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "module": "./lib/index.mjs", "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}, "gitHead": "47af9139b93340954023f9d04afa0892612392cf", "scripts": {"lint": "npx @biomejs/biome lint && prettier --check .", "size": "ls -lh lib/index.mjs | awk '{print $5}'", "build": "tsc && npm run build:esm && npm run build:browser", "update": "pu minor && npm i && npm audit fix", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "prebuild": "rm -rf ./browser ./lib", "test:bun": "poku --bun -p", "benchmark": "cd benchmark && npm ci && node lib/index.js", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "test:deno": "poku --deno -p", "test:node": "poku --node -p", "postupdate": "npm run lint:fix", "build:browser": "tsx tools/browserfy.ts"}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/wellwelwel/lru.min.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript (Browser compatible) — 4.8KB.", "directories": {}, "_nodeVersion": "22.7.0", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru.min_0.2.0_1724665392379_0.4775986330883364", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-08-26T09:43:12.504Z", "publish_time": 1724665392504, "_source_registry_name": "default"}, "0.2.1": {"name": "lru.min", "version": "0.2.1", "keywords": ["lru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "lru.min@0.2.1", "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/wellwelwel/lru.min#readme", "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "dist": {"shasum": "f90ce37b4a3f1056e6f2973970e846cfd9d8a69e", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-0.2.1.tgz", "fileCount": 7, "integrity": "sha512-ayEIl3/1xz42PEpLTDy98DEmdwvAETUCPUdTC4gasTP65z4Sy81OE7PlNxHinto1ebmLnpm1BxyZhcW3a9/5RA==", "signatures": [{"sig": "MEYCIQDkUIMJFCp3HusfcVtWUiFoZvYubHkOIJLqR0GcRXUTXAIhANjktJYF7+RVjw6u6NYQsdY1iju85lB5E+Egdp6i/znF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23588, "size": 6460}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "module": "./lib/index.mjs", "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}, "gitHead": "db8c90abd4328e170c7ab4819a3bdb17871d0ea8", "scripts": {"lint": "npx @biomejs/biome lint && prettier --check .", "size": "ls -lh lib/index.mjs | awk '{print $5}'", "build": "tsc && npm run build:esm && npm run build:browser", "update": "pu minor && npm i && npm audit fix", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "prebuild": "rm -rf ./browser ./lib", "test:bun": "poku --bun -p", "benchmark": "cd benchmark && npm ci && node lib/index.js", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "test:deno": "poku --deno -p", "test:node": "poku --node -p", "postupdate": "npm run lint:fix", "build:browser": "tsx tools/browserfy.ts"}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/wellwelwel/lru.min.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript (Browser compatible) — 4.8KB.", "directories": {}, "_nodeVersion": "22.7.0", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru.min_0.2.1_1724667309895_0.24260719353311044", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-08-26T10:15:10.025Z", "publish_time": 1724667310025, "_source_registry_name": "default"}, "0.2.2": {"name": "lru.min", "version": "0.2.2", "keywords": ["lru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "lru.min@0.2.2", "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/wellwelwel/lru.min#readme", "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "dist": {"shasum": "44c552dc9431b3a19ca8861098ec977259344f59", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-0.2.2.tgz", "fileCount": 7, "integrity": "sha512-ici1GOKmgoquR+8Von86IW9wSZvIlypb1Ry5zNFo4zp4YSJqXLjU4e/sLlrVDXkh82bvAPod272PRGvDlw9a7Q==", "signatures": [{"sig": "MEUCIB0EgNtlK9D6s7O5ihA56vgaKHgYmv9d8Yk4EJDnMvRZAiEAthSzCuxLnxUsxYcmKbOjdms5gtEXVj9caWrTQwhVQG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23588, "size": 6460}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "module": "./lib/index.mjs", "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}, "gitHead": "de1c90ccd2073d26f9eb22f928fbafcb540a1c21", "scripts": {"lint": "npx @biomejs/biome lint && prettier --check .", "size": "ls -lh lib/index.mjs | awk '{print $5}'", "build": "tsc && npm run build:esm && npm run build:browser", "update": "pu minor && npm i && npm audit fix", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "prebuild": "rm -rf ./browser ./lib", "test:bun": "poku --bun -p", "benchmark": "cd benchmark && npm ci && node lib/index.js", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "test:deno": "poku --deno -p", "test:node": "poku --node -p", "postupdate": "npm run lint:fix", "build:browser": "tsx tools/browserfy.ts"}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/wellwelwel/lru.min.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript (Browser compatible) — 4.8KB.", "directories": {}, "_nodeVersion": "22.7.0", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru.min_0.2.2_1724670281270_0.5900908892033654", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-08-26T11:04:41.403Z", "publish_time": 1724670281403, "_source_registry_name": "default"}, "0.2.3": {"name": "lru.min", "version": "0.2.3", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript (Browser compatible) — 4.8KB.", "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/wellwelwel/lru.min.git"}, "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "author": {"name": "https://github.com/wellwelwel"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}, "engines": {"node": ">=8.0.0", "bun": ">=1.0.0", "deno": ">=1.30.0"}, "scripts": {"benchmark": "cd benchmark && npm ci && node lib/index.js", "prebuild": "rm -rf ./browser ./lib", "build:browser": "tsx tools/browserfy.ts", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "build": "tsc && npm run build:esm && npm run build:browser", "test:node": "poku --node -p", "test:bun": "poku --bun -p", "test:deno": "poku --deno -p", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "update": "pu minor && npm i && npm audit fix", "postupdate": "npm run lint:fix", "size": "ls -lh lib/index.mjs | awk '{print $5}'"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@types/node": "^22.5.0", "esbuild": "^0.23.1", "happy-dom": "^15.0.0", "packages-update": "^2.0.0", "poku": "^2.5.0", "prettier": "^3.3.3", "tsx": "^4.17.0", "typescript": "^5.5.4"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "keywords": ["lru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "_id": "lru.min@0.2.3", "gitHead": "de1c90ccd2073d26f9eb22f928fbafcb540a1c21", "homepage": "https://github.com/wellwelwel/lru.min#readme", "_nodeVersion": "22.7.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-DPmeNBgjmeymlEnxeFnG+AZFYSyP2B/Akf409HWjanYgiuIDVCgoXuuo4O2v0g9PtfWdDO+G5sxNqVUzNL0rwg==", "shasum": "af14809357908ae189d662c2556f2685aa6271d1", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-0.2.3.tgz", "fileCount": 7, "unpackedSize": 23588, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDN9yFD9sceNzjWaI44NxHAl1JxHpM5trmMyZY4qilpIAiAqvMBRPxQjOVk71YvUo+dngPlE1k6QXWez1qABi6QzTw=="}], "size": 6467}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lru.min_0.2.3_1724670538187_0.8196503024583186"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-26T11:08:58.377Z", "publish_time": 1724670538377, "_source_registry_name": "default"}, "0.3.0": {"name": "lru.min", "version": "0.3.0", "keywords": ["lru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "lru.min@0.3.0", "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/wellwelwel/lru.min#readme", "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "dist": {"shasum": "9244faf67b8b963de2e1e7f71c7d8ab35c3112a5", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-0.3.0.tgz", "fileCount": 7, "integrity": "sha512-PCIo5aSfpLy/dkLK0NHMf9brfgJ1FLa9IJ4iTbORaF6fFGzhnztc1Y34WSfzrsw/kkDevqAViGOVsBqpIAdyBw==", "signatures": [{"sig": "MEUCIQDG5KYatqxM8j3bINKKKfDAtspsRnW5EqxfCK1e+yDnGQIgN/3bWi5vCCXpFHyJr09f8NGw5jpx5CQxhcMMg80MDjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28575, "size": 7390}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "module": "./lib/index.mjs", "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}, "gitHead": "bb1651feb3fe0cb8eaaa4f984fda12491ccb9397", "scripts": {"lint": "npx @biomejs/biome lint && prettier --check .", "size": "ls -lh lib/index.mjs | awk '{print $5}'", "build": "tsc && npm run build:esm && npm run build:browser", "update": "pu minor && npm i && npm audit fix", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "prebuild": "rm -rf ./browser ./lib", "test:bun": "poku --bun -p", "benchmark": "cd benchmark && npm ci && node lib/index.js", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "test:deno": "poku --deno -p", "test:node": "poku --node -p", "postupdate": "npm run lint:fix", "build:browser": "tsx tools/browserfy.ts", "test:coverage": "mcr --import tsx --config mcr.config.ts npm run test:node"}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/wellwelwel/lru.min.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript (Browser compatible) — 6.5KB.", "directories": {}, "_nodeVersion": "22.7.0", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0", "monocart-coverage-reports": "^2.10.3"}, "_npmOperationalInternal": {"tmp": "tmp/lru.min_0.3.0_1724727513605_0.9840348775441239", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-08-27T02:58:33.769Z", "publish_time": 1724727513769, "_source_registry_name": "default"}, "0.3.1": {"name": "lru.min", "version": "0.3.1", "keywords": ["lru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "lru.min@0.3.1", "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/wellwelwel/lru.min#readme", "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "dist": {"shasum": "57fc34e717d969a4f2240c3a91def8c50830c8b8", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-0.3.1.tgz", "fileCount": 7, "integrity": "sha512-Q0f9iCCN2qjRVDTxZ80joyr5Tv9WUAWY8nLKXFC1VnNcBxY4GJMiEutqVt0K1nuxBuVNeXhcm3Zi/ZuCP29zug==", "signatures": [{"sig": "MEQCIHRNMWTlXgDKbtSD4M4rgPCuoGDQRLAVwXyTmt7XwVC5AiBGW7zt2uMmrE6oCiwedQnKWuWaYh8wFFlOmNXc5KR0FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27866, "size": 7192}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "module": "./lib/index.mjs", "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}, "gitHead": "1eea82dc9c1fb9f22c84b4305a1a9172335b840f", "scripts": {"lint": "npx @biomejs/biome lint && prettier --check .", "size": "ls -lh lib/index.mjs | awk '{print $5}'", "build": "tsc && npm run build:esm && npm run build:browser", "update": "pu minor && npm i && npm audit fix", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "prebuild": "rm -rf ./browser ./lib", "test:bun": "poku --bun -p", "benchmark": "cd benchmark && npm ci && node lib/index.js", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "test:deno": "poku --deno -p", "test:node": "poku --node -p", "postupdate": "npm run lint:fix", "build:browser": "tsx tools/browserfy.ts", "test:coverage": "mcr --import tsx --config mcr.config.ts npm run test:node"}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/wellwelwel/lru.min.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript (Browser compatible) — 6.4KB.", "directories": {}, "_nodeVersion": "22.7.0", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.17.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0", "monocart-coverage-reports": "^2.10.3"}, "_npmOperationalInternal": {"tmp": "tmp/lru.min_0.3.1_1724729161869_0.36294886558075334", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-08-27T03:26:01.998Z", "publish_time": 1724729161998, "_source_registry_name": "default"}, "0.3.2": {"name": "lru.min", "version": "0.3.2", "keywords": ["lru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "author": {"name": "https://github.com/wellwelwel"}, "license": "MIT", "_id": "lru.min@0.3.2", "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "homepage": "https://github.com/wellwelwel/lru.min#readme", "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "dist": {"shasum": "ffcf3ee961d4cacb4505191ed9745eb93106eded", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-0.3.2.tgz", "fileCount": 7, "integrity": "sha512-u2fhvG85ThKP3yxnjoWY79FjJbSwepsDuCL5D9CgGEeHCZhZ2UY32wjYDaaXOEplC6Qow9C96ZxW/Mk7TFN8QQ==", "signatures": [{"sig": "MEUCIQC/o5lFMtm6GGSyNBeBHvulU+CXVu6FqtwVxwx1v/0qQgIgOZToIhNKDS4bBSmHWbx0CvoGnURO96SLYvM94jpzD6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30054, "size": 7624}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "module": "./lib/index.mjs", "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "funding": {"url": "https://github.com/sponsors/wellwelwel", "type": "github"}, "gitHead": "d7163776ba95476966a6bdef65f573a49a3cd6f0", "scripts": {"lint": "npx @biomejs/biome lint && prettier --check .", "size": "ls -lh lib/index.mjs | awk '{print $5}'", "build": "tsc && npm run build:esm && npm run build:browser", "update": "pu minor && npm i && npm audit fix", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "prebuild": "rm -rf ./browser ./lib", "test:bun": "poku --bun -p", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "test:deno": "poku --deno -p", "test:node": "poku --node -p", "postupdate": "npm run lint:fix", "benchmark:cjs": "cd benchmark && npm ci && node index.cjs", "benchmark:esm": "cd benchmark && npm ci && node index.mjs", "build:browser": "tsx tools/browserfy.ts", "test:coverage": "mcr --import tsx --config mcr.config.ts npm run test:node"}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/wellwelwel/lru.min.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript (Browser compatible) — 6.7KB.", "directories": {}, "_nodeVersion": "22.7.0", "_hasShrinkwrap": false, "devDependencies": {"tsx": "^4.19.0", "poku": "^2.5.0", "esbuild": "^0.23.1", "prettier": "^3.3.3", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "@types/node": "^22.5.0", "@biomejs/biome": "^1.8.3", "packages-update": "^2.0.0", "monocart-coverage-reports": "^2.10.3"}, "_npmOperationalInternal": {"tmp": "tmp/lru.min_0.3.2_1724749457856_0.3870008262672968", "host": "s3://npm-registry-packages"}, "_cnpmcore_publish_time": "2024-08-27T09:04:17.991Z", "publish_time": 1724749457991, "_source_registry_name": "default"}, "1.0.0": {"name": "lru.min", "version": "1.0.0", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript (Browser compatible) — 6.7KB.", "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/wellwelwel/lru.min.git"}, "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "author": {"name": "https://github.com/wellwelwel"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}, "engines": {"node": ">=8.0.0", "bun": ">=1.0.0", "deno": ">=1.30.0"}, "scripts": {"benchmark:esm": "cd benchmark && npm ci && node index.mjs", "benchmark:cjs": "cd benchmark && npm ci && node index.cjs", "prebuild": "rm -rf ./browser ./lib", "build:browser": "tsx tools/browserfy.ts", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "build": "tsc && npm run build:esm && npm run build:browser", "test:node": "poku --node -p", "test:bun": "poku --bun -p", "test:deno": "poku --deno -p", "test:coverage": "mcr --import tsx --config mcr.config.ts npm run test:node", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "update": "pu minor && npm i && npm audit fix", "postupdate": "npm run lint:fix", "size": "ls -lh lib/index.mjs | awk '{print $5}'"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@types/node": "^22.5.0", "esbuild": "^0.23.1", "happy-dom": "^15.0.0", "monocart-coverage-reports": "^2.10.3", "packages-update": "^2.0.0", "poku": "^2.5.0", "prettier": "^3.3.3", "tsx": "^4.19.0", "typescript": "^5.5.4"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "keywords": ["lru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "_id": "lru.min@1.0.0", "gitHead": "ed985393f92ca63ee47711e9410ae3d16f357eb3", "homepage": "https://github.com/wellwelwel/lru.min#readme", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-YthLl3hdewA3lIwPrpgDLNlq6bvgbZjJQR4qr6oQ2c6lC78sCOwJkn0AkuUArbydQNQ+PjwIVz9IwZNrmLhXeg==", "shasum": "b54b7f2143620d4415822098aada950aad781968", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-1.0.0.tgz", "fileCount": 7, "unpackedSize": 30058, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/lru.min@1.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGO+tgOvU+Ny1jmicCSzYAWRj3rCSfihI2BHQhhtQYVfAiBXoHTsVo8pYPgVdCYr5RPLK6RkEmxSH8sYEEnZaKz0xA=="}], "size": 7692}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lru.min_1.0.0_1724752821992_0.44922822544112573"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-27T10:00:22.189Z", "publish_time": 1724752822189, "_source_registry_name": "default"}, "1.1.0": {"name": "lru.min", "version": "1.1.0", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript (Browser compatible) — 6.7KB.", "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/wellwelwel/lru.min.git"}, "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "author": {"name": "https://github.com/wellwelwel"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}, "engines": {"node": ">=8.0.0", "bun": ">=1.0.0", "deno": ">=1.30.0"}, "scripts": {"benchmark:esm": "cd benchmark && npm ci && node index.mjs", "benchmark:cjs": "cd benchmark && npm ci && node index.cjs", "prebuild": "rm -rf ./browser ./lib", "build:browser": "tsx tools/browserfy.ts", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "build": "tsc && npm run build:esm && npm run build:browser", "test:node": "poku --node -p", "test:bun": "poku --bun -p", "test:deno": "poku --deno -p", "test:coverage": "mcr --import tsx --config mcr.config.ts npm run test:node", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "update": "pu minor && npm i && npm audit fix", "postupdate": "npm run lint:fix", "size": "ls -lh lib/index.mjs | awk '{print $5}'"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@types/node": "^22.5.0", "esbuild": "^0.23.1", "monocart-coverage-reports": "^2.10.3", "packages-update": "^2.0.0", "poku": "^2.5.0", "prettier": "^3.3.3", "tsx": "^4.19.0", "typescript": "^5.5.4"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "keywords": ["lru", "cache", "caching", "ttl", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "_id": "lru.min@1.1.0", "gitHead": "82f5f0be4112dd0893d35a990c01944bc939c76b", "homepage": "https://github.com/wellwelwel/lru.min#readme", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-86xXMB6DiuKrTqkE/lRL0drlNh568awttBPJ7D66fzDHpy6NC5r3N+Ly/lKCS2zjmeGyvFDx670z0cD0PVBwGA==", "shasum": "fd222364c440a389d2c4e5f637cc0521a114bfca", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-1.1.0.tgz", "fileCount": 7, "unpackedSize": 32625, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/lru.min@1.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbV/xt+OAhAar3TqcoF4wf7czZ6eBVxwrAMb+mCxlgvAIhAKYzVdMOQQFPJc0c6jSos1H+ulkvzsz7sU/JzYUTyx5h"}], "size": 8525}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lru.min_1.1.0_1724882776022_0.11392289793324473"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-08-28T22:06:16.309Z", "publish_time": 1724882776309, "_source_registry_name": "default"}, "1.1.1": {"name": "lru.min", "version": "1.1.1", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript with high compatibility (including Browsers) — 6.8KB.", "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/wellwelwel/lru.min.git"}, "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "author": {"name": "https://github.com/wellwelwel"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}, "engines": {"node": ">=8.0.0", "bun": ">=1.0.0", "deno": ">=1.30.0"}, "scripts": {"benchmark:esm": "cd benchmark && npm ci && node index.mjs", "benchmark:cjs": "cd benchmark && npm ci && node index.cjs", "prebuild": "rm -rf ./browser ./lib", "build:browser": "tsx tools/browserfy.ts", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "build": "tsc && npm run build:esm && npm run build:browser", "test:node": "poku --node -p", "test:bun": "poku --bun -p", "test:deno": "poku --deno -p", "test:coverage": "mcr --import tsx --config mcr.config.ts npm run test:node", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "update": "pu minor && npm i && npm audit fix", "postupdate": "npm run lint:fix", "size": "ls -lh lib/index.mjs | awk '{print $5}'"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@biomejs/biome": "^1.9.2", "@types/babel__core": "^7.20.5", "@types/node": "^22.5.5", "esbuild": "^0.23.1", "monocart-coverage-reports": "^2.10.9", "packages-update": "^2.0.0", "poku": "^2.7.0", "prettier": "^3.3.3", "terser": "^5.33.0", "tsx": "^4.19.1", "typescript": "^5.6.2"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "keywords": ["lru", "cache", "caching", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "_id": "lru.min@1.1.1", "gitHead": "700ac33da2fe6510e24d4fe61146f3cb11f37d25", "homepage": "https://github.com/wellwelwel/lru.min#readme", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-FbAj6lXil6t8z4z3j0E5mfRlPzxkySotzUHwRXjlpRh10vc6AI6WN62ehZj82VG7M20rqogJ0GLwar2Xa05a8Q==", "shasum": "146e01e3a183fa7ba51049175de04667d5701f0e", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-1.1.1.tgz", "fileCount": 7, "unpackedSize": 33542, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/lru.min@1.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICSoqvOsIdCIWeZFaa0RYQb779ky8foFHNhMEV4ylhxrAiEAv87MCM/XvNj5yOezPnURaWYOTBtSIINNQFYoGf2DrDo="}], "size": 8843}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/lru.min_1.1.1_1726798707732_0.22099217773472923"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-20T02:18:28.006Z", "publish_time": 1726798708006, "_source_registry_name": "default"}, "1.1.2": {"name": "lru.min", "version": "1.1.2", "description": "🔥 An extremely fast and efficient LRU cache for JavaScript with high compatibility (including Browsers) — 6.8KB.", "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/wellwelwel/lru.min.git"}, "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "author": {"name": "https://github.com/wellwelwel"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}, "engines": {"node": ">=8.0.0", "bun": ">=1.0.0", "deno": ">=1.30.0"}, "scripts": {"benchmark:esm": "cd benchmark && npm ci && node index.mjs", "benchmark:cjs": "cd benchmark && npm ci && node index.cjs", "prebuild": "rm -rf ./browser ./lib", "build:browser": "tsx tools/browserfy.ts", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "build": "tsc && npm run build:esm && npm run build:browser", "test:node": "poku", "test:bun": "bun poku", "test:deno": "deno run -A npm:poku", "test:coverage": "mcr --import tsx --config mcr.config.ts npm run test:node", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "update": "pu minor && npm i && npm audit fix", "postupdate": "npm run lint:fix", "size": "ls -lh lib/index.mjs | awk '{print $5}'"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@biomejs/biome": "^1.9.4", "@types/babel__core": "^7.20.5", "@types/node": "^22.13.10", "esbuild": "^0.25.0", "monocart-coverage-reports": "2.12.1", "packages-update": "^2.0.0", "poku": "^3.0.1", "prettier": "^3.5.3", "terser": "^5.39.0", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "keywords": ["lru", "cache", "caching", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "_id": "lru.min@1.1.2", "gitHead": "9f7ba63653bdd886a59485bdc0f0850b494dadeb", "homepage": "https://github.com/wellwelwel/lru.min#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Nv9KddBcQSlQopmBHXSsZVY5xsdlZkdH/Iey0BlcBYggMd4two7cZnKOK9vmy3nY0O5RGH99z1PCeTpPqszUYg==", "shasum": "01ce1d72cc50c7faf8bd1f809ebf05d4331021eb", "tarball": "https://registry.npmmirror.com/lru.min/-/lru.min-1.1.2.tgz", "fileCount": 7, "unpackedSize": 34457, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/lru.min@1.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCePY931wlKRBW/pkfBTgndIE0ddaLH4WCn03eZPaELuwIhANl06TdTxfgGLGI0pjFWKFfZo0hWanoxbcVqYHaPIxTp"}], "size": 9127}, "_npmUser": {"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lru.min_1.1.2_1741544977295_0.9931831155946518"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-09T18:29:37.481Z", "publish_time": 1741544977481, "_source_registry_name": "default"}}, "author": {"name": "https://github.com/wellwelwel"}, "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "description": "🔥 An extremely fast and efficient LRU cache for JavaScript with high compatibility (including Browsers) — 6.8KB.", "homepage": "https://github.com/wellwelwel/lru.min#readme", "keywords": ["lru", "cache", "caching", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "license": "MIT", "maintainers": [{"name": "weslley.io", "email": "w.es<PERSON><EMAIL>"}], "readme": "<h1 align=\"center\">lru.min</h1>\n<div align=\"center\">\n\n[![NPM Version](https://img.shields.io/npm/v/lru.min.svg?label=&color=70a1ff&logo=npm&logoColor=white)](https://www.npmjs.com/package/lru.min)\n[![NPM Downloads](https://img.shields.io/npm/dm/lru.min.svg?label=&logo=npm&logoColor=white&color=45aaf2)](https://www.npmjs.com/package/lru.min)\n[![Coverage](https://img.shields.io/codecov/c/github/wellwelwel/lru.min?label=&logo=codecov&logoColor=white&color=98cc00)](https://app.codecov.io/gh/wellwelwel/lru.min)<br />\n[![GitHub Workflow Status (Node.js)](https://img.shields.io/github/actions/workflow/status/wellwelwel/lru.min/ci_node.yml?event=push&label=&branch=main&logo=nodedotjs&logoColor=535c68&color=badc58)](https://github.com/wellwelwel/lru.min/actions/workflows/ci_node.yml?query=branch%3Amain)\n[![GitHub Workflow Status (Bun)](https://img.shields.io/github/actions/workflow/status/wellwelwel/lru.min/ci_bun.yml?event=push&label=&branch=main&logo=bun&logoColor=ffffff&color=f368e0)](https://github.com/wellwelwel/lru.min/actions/workflows/ci_bun.yml?query=branch%3Amain)\n[![GitHub Workflow Status (Deno)](https://img.shields.io/github/actions/workflow/status/wellwelwel/lru.min/ci_deno.yml?event=push&label=&branch=main&logo=deno&logoColor=ffffff&color=079992)](https://github.com/wellwelwel/lru.min/actions/workflows/ci_deno.yml?query=branch%3Amain)\n\n🔥 An extremely fast, efficient, and lightweight <strong><a href=\"https://en.m.wikipedia.org/wiki/Cache_replacement_policies#Least_Recently_Used_.28LRU.29\">LRU</a> Cache</strong> for <strong>JavaScript</strong> (<strong>Browser</strong> compatible).\n\n</div>\n\n## Why another LRU?\n\n- 🎖️ **lru.min** is fully compatible with both **Node.js** _(8+)_, **Bun**, **Deno** and, browser environments. All of this, while maintaining the same high performance [_(and a little more)_](https://github.com/wellwelwel/lru.min?tab=readme-ov-file#performance) as the most popular **LRU** packages.\n\n---\n\n## Install\n\n```bash\n# Node.js\nnpm i lru.min\n```\n\n```bash\n# Bun\nbun add lru.min\n```\n\n```bash\n# Deno\ndeno add npm:lru.min\n```\n\n---\n\n## Usage\n\n### Quickstart\n\n```js\nimport { createLRU } from 'lru.min';\n\nconst max = 2;\nconst onEviction = (key, value) => {\n  console.log(`Key \"${key}\" with value \"${value}\" has been evicted.`);\n};\n\nconst LRU = createLRU({\n  max,\n  onEviction,\n});\n\nLRU.set('A', 'My Value');\nLRU.set('B', 'Other Value');\nLRU.set('C', 'Another Value');\n\n// => Key \"A\" with value \"My Value\" has been evicted.\n\nLRU.has('B');\nLRU.get('B');\nLRU.delete('B');\n\n// => Key \"B\" with value \"Other Value\" has been evicted.\n\nLRU.peek('C');\n\nLRU.clear(); // ← recommended | LRU.evict(max) → (slower alternative)\n\n// => Key \"C\" with value \"Another Value\" has been evicted.\n\nLRU.set('D', \"You're amazing 💛\");\n\nLRU.size; // 1\nLRU.max; // 2\nLRU.available; // 1\n\nLRU.resize(10);\n\nLRU.size; // 1\nLRU.max; // 10\nLRU.available; // 9\n```\n\n> For _up-to-date_ documentation, always follow the [**README.md**](https://github.com/wellwelwel/lru.min?tab=readme-ov-file#readme) in the **GitHub** repository.\n\n### Import\n\n#### ES Modules\n\n```js\nimport { createLRU } from 'lru.min';\n```\n\n#### CommonJS\n\n```js\nconst { createLRU } = require('lru.min');\n```\n\n#### Browser\n\n> Requires **ES6**.\n\n```html\n<script src=\"https://cdn.jsdelivr.net/npm/lru.min@1.x.x/browser/lru.min.js\"></script>\n```\n\n- You can use tools such as [**Babel**](https://github.com/babel/babel) to increase the compatibility rate.\n\n### Create a new LRU Cache\n\n> Set maximum size when creating **LRU**.\n\n```ts\nconst LRU = createLRU({ max: 150_000 });\n```\n\nAlso, you can set a callback for every deletion/eviction:\n\n```ts\nconst LRU = createLRU({\n  max: 150_000,\n  onEviction: (key, value) => {\n    // do something\n  },\n});\n```\n\n### Set a cache\n\nAdds a key-value pair to the cache. Updates the value if the key already exists\n\n```ts\nLRU.set('key', 'value');\n```\n\n> `undefined` keys will simply be ignored.\n\n- Complexity: **O(1)**.\n\n### Get a cache\n\nRetrieves the value for a given key and moves the key to the most recent position.\n\n```ts\nLRU.get('key');\n```\n\n- Complexity: **O(1)**.\n\n### Peek a cache\n\nRetrieves the value for a given key without changing its position.\n\n```ts\nLRU.peek('key');\n```\n\n- Complexity: **O(1)**.\n\n### Check if a key exists\n\n```ts\nLRU.has('key');\n```\n\n- Complexity: **O(1)**.\n\n### Delete a cache\n\n```ts\nLRU.delete('key');\n```\n\n- Complexity: **O(1)**.\n\n### Evict from the oldest cache\n\nEvicts the specified number of the oldest items from the cache.\n\n```ts\nLRU.evict(1000);\n```\n\n- Complexity: **O(key)** — even if passed a number greater than the number of items, only existing items will be evicted.\n\n> [!TIP]\n>\n> - Methods that perform eviction(s) when maximum size is reached: `set` and `resize`.\n> - Methods that always perform eviction(s): `delete`, `clear`, and `evict` itself.\n\n### Resize the cache\n\nResizes the cache to a new maximum size, evicting items if necessary.\n\n```ts\nLRU.resize(50_000);\n```\n\n- Complexity:\n  - Increasing: **O(newMax - max)**.\n  - Downsizing: **O(n)**.\n\n### Clear the cache\n\nClears and disposes (if used) all key-value pairs from the cache.\n\n```ts\nLRU.clear();\n```\n\n- Complexity:\n  - Without `onEviction`: **O(1)**.\n  - Using `onEviction`: **O(entries)**.\n\n### Debugging\n\n#### Get the max size of the cache\n\n```ts\nLRU.max;\n```\n\n- Complexity: **O(1)**.\n\n#### Get the current size of the cache\n\n```ts\nLRU.size;\n```\n\n- Complexity: **O(1)**.\n\n#### Get the available slots in the cache\n\n```ts\nLRU.available;\n```\n\n- Complexity: **O(1)**.\n\n### Iterating the cache\n\n#### Get all keys\n\nIterates over all keys in the cache, from most recent to least recent.\n\n```ts\nconst keys = [...LRU.keys()];\n```\n\n- Complexity: **O(keys)**.\n\n#### Get all values\n\nIterates over all values in the cache, from most recent to least recent.\n\n```ts\nconst values = [...LRU.values()];\n```\n\n- Complexity: **O(values)**.\n\n#### Get all entries\n\nIterates over `[key, value]` pairs in the cache, from most recent to least recent.\n\n```ts\nconst entries = [...LRU.entries()];\n```\n\n- Complexity: **O(entries)**.\n\n#### Run a callback for each entry\n\nIterates over each value-key pair in the cache, from most recent to least recent.\n\n```ts\nLRU.forEach((value, key) => {\n  // do something\n});\n```\n\n- Complexity: **O(entries)**.\n\n---\n\n> [!NOTE]\n>\n> - We use `O(keys)`, `O(values)`, `O(entries)`, and `O(newMax - max)` to explicitly indicate what is being iterated over. In traditional complexity notation, this would be represented as `O(n)`.\n\n---\n\n### TypeScript\n\nYou can set types for both keys and values. For example:\n\n```ts\nimport { createLRU } from 'lru.min';\n\ntype Key = number;\n\ntype Value = {\n  name: string;\n};\n\nconst LRU = createLRU<Key, Value>({ max: 1000 });\n\nLRU.set(1, { name: 'Peter' });\nLRU.set(2, { name: 'Mary' });\n```\n\nAlso:\n\n```ts\nimport { createLRU, type CacheOptions } from 'lru.min';\n\ntype Key = number;\n\ntype Value = {\n  name: string;\n};\n\nconst options: CacheOptions<Key, Value> = {\n  max: 10,\n  onEviction(key, value) {\n    console.log(key, value);\n  },\n};\n\n// No need to repeat the type params\nconst LRU = createLRU(options);\n\nLRU.set(1, { name: 'Peter' });\nLRU.set(2, { name: 'Mary' });\n```\n\n---\n\n### Performance\n\nThe benchmark is performed by comparing `1,000,000` runs through a maximum cache limit of `100,000`, getting `333,333` caches and deleting `200,000` keys 10 consecutive times, clearing the cache every run.\n\n> - [**lru-cache**](https://github.com/isaacs/node-lru-cache) `v11.0.0`\n> - [**quick-lru**](https://github.com/sindresorhus/quick-lru) `v7.0.0`\n\n```sh\n# Time:\n  lru.min:    240.45ms\n  lru-cache:  258.32ms\n  quick-lru:  279.89ms\n\n# CPU:\n  lru.min:    275558.30µs\n  lru-cache:  306858.30µs\n  quick-lru:  401318.80µs\n```\n\n- See detailed results and how the tests are run and compared in the [**benchmark**](https://github.com/wellwelwel/lru.min/tree/main/benchmark) directory.\n\n---\n\n## Security Policy\n\n[![GitHub Workflow Status (with event)](https://img.shields.io/github/actions/workflow/status/wellwelwel/lru.min/ci_codeql.yml?event=push&label=&branch=main&logo=github&logoColor=white&color=f368e0)](https://github.com/wellwelwel/lru.min/actions/workflows/ci_codeql.yml?query=branch%3Amain)\n\nPlease check the [**SECURITY.md**](https://github.com/wellwelwel/lru.min/blob/main/SECURITY.md).\n\n---\n\n## Contributing\n\nSee the [**Contributing Guide**](https://github.com/wellwelwel/lru.min/blob/main/CONTRIBUTING.md) and please follow our [**Code of Conduct**](https://github.com/wellwelwel/lru.min/blob/main/CODE_OF_CONDUCT.md) 🚀\n\n---\n\n## Acknowledgements\n\n**lru.min** is based and inspired on the architecture and code of both [**lru-cache**](https://github.com/isaacs/node-lru-cache) and [**quick-lru**](https://github.com/sindresorhus/quick-lru), simplifying their core concepts for enhanced performance and compatibility.\n\nFor more comprehensive features such as **TTL** support, consider using and supporting them 🤝\n\n- The architecture is mostly based on [@isaacs](https://github.com/isaacs) — [**lru-cache**](https://github.com/isaacs/node-lru-cache/blob/8f51d75351cbb4ac819952eb8e9f95eda00ef800/src/index.ts).\n- Most of the methods names and its functionalities were inspired by [@sindresorhus](https://github.com/sindresorhus) — [**quick-lru**](https://github.com/sindresorhus/quick-lru/blob/a2262c65e1952539cb4d985a67c46363a780d234/index.js).\n- [![Contributors](https://img.shields.io/github/contributors/wellwelwel/lru.min?label=Contributors)](https://github.com/wellwelwel/lru.min/graphs/contributors)\n\n---\n\n#### What comes from [**lru-cache**](https://github.com/isaacs/node-lru-cache)?\n\nArchitecture's essence:\n\n> _It's not the same code, but majority based on [this](https://github.com/isaacs/node-lru-cache/blob/8f51d75351cbb4ac819952eb8e9f95eda00ef800/src/index.ts#L1385-L1394)._\n\n```ts\nlet free: number[] = [];\n\nconst keyMap: Map<Key, number> = new Map();\nconst keyList: (Key | undefined)[] = new Array(max).fill(undefined);\nconst valList: (Value | undefined)[] = new Array(max).fill(undefined);\nconst next: number[] = new Array(max).fill(0);\nconst prev: number[] = new Array(max).fill(0);\n```\n\n---\n\n#### What comes from [**quick-lru**](https://github.com/sindresorhus/quick-lru)?\n\nName of methods and options _(including their final functionality ideas)_:\n\n- `resize`\n- `peek`\n- `onEviction`\n- `forEach`\n- `entriesDescending` as `entries`\n\n---\n\n## License\n\n**lru.min** is under the [**MIT License**](https://github.com/wellwelwel/lru.min/blob/main/LICENSE).<br />\nCopyright © 2024-present [Weslley Araújo](https://github.com/wellwelwel) and **lru.min** [contributors](https://github.com/wellwelwel/lru.min/graphs/contributors).\n", "repository": {"type": "git", "url": "git+https://github.com/wellwelwel/lru.min.git"}, "_source_registry_name": "default"}