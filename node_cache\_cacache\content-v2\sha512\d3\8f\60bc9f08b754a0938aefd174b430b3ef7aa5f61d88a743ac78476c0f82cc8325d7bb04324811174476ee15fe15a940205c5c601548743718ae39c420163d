{"_attachments": {}, "_id": "https-proxy-agent", "_rev": "2836-61f14a284ce7cf8f582675d7", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "dist-tags": {"latest": "7.0.6"}, "license": "MIT", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "name": "https-proxy-agent", "readme": "https-proxy-agent\n================\n### An HTTP(s) proxy `http.Agent` implementation for HTTPS\n\nThis module provides an `http.Agent` implementation that connects to a specified\nHTTP or HTTPS proxy server, and can be used with the built-in `https` module.\n\nSpecifically, this `Agent` implementation connects to an intermediary \"proxy\"\nserver and issues the [CONNECT HTTP method][CONNECT], which tells the proxy to\nopen a direct TCP connection to the destination server.\n\nSince this agent implements the CONNECT HTTP method, it also works with other\nprotocols that use this method when connecting over proxies (i.e. WebSockets).\nSee the \"Examples\" section below for more.\n\nExamples\n--------\n\n#### `https` module example\n\n```ts\nimport * as https from 'https';\nimport { HttpsProxyAgent } from 'https-proxy-agent';\n\nconst agent = new HttpsProxyAgent('http://************:3128');\n\nhttps.get('https://example.com', { agent }, (res) => {\n  console.log('\"response\" event!', res.headers);\n  res.pipe(process.stdout);\n});\n```\n\n#### `ws` WebSocket connection example\n\n```ts\nimport WebSocket from 'ws';\nimport { HttpsProxyAgent } from 'https-proxy-agent';\n\nconst agent = new HttpsProxyAgent('http://************:3128');\nconst socket = new WebSocket('ws://echo.websocket.org', { agent });\n\nsocket.on('open', function () {\n  console.log('\"open\" event!');\n  socket.send('hello world');\n});\n\nsocket.on('message', function (data, flags) {\n  console.log('\"message\" event! %j %j', data, flags);\n  socket.close();\n});\n```\n\nAPI\n---\n\n### new HttpsProxyAgent(proxy: string | URL, options?: HttpsProxyAgentOptions)\n\nThe `HttpsProxyAgent` class implements an `http.Agent` subclass that connects\nto the specified \"HTTP(s) proxy server\" in order to proxy HTTPS and/or WebSocket\nrequests. This is achieved by using the [HTTP `CONNECT` method][CONNECT].\n\nThe `proxy` argument is the URL for the proxy server.\n\nThe `options` argument accepts the usual `http.Agent` constructor options, and\nsome additional properties:\n\n * `headers` - Object containing additional headers to send to the proxy server\n   in the `CONNECT` request.\n\n[CONNECT]: http://en.wikipedia.org/wiki/HTTP_tunnel#HTTP_CONNECT_Tunneling\n", "time": {"created": "2022-01-26T13:18:32.887Z", "modified": "2024-12-07T03:36:55.323Z", "5.0.0": "2020-02-07T20:15:18.381Z", "4.0.0": "2019-12-10T21:22:34.549Z", "2.2.4": "2019-10-25T20:12:48.053Z", "3.0.1": "2019-10-23T19:12:15.824Z", "2.2.3": "2019-10-22T01:24:56.755Z", "3.0.0": "2019-10-07T20:13:47.003Z", "2.2.2": "2019-07-06T02:42:57.697Z", "2.2.1": "2018-03-29T08:02:26.610Z", "2.2.0": "2018-03-03T19:34:45.914Z", "2.1.1": "2017-11-28T18:40:52.995Z", "2.1.0": "2017-08-08T23:32:35.950Z", "2.0.0": "2017-06-27T00:38:55.004Z", "1.0.0": "2015-07-11T01:01:57.036Z", "0.3.6": "2015-07-06T22:53:04.798Z", "0.3.5": "2014-06-11T21:55:19.250Z", "0.3.4": "2014-04-09T23:50:27.653Z", "0.3.3": "2014-01-13T18:43:35.709Z", "0.3.2": "2013-11-18T19:51:53.270Z", "0.3.1": "2013-11-16T21:01:26.573Z", "0.3.0": "2013-09-16T23:30:53.989Z", "0.2.0": "2013-09-03T22:55:56.886Z", "0.1.0": "2013-08-21T18:49:18.059Z", "0.0.2": "2013-07-11T20:30:01.860Z", "0.0.1": "2013-07-09T20:44:52.547Z", "5.0.1": "2022-04-14T18:42:00.761Z", "6.0.0": "2023-05-04T20:33:19.866Z", "6.1.0": "2023-05-05T22:04:10.473Z", "6.2.0": "2023-05-18T19:31:24.151Z", "6.2.1": "2023-05-24T08:11:55.474Z", "7.0.0": "2023-05-25T00:23:00.180Z", "7.0.1": "2023-07-10T19:39:34.860Z", "7.0.2": "2023-09-04T08:09:39.350Z", "7.0.3": "2024-02-12T18:24:39.723Z", "7.0.4": "2024-02-15T19:14:23.182Z", "7.0.5": "2024-06-28T07:33:48.371Z", "7.0.6": "2024-12-07T03:32:09.302Z"}, "versions": {"5.0.0": {"name": "https-proxy-agent", "version": "5.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "dist/index", "types": "dist/index", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --reporter spec", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "6", "debug": "4"}, "devDependencies": {"@types/debug": "4", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "proxy": "1", "rimraf": "^3.0.0", "typescript": "^3.5.3"}, "engines": {"node": ">= 6"}, "gitHead": "8fdb1a5dd6a124951db39cb33f2438a89e0bb027", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@5.0.0", "_nodeVersion": "12.15.0", "_npmVersion": "6.13.7", "dist": {"shasum": "e2a90542abb68a762e0a0850f6c9edadfd8506b2", "size": 8494, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-5.0.0.tgz", "integrity": "sha512-EkYm5BcKUGiduxzSt3Eppko+PiNWNEpa4ySk9vTC6wDsQJW9rHSa+UhGNJoRYp7bz6Ht1eaRIa6QaJqO5rCFbA=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_5.0.0_1581106518210_0.20402709357454718"}, "_hasShrinkwrap": false, "publish_time": 1581106518381, "_cnpm_publish_time": 1581106518381, "_cnpmcore_publish_time": "2021-12-13T19:10:28.431Z"}, "4.0.0": {"name": "https-proxy-agent", "version": "4.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "types": "./index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "5", "debug": "4"}, "devDependencies": {"mocha": "6", "proxy": "1"}, "engines": {"node": ">= 6.0.0"}, "gitHead": "176d4b4fb20e229cf6cd1008f06bf97833fd725f", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@4.0.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"shasum": "702b71fb5520a132a66de1f67541d9e62154d82b", "size": 6519, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-4.0.0.tgz", "integrity": "sha512-zoDhWrkR3of1l9QAL8/scJZyLu8j/gBkcwcaQOZh7Gyh/+uJQzGVETdgT30akuwkpL8HTRfssqI3BZuV18teDg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_4.0.0_1576012954457_0.5260466677417492"}, "_hasShrinkwrap": false, "publish_time": 1576012954549, "_cnpm_publish_time": 1576012954549, "_cnpmcore_publish_time": "2021-12-13T19:10:28.647Z"}, "2.2.4": {"name": "https-proxy-agent", "version": "2.2.4", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "types": "./index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.3.0", "debug": "^3.1.0"}, "devDependencies": {"mocha": "^6.2.0", "proxy": "1"}, "engines": {"node": ">= 4.5.0"}, "gitHead": "4c4cce8cb60fd3ac6171e4428f972698eb49f45a", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@2.2.4", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "4ee7a737abd92678a293d9b34a1af4d0d08c787b", "size": 7403, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-2.2.4.tgz", "integrity": "sha512-OmvfoQ53WLjtA9HeYP9RNrWMJzzAz1JGaSFr1nijg0PVR1JaD/xbJq1mdEIIlxGpXp9eSe/O2LgU9DJmTPd0Eg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_2.2.4_1572034367919_0.9750376921194577"}, "_hasShrinkwrap": false, "publish_time": 1572034368053, "_cnpm_publish_time": 1572034368053, "_cnpmcore_publish_time": "2021-12-13T19:10:28.905Z"}, "3.0.1": {"name": "https-proxy-agent", "version": "3.0.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "types": "./index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.3.0", "debug": "^3.1.0"}, "devDependencies": {"mocha": "^6.2.0", "proxy": "1.0.1"}, "engines": {"node": ">= 4.5.0"}, "gitHead": "c562fb665646e72e3285fe1e6df238a89658d574", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@3.0.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "b8c286433e87602311b01c8ea34413d856a4af81", "size": 6545, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-3.0.1.tgz", "integrity": "sha512-+ML2Rbh6DAuee7d07tYGEKOEi2voWPUGan+ExdPbPW6Z3svq+JCqr0v8WmKPOkz1vOVykPCBSuobe7G8GJUtVg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_3.0.1_1571857935665_0.858421721447781"}, "_hasShrinkwrap": false, "publish_time": 1571857935824, "_cnpm_publish_time": 1571857935824, "_cnpmcore_publish_time": "2021-12-13T19:10:29.171Z"}, "2.2.3": {"name": "https-proxy-agent", "version": "2.2.3", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "types": "./index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.3.0", "debug": "^3.1.0"}, "devDependencies": {"mocha": "^6.2.0", "proxy": "1"}, "engines": {"node": ">= 4.5.0"}, "gitHead": "0d8e8bfe8b12e6ffe79a39eb93068cdf64c17e78", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@2.2.3", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "dist": {"shasum": "fb6cd98ed5b9c35056b5a73cd01a8a721d7193d1", "size": 6221, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-2.2.3.tgz", "integrity": "sha512-Ytgnz23gm2DVftnzqRRz2dOXZbGd2uiajSw/95bPp6v53zPRspQjLm/AfBgqbJ2qfeRXWIOMVLpp86+/5yX39Q=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_2.2.3_1571707496608_0.4908296147655695"}, "_hasShrinkwrap": false, "publish_time": 1571707496755, "_cnpm_publish_time": 1571707496755, "_cnpmcore_publish_time": "2021-12-13T19:10:29.366Z"}, "3.0.0": {"name": "https-proxy-agent", "version": "3.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "types": "./index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.3.0", "debug": "^3.1.0"}, "devDependencies": {"mocha": "^6.2.0", "proxy": "1"}, "engines": {"node": ">= 4.5.0"}, "gitHead": "200cc9f18ff25e6cb8e5f1d61db5fea159a103dd", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@3.0.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"shasum": "0106efa5d63d6d6f3ab87c999fa4877a3fd1ff97", "size": 7415, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-3.0.0.tgz", "integrity": "sha512-y4jAxNEihqvBI5F3SaO2rtsjIOnnNA8sEbuiP+UhJZJHeM2NRm6c09ax2tgqme+SgUUvjao2fJXF4h3D6Cb2HQ=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_3.0.0_1570479226835_0.5288499934220063"}, "_hasShrinkwrap": false, "publish_time": 1570479227003, "_cnpm_publish_time": 1570479227003, "_cnpmcore_publish_time": "2021-12-13T19:10:29.611Z"}, "2.2.2": {"name": "https-proxy-agent", "version": "2.2.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "types": "./index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.3.0", "debug": "^3.1.0"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "engines": {"node": ">= 4.5.0"}, "gitHead": "e1457142b9978b7491af0c6a505985aea2aa35c5", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@2.2.2", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"shasum": "271ea8e90f836ac9f119daccd39c19ff7dfb0793", "size": 6076, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-2.2.2.tgz", "integrity": "sha512-c8Ndjc9Bkpfx/vCJueCPy0jlP4ccCCSNDp8xwCZzPjKJUm+B+u9WX2x98Qx4n1PiMNTWo3D7KK5ifNV/yJyRzg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_2.2.2_1562380977572_0.40384044301767585"}, "_hasShrinkwrap": false, "publish_time": 1562380977697, "_cnpm_publish_time": 1562380977697, "_cnpmcore_publish_time": "2021-12-13T19:10:29.876Z"}, "2.2.1": {"name": "https-proxy-agent", "version": "2.2.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.1.0", "debug": "^3.1.0"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "engines": {"node": ">= 4.5.0"}, "gitHead": "8c3a75baddecae7e2fe2921d1adde7edd0203156", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@2.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.8.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "51552970fa04d723e04c56d04178c3f92592bbc0", "size": 8953, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-2.2.1.tgz", "integrity": "sha512-HPCTS1LW51bcyMYbxUIOO4HEOlQ1/1qRaFWcyxvwaqUS9TY88aoEuHUY33kuAh1YhVVaDQhLZsnPd+XNARWZlQ=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_2.2.1_1522310546565_0.5165395674470354"}, "_hasShrinkwrap": false, "publish_time": 1522310546610, "_cnpm_publish_time": 1522310546610, "_cnpmcore_publish_time": "2021-12-13T19:10:30.085Z"}, "2.2.0": {"name": "https-proxy-agent", "version": "2.2.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.1.0", "debug": "^3.1.0"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "engines": {"node": ">= 4.5.0"}, "gitHead": "b9d5b7ec336264e9d8208287654060ae9a880976", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@2.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "7fbba856be8cd677986f42ebd3664f6317257887", "size": 8824, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-2.2.0.tgz", "integrity": "sha512-uUWcfXHvy/dwfM9bqa6AozvAjS32dZSTUYd/4SEpYKRg6LEcPLshksnQYRudM9AyNvUARMfAg5TLjUDyX/K4vA=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_2.2.0_1520105685829_0.726763197736217"}, "_hasShrinkwrap": false, "publish_time": 1520105685914, "_cnpm_publish_time": 1520105685914, "_cnpmcore_publish_time": "2021-12-13T19:10:30.294Z"}, "2.1.1": {"name": "https-proxy-agent", "version": "2.1.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.1.0", "debug": "^3.1.0"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "gitHead": "c58d365dd153104d1147967a0a6b4e1dd1698e50", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@2.1.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "a7ce4382a1ba8266ee848578778122d491260fd9", "size": 8794, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-2.1.1.tgz", "integrity": "sha512-LK6tQUR/VOkTI6ygAfWUKKP95I+e6M1h7N3PncGu1CATHCnex+CAv9ttR0lbHu1Uk2PXm/WoAHFo6JCGwMjVMw=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent-2.1.1.tgz_1511894451711_0.7408082666806877"}, "directories": {}, "publish_time": 1511894452995, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511894452995, "_cnpmcore_publish_time": "2021-12-13T19:10:30.499Z"}, "2.1.0": {"name": "https-proxy-agent", "version": "2.1.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.1.0", "debug": "^2.4.1"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "gitHead": "5543d28b3c3b6519cdc7346fb517261cd47998b1", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@2.1.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "1391bee7fd66aeabc0df2a1fa90f58954f43e443", "size": 8795, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-2.1.0.tgz", "integrity": "sha512-/DTVSUCbRc6AiyOV4DBRvPDpKKCJh4qQJNaCgypX0T41quD9hp/PB5iUyx/60XobuMPQa9ce1jNV9UOUq6PnTg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent-2.1.0.tgz_1502235155845_0.9402012808714062"}, "directories": {}, "publish_time": 1502235155950, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502235155950, "_cnpmcore_publish_time": "2021-12-13T19:10:30.709Z"}, "2.0.0": {"name": "https-proxy-agent", "version": "2.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.1.0", "debug": "^2.4.1"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "^0.2.4"}, "gitHead": "6c50a9acc5b00a2f559bba19c7b5d78120b0415d", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@2.0.0", "_shasum": "ffaa4b6faf586ac340c18a140431e76b7d7f2944", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "ffaa4b6faf586ac340c18a140431e76b7d7f2944", "size": 8465, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-2.0.0.tgz", "integrity": "sha512-Nbsiz3zjp5zmJHvbIY3PGHoxh3Y4q+wFAA2UHvLAPAa3K7yzSGgyW3WBxV05xJUb0K76KjDJWhb6CsYErwUHaA=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent-2.0.0.tgz_1498523934878_0.9455580299254507"}, "directories": {}, "publish_time": 1498523935004, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498523935004, "_cnpmcore_publish_time": "2021-12-13T19:10:30.944Z"}, "1.0.0": {"name": "https-proxy-agent", "version": "1.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "2", "debug": "2", "extend": "3"}, "devDependencies": {"mocha": "2", "proxy": "~0.2.3", "semver": "~2.2.1"}, "gitHead": "cb7577b6aa9a2466ca7612b1ebd6fc281407187f", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@1.0.0", "_shasum": "35f7da6c48ce4ddbfa264891ac593ee5ff8671e6", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.6", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "35f7da6c48ce4ddbfa264891ac593ee5ff8671e6", "size": 8341, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-1.0.0.tgz", "integrity": "sha512-OZhm7//JDnQthMVqlPAfkZyPO2fMhfHY6gY+jZcX8rLfFiGtHiIQrfD80WvCDHNMQ77Ak3r5CiPRDD2rNzo2OQ=="}, "directories": {}, "publish_time": 1436576517036, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436576517036, "_cnpmcore_publish_time": "2021-12-13T19:10:31.124Z"}, "0.3.6": {"name": "https-proxy-agent", "version": "0.3.6", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "debug": "2", "extend": "3"}, "devDependencies": {"mocha": "2", "proxy": "~0.2.3", "semver": "~2.2.1"}, "gitHead": "328299fa0481be2ba34d654dba9252494cf380c2", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@0.3.6", "_shasum": "713fa38e5d353f50eb14a342febe29033ed1619b", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.6", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "713fa38e5d353f50eb14a342febe29033ed1619b", "size": 8692, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.3.6.tgz", "integrity": "sha512-ZuLafAeUu97abfbpAO9Cwjl3slsx6yZ7apTYBNVtMdoDhlVzUhxXO0qh+Xxqc5FAm7oq747k2jjbICYJdEYShg=="}, "directories": {}, "publish_time": 1436223184798, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436223184798, "_cnpmcore_publish_time": "2021-12-13T19:10:31.311Z"}, "0.3.5": {"name": "https-proxy-agent", "version": "0.3.5", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "debug": "~1.0.0", "extend": "~1.2.1"}, "devDependencies": {"mocha": "~1.18.2", "proxy": "~0.2.3", "semver": "~2.2.1"}, "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "_id": "https-proxy-agent@0.3.5", "_shasum": "d41d43a912c0592f17552fc1a29cd484a2145648", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "d41d43a912c0592f17552fc1a29cd484a2145648", "size": 8643, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.3.5.tgz", "integrity": "sha512-1x3AWuqw03J1FinFWBoWONQFQOP+ejo43nFGOsEd2ifTdcSP83i0dT3z4gov22vePw2zw+fCb0yNa57gOCVYpA=="}, "directories": {}, "publish_time": 1402523719250, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402523719250, "_cnpmcore_publish_time": "2021-12-13T19:10:31.519Z"}, "0.3.4": {"name": "https-proxy-agent", "version": "0.3.4", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "debug": "~0.8.0", "extend": "~1.2.1"}, "devDependencies": {"mocha": "~1.18.2", "proxy": "~0.2.3", "semver": "~2.2.1"}, "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "_id": "https-proxy-agent@0.3.4", "dist": {"shasum": "df7229a25ca7b446c677e41ee2b9dd60e5bd8680", "size": 8625, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.3.4.tgz", "integrity": "sha512-OCxoWrj5EiQpid+BLxYOLdFcqE+i22ufjftHTTmAwWdbiGfiZUekSFbdr9CjN4v0KJediDI3CcPp+ltmsR9ofQ=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1397087427653, "_hasShrinkwrap": false, "_cnpm_publish_time": 1397087427653, "_cnpmcore_publish_time": "2021-12-13T19:10:31.718Z"}, "0.3.3": {"name": "https-proxy-agent", "version": "0.3.3", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "debug": "~0.7.2", "extend": "~1.2.0"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.1", "semver": "~2.1.0"}, "readmeFilename": "README.md", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "_id": "https-proxy-agent@0.3.3", "dist": {"shasum": "00d5f4c1e656c8a9b2b1b479ebd417c69a16e251", "size": 8570, "noattachment": false, "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.3.3.tgz", "integrity": "sha512-TdYjY9pMwUdADfiHLhrL0roHEySW46vTqsDfJUt51fsllEG1Xrr6O7nfsZR/DKONJ4zCo3R158BMD2ov7pLlmw=="}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1389638615709, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389638615709, "_cnpmcore_publish_time": "2021-12-13T19:10:31.956Z"}, "0.3.2": {"name": "https-proxy-agent", "version": "0.3.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "debug": "~0.7.2", "extend": "~1.2.0"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.1", "semver": "~2.1.0"}, "readmeFilename": "README.md", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "_id": "https-proxy-agent@0.3.2", "dist": {"tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.3.2.tgz", "shasum": "fbe571fd2b52406faa8c5b77cd85b2d3241522e9", "size": 8516, "noattachment": false, "integrity": "sha512-oqMw5RBH85DcXOze6XSsWhu2CqCg7DcH/TTRUKewS5/C/I2YH1WGHuzxQpruiXj3wWCV2e7sBpyBoM8Kvz9h6A=="}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384804313270, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384804313270, "_cnpmcore_publish_time": "2021-12-13T19:10:32.170Z"}, "0.3.1": {"name": "https-proxy-agent", "version": "0.3.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "debug": "~0.7.2", "extend": "~1.2.0"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.1", "semver": "~2.1.0"}, "readmeFilename": "README.md", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent", "_id": "https-proxy-agent@0.3.1", "dist": {"tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.3.1.tgz", "shasum": "24c95499957359e69b3ab8f061a5d2fa96ba88e7", "size": 8465, "noattachment": false, "integrity": "sha512-2A85U9l3ubcsCYGR4Lde12Hf2lKPV7HXtYSZNygkFbGhDE/5QsL89e2IMhB3N4X0K+yDoU0YwgC1tPk6ASOT1Q=="}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384635686573, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384635686573, "_cnpmcore_publish_time": "2021-12-13T19:10:32.378Z"}, "0.3.0": {"name": "https-proxy-agent", "version": "0.3.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "debug": "~0.7.2", "extend": "~1.2.0"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.1", "semver": "~2.1.0"}, "readmeFilename": "README.md", "_id": "https-proxy-agent@0.3.0", "dist": {"tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.3.0.tgz", "shasum": "a0bc015df43de954e41fb7e55a2d93ca4be2b3a3", "size": 9486, "noattachment": false, "integrity": "sha512-QMMtoSeRFH3q39cgnMNPKtHRyLB+kcXz89YqbQ1SLV1C0uFIZChZvgqUPxpF8gslj8EPe1blAhsVTIFbvRFgcw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379374253989, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379374253989, "_cnpmcore_publish_time": "2021-12-13T19:10:32.776Z"}, "0.2.0": {"name": "https-proxy-agent", "version": "0.2.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}, "readmeFilename": "README.md", "_id": "https-proxy-agent@0.2.0", "dist": {"tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.2.0.tgz", "shasum": "bee5100afdb79e7839f2f0031ac3f6dfc811ea9f", "size": 3832, "noattachment": false, "integrity": "sha512-50IQ7WI2x11W6628PDoml2tfw4yWUpmvdk6VJs+6rPLy/B+k6yTr7+eyfFsmF1ZFU6jGtQxLPcKWp8SBkejhcA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378248956886, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378248956886, "_cnpmcore_publish_time": "2021-12-13T19:10:32.998Z"}, "0.1.0": {"name": "https-proxy-agent", "version": "0.1.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}, "readmeFilename": "README.md", "_id": "https-proxy-agent@0.1.0", "dist": {"tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.1.0.tgz", "shasum": "e976799604f1528cc657900d5e10a82d1595e0cd", "size": 3732, "noattachment": false, "integrity": "sha512-AOpJByKEwbB5HKACHNFL1IwWIRRLNSjesEGxNBio9LpT5vj/wF8/YS1B2QwQ+Mc4suand8qhS4/+3sdTzjeXlQ=="}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1377110958059, "_hasShrinkwrap": false, "_cnpm_publish_time": 1377110958059, "_cnpmcore_publish_time": "2021-12-13T19:10:33.287Z"}, "0.0.2": {"name": "https-proxy-agent", "version": "0.0.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}, "readmeFilename": "README.md", "_id": "https-proxy-agent@0.0.2", "dist": {"tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.0.2.tgz", "shasum": "8822f8626f62b09d299cff9a1d7ed56b552df4b0", "size": 3248, "noattachment": false, "integrity": "sha512-ChwhpTj9TppuU3r0JhpwqW9bALryvWJafQxzU5vi3Z56CWKrs01CMZYB1yJSl2mezfghUHd2IfNy20hmrRENWw=="}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373574601860, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373574601860, "_cnpmcore_publish_time": "2021-12-13T19:10:33.484Z"}, "0.0.1": {"name": "https-proxy-agent", "version": "0.0.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "https-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}, "readmeFilename": "README.md", "_id": "https-proxy-agent@0.0.1", "dist": {"tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-0.0.1.tgz", "shasum": "c3390d917e6c5fbc25559f3ec73b711f9dd4c063", "size": 2438, "noattachment": false, "integrity": "sha512-AM0D9xJF3QHMlvCmVS4cZ7ApSfI549tTsdD213PUxWc85Wkg4SS2d0RVnKi8a9/2yr3Gt5DE/ZljpNzoAs9F5Q=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373402692547, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373402692547, "_cnpmcore_publish_time": "2021-12-13T19:10:33.677Z"}, "5.0.1": {"name": "https-proxy-agent", "version": "5.0.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "dist/index", "types": "dist/index", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --reporter spec", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "6", "debug": "4"}, "devDependencies": {"@types/debug": "4", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "proxy": "1", "rimraf": "^3.0.0", "typescript": "^3.5.3"}, "engines": {"node": ">= 6"}, "gitHead": "d0d80cc0482f20495aa8595f802e1a9f3b1b3409", "homepage": "https://github.com/TooTallNate/node-https-proxy-agent#readme", "_id": "https-proxy-agent@5.0.1", "_nodeVersion": "14.19.1", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==", "shasum": "c59ef224a04fe8b754f3db0063a25ea30d0005d6", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "fileCount": 11, "unpackedSize": 26008, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAX0ip0Ku5jpGaVVN/e5guGb0LZF5ysJ5/lBZsGMFRDjAiBI4+fcp/pgtGWTIMFBpL8Hj0bq2tSCUiLUtjMTB7cAhw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWGr4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLrRAAnV1xcQL29ZKTpBVOZKXtPbM1HeDWeslZay5K7BNGdVEIVcib\r\nenaV7LvSuV4tsWmThRzEaCV8YpQ/rLmU3MhJAQ4gL36BWzyBZLbpH8GXA/ms\r\norVG3Brn+rsBn5qd3Rf1API4QEO97nb7dEqec1vfvneJ0I1Pj6bkYOp0GSeP\r\nL8reHwboA2R/rSnpDGphEDTRclUH3GC10YKrJ5+GJGQlwERf+FrdJlHtI2RK\r\nWXkras3bf3WTcZ0RucJOrJmYFkGT3jAlNmnnspzNB2TeXYIdSTOf3AV8pQYA\r\npqIj7f6UE0U782ni7b8tDnQ+dUSk5WQylF1zsCcao+wnwm+ScatirnIfMU4t\r\nP1ZdiwREgslVwMmKK+U5NjDOTPRJF+cJq9AnDsIcxhaCkpQDWseR4VfVnE0o\r\nAc6YIaEIWVdj+yGK8aXk7j3LYsK1D4fSBZxiGCXqZuOFXsBpwslA3dAMD7f4\r\nrZfVBiO5I660nH+aBn6T3doBGv6OuXjR+kn5MKWB8RNwymCNzrE+aeyjpVf+\r\nTBSSgdJEskMowLJe4D7yIoKfQDHJ+fWAKkOPNMV1SpZkY8Q5e2dmwXRSPfP8\r\nM7y2ghrIK5w8CwK8zucn3ul1111ZpM5syjtp79GquAYGwqwuiDwO/io0hCqI\r\nDtV4891qc3Mi6yTW6g7t/JKEQ1UlS+ktku0=\r\n=iREH\r\n-----END PGP SIGNATURE-----\r\n", "size": 8412}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_5.0.1_1649961720594_0.7177664695298436"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-14T18:42:39.395Z"}, "6.0.0": {"name": "https-proxy-agent", "version": "6.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.0", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "async-listen": "^2.1.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.0.0", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "https-proxy-agent@6.0.0", "_integrity": "sha512-g821um/ZvXlENs8tqKh96b6G0wafab6ypfkZdFZImJEGZrn47oLeRhWMKvCYxrasOgNi3Yh6Cxkws2Zn13v2QA==", "_resolved": "/tmp/2b1b67f3f6142559d3aaf8c5a2592304/https-proxy-agent-6.0.0.tgz", "_from": "file:https-proxy-agent-6.0.0.tgz", "_nodeVersion": "20.1.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-g821um/ZvXlENs8tqKh96b6G0wafab6ypfkZdFZImJEGZrn47oLeRhWMKvCYxrasOgNi3Yh6Cxkws2Zn13v2QA==", "shasum": "036adbaba1c6810ce87216ed13cf698ec8672c17", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-6.0.0.tgz", "fileCount": 11, "unpackedSize": 31702, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGtJ5lhTZrkGqYsKhaKC1bAT/Lo74zmlY57mWm5V0ojOAiBulZ+RnRcqxd5XTiTjMGrYsqxNYY+y+YR5RpMk501Jjg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8Hw//TUhpuvJNxvXRFzLRmseDOmAwBuGyjYsfEFMvB2/ZBXXiZFP2\r\ndurDy/kh5YFQEC3K85DRDg0kazG93/Aot+YfkC29l0xhyV+oZ+XdfKEp8O+j\r\nQtKTWFKVRzxFH4/5HpodgFEdjW2Rp6UUgibcH+VddILj9QnqsxWbbLwCEBNs\r\nZ+g8SeAiR5tWZRXSUqNUdMLgWls6mTcBv0HdyztyWs/XD+qlmOvqvzdjdof3\r\nGH4hpcnNrYqcV/kAyhm5nwCgC+8cXeZV5Px2XypfQyS34Fn1YI1WWN/JS92Z\r\n7l5v9V51wW+KJCK79fN8e7UE2YogV70qyYZFPpJpS3Ia8t1dWtR/o84Nj+vi\r\nP49Cah1sH6jqc1IRhE8+w6/9m0PgJ0q5LzWscCiJWBOXUIxDxMIEWr5N78Wi\r\nAYQ8dnQDuqdW6V/ubm0I9gOOYDg713ZBeEzwWZ008k2mf91O51jmP52b7Py1\r\nm9ZIY4+ioC5Gm/LVL6yBfNBSuznxVvNbnYGnUB8MnPp7Sp8/RV7JpeegAR8J\r\n1mOC6XiCR+EzuOvPEKVGeOvPl49On5/pyS5XjDBHkQ8v4Df9NAANp1b7pk4f\r\nJANYie5mcUxkyfYj+EAlihMlCrfDcraQOODsJg53BwbOocHNzhvLDXuhR+A+\r\ngbGwAD3c2fJZM5Y9Bdi5zVCZTLrZ042ZUoI=\r\n=LlBd\r\n-----END PGP SIGNATURE-----\r\n", "size": 8129}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_6.0.0_1683232399696_0.010486679555168354"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-04T20:33:19.866Z", "publish_time": 1683232399866, "_source_registry_name": "default"}, "6.1.0": {"name": "https-proxy-agent", "version": "6.1.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.1", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^2.1.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.0.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "https-proxy-agent@6.1.0", "_integrity": "sha512-rvGRAlc3y+iS7AC9Os2joN91mX8wHpJ4TEklmHHxr7Gz2Juqa7fJmJ8wWxXNpTaRt56MQTwojxV5d82UW/+jwg==", "_resolved": "/tmp/250c446b8463b8afd2e7bae14150fee9/https-proxy-agent-6.1.0.tgz", "_from": "file:https-proxy-agent-6.1.0.tgz", "_nodeVersion": "20.1.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-rvGRAlc3y+iS7AC9Os2joN91mX8wHpJ4TEklmHHxr7Gz2Juqa7fJmJ8wWxXNpTaRt56MQTwojxV5d82UW/+jwg==", "shasum": "e00f1efb849171ea349721481d3bcbef03ab4d13", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-6.1.0.tgz", "fileCount": 11, "unpackedSize": 34504, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfJVw0ptoTfxRewi4X9O0H4bjaXloOG0z8LEqIYj/LawIhAOgszZo+GnveXuUri3zNsCpxiG1YLtf5BU/nIfcUWokv"}], "size": 8704}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_6.1.0_1683324250258_0.7834208137850858"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-05T22:04:10.473Z", "publish_time": 1683324250473, "_source_registry_name": "default"}, "6.2.0": {"name": "https-proxy-agent", "version": "6.2.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^2.1.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "https-proxy-agent@6.2.0", "_integrity": "sha512-4xhCnMpxR9fupa7leh9uJK2P/qjYIeaM9uZ9c1bi1JDSwX2VH9NDk/oKSToNX4gBKa2WT31Mldne7e26ckohLQ==", "_resolved": "/tmp/f8dcb3e5788b410b17908bf09c26bf3e/https-proxy-agent-6.2.0.tgz", "_from": "file:https-proxy-agent-6.2.0.tgz", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-4xhCnMpxR9fupa7leh9uJK2P/qjYIeaM9uZ9c1bi1JDSwX2VH9NDk/oKSToNX4gBKa2WT31Mldne7e26ckohLQ==", "shasum": "58c525a299663d958556969a8e3536dd1e007485", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-6.2.0.tgz", "fileCount": 11, "unpackedSize": 34885, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE7rtxXaj9dt3EZLp9R6PSvd4znf2OaO+NldfsRPqfbNAiBi8JjkVyCVbiJOX9SP4CoRYuHEIwZtBDiG3o0uOs9X8g=="}], "size": 8770}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_6.2.0_1684438283929_0.2928485102130345"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T19:31:24.151Z", "publish_time": 1684438284151, "_source_registry_name": "default"}, "6.2.1": {"name": "https-proxy-agent", "version": "6.2.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "https-proxy-agent@6.2.1", "_integrity": "sha512-ONsE3+yfZF2caH5+bJlcddtWqNI3Gvs5A38+ngvljxaBiRXRswym2c7yf8UAeFpRFKjFNHIFEHqR/OLAWJzyiA==", "_resolved": "/tmp/e74649210c0d30d3a55211d70d4addc3/https-proxy-agent-6.2.1.tgz", "_from": "file:https-proxy-agent-6.2.1.tgz", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-ONsE3+yfZF2caH5+bJlcddtWqNI3Gvs5A38+ngvljxaBiRXRswym2c7yf8UAeFpRFKjFNHIFEHqR/OLAWJzyiA==", "shasum": "0965ab47371b3e531cf6794d1eb148710a992ba7", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-6.2.1.tgz", "fileCount": 11, "unpackedSize": 34972, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGmNkv0v8mXGKa2m7LkJa+z83J3CKn4GDCVXeH8sOoZDAiEA3V/6s8RyX5xUJ73K+A9x8l1E7BQJFq7U3DHd8q9ywMg="}], "size": 8829}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_6.2.1_1684915915307_0.3856108065272996"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-24T08:11:55.474Z", "publish_time": 1684915915474, "_source_registry_name": "default"}, "7.0.0": {"name": "https-proxy-agent", "version": "7.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "https-proxy-agent@7.0.0", "_integrity": "sha512-0euwPCRyAPSgGdzD1IVN9nJYHtBhJwb6XPfbpQcYbPCwrBidX6GzxmchnaF4sfF/jPb74Ojx5g4yTg3sixlyPw==", "_resolved": "/tmp/f0f11c9fa125e61e57609041ddb23641/https-proxy-agent-7.0.0.tgz", "_from": "file:https-proxy-agent-7.0.0.tgz", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-0euwPCRyAPSgGdzD1IVN9nJYHtBhJwb6XPfbpQcYbPCwrBidX6GzxmchnaF4sfF/jPb74Ojx5g4yTg3sixlyPw==", "shasum": "75cb70d04811685667183b31ab158d006750418a", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-7.0.0.tgz", "fileCount": 11, "unpackedSize": 34399, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/XLDDRDf8SyZFjx6CkNuPG+GeVGboK0xc1Od7C0kSkAIgJlVhM3IrT1Gey4OWnXaPu0Wp5fvSlHM6+sC+8WJPOeo="}], "size": 8699}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_7.0.0_1684974179969_0.7839919362984578"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-25T00:23:00.180Z", "publish_time": 1684974180180, "_source_registry_name": "default"}, "7.0.1": {"name": "https-proxy-agent", "version": "7.0.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "https-proxy-agent@7.0.1", "_integrity": "sha512-Eun8zV0kcYS1g19r78osiQLEFIRspRUDd9tIfBCTBPBeMieF/EsJNL8VI3xOIdYRDEkjQnqOYPsZ2DsWsVsFwQ==", "_resolved": "/tmp/4f3480c301f735c9e62fa7ea35493a29/https-proxy-agent-7.0.1.tgz", "_from": "file:https-proxy-agent-7.0.1.tgz", "_nodeVersion": "20.4.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-Eun8zV0kcYS1g19r78osiQLEFIRspRUDd9tIfBCTBPBeMieF/EsJNL8VI3xOIdYRDEkjQnqOYPsZ2DsWsVsFwQ==", "shasum": "0277e28f13a07d45c663633841e20a40aaafe0ab", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-7.0.1.tgz", "fileCount": 11, "unpackedSize": 34457, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCilg8KABNJhORBdvxh3w9vyxlxdjtAOrOQJ2yf8qg/gwIgXZz+f8R1s/3T/fAORPjyFXgb3huGQxPoYdOrS9/KlBE="}], "size": 8712}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_7.0.1_1689017974671_0.3514824815405546"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-10T19:39:34.860Z", "publish_time": 1689017974860, "_source_registry_name": "default"}, "7.0.2": {"name": "https-proxy-agent", "version": "7.0.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "https-proxy-agent@7.0.2", "_integrity": "sha512-NmLNjm6ucYwtcUmL7JQC1ZQ57LmHP4lT15FQ8D61nak1rO6DH+fz5qNK2Ap5UN4ZapYICE3/0KodcLYSPsPbaA==", "_resolved": "/tmp/93dc39357900debe1b384fbf5a463e16/https-proxy-agent-7.0.2.tgz", "_from": "file:https-proxy-agent-7.0.2.tgz", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-NmLNjm6ucYwtcUmL7JQC1ZQ57LmHP4lT15FQ8D61nak1rO6DH+fz5qNK2Ap5UN4ZapYICE3/0KodcLYSPsPbaA==", "shasum": "e2645b846b90e96c6e6f347fb5b2e41f1590b09b", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-7.0.2.tgz", "fileCount": 11, "unpackedSize": 35127, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMPfNzixuLG6UUpXqZh0X3szvo3Q3zQszUqLJhimdJrwIgRYEFLigSxWtoE60CNgIk5gfvFVp51tpEui3Ef1ZLUG0="}], "size": 8810}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_7.0.2_1693814979144_0.2430773099728103"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-04T08:09:39.350Z", "publish_time": 1693814979350, "_source_registry_name": "default"}, "7.0.3": {"name": "https-proxy-agent", "version": "7.0.3", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "https-proxy-agent@7.0.3", "_integrity": "sha512-kCnwztfX0KZJSLOBrcL0emLeFako55NWMovvyPP2AjsghNk9RB1yjSI+jVumPHYZsNXegNoqupSW9IY3afSH8w==", "_resolved": "/tmp/cfab0f985f918976bf1320d7991f27e1/https-proxy-agent-7.0.3.tgz", "_from": "file:https-proxy-agent-7.0.3.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-kCnwztfX0KZJSLOBrcL0emLeFako55NWMovvyPP2AjsghNk9RB1yjSI+jVumPHYZsNXegNoqupSW9IY3afSH8w==", "shasum": "93f115f0f106a746faf364d1301b2e561cdf70de", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-7.0.3.tgz", "fileCount": 12, "unpackedSize": 35291, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTkD6889RNzZo5yuFP4D79l+mDruU9T3YbzdBiBJ3esQIhAJ6QGfpixR3ZNp3SF6YrCwxlgC5CzM53hp7hboihOggH"}], "size": 8955}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_7.0.3_1707762279552_0.8572989034685161"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-12T18:24:39.723Z", "publish_time": 1707762279723, "_source_registry_name": "default"}, "7.0.4": {"name": "https-proxy-agent", "version": "7.0.4", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "https-proxy-agent@7.0.4", "_integrity": "sha512-wlwpilI7YdjSkWaQ/7omYBMTliDcmCN8OLihO6I9B86g06lMyAoqgoDpV0XqoaPOKj+0DIdAvnsWfyAAhmimcg==", "_resolved": "/tmp/591b0714b04acbac1c4451828dc84a64/https-proxy-agent-7.0.4.tgz", "_from": "file:https-proxy-agent-7.0.4.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-wlwpilI7YdjSkWaQ/7omYBMTliDcmCN8OLihO6I9B86g06lMyAoqgoDpV0XqoaPOKj+0DIdAvnsWfyAAhmimcg==", "shasum": "8e97b841a029ad8ddc8731f26595bad868cb4168", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-7.0.4.tgz", "fileCount": 12, "unpackedSize": 35256, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyoL7nQuArTb0nvfi88zDY+FDvdCnEQUZ/N5f/APkM7AIgUkiqvmQhYTXROlBEnybz66orsb9Y4lhqR4zuC2QWgf0="}], "size": 8948}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_7.0.4_1708024462970_0.2415221627294155"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-15T19:14:23.182Z", "publish_time": 1708024463182, "_source_registry_name": "default"}, "7.0.5": {"name": "https-proxy-agent", "version": "7.0.5", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.2.0", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "https-proxy-agent@7.0.5", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-1e4Wqeblerz+tMKPIq2EMGiiWW1dIjZOksyHWSUm1rmuvw/how9hBHZ38lAGj5ID4Ik6EdkOw7NmWPy6LAwalw==", "_resolved": "/tmp/5180433bb9463a934c98795a9595b7a5/https-proxy-agent-7.0.5.tgz", "_from": "file:https-proxy-agent-7.0.5.tgz", "_nodeVersion": "20.15.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-1e4Wqeblerz+tMKPIq2EMGiiWW1dIjZOksyHWSUm1rmuvw/how9hBHZ38lAGj5ID4Ik6EdkOw7NmWPy6LAwalw==", "shasum": "9e8b5013873299e11fab6fd548405da2d6c602b2", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-7.0.5.tgz", "fileCount": 12, "unpackedSize": 34878, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDFZmWUU+D3oE97K/cAiDmaGBWqWbaJGdP4cXqLZkp/qAiBUAt9Z2J0FL8xGKJYYpXlFk+nOjdkWPWIy0xRwtfTq6g=="}], "size": 8900}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_7.0.5_1719560028200_0.2922565123601131"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-06-28T07:33:48.371Z", "publish_time": 1719560028371, "_source_registry_name": "default"}, "7.0.6": {"name": "https-proxy-agent", "version": "7.0.6", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.2.0", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "https-proxy-agent@7.0.6", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==", "_resolved": "/tmp/6eec5581e8f502f3b759131ee0e89546/https-proxy-agent-7.0.6.tgz", "_from": "file:https-proxy-agent-7.0.6.tgz", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==", "shasum": "da8dfeac7da130b05c2ba4b59c9b6cd66611a6b9", "tarball": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz", "fileCount": 12, "unpackedSize": 35157, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGQmE/GtFh47FHNYydO0kHVKVPJ3xHC8374L5yFaZwr7AiBPZDC76EJqXcrgPjMTGm9K5WZjFnK9aQJZh1/K2XXGXQ=="}], "size": 8998}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/https-proxy-agent_7.0.6_1733542329054_0.7202664074443708"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-07T03:32:09.302Z", "publish_time": 1733542329302, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["https", "proxy", "endpoint", "agent"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/https-proxy-agent"}, "_source_registry_name": "default"}