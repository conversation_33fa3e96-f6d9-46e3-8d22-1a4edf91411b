{"_attachments": {}, "_id": "nopt", "_rev": "1557-61f146a823990e8a812ee375", "author": {"name": "GitHub Inc."}, "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "dist-tags": {"latest": "8.1.0"}, "license": "ISC", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "name": "nopt", "readme": "If you want to write an option parser, and have it be good, there are\ntwo ways to do it.  The Right Way, and the Wrong Way.\n\nThe Wrong Way is to sit down and write an option parser.  We've all done\nthat.\n\nThe Right Way is to write some complex configurable program with so many\noptions that you hit the limit of your frustration just trying to\nmanage them all, and defer it with duct-tape solutions until you see\nexactly to the core of the problem, and finally snap and write an\nawesome option parser.\n\nIf you want to write an option parser, don't write an option parser.\nWrite a package manager, or a source control system, or a service\nrestarter, or an operating system.  You probably won't end up with a\ngood one of those, but if you don't give up, and you are relentless and\ndiligent enough in your procrastination, you may just end up with a very\nnice option parser.\n\n## USAGE\n\n```javascript\n// my-program.js\nvar nopt = require(\"nopt\")\n  , Stream = require(\"stream\").Stream\n  , path = require(\"path\")\n  , knownOpts = { \"foo\" : [String, null]\n                , \"bar\" : [Stream, Number]\n                , \"baz\" : path\n                , \"bloo\" : [ \"big\", \"medium\", \"small\" ]\n                , \"flag\" : <PERSON><PERSON>an\n                , \"pick\" : <PERSON><PERSON>an\n                , \"many1\" : [String, Array]\n                , \"many2\" : [path, Array]\n                }\n  , shortHands = { \"foofoo\" : [\"--foo\", \"Mr. Foo\"]\n                 , \"b7\" : [\"--bar\", \"7\"]\n                 , \"m\" : [\"--bloo\", \"medium\"]\n                 , \"p\" : [\"--pick\"]\n                 , \"f\" : [\"--flag\"]\n                 }\n             // everything is optional.\n             // knownOpts and shorthands default to {}\n             // arg list defaults to process.argv\n             // slice defaults to 2\n  , parsed = nopt(knownOpts, shortHands, process.argv, 2)\nconsole.log(parsed)\n```\n\nThis would give you support for any of the following:\n\n```console\n$ node my-program.js --foo \"blerp\" --no-flag\n{ \"foo\" : \"blerp\", \"flag\" : false }\n\n$ node my-program.js ---bar 7 --foo \"Mr. Hand\" --flag\n{ bar: 7, foo: \"Mr. Hand\", flag: true }\n\n$ node my-program.js --foo \"blerp\" -f -----p\n{ foo: \"blerp\", flag: true, pick: true }\n\n$ node my-program.js -fp --foofoo\n{ foo: \"Mr. Foo\", flag: true, pick: true }\n\n$ node my-program.js --foofoo -- -fp  # -- stops the flag parsing.\n{ foo: \"Mr. Foo\", argv: { remain: [\"-fp\"] } }\n\n$ node my-program.js --blatzk -fp # unknown opts are ok.\n{ blatzk: true, flag: true, pick: true }\n\n$ node my-program.js --blatzk=1000 -fp # but you need to use = if they have a value\n{ blatzk: 1000, flag: true, pick: true }\n\n$ node my-program.js --no-blatzk -fp # unless they start with \"no-\"\n{ blatzk: false, flag: true, pick: true }\n\n$ node my-program.js --baz b/a/z # known paths are resolved.\n{ baz: \"/Users/<USER>/b/a/z\" }\n\n# if Array is one of the types, then it can take many\n# values, and will always be an array.  The other types provided\n# specify what types are allowed in the list.\n\n$ node my-program.js --many1 5 --many1 null --many1 foo\n{ many1: [\"5\", \"null\", \"foo\"] }\n\n$ node my-program.js --many2 foo --many2 bar\n{ many2: [\"/path/to/foo\", \"path/to/bar\"] }\n```\n\nRead the tests at the bottom of `lib/nopt.js` for more examples of\nwhat this puppy can do.\n\n## Types\n\nThe following types are supported, and defined on `nopt.typeDefs`\n\n* String: A normal string.  No parsing is done.\n* path: A file system path.  Gets resolved against cwd if not absolute.\n* url: A url.  If it doesn't parse, it isn't accepted.\n* Number: Must be numeric.\n* Date: Must parse as a date. If it does, and `Date` is one of the options,\n  then it will return a Date object, not a string.\n* Boolean: Must be either `true` or `false`.  If an option is a boolean,\n  then it does not need a value, and its presence will imply `true` as\n  the value.  To negate boolean flags, do `--no-whatever` or `--whatever\n  false`\n* NaN: Means that the option is strictly not allowed.  Any value will\n  fail.\n* Stream: An object matching the \"Stream\" class in node.  Valuable\n  for use when validating programmatically.  (npm uses this to let you\n  supply any WriteStream on the `outfd` and `logfd` config options.)\n* Array: If `Array` is specified as one of the types, then the value\n  will be parsed as a list of options.  This means that multiple values\n  can be specified, and that the value will always be an array.\n\nIf a type is an array of values not on this list, then those are\nconsidered valid values.  For instance, in the example above, the\n`--bloo` option can only be one of `\"big\"`, `\"medium\"`, or `\"small\"`,\nand any other value will be rejected.\n\nWhen parsing unknown fields, `\"true\"`, `\"false\"`, and `\"null\"` will be\ninterpreted as their JavaScript equivalents.\n\nYou can also mix types and values, or multiple types, in a list.  For\ninstance `{ blah: [Number, null] }` would allow a value to be set to\neither a Number or null.  When types are ordered, this implies a\npreference, and the first type that can be used to properly interpret\nthe value will be used.\n\nTo define a new type, add it to `nopt.typeDefs`.  Each item in that\nhash is an object with a `type` member and a `validate` method.  The\n`type` member is an object that matches what goes in the type list.  The\n`validate` method is a function that gets called with `validate(data,\nkey, val)`.  Validate methods should assign `data[key]` to the valid\nvalue of `val` if it can be handled properly, or return boolean\n`false` if it cannot.\n\nYou can also call `nopt.clean(data, types, typeDefs)` to clean up a\nconfig object and remove its invalid properties.\n\n## Error Handling\n\nBy default nopt logs debug messages if `DEBUG_NOPT` or `NOPT_DEBUG` are set in the environment.\n\nYou can assign the following methods to `nopt` for a more granular notification of invalid, unknown, and expanding options:\n\n`nopt.invalidHandler(key, value, type, data)` - Called when a value is invalid for its option.\n`nopt.unknownHandler(key, next)` - Called when an option is found that has no configuration.  In certain situations the next option on the command line will be parsed on its own instead of as part of the unknown option. In this case `next` will contain that option.\n`nopt.abbrevHandler(short, long)` - Called when an option is automatically translated via abbreviations.\n\nYou can also set any of these to `false` to disable the debugging messages that they generate.\n\n## Abbreviations\n\nYes, they are supported.  If you define options like this:\n\n```javascript\n{ \"foolhardyelephants\" : Boolean\n, \"pileofmonkeys\" : Boolean }\n```\n\nThen this will work:\n\n```bash\nnode program.js --foolhar --pil\nnode program.js --no-f --pileofmon\n# etc.\n```\n\n## Shorthands\n\nShorthands are a hash of shorter option names to a snippet of args that\nthey expand to.\n\nIf multiple one-character shorthands are all combined, and the\ncombination does not unambiguously match any other option or shorthand,\nthen they will be broken up into their constituent parts.  For example:\n\n```json\n{ \"s\" : [\"--loglevel\", \"silent\"]\n, \"g\" : \"--global\"\n, \"f\" : \"--force\"\n, \"p\" : \"--parseable\"\n, \"l\" : \"--long\"\n}\n```\n\n```bash\nnpm ls -sgflp\n# just like doing this:\nnpm ls --loglevel silent --global --force --long --parseable\n```\n\n## The Rest of the args\n\nThe config object returned by nopt is given a special member called\n`argv`, which is an object with the following fields:\n\n* `remain`: The remaining args after all the parsing has occurred.\n* `original`: The args as they originally appeared.\n* `cooked`: The args after flags and shorthands are expanded.\n\n## Slicing\n\nNode programs are called with more or less the exact argv as it appears\nin C land, after the v8 and node-specific options have been plucked off.\nAs such, `argv[0]` is always `node` and `argv[1]` is always the\nJavaScript program being run.\n\nThat's usually not very useful to you.  So they're sliced off by\ndefault.  If you want them, then you can pass in `0` as the last\nargument, or any other number that you'd like to slice off the start of\nthe list.\n", "time": {"created": "2022-01-26T13:03:36.430Z", "modified": "2025-06-03T09:08:02.389Z", "5.0.0": "2020-08-17T08:06:31.528Z", "4.0.3": "2020-03-08T21:53:56.097Z", "4.0.2": "2020-03-08T21:52:43.958Z", "4.0.1": "2016-12-14T18:26:26.094Z", "4.0.0": "2016-12-13T23:38:42.368Z", "3.0.6": "2015-11-12T21:58:26.454Z", "3.0.5": "2015-11-12T21:23:16.377Z", "3.0.4": "2015-09-10T01:29:59.419Z", "3.0.3": "2015-06-23T01:16:18.259Z", "3.0.2": "2015-05-19T01:38:16.099Z", "3.0.1": "2014-07-01T17:12:00.014Z", "3.0.0": "2014-06-06T20:36:37.144Z", "2.2.1": "2014-04-28T21:59:11.261Z", "2.2.0": "2014-02-16T20:54:31.122Z", "2.1.2": "2013-07-17T15:24:56.574Z", "2.1.1": "2013-01-18T16:26:25.780Z", "2.1.0": "2013-01-17T20:23:13.858Z", "2.0.0": "2012-07-23T22:36:57.179Z", "1.0.10": "2011-10-05T21:47:05.876Z", "1.0.9": "2011-09-22T21:20:18.314Z", "1.0.8": "2011-09-15T21:26:19.372Z", "1.0.7": "2011-09-08T17:49:45.337Z", "1.0.6": "2011-07-06T03:49:31.397Z", "1.0.5": "2011-04-29T19:50:02.032Z", "1.0.4": "2011-03-31T04:42:56.217Z", "1.0.3": "2011-03-31T01:12:32.481Z", "1.0.2": "2011-03-31T01:07:58.593Z", "1.0.1": "2011-03-30T06:58:18.917Z", "1.0.0": "2011-03-30T03:23:56.092Z", "6.0.0": "2022-07-20T21:18:14.824Z", "7.0.0": "2022-11-02T14:33:36.290Z", "7.1.0": "2023-03-21T18:48:47.189Z", "7.2.0": "2023-06-15T16:16:31.586Z", "7.2.1": "2024-05-04T01:10:37.691Z", "8.0.0": "2024-09-04T22:12:31.585Z", "8.1.0": "2025-01-21T17:15:45.225Z"}, "versions": {"5.0.0": {"name": "nopt", "version": "5.0.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "^14.10.6"}, "engines": {"node": ">=6"}, "gitHead": "fee9d25fb8518cf202eb0bbdfa6e1c516ee8dff7", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@5.0.0", "_nodeVersion": "14.8.0", "_npmVersion": "7.0.0-beta.4", "dist": {"shasum": "530942bb58a512fccafe53fe210f13a25355dc88", "size": 9343, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-5.0.0.tgz", "integrity": "sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nopt_5.0.0_1597651591432_0.3563498258220257"}, "_hasShrinkwrap": false, "publish_time": 1597651591528, "_cnpm_publish_time": 1597651591528, "_cnpmcore_publish_time": "2021-12-13T06:49:44.501Z"}, "4.0.3": {"name": "nopt", "version": "4.0.3", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "devDependencies": {"tap": "^14.10.6"}, "gitHead": "2b1ef35dfaaee7a79a7e1b5ed83dcda2853214d4", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@4.0.3", "_nodeVersion": "13.9.0", "_npmVersion": "6.14.2", "dist": {"shasum": "a375cad9d02fd921278d954c2254d5aa57e15e48", "size": 9327, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-4.0.3.tgz", "integrity": "sha512-CvaGwVMztSMJLOeXPrez7fyfObdZqNUK1cPAEzLHrTybIua9pMdmmPR5YwtfNftIOMv3DPUhFaxsZMNTQO20Kg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nopt_4.0.3_1583704435982_0.1350573274257274"}, "_hasShrinkwrap": false, "publish_time": 1583704436097, "_cnpm_publish_time": 1583704436097, "_cnpmcore_publish_time": "2021-12-13T06:49:44.831Z"}, "4.0.2": {"name": "nopt", "version": "4.0.2", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "devDependencies": {"tap": "^14.10.6"}, "gitHead": "53f158e3edf55fc44b5275a6270abe1b1a422c5a", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@4.0.2", "_nodeVersion": "13.9.0", "_npmVersion": "6.14.2", "dist": {"shasum": "ccdb22d06b9990dab9704033bb31209dbd1f5b1e", "size": 40893, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-4.0.2.tgz", "integrity": "sha512-S+Q/CsNK7M1EFtMslCR/Xweq9WSuKy3O2fwITYEu9mtoBF/Du3yqKfiGfEdGBHhvN2BQUsPW+2fIg+7moyP6Rw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nopt_4.0.2_1583704363758_0.020218475236866817"}, "_hasShrinkwrap": false, "publish_time": 1583704363958, "_cnpm_publish_time": 1583704363958, "_cnpmcore_publish_time": "2021-12-13T06:49:45.222Z"}, "4.0.1": {"name": "nopt", "version": "4.0.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "devDependencies": {"tap": "^8.0.1"}, "gitHead": "24921187dc52825d628042c9708bbd8e8734698c", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@4.0.1", "_shasum": "d0d4685afd5415193c8c7505602d0d17cd64474d", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.2.0", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "dist": {"shasum": "d0d4685afd5415193c8c7505602d0d17cd64474d", "size": 11766, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-4.0.1.tgz", "integrity": "sha512-+5XZFpQZEY0cg5JaxLwGxDlKNKYxuXwGt8/Oi3UXm5/4ymrJve9d2CURituxv3rSrVCGZj4m1U1JlHTdcKt2Ng=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/nopt-4.0.1.tgz_1481739985863_0.18861285015009344"}, "directories": {}, "publish_time": 1481739986094, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481739986094, "_cnpmcore_publish_time": "2021-12-13T06:49:45.531Z"}, "4.0.0": {"name": "nopt", "version": "4.0.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "devDependencies": {"tap": "^8.0.1"}, "gitHead": "ae1fb2967063a47ad1f0b31a7cd335cef0f6dd6c", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@4.0.0", "_shasum": "a4f9c541d2f84e0e2288057125fefe7329836694", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.2.0", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "dist": {"shasum": "a4f9c541d2f84e0e2288057125fefe7329836694", "size": 11650, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-4.0.0.tgz", "integrity": "sha512-+iVDulYPQfPGupAup3iAmQJC+DKRqTCP3tBrnMkczFLp/VIMFQduspOmnWQzLIPsYpBCRfUvzlQU7YPTPqmQkg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/nopt-4.0.0.tgz_1481672322129_0.9774678370449692"}, "directories": {}, "publish_time": 1481672322368, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481672322368, "_cnpmcore_publish_time": "2021-12-13T06:49:45.857Z"}, "3.0.6": {"name": "nopt", "version": "3.0.6", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "^1.2.0"}, "gitHead": "10a750c9bb99c1950160353459e733ac2aa18cb6", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@3.0.6", "_shasum": "c6465dbf08abcd4db359317f79ac68a646b28ff9", "_from": ".", "_npmVersion": "2.14.10", "_nodeVersion": "4.2.1", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "dist": {"shasum": "c6465dbf08abcd4db359317f79ac68a646b28ff9", "size": 10314, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-3.0.6.tgz", "integrity": "sha512-4GUt3kSEYmk4ITxzB/b9vaIDfUVWN/Ml1Fwl11IlnIG2iaJ9O6WXZ9SrYM9NLI8OCBieN2Y8SWC2oJV0RQ7qYg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1447365506454, "_hasShrinkwrap": false, "_cnpm_publish_time": 1447365506454, "_cnpmcore_publish_time": "2021-12-13T06:49:46.163Z"}, "3.0.5": {"name": "nopt", "version": "3.0.5", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "^1.2.0"}, "gitHead": "0af9e18228fff757bcc7dccf57623423719ca255", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@3.0.5", "_shasum": "34adf7482cf70b06d24693e094c2c1e2e7fab403", "_from": ".", "_npmVersion": "2.14.10", "_nodeVersion": "4.2.1", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "dist": {"shasum": "34adf7482cf70b06d24693e094c2c1e2e7fab403", "size": 10294, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-3.0.5.tgz", "integrity": "sha512-JiJ1B7WkhiGhkdHXUrz5IOnrEqkXOxqhofyuK8t4LHKAmLcWj0JY0s2izJWEpuEi5h25S+k70EG45CKOoLvxlg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1447363396377, "_hasShrinkwrap": false, "_cnpm_publish_time": 1447363396377, "_cnpmcore_publish_time": "2021-12-13T06:49:46.545Z"}, "3.0.4": {"name": "nopt", "version": "3.0.4", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/isaacs/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "^1.2.0"}, "gitHead": "f52626631ea1afef5a6dd9acf23ddd1466831a08", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "homepage": "https://github.com/isaacs/nopt#readme", "_id": "nopt@3.0.4", "_shasum": "dd63bc9c72a6e4e85b85cdc0ca314598facede5e", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "2.2.2", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "dd63bc9c72a6e4e85b85cdc0ca314598facede5e", "size": 10169, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-3.0.4.tgz", "integrity": "sha512-VNAVhwDn16LQ7mLE6jGGuF+sXNI3Go22oCVrwZiOYvweZkH6iUpd+y+BoFOSgOHdMl3u+bDkmJcejNFw/471GQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1441848599419, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441848599419, "_cnpmcore_publish_time": "2021-12-13T06:49:46.949Z"}, "3.0.3": {"name": "nopt", "version": "3.0.3", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/isaacs/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "^1.2.0"}, "gitHead": "f64a64cd48d9f2660dd4e59191ff46a26397d6b1", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "homepage": "https://github.com/isaacs/nopt#readme", "_id": "nopt@3.0.3", "_shasum": "0e9978f33016bae0b75e3748c03bbbb71da5c530", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "0e9978f33016bae0b75e3748c03bbbb71da5c530", "size": 10166, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-3.0.3.tgz", "integrity": "sha512-oCT64BXP373m3EApJPNCr6xHmIZtsVtlbj6gBq1YqRjNIrjGz7DRvDk8te7fwh4J1UHxsqYKpq4mwa+lyq9Vqw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1435022178259, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435022178259, "_cnpmcore_publish_time": "2021-12-13T06:49:47.344Z"}, "3.0.2": {"name": "nopt", "version": "3.0.2", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+ssh://**************/isaacs/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}, "gitHead": "a0ff8dcbb29ae9da68769c9f782bd4d70746b02d", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "homepage": "https://github.com/isaacs/nopt#readme", "_id": "nopt@3.0.2", "_shasum": "a82a87f9d8c3df140fe78fb29657a7a774403b5e", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "a82a87f9d8c3df140fe78fb29657a7a774403b5e", "size": 10033, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-3.0.2.tgz", "integrity": "sha512-WoB9oKq8r03hlIeU/PvXzsE2XaI97VQMSbw+8YoybgGI/Mow+qeFi7TDpVLCFEBaAhnp8uj5f7kQsuTgTCjRFA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1431999496099, "_hasShrinkwrap": false, "_cnpm_publish_time": 1431999496099, "_cnpmcore_publish_time": "2021-12-13T06:49:47.862Z"}, "3.0.1": {"name": "nopt", "version": "3.0.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/nopt"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}, "gitHead": "4296f7aba7847c198fea2da594f9e1bec02817ec", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "homepage": "https://github.com/isaacs/nopt", "_id": "nopt@3.0.1", "_shasum": "bce5c42446a3291f47622a370abbf158fbbacbfd", "_from": ".", "_npmVersion": "1.4.18", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "bce5c42446a3291f47622a370abbf158fbbacbfd", "size": 10226, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-3.0.1.tgz", "integrity": "sha512-buf094p2Zp3BOwcjyI9V3zfZJVKkb/BpPl+3NHBoOqIv1vW6Bw24/ucbuO1zmbP+jPfdqvnq0lKB2FulpILeaA=="}, "directories": {}, "publish_time": 1404234720014, "_hasShrinkwrap": false, "_cnpm_publish_time": 1404234720014, "_cnpmcore_publish_time": "2021-12-13T06:49:48.283Z"}, "3.0.0": {"name": "nopt", "version": "3.0.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/nopt"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}, "gitHead": "b08ea1db39ca91cb5db37bc1b2fcf07e16386094", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "homepage": "https://github.com/isaacs/nopt", "_id": "nopt@3.0.0", "_shasum": "4fcf4bf09123d5ee6b2f70214a4d95789b875c79", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "4fcf4bf09123d5ee6b2f70214a4d95789b875c79", "size": 10201, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-3.0.0.tgz", "integrity": "sha512-5Ixzt7GN/YCH8TeTfJN9LE91RZ4r5MnY/FWr7Nl37ZsBkEh7W3CNZxQA4Bt1/WmmZS2vb0ZLFIl7WF1u/oj+tg=="}, "directories": {}, "publish_time": 1402086997144, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402086997144, "_cnpmcore_publish_time": "2021-12-13T06:49:48.745Z"}, "2.2.1": {"name": "nopt", "version": "2.2.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/nopt"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}, "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "homepage": "https://github.com/isaacs/nopt", "_id": "nopt@2.2.1", "_shasum": "2aa09b7d1768487b3b89a9c5aa52335bff0baea7", "_from": ".", "_npmVersion": "1.4.7", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "2aa09b7d1768487b3b89a9c5aa52335bff0baea7", "size": 10116, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-2.2.1.tgz", "integrity": "sha512-gIOTA/uJuhPwFqp+spY7VQ1satbnGlD+iQVZxI18K6hs8Evq4sX81Ml7BB5byP/LsbR2yBVtmvdEmhi7evJ6Aw=="}, "directories": {}, "publish_time": 1398722351261, "_hasShrinkwrap": false, "_cnpm_publish_time": 1398722351261, "_cnpmcore_publish_time": "2021-12-13T06:49:49.173Z"}, "2.2.0": {"name": "nopt", "version": "2.2.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/nopt"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "devDependencies": {"tap": "~0.4.8"}, "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "homepage": "https://github.com/isaacs/nopt", "_id": "nopt@2.2.0", "dist": {"shasum": "3d106676f3607ac466af9bf82bd707b1501d3bd5", "size": 9966, "noattachment": false, "tarball": "https://registry.npmmirror.com/nopt/-/nopt-2.2.0.tgz", "integrity": "sha512-r0XnozFD291fNFB5BtCY/buz/6Zl+eei8m7El86awJkIF93NphAk1gkCG3R+ZTbygtSzznwyloGGQS0KzSnkjw=="}, "_from": ".", "_npmVersion": "1.4.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1392584071122, "_hasShrinkwrap": false, "_cnpm_publish_time": 1392584071122, "_cnpmcore_publish_time": "2021-12-13T06:49:49.705Z"}, "2.1.2": {"name": "nopt", "version": "2.1.2", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/nopt"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/nopt/issues"}, "_id": "nopt@2.1.2", "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-2.1.2.tgz", "shasum": "6cccd977b80132a07731d6e8ce58c2c8303cf9af", "size": 9690, "noattachment": false, "integrity": "sha512-x8vXm7BZ2jE1Txrxh/hO74HTuYZQEbo8edoRcANgdZ4+PCV+pbjd/xdummkmjjC7LU5EjPzlu8zEq/oxWylnKA=="}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1374074696574, "_hasShrinkwrap": false, "_cnpm_publish_time": 1374074696574, "_cnpmcore_publish_time": "2021-12-13T06:49:50.127Z"}, "2.1.1": {"name": "nopt", "version": "2.1.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/nopt"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "readmeFilename": "README.md", "_id": "nopt@2.1.1", "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-2.1.1.tgz", "shasum": "91eb7c4b017e7c00adcad1fd6d63944d0fdb75c1", "size": 9648, "noattachment": false, "integrity": "sha512-iKfahTKYJbKlv1JeIV0UFT5kzNdbeKe6AY69GQWm9feJEs3/fZQkjs2fDw3T7494PDXf5U1nu1hoqwkRcLycAw=="}, "_from": ".", "_npmVersion": "1.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1358526385780, "_hasShrinkwrap": false, "_cnpm_publish_time": 1358526385780, "_cnpmcore_publish_time": "2021-12-13T06:49:50.537Z"}, "2.1.0": {"name": "nopt", "version": "2.1.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/nopt"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "readmeFilename": "README.md", "_id": "nopt@2.1.0", "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-2.1.0.tgz", "shasum": "2334c03a00c1dcb22eb1c4a4c34ebde213ee49e2", "size": 9421, "noattachment": false, "integrity": "sha512-OV/WvjV0J3Fae1Kg/fFO3NhDR7IgefaDiuAvPx/KfK8zuDATjbEKEsMTLjz+bNl+ZzwH3RxaqL/+eiAPKurvOA=="}, "_from": ".", "_npmVersion": "1.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1358454193858, "_hasShrinkwrap": false, "_cnpm_publish_time": 1358454193858, "_cnpmcore_publish_time": "2021-12-13T06:49:51.008Z"}, "2.0.0": {"name": "nopt", "version": "2.0.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/nopt"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "_id": "nopt@2.0.0", "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-2.0.0.tgz", "shasum": "ca7416f20a5e3f9c3b86180f96295fa3d0b52e0d", "size": 9208, "noattachment": false, "integrity": "sha512-uVTsuT8Hm3aN3VttY+BPKw4KU9lVpI0F22UAr/I1r6+kugMr3oyhMALkycikLcdfvGRsgzCYN48DYLBFcJEUVg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1343083017179, "_hasShrinkwrap": false, "_cnpm_publish_time": 1343083017179, "_cnpmcore_publish_time": "2021-12-13T06:49:51.557Z"}, "1.0.10": {"name": "nopt", "version": "1.0.10", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "nopt@1.0.10", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.93", "_nodeVersion": "v0.5.9-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.10.tgz", "shasum": "6ddd21bd2a31417b92727dd585f8a6f37608ebee", "size": 10240, "noattachment": false, "integrity": "sha512-NWmpvLSqUrgrAC9HCuxEvb+PSloHpqVu+FqcO4eeF2h5qYRhA7ev6KvelyQAKtegUbC6RypJnlEOhd8vloNKYg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1317851225876, "_hasShrinkwrap": false, "_cnpm_publish_time": 1317851225876, "_cnpmcore_publish_time": "2021-12-13T06:49:51.985Z"}, "1.0.9": {"name": "nopt", "version": "1.0.9", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "nopt@1.0.9", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.30", "_nodeVersion": "v0.5.8-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.9.tgz", "shasum": "3bc0d7cba7bfb0d5a676dbed7c0ebe48a4fd454e", "size": 8906, "noattachment": false, "integrity": "sha512-CmUZ3rzN0/4kRHum5pGRiGkhmBMzgtEDxrZVHqRJDSv8qK6s+wzaig/xeyB22Due5aZQeTiEZg/nrmMH2tapDQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1316726418314, "_hasShrinkwrap": false, "_cnpm_publish_time": 1316726418314, "_cnpmcore_publish_time": "2021-12-13T06:49:52.424Z"}, "1.0.8": {"name": "nopt", "version": "1.0.8", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/nopt/1.0.8/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "nopt@1.0.8", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.28-pre-DEV-UNSTABLE", "_nodeVersion": "v0.5.7-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.8.tgz", "shasum": "d4ac752df307f1a02eb771c40ed23188e7ca44c6", "size": 8802, "noattachment": false, "integrity": "sha512-JgQN7eri0sfXcEtFCXEIuOevrUZcEmJsg7gQapD4MDJJcdrcpZCduPSc38k5Fsbunw1LaP/wE03a7Zk0eTzhiQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1316121979372, "_hasShrinkwrap": false, "_cnpm_publish_time": 1316121979372, "_cnpmcore_publish_time": "2021-12-13T06:49:52.900Z"}, "1.0.7": {"name": "nopt", "version": "1.0.7", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/nopt/1.0.7/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "nopt@1.0.7", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.28-pre-DEV-UNSTABLE", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.7.tgz", "shasum": "cc72658b52a3f653a70883a1823dd8f3ddc57f75", "size": 8639, "noattachment": false, "integrity": "sha512-SH2lJYQRxmZw7Yg7DgH8JqN8Kut2c/SwT6xh5yMqZoIh+ZltZ+e9s8hQLZc4zFp8cd6LSQZ1LywJ9mNqD+bwnw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1315504185337, "_hasShrinkwrap": false, "_cnpm_publish_time": 1315504185337, "_cnpmcore_publish_time": "2021-12-13T06:49:53.448Z"}, "1.0.6": {"name": "nopt", "version": "1.0.6", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "bin": {"nopt": "./bin/nopt.js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/nopt/raw/master/LICENSE"}, "dependencies": {"abbrev": "1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/nopt/1.0.6/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "nopt@1.0.6", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.6.tgz", "shasum": "37307cafcdccf78b954ec06dcef31b936b4d03df", "size": 8243, "noattachment": false, "integrity": "sha512-dreXmuduYye7FMkIKxphhgi7V1bdAWCbaOWUfwz5Lpfyi+NQ0oazOOKa1YJGaXUlAr+28+8xw4D3VT5F+isAYg=="}, "directories": {}, "publish_time": 1309924171397, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1309924171397, "_cnpmcore_publish_time": "2021-12-13T06:49:53.932Z"}, "1.0.5": {"name": "nopt", "version": "1.0.5", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "dependencies": {"abbrev": "1"}, "devDependencies": {}, "_id": "nopt@1.0.5", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rcFINAL", "_nodeVersion": "v0.4.8-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.5.tgz", "shasum": "fc79e34a4e8862e9c413d2e1cac07ee645ac4cc8", "size": 6804, "noattachment": false, "integrity": "sha512-pO8QRDGSBdY7SZSMLcL+P/VZddO/MT29MrDLHLVw86O/jWZRWPxnHeNjmKS7yjC+I7sNhuCpTdXcezhTXz4Ofg=="}, "directories": {}, "publish_time": 1304106602032, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1304106602032, "_cnpmcore_publish_time": "2021-12-13T06:49:54.568Z"}, "1.0.4": {"name": "nopt", "version": "1.0.4", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "dependencies": {"abbrev": "1"}, "_id": "nopt@1.0.4", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rc4", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.4.tgz", "shasum": "023fc93f439094e662e2e4186345bfabda8eceda", "size": 6815, "noattachment": false, "integrity": "sha512-LDtXvd1Iez7+aqoSpLS4s1eWpEqfheV5pBqRyqZd3akZsm0mkb9IYx8RLNpm5VO/6ZAO2q4DuFsKbKxZIQtq2Q=="}, "directories": {}, "publish_time": 1301546576217, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1301546576217, "_cnpmcore_publish_time": "2021-12-13T06:49:55.243Z"}, "1.0.3": {"name": "nopt", "version": "1.0.3", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "dependencies": {"abbrev": "1"}, "_id": "nopt@1.0.3", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rc3", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.3.tgz", "shasum": "a5557211e05f4baad09bbf8e9d798072bff69166", "size": 6789, "noattachment": false, "integrity": "sha512-0RPQ4srMGu3J+z7xLd+L/Any+79zazeN1KQsm2kT9UePl2yKCfpyoTmRtLtH3+zI/wda+Ghiw9clgsQJZN7Nrg=="}, "directories": {}, "publish_time": 1301533952481, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1301533952481, "_cnpmcore_publish_time": "2021-12-13T06:49:55.861Z"}, "1.0.2": {"name": "nopt", "version": "1.0.2", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/nopt.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "dependencies": {"abbrev": "1"}, "_id": "nopt@1.0.2", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rc3", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.2.tgz", "shasum": "bb26ab771fb09411f716b122c12cd98fdc98f4d1", "size": 6788, "noattachment": false, "integrity": "sha512-df/jPsj3IzBEpTJ5m30R6hjsYhlHytYK2rZqTqFcMIf0V4hkMp8H2++8nQ1GeG45osen1UtM0dXjQPbGPGDMyg=="}, "directories": {}, "publish_time": 1301533678593, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1301533678593, "_cnpmcore_publish_time": "2021-12-13T06:49:56.379Z"}, "1.0.1": {"name": "nopt", "version": "1.0.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/optparse.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "dependencies": {"abbrev": "1"}, "_id": "nopt@1.0.1", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rc2", "_nodeVersion": "v0.4.4", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.1.tgz", "shasum": "585e38c61508b02b1ea2cc0028eef8c303079285", "size": 6756, "noattachment": false, "integrity": "sha512-5vYyxgk7nJ+GOifz3IKX6S1VqSzYCIey2Pf+8AApQvwr9OTi1KnenoYUB6f9cKDW9tavAnvvZAK4s10/Oh89gQ=="}, "directories": {}, "publish_time": 1301468298917, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1301468298917, "_cnpmcore_publish_time": "2021-12-13T06:49:56.984Z"}, "1.0.0": {"name": "nopt", "version": "1.0.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "main": "lib/nopt.js", "scripts": {"test": "node lib/optparse.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/nopt.git"}, "dependencies": {"abbrev": "1"}, "_id": "nopt@1.0.0", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rc0", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/nopt/-/nopt-1.0.0.tgz", "shasum": "a786d439b09c142dca74b0b29ef1458da50e37d8", "size": 6520, "noattachment": false, "integrity": "sha512-g4yg1XbYuPnbiq4+ylgT+E2T0EPQB4RM0u/XBBspw2p8w/9ABkwCG2itNNX2TtX53ZncZySvPizS15oc0HE4JA=="}, "directories": {}, "publish_time": 1301455436092, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1301455436092, "_cnpmcore_publish_time": "2021-12-13T06:49:57.585Z"}, "6.0.0": {"name": "nopt", "version": "6.0.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "GitHub Inc."}, "main": "lib/nopt.js", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "^1.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "tap": "^16.3.0"}, "tap": {"lines": 87, "functions": 91, "branches": 81, "statements": 87}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "3.5.0"}, "gitHead": "d4ad62dacb376e953bc61984e48998110cb37382", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@6.0.0", "_nodeVersion": "18.6.0", "_npmVersion": "8.14.0", "dist": {"integrity": "sha512-ZwLpbTgdhuZUnZzjd7nb1ZV+4DoiC6/sfiVKok72ym/4Tlf+DFdlHYmT2JPmcNNWV6Pi3SDf1kT+A4r9RTuT9g==", "shasum": "245801d8ebf409c6df22ab9d95b65e1309cdb16d", "tarball": "https://registry.npmmirror.com/nopt/-/nopt-6.0.0.tgz", "fileCount": 5, "unpackedSize": 23946, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDU1NVk8kMNveW2BV2+frDkfZTlQ7k0p8/Y47/1/ORt2AiAa5Y74MaA6iSbKq/SxcQSZ/WXa9RxNOnydDzxiwvk58A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2HEWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9hg/+IPBeHM5GKcA+Q3lqdhKzMIr0ISKi+qprqLO5WWG95+CP/rs2\r\nlfROhxbIm1oc79siFfGOAxocy86JasuCVVHK5pWVs25JM0TcUALa0TBkiQCt\r\nEthxDqRTAQQ+UVuACAcaPpMf7KlM2dvvMKbuDpCgkGjFyvUpdPhvzh/XxO1q\r\nXXJW6+G7FBxTVG3rmuvuESfgWNNNy/k7Xk4hRgBNpGahAl5AAIM6klHYHk55\r\nOmWHvzMAmv7CjdfkgvPDLyapY85gtUF5G3W22czfOgHPBuKyqhHGvQy1g6E6\r\nDIKWAjW5N95n6s19bM0MB8MwTSPZA+NXVbeqdIwdWY7OjjQS97elm4TGH8nP\r\nD6gX8Q6977DEu1VjP4U5RltHDwlJhMJ8zmdJ5jPlwWAcSzuffGAT/iH1zYlc\r\n3PFKwWyTJmt4/JtS5NLKj2+X6fQ6bKVY7v85sOon3wscvX8a1kFoN61GqtMr\r\n/pLXOc8RhXlkxvDoDC140eXJwN64Dji8xt8AunG93BJXxneB5Dcq1bDFCu4d\r\nq4nrwGCOHFmsZ6C8p/dIqWgAErZfLDFT0OZuk7poslZdJF8ITdXxb0M1BsYT\r\np6NPZqLjr52oPsOBJjLBLZ3siGE2bguonxRrh4bVPbhLGVN1TiDu6VA4fpyL\r\nAuhv107j9EzZapuIFGpWS2VNMg0hHzZV6GE=\r\n=vcSa\r\n-----END PGP SIGNATURE-----\r\n", "size": 8578}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nopt_6.0.0_1658351894630_0.16149864199157515"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-20T21:21:09.444Z"}, "7.0.0": {"name": "nopt", "version": "7.0.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "GitHub Inc."}, "main": "lib/nopt.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.8.0", "tap": "^16.3.0"}, "tap": {"lines": 87, "functions": 91, "branches": 81, "statements": 87, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.8.0"}, "gitHead": "d7a16ec25d568347269980153a65a19c0dfeace9", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@7.0.0", "_nodeVersion": "18.12.0", "_npmVersion": "9.0.1", "dist": {"integrity": "sha512-e6Qw1rcrGoSxEH0hQ4GBSdUjkMOtXGhGFXdNT/3ZR0S37eR9DMj5za3dEDWE6o1T3/DP8ZOsPP4MIiky0c3QeA==", "shasum": "09220f930c072109c98ef8aaf39e1d5f0ff9b0d4", "tarball": "https://registry.npmmirror.com/nopt/-/nopt-7.0.0.tgz", "fileCount": 5, "unpackedSize": 23895, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICkdBH9faMr/4xRVU7k0ArDYn4gA08GMeDbKQQVEjxnkAiBeIudXPJE7knZpBdYAEhi75GuyFmnpTCNzQjEhR0trfQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYn/AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrY2g//ZyLZwbNxoMToxnvF86xkiVijjmr1bXErQ1dmT5g/6RRWtq2u\r\nhVvxj8kbU/y1Hy8Gw3Jf9TYnCLWp34l87+xxjF95nwAoI7n4mmeKgmlrSEkg\r\nqgjhqbiVl5zLsHcsFlmLOZ1VgFjf2SU7Gus6jHGw2x61EcwV1NEbKFVQciNz\r\nqACb20nXSmAyqmfMbe3QwhFKY/wxV3BKpgj+jMNUD1iH0enSMTEM34Mh51Il\r\ncDfLKwVGskji2EXR6HhQ48x4GHAoxyt2e7ZKuPKUp5adnJJvgz6p/Lc5rYVr\r\nsONnP8lg3e9L8tNQMEZLgkIvAdkTjiUVj1ehYCXZJB5wwGaFqRcEFy5RAS6n\r\nA0OibDHJcxs+QMErf5DKrPvRF4NHBJQueCasyHQBbTqdQfzDH56juao1nTIf\r\nyg64/SJTk0sGkjrEoJygheiBoRAQlr0ztKuXUtANjEP/r7FMU+KbRK4GhyO3\r\nCzZ7/DJexdbyRXUMf35nPDSeWHyZakk9N7qvuVCcEDZMKjMoKqeOVq1UGYB8\r\nTuqGXvD230Vjc71crn88t8cXbWHsyO6zUudsECmw852ekd/bHgfHWHIxVIS9\r\nXK94lkPGvj1TJJdMhx1zgMK5XERmuFoiqPP1bIVAQbBfO9dolMJ3D4UvWX3l\r\nZzkSWNkztnGYUGEXUtKz1X9pWjFHDbY89+Y=\r\n=fxjA\r\n-----END PGP SIGNATURE-----\r\n", "size": 8570}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nopt_7.0.0_1667399616072_0.9472621583883882"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-02T14:53:22.667Z"}, "7.1.0": {"name": "nopt", "version": "7.1.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "GitHub Inc."}, "main": "lib/nopt.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.12.0", "tap": "^16.3.0"}, "tap": {"lines": 91, "branches": 87, "statements": 91, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.12.0"}, "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@7.1.0", "_nodeVersion": "18.13.0", "_npmVersion": "9.6.1", "dist": {"integrity": "sha512-ZFPLe9Iu0tnx7oWhFxAo4s7QTn8+NNDDxYNaKLjE7Dp0tbakQ3M1QhQzsnzXHQBTUO3K9BmwaxnyO8Ayn2I95Q==", "shasum": "91f6a3366182176e72ecab93a09c19b63b485f28", "tarball": "https://registry.npmmirror.com/nopt/-/nopt-7.1.0.tgz", "fileCount": 8, "unpackedSize": 25136, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG3tJnYBO4YfuraHWmbFvDkEmHknyGz0lh02V13YkNfUAiEAjXw1AViSTg4EOiHrVkTaSG6L1QBFV4C2uc9Hfa9HVCc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGfwPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqT0A//fk6yKKt572DJ3PSV2fArB5TSdCnffdFZl0aOWtjYNISRjEuY\r\n+7AcJ0w1nostyv0KiInqFILNK6oXjYk3Oo+4wgup+7kcWWZXMBy6/Qm4vv/2\r\niNpMwwtWkVkRm5+fx1BxqEMCkeZ1Mo9SSJFdk9ilEQrRjsJAXX9ncSpXFKyy\r\n5s5lPpSI68sDLVr51t7fO22JVxo1VhAGpSb7G//PZgwJ6/rvQCuKCyY26RRT\r\ntmu3RNdTPLaGdWNh0JeuTK+/AvieqYY6sUXyh6u3nancgvPJtqBxf7/cMPqG\r\noe4jPYh9WOnOlt9acIu3XT2fx2H1GUQQQh2z4iQjbmXo0aDNE9itiuntmfR/\r\nhsRVb32TrKN+BE71foaMNq9FrKxiv/4E8fxbZg0EynBSsBrgbTRDiDclD8IF\r\n4YO4Dfp+CuyU5bIbrGONrRC6u4+qg39fdZOEt6tFGEammPztXqyHROlz1NNd\r\nJKatynGocHLwppPHxT52vTD+QjYqkdcrA3IixRXQ6fj4vyHvqH2zKIoN7WIg\r\n4PoyY2m+2icqlMWt2hVTcsXRyscLnvwuqx+uLKpuB2lzjRT0AHpbDj0p15y4\r\n5eypiGhQPetc6zT84YzYLbz6cfq7SgE26Ez+kTzOf5Uss9dBY9D0yO3o59oJ\r\nwEzqhyMQ7256DZ4JiGmQzdMz20u6Pos9ddY=\r\n=2+2R\r\n-----END PGP SIGNATURE-----\r\n", "size": 8995}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nopt_7.1.0_1679424527041_0.8887091932430908"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-21T18:48:47.189Z", "publish_time": 1679424527189}, "7.2.0": {"name": "nopt", "version": "7.2.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "GitHub Inc."}, "main": "lib/nopt.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.15.1", "tap": "^16.3.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.15.1", "publish": true}, "gitHead": "1d7f39b0dc94bd6ccd154907f2c64456759ae215", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_id": "nopt@7.2.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.1", "dist": {"integrity": "sha512-CVDtwCdhYIvnAzFoJ6NJ6dX3oga9/HyciQDnG1vQDjSLMeKLJ4A93ZqYKDrgYSr1FBY5/hMYC+2VCi24pgpkGA==", "shasum": "067378c68116f602f552876194fd11f1292503d7", "tarball": "https://registry.npmmirror.com/nopt/-/nopt-7.2.0.tgz", "fileCount": 8, "unpackedSize": 26137, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nopt@7.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBPMY2qIFCaF0E6r6R6cn/mmemO+fr8EvP+iKxM64XEOAiAYm4AnuEnNSlGxQRO9ej6Z38/Pks1qr1kaxUJwGBy8zQ=="}], "size": 9304}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nopt_7.2.0_1686845791390_0.5960965762719304"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-15T16:16:31.586Z", "publish_time": 1686845791586, "_source_registry_name": "default"}, "7.2.1": {"name": "nopt", "version": "7.2.1", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "GitHub Inc."}, "main": "lib/nopt.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "tap": "^16.3.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.22.0", "publish": true}, "_id": "nopt@7.2.1", "gitHead": "6d33cd7a772c9594d42417c69e7adeb497b16b03", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_nodeVersion": "22.1.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-taM24ViiimT/XntxbPyJQzCG+p4EKOpgD3mxFwW38mGjVUrfERQOeY4EDHjdnptttfHuHQXFx+lTP08Q+mLa/w==", "shasum": "1cac0eab9b8e97c9093338446eddd40b2c8ca1e7", "tarball": "https://registry.npmmirror.com/nopt/-/nopt-7.2.1.tgz", "fileCount": 8, "unpackedSize": 26203, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nopt@7.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsGsywXebnr3VNxXxSfSuZKiPrOTyj3J5TmMy6Wh+dIQIgOqooSxmxbTSlbegMSG8W/S9pHAsXDSFZAdCxO9OQqaU="}], "size": 9355}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nopt_7.2.1_1714785037461_0.2372552074127241"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-04T01:10:37.691Z", "publish_time": 1714785037691, "_source_registry_name": "default"}, "8.0.0": {"name": "nopt", "version": "8.0.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "GitHub Inc."}, "main": "lib/nopt.js", "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.3.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.23.3", "publish": true}, "_id": "nopt@8.0.0", "gitHead": "a3b0bfc867502c4ca2faba36a217cb7af029227c", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-1L/fTJ4UmV/lUxT2Uf006pfZKTvAgCF+chz+0OgBHO8u2Z67pE7AaAUUj7CJy0lXqHmymUvGFt6NE9R3HER0yw==", "shasum": "644f1e78da564b70e3606ab8db4836b0e32e198a", "tarball": "https://registry.npmmirror.com/nopt/-/nopt-8.0.0.tgz", "fileCount": 8, "unpackedSize": 26225, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nopt@8.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICzSXe/kmq9q6ZkryrJvfla0DQkyKTocWcrKxEKtZnsmAiEAzQ1CCsHOkBRNvX2CNfT4Q2HpPynk7R4v+L5Rnr+IOK0="}], "size": 9357}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/nopt_8.0.0_1725487951412_0.24294595974179733"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-04T22:12:31.585Z", "publish_time": 1725487951585, "_source_registry_name": "default"}, "8.1.0": {"name": "nopt", "version": "8.1.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": {"name": "GitHub Inc."}, "main": "lib/nopt.js", "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "^3.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.6", "tap": "^16.3.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.23.6", "publish": true}, "_id": "nopt@8.1.0", "gitHead": "e8cc07120b4acb938a182fa7a1cc7453d0c4cbfa", "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "_nodeVersion": "22.12.0", "_npmVersion": "11.0.0", "dist": {"integrity": "sha512-ieGu42u/Qsa4TFktmaKEwM6MQH0pOWnaB3htzh0JRtx84+Mebc0cbZYN5bC+6WTZ4+77xrL9Pn5m7CV6VIkV7A==", "shasum": "b11d38caf0f8643ce885818518064127f602eae3", "tarball": "https://registry.npmmirror.com/nopt/-/nopt-8.1.0.tgz", "fileCount": 8, "unpackedSize": 28128, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nopt@8.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCZL81e8uR+danfKFoFUHkO//R1eMRbfTZ0ROPeVoh7LAIgLoH5iRBtSGeZjMiid2sDN4WSnBEXqAnnOqUxDDnRRFg="}], "size": 9865}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/nopt_8.1.0_1737479745037_0.14450234149419994"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-21T17:15:45.225Z", "publish_time": 1737479745225, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/npm/nopt/issues"}, "homepage": "https://github.com/npm/nopt#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "_source_registry_name": "default"}