{"_attachments": {}, "_id": "mime-db", "_rev": "5830-61f153924ce7cf8f5827d9d5", "description": "Media Type Database", "dist-tags": {"latest": "1.54.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}], "name": "mime-db", "readme": "# mime-db\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][ci-image]][ci-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nThis is a large database of mime types and information about them.\nIt consists of a single, public JSON file and does not include any logic,\nallowing it to remain as un-opinionated as possible with an API.\nIt aggregates data from the following sources:\n\n- https://www.iana.org/assignments/media-types/media-types.xhtml\n- https://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types\n- https://hg.nginx.org/nginx/raw-file/default/conf/mime.types\n\n## Installation\n\n```bash\nnpm install mime-db\n```\n\n### Database Download\n\nIf you intend to use this in a web browser, you can conveniently access the JSON file via [jsDelivr](https://www.jsdelivr.com/), a popular CDN (Content Delivery Network). To ensure stability and compatibility, it is advisable to specify [a release tag](https://github.com/jshttp/mime-db/tags) instead of using the 'master' branch. This is because the JSON file's format might change in future updates, and relying on a specific release tag will prevent potential issues arising from these changes.\n\n```\nhttps://cdn.jsdelivr.net/gh/jshttp/mime-db@master/db.json\n```\n\n## Usage\n\n```js\nvar db = require('mime-db')\n\n// grab data on .js files\nvar data = db['application/javascript']\n```\n\n## Data Structure\n\nThe JSON file is a map lookup for lowercased mime types.\nEach mime type has the following properties:\n\n- `.source` - where the mime type is defined.\n    If not set, it's probably a custom media type.\n    - `apache` - [Apache common media types](https://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types)\n    - `iana` - [IANA-defined media types](https://www.iana.org/assignments/media-types/media-types.xhtml)\n    - `nginx` - [nginx media types](https://hg.nginx.org/nginx/raw-file/default/conf/mime.types)\n- `.extensions[]` - known extensions associated with this mime type.\n- `.compressible` - whether a file of this type can be gzipped.\n- `.charset` - the default charset associated with this type, if any.\n\nIf unknown, every property could be `undefined`.\n\n## Note on MIME Type Data and Semver\n\nThis package considers the programmatic api as the semver compatibility. This means the MIME type resolution is *not* considered\nin the semver bumps. This means that if you want to pin your `mime-db` data you will need to do it in your application. While\nthis expectation was not set in docs until now, it is how the pacakge operated, so we do not feel this is a breaking change.\n\n## Contributing\n\nThe primary way to contribute to this database is by updating the data in\none of the upstream sources. The database is updated from the upstreams\nperiodically and will pull in any changes.\n\n### Registering Media Types\n\nThe best way to get new media types included in this library is to register\nthem with the IANA. The community registration procedure is outlined in\n[RFC 6838 section 5](https://tools.ietf.org/html/rfc6838#section-5). Types\nregistered with the IANA are automatically pulled into this library.\n\n### Direct Inclusion\n\nIf that is not possible / feasible, they can be added directly here as a\n\"custom\" type. To do this, it is required to have a primary source that\ndefinitively lists the media type. If an extension is going to be listed as\nassociated with this media type, the source must definitively link the\nmedia type and extension as well.\n\nTo edit the database, only make PRs against `src/custom-types.json` or\n`src/custom-suffix.json`.\n\nThe `src/custom-types.json` file is a JSON object with the MIME type as the\nkeys and the values being an object with the following keys:\n\n- `compressible` - leave out if you don't know, otherwise `true`/`false` to\n  indicate whether the data represented by the type is typically compressible.\n- `extensions` - include an array of file extensions that are associated with\n  the type.\n- `notes` - human-readable notes about the type, typically what the type is.\n- `sources` - include an array of URLs of where the MIME type and the associated\n  extensions are sourced from. This needs to be a [primary source](https://en.wikipedia.org/wiki/Primary_source);\n  links to type aggregating sites and Wikipedia are _not acceptable_.\n\nTo update the build, run `npm run build`.\n\n[ci-image]: https://badgen.net/github/checks/jshttp/mime-db/master?label=ci\n[ci-url]: https://github.com/jshttp/mime-db/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/mime-db/master\n[coveralls-url]: https://coveralls.io/r/jshttp/mime-db?branch=master\n[node-image]: https://badgen.net/npm/node/mime-db\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/mime-db\n[npm-url]: https://npmjs.org/package/mime-db\n[npm-version-image]: https://badgen.net/npm/v/mime-db\n", "time": {"created": "2022-01-26T13:58:42.434Z", "modified": "2025-05-14T14:56:53.875Z", "1.51.0": "2021-11-09T02:44:55.389Z", "1.50.0": "2021-09-16T01:37:03.130Z", "1.49.0": "2021-07-26T21:35:47.681Z", "1.48.0": "2021-05-31T03:59:02.465Z", "1.47.0": "2021-04-01T19:39:44.113Z", "1.46.0": "2021-02-13T05:37:31.854Z", "1.45.0": "2020-09-23T03:47:56.743Z", "1.44.0": "2020-04-23T00:55:24.718Z", "1.43.0": "2020-01-06T03:24:37.942Z", "1.42.0": "2019-09-26T03:31:23.896Z", "1.41.0": "2019-08-30T15:30:01.529Z", "1.40.0": "2019-04-21T01:58:25.612Z", "1.39.0": "2019-04-05T02:08:43.078Z", "1.38.0": "2019-02-05T03:20:32.705Z", "1.37.0": "2018-10-20T01:39:34.333Z", "1.36.0": "2018-08-20T15:25:10.121Z", "1.35.0": "2018-07-15T15:53:24.203Z", "1.34.0": "2018-06-03T23:39:12.219Z", "1.33.0": "2018-02-16T05:10:20.269Z", "1.32.0": "2017-11-29T21:07:28.456Z", "1.31.0": "2017-10-26T03:25:52.356Z", "1.30.0": "2017-08-28T02:28:51.133Z", "1.29.0": "2017-07-11T02:19:51.110Z", "1.28.0": "2017-05-15T05:27:52.930Z", "1.27.0": "2017-03-17T03:44:59.581Z", "1.26.0": "2017-01-15T04:04:59.107Z", "1.25.0": "2016-11-12T01:49:07.167Z", "1.24.0": "2016-09-18T11:39:54.651Z", "1.23.0": "2016-05-02T04:36:39.170Z", "1.22.0": "2016-02-15T17:53:37.862Z", "1.21.0": "2016-01-06T17:25:52.120Z", "1.20.0": "2015-11-11T05:30:10.962Z", "1.19.0": "2015-09-18T04:10:52.533Z", "1.18.0": "2015-09-03T15:44:26.171Z", "1.17.0": "2015-08-14T02:52:23.446Z", "1.16.0": "2015-07-29T16:19:33.504Z", "1.15.0": "2015-07-13T21:56:36.379Z", "1.14.0": "2015-06-26T02:12:03.398Z", "1.13.0": "2015-06-08T00:27:23.098Z", "1.12.0": "2015-06-06T03:19:37.308Z", "1.11.0": "2015-06-01T04:03:45.970Z", "1.10.0": "2015-05-20T03:02:15.863Z", "1.9.1": "2015-04-20T02:06:55.370Z", "1.9.0": "2015-04-20T01:38:12.189Z", "1.8.0": "2015-03-14T00:24:56.612Z", "1.7.0": "2015-02-09T01:52:13.439Z", "1.6.1": "2015-02-05T17:50:22.349Z", "1.6.0": "2015-01-30T03:49:26.689Z", "1.5.0": "2014-12-30T20:18:18.941Z", "1.4.0": "2014-12-22T06:23:30.189Z", "1.3.1": "2014-12-16T17:51:31.299Z", "1.3.0": "2014-12-08T04:14:15.910Z", "1.2.0": "2014-11-09T18:01:27.065Z", "1.1.2": "2014-10-24T05:57:39.626Z", "1.1.1": "2014-10-20T18:11:58.366Z", "1.1.0": "2014-09-28T21:54:48.591Z", "1.0.3": "2014-09-25T07:54:26.345Z", "1.0.2": "2014-09-25T07:47:47.870Z", "1.0.1": "2014-08-30T11:57:45.485Z", "1.0.0": "2014-08-30T11:33:45.622Z", "0.0.0": "2014-08-14T22:17:35.560Z", "1.52.0": "2022-02-21T19:41:51.123Z", "1.53.0": "2024-07-12T20:35:00.644Z", "1.54.0": "2025-03-18T15:06:44.354Z"}, "versions": {"1.51.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.51.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.16.3", "eslint": "7.32.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.25.2", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.1", "eslint-plugin-standard": "4.1.0", "gnode": "0.1.2", "mocha": "9.1.3", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "562fb6b8a4a7da6c4882490c44441dcaf3e6cc31", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.51.0", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"shasum": "d9ff62451859b18342d960850dc3cfb77e63fb0c", "size": 26765, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.51.0.tgz", "integrity": "sha512-5y8A56jg7XVQx2mbv1lu49NR4dokRnhZYTtL+KGfaa27uq4pSTXkwQkFJl4pkRMyNFz/EtYDSkiiEHx3F7UN6g=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.51.0_1636425895197_0.25054133941481327"}, "_hasShrinkwrap": false, "publish_time": 1636425895389, "_cnpm_publish_time": 1636425895389, "_cnpmcore_publish_time": "2021-12-13T12:35:01.182Z"}, "1.50.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.50.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.16.3", "eslint": "7.32.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.24.2", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.0", "eslint-plugin-standard": "4.1.0", "gnode": "0.1.2", "mocha": "9.1.1", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "84cf675f3fffeeaced20bd35971e2058356c8f57", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.50.0", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"shasum": "abd4ac94e98d3c0e185016c67ab45d5fde40c11f", "size": 26689, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.50.0.tgz", "integrity": "sha512-9tMZCDlYHqeERXEHO9f/hKfNXhre5dK2eE/krIvUjZbS2KPcqGDfNShIWS1uW9XOTKQKqK6qbeOci18rbfW77A=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.50.0_1631756222986_0.2706268478967757"}, "_hasShrinkwrap": false, "publish_time": 1631756223130, "_cnpm_publish_time": 1631756223130, "_cnpmcore_publish_time": "2021-12-13T12:35:01.513Z"}, "1.49.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.49.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.16.0", "eslint": "7.31.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.23.4", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.0", "eslint-plugin-standard": "4.1.0", "gnode": "0.1.2", "mocha": "9.0.3", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "45c8941cf2324f24f7b761ff23d6576a29fddddb", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.49.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "f3dfde60c99e9cf3bc9701d687778f537001cbed", "size": 26569, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.49.0.tgz", "integrity": "sha512-CIc8j9URtOVApSFCQIF+VBkX1RwXp/oMMOrqdyXSBXq5RWNEsRfyj1kiRnQgmNXmHxPoFIxOroKA3zcU9P+nAA=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.49.0_1627335347579_0.017607142820044608"}, "_hasShrinkwrap": false, "publish_time": 1627335347681, "_cnpm_publish_time": 1627335347681, "_cnpmcore_publish_time": "2021-12-13T12:35:01.854Z"}, "1.48.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.48.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.15.4", "eslint": "7.27.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.23.4", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.0", "eslint-plugin-standard": "4.1.0", "gnode": "0.1.2", "mocha": "8.4.0", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "ddb7fcc8b7c2e853484414ad317829ebb536fc07", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.48.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "e35b31045dd7eada3aaad537ed88a33afbef2d1d", "size": 27679, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.48.0.tgz", "integrity": "sha512-FM3QwxV+TnZYQ2aRqhlKBMHxk10lTbMt3bBkMAp54ddrNeVSfcQYOOKuGuy3Ddrm38I04If834fOUSq1yzslJQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.48.0_1622433542342_0.4935482577288639"}, "_hasShrinkwrap": false, "publish_time": 1622433542465, "_cnpm_publish_time": 1622433542465, "_cnpmcore_publish_time": "2021-12-13T12:35:02.161Z"}, "1.47.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.47.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.15.3", "eslint": "7.23.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-markdown": "2.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "gnode": "0.1.2", "mocha": "8.3.2", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "214bc5f2b3ee41aad95534b723a04f5df6b45cdd", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.47.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "8cb313e59965d3c05cfbf898915a267af46a335c", "size": 27513, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.47.0.tgz", "integrity": "sha512-QBmA/G2y+IfeS4oktet3qRZ+P5kPhCKRXxXnQEudYqUaEioAU1/Lq2us3D/t1Jfo4hE9REQPrbB7K5sOczJVIw=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.47.0_1617305983965_0.009470012054405563"}, "_hasShrinkwrap": false, "publish_time": 1617305984113, "_cnpm_publish_time": 1617305984113, "_cnpmcore_publish_time": "2021-12-13T12:35:02.527Z"}, "1.46.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.46.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.15.1", "eslint": "7.20.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "gnode": "0.1.2", "mocha": "8.3.0", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "93b1c9c90316484c682532384c493c682f4a459f", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.46.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"shasum": "6267748a7f799594de3cbc8cde91def349661cee", "size": 27500, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.46.0.tgz", "integrity": "sha512-svXaP8UQRZ5K7or+ZmfNhg2xX3yKDMUzqadsSqi4NCH/KomcH75MAMYAGVlvXn4+b/xOPhS3I2uHKRUzvjY7BQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.46.0_1613194651696_0.5250066104358107"}, "_hasShrinkwrap": false, "publish_time": 1613194651854, "_cnpm_publish_time": 1613194651854, "_cnpmcore_publish_time": "2021-12-13T12:35:02.974Z"}, "1.45.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.45.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.12.0", "eslint": "7.9.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.0", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "gnode": "0.1.2", "mocha": "8.1.3", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "258c9dcc14b689f53dd2891ccc2ee62a35fc5b22", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.45.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"shasum": "cceeda21ccd7c3a745eba2decd55d4b73e7879ea", "size": 27273, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.45.0.tgz", "integrity": "sha512-CkqLUxUk15hofLoLyljJSrukZi8mAtgd+yE5uO4tqRZsdsAJKv0O+rFMhVDRJgozy+yG6md5KwuXhD4ocIoP+w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.45.0_1600832876623_0.7047369538530679"}, "_hasShrinkwrap": false, "publish_time": 1600832876743, "_cnpm_publish_time": 1600832876743, "_cnpmcore_publish_time": "2021-12-13T12:35:03.334Z"}, "1.44.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.44.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.8.9", "eslint": "6.8.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.20.2", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "gnode": "0.1.2", "mocha": "7.1.1", "nyc": "15.0.1", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "661ef0cf02b2800c5c5c6dc273c0a0b9eb3c410b", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.44.0", "_nodeVersion": "13.12.0", "_npmVersion": "6.14.4", "dist": {"shasum": "fa11c5eb0aca1334b4233cb4d52f10c5a6272f92", "size": 27036, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.44.0.tgz", "integrity": "sha512-/NOTfLrsPBVeH7YtFPgsVWveuL+4SjjYxaQ1xtM1KMFj7HdxlBlxeyNLzhyJVx7r4rZGJAZ/6lkKCitSc/Nmpg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.44.0_1587603324599_0.4143458085398726"}, "_hasShrinkwrap": false, "publish_time": 1587603324718, "_cnpm_publish_time": 1587603324718, "_cnpmcore_publish_time": "2021-12-13T12:35:03.706Z"}, "1.43.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.43.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.8.3", "eslint": "6.8.0", "eslint-config-standard": "14.1.0", "eslint-plugin-import": "2.19.1", "eslint-plugin-node": "11.0.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "gnode": "0.1.2", "mocha": "7.0.0", "nyc": "15.0.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "c28146bfd5a10f3bc1da482e1751e7782817a60b", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.43.0", "_nodeVersion": "12.12.0", "_npmVersion": "6.11.3", "dist": {"shasum": "0a12e0502650e473d735535050e7c8f4eb4fae58", "size": 26711, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.43.0.tgz", "integrity": "sha512-+5dsGEEovYbT8UY9yD7eE4XTc4UwJ1jBYlgaQQF38ENsKR3wj/8q8RFZrF9WIZpB2V1ArTVFUva8sAul1NzRzQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.43.0_1578281077812_0.41242011371667253"}, "_hasShrinkwrap": false, "publish_time": 1578281077942, "_cnpm_publish_time": 1578281077942, "_cnpmcore_publish_time": "2021-12-13T12:35:04.052Z"}, "1.42.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.42.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.5", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.4.6", "eslint": "6.4.0", "eslint-config-standard": "14.1.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-node": "10.0.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "gnode": "0.1.2", "mocha": "6.2.0", "nyc": "14.1.1", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "102102ca893db1805b832d05c774ce5f883871cf", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.42.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.11.3", "dist": {"shasum": "3e252907b4c7adb906597b4b65636272cf9e7bac", "size": 26380, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.42.0.tgz", "integrity": "sha512-UbfJCR4UAVRNgMpfImz05smAXK7+c+ZntjaA26ANtkXLlOe947Aag5zdIcKQULAiF9Cq4WxBi9jUs5zkA84bYQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.42.0_1569468683752_0.9251934830996231"}, "_hasShrinkwrap": false, "publish_time": 1569468683896, "_cnpm_publish_time": 1569468683896, "_cnpmcore_publish_time": "2021-12-13T12:35:04.424Z"}, "1.41.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.41.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.5", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.4.5", "eslint": "6.2.2", "eslint-config-standard": "14.1.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-node": "9.2.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "gnode": "0.1.2", "mocha": "6.2.0", "nyc": "14.1.1", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "4b28620277a4d8d9cfbdc9a74cf6304a8495e464", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.41.0", "_nodeVersion": "12.9.0", "_npmVersion": "6.10.3", "dist": {"shasum": "9110408e1f6aa1b34aef51f2c9df3caddf46b6a0", "size": 26180, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.41.0.tgz", "integrity": "sha512-B5gxBI+2K431XW8C2rcc/lhppbuji67nf9v39eH8pkWoZDxnAL0PxdpH32KYRScniF8qDHBDlI+ipgg5WrCUYw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.41.0_1567179001393_0.5466457984699724"}, "_hasShrinkwrap": false, "publish_time": 1567179001529, "_cnpm_publish_time": 1567179001529, "_cnpmcore_publish_time": "2021-12-13T12:35:04.821Z"}, "1.40.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.40.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.4", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.3.4", "eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "gnode": "0.1.2", "mocha": "6.1.4", "nyc": "14.0.0", "raw-body": "2.3.3", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "4db92114124a76a38bf29b7d572d4c7da33a1261", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.40.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a65057e998db090f732a68f6c276d387d4126c32", "size": 25945, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.40.0.tgz", "integrity": "sha512-jYdeOMPy9vnxEqFRRo6ZvTZ8d9oPb+k18PKoYNYUe2stVEBPPwsln/qWzdbmaIvnhZ9v2P+CuecK+fpUfsV2mA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.40.0_1555811905445_0.9439157872850141"}, "_hasShrinkwrap": false, "publish_time": 1555811905612, "_cnpm_publish_time": 1555811905612, "_cnpmcore_publish_time": "2021-12-13T12:35:05.288Z"}, "1.39.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.39.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.4", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "3.2.0", "eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "gnode": "0.1.2", "mocha": "6.0.2", "nyc": "13.3.0", "raw-body": "2.3.3", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "8beb4223f9c8d0e36f438747359f0f6465cfd0b7", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.39.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f95a20275742f7d2ad0429acfe40f4233543780e", "size": 25893, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.39.0.tgz", "integrity": "sha512-DTsrw/iWVvwHH+9Otxccdyy0Tgiil6TWK/xhfARJZF/QFhwOgZgOIvA2/VIGpM8U7Q8z5nDmdDWC6tuVMJNibw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.39.0_1554430122853_0.8714978089749246"}, "_hasShrinkwrap": false, "publish_time": 1554430123078, "_cnpm_publish_time": 1554430123078, "_cnpmcore_publish_time": "2021-12-13T12:35:05.744Z"}, "1.38.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.38.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.3", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "3.2.0", "eslint": "5.13.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "gnode": "0.1.2", "mocha": "5.2.0", "nyc": "13.2.0", "raw-body": "2.3.3", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "73802502feea4d2ec25f8a5f1a8d4b1d64a8ed69", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.38.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1a2aab16da9eb167b49c6e4df2d9c68d63d8e2ad", "size": 25823, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.38.0.tgz", "integrity": "sha512-bqVioMFFzc2awcdJZIzR3HjZFX20QhilVS7hytkKrv7xFAn8bM1gzc/FOX2awLISvWe0PV8ptFKcon+wZ5qYkg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.38.0_1549336832525_0.9039233421831574"}, "_hasShrinkwrap": false, "publish_time": 1549336832705, "_cnpm_publish_time": 1549336832705, "_cnpmcore_publish_time": "2021-12-13T12:35:06.173Z"}, "1.37.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.37.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "2.5.0", "eslint": "5.7.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "gnode": "0.1.2", "mocha": "5.2.0", "nyc": "13.1.0", "raw-body": "2.3.3", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}, "gitHead": "a9dd53b6b9f60107d81b29d6dd31fbc08c5b9782", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.37.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0b6a0ce6fdbe9576e25f1f2d2fde8830dc0ad0d8", "size": 25548, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.37.0.tgz", "integrity": "sha512-R3C4db6bgQhlIhPU48fUtdVmKnflq+hRdad7IyKhtFj06VPNVdk2RhiYL3UjQIlso8L+YxAtFkobT0VK+S/ybg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.37.0_1539999574087_0.7412279641429007"}, "_hasShrinkwrap": false, "publish_time": 1539999574333, "_cnpm_publish_time": 1539999574333, "_cnpmcore_publish_time": "2021-12-13T12:35:06.703Z"}, "1.36.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.36.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.1", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "2.5.0", "eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.13.0", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.8.0", "eslint-plugin-standard": "3.1.0", "gnode": "0.1.2", "mocha": "1.21.5", "nyc": "11.8.0", "raw-body": "2.3.3", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}, "gitHead": "dd02f32bbd23cc2c081888754a5c1b688c6e9ebb", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.36.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5020478db3c7fe93aad7bbcc4dcf869c43363397", "size": 25535, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.36.0.tgz", "integrity": "sha512-L+xvyD9MkoYMXb1jAmzI/lWYAxAMCPvIBSWur0PZ5nOf5euahRLVqH//FKW9mWp2lkqUgYiXPgkzfMUFi4zVDw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.36.0_1534778710007_0.7707137096744558"}, "_hasShrinkwrap": false, "publish_time": 1534778710121, "_cnpm_publish_time": 1534778710121, "_cnpmcore_publish_time": "2021-12-13T12:35:07.262Z"}, "1.35.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.35.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.1", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "2.5.0", "eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.13.0", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.8.0", "eslint-plugin-standard": "3.1.0", "gnode": "0.1.2", "mocha": "1.21.5", "nyc": "11.8.0", "raw-body": "2.3.3", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}, "gitHead": "482cd6a25bbd6177de04a686d0e2a0c2465bf445", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.35.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0569d657466491283709663ad379a99b90d9ab47", "size": 25382, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.35.0.tgz", "integrity": "sha512-JWT/IcCTsB0Io3AhWUMjRqucrHSPsSf2xKLaRldJVULioggvkJvggZ3VXNNSRkCddE6D+BUI4HEIZIA2OjwIvg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.35.0_1531670004114_0.27833287879365853"}, "_hasShrinkwrap": false, "publish_time": 1531670004203, "_cnpm_publish_time": 1531670004203, "_cnpmcore_publish_time": "2021-12-13T12:35:07.797Z"}, "1.34.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.34.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.1", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "2.4.0", "eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.11.0", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.7.0", "eslint-plugin-standard": "3.1.0", "gnode": "0.1.2", "mocha": "1.21.5", "nyc": "11.8.0", "raw-body": "2.3.3", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}, "gitHead": "424fb61ca34d480d3f25dd945acc44f37c360f56", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.34.0", "_shasum": "452d0ecff5c30346a6dc1e64b1eaee0d3719ff9a", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.14.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "452d0ecff5c30346a6dc1e64b1eaee0d3719ff9a", "size": 26427, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.34.0.tgz", "integrity": "sha512-0zfriqD7aQIU3rbKGiUkMoWS+7ar3+4ukIGcz6Y4suPuYk4RqALKXj6bnQkyV6eDxr4lgSjwVPl/s5prj91Efw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.34.0_1528069151961_0.2070403796654765"}, "_hasShrinkwrap": false, "publish_time": 1528069152219, "_cnpm_publish_time": 1528069152219, "_cnpmcore_publish_time": "2021-12-13T12:35:08.403Z"}, "1.33.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.33.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.1", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.3.1", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "gnode": "0.1.2", "mocha": "1.21.5", "nyc": "11.4.1", "raw-body": "2.3.2", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}, "gitHead": "e7c849b1c70ff745a4ae456a0cd5e6be8b05c2fb", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.33.0", "_npmVersion": "5.6.0", "_nodeVersion": "6.13.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a3492050a5cb9b63450541e39d9788d2272783db", "size": 24821, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.33.0.tgz", "integrity": "sha512-BHJ/EKruNIqJf/QahvxwQZXKygOQ256myeN/Ew+THcAa5q+PjyTTMMeNQC4DZw5AwfvelsUrA6B67NKMqXDbzQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.33.0_1518757820140_0.8293249007794938"}, "_hasShrinkwrap": false, "publish_time": 1518757820269, "_cnpm_publish_time": 1518757820269, "_cnpmcore_publish_time": "2021-12-13T12:35:08.979Z"}, "1.32.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.32.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.1", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.3.1", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "gnode": "0.1.2", "mocha": "1.21.5", "nyc": "11.3.0", "raw-body": "2.3.2", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}, "gitHead": "555f55537d688a6ba935253d8d36bf270a4a0ffa", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.32.0", "_npmVersion": "5.5.1", "_nodeVersion": "6.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "485b3848b01a3cda5f968b4882c0771e58e09414", "size": 24680, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.32.0.tgz", "integrity": "sha512-+ZWo/xZN40Tt6S+HyakUxnSOgff+JEdaneLWIm0Z6LmpCn5DMcZntLyUY5c/rTDog28LhXLKOUZKoTxTCAdBVw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db-1.32.0.tgz_1511989647467_0.298956407699734"}, "directories": {}, "publish_time": 1511989648456, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511989648456, "_cnpmcore_publish_time": "2021-12-13T12:35:09.628Z"}, "1.31.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.31.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.1", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.3.1", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "gnode": "0.1.2", "mocha": "1.21.5", "nyc": "11.3.0", "raw-body": "2.3.2", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}, "gitHead": "31cd8135785c237e4a12955030b05926530102d3", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.31.0", "_npmVersion": "5.4.2", "_nodeVersion": "6.11.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a49cd8f3ebf3ed1a482b60561d9105ad40ca74cb", "size": 24624, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.31.0.tgz", "integrity": "sha512-oB3w9lx50CMd6nfonoV5rBRUbJtjMifUHaFb5MfzjC8ksAIfVjT0BsX46SjjqBz7n9JGTrTX3paIeLSK+rS5fQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db-1.31.0.tgz_1508988351387_0.7232930199243128"}, "directories": {}, "publish_time": 1508988352356, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508988352356, "_cnpmcore_publish_time": "2021-12-13T12:35:10.288Z"}, "1.30.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.30.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.0", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.2.1", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "gnode": "0.1.2", "mocha": "1.21.5", "nyc": "11.1.0", "raw-body": "2.3.0", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}, "gitHead": "e62cf46c206681ca88b2e275f442a9885f1f86e4", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.30.0", "_shasum": "74c643da2dd9d6a45399963465b26d5ca7d71f01", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "74c643da2dd9d6a45399963465b26d5ca7d71f01", "size": 24255, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.30.0.tgz", "integrity": "sha512-SUaL89ROHF5P6cwrhLxE1Xmk60cFcctcJl3zwMeQWcoQzt0Al/X8qxUz2gi19NECqYspzbYpAJryIRnLcjp20g=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db-1.30.0.tgz_1503887330099_0.8198229141999036"}, "directories": {}, "publish_time": 1503887331133, "_hasShrinkwrap": false, "_cnpm_publish_time": 1503887331133, "_cnpmcore_publish_time": "2021-12-13T12:35:10.873Z"}, "1.29.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.29.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.0", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.2.0", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.2.0", "eslint-plugin-node": "4.2.2", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "gnode": "0.1.2", "mocha": "1.21.5", "nyc": "11.0.3", "raw-body": "2.2.0", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}, "gitHead": "ee8f2459964025c3969a49b8f80c34b182d35e2f", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.29.0", "_shasum": "48d26d235589651704ac5916ca06001914266878", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "48d26d235589651704ac5916ca06001914266878", "size": 23931, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.29.0.tgz", "integrity": "sha512-7lKSppGEWkMwBxzcWKCwXreQ0GDj0fPtlwQG+jvtSMDICY1jTgYxnGmAFP35eIDmC2ldkJQdXj0Ot/j7tyahcA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db-1.29.0.tgz_1499739590002_0.7720734812319279"}, "directories": {}, "publish_time": 1499739591110, "_hasShrinkwrap": false, "_cnpm_publish_time": 1499739591110, "_cnpmcore_publish_time": "2021-12-13T12:35:11.429Z"}, "1.28.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.28.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.0", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.2.0", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.2.0", "eslint-plugin-node": "4.2.2", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "gnode": "0.1.2", "istanbul": "0.4.5", "mocha": "1.21.5", "raw-body": "2.2.0", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "f5d4f91f4fc1ba6c078b1e511df2534a10cbfffe", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.28.0", "_shasum": "fedd349be06d2865b7fc57d837c6de4f17d7ac3c", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fedd349be06d2865b7fc57d837c6de4f17d7ac3c", "size": 24696, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.28.0.tgz", "integrity": "sha512-xgdyjLGIikqOXO2g4aw0TDjd7HRHJHN8dwppYod1LqrIxjbBjPjuoC5ysZEYbn0+Mp0tTx3G5tUQrWIjnIOsog=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/mime-db-1.28.0.tgz_1494826071205_0.6150839638430625"}, "directories": {}, "publish_time": 1494826072930, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494826072930, "_cnpmcore_publish_time": "2021-12-13T12:35:12.027Z"}, "1.27.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.27.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.5.0", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.2.0", "eslint": "3.17.1", "eslint-config-standard": "7.0.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "2.1.1", "gnode": "0.1.2", "istanbul": "0.4.5", "mocha": "1.21.5", "raw-body": "2.2.0", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "c232c21378647dfbb7762410c7b025a47f114b94", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.27.0", "_shasum": "820f572296bbd20ec25ed55e5b5de869e5436eb1", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "820f572296bbd20ec25ed55e5b5de869e5436eb1", "size": 24091, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.27.0.tgz", "integrity": "sha512-DNhC90PjVkQJpLVP+ct0lmKPQWAHFy+67X8IBOx+mda/I9vsrdJO/zoyEJdQdLsofi/l8GAG+IsfB0XCPLyLHg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mime-db-1.27.0.tgz_1489722296902_0.15233952621929348"}, "directories": {}, "publish_time": 1489722299581, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489722299581, "_cnpmcore_publish_time": "2021-12-13T12:35:12.725Z"}, "1.26.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.26.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.4.7", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.1.9", "eslint": "3.13.1", "eslint-config-standard": "6.2.1", "eslint-plugin-promise": "3.3.0", "eslint-plugin-standard": "2.0.1", "gnode": "0.1.2", "istanbul": "0.4.5", "mocha": "1.21.5", "raw-body": "2.2.0", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "1d9ff30e45b07a506a20f25df3a3c7106d219e24", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.26.0", "_shasum": "eaffcd0e4fc6935cf8134da246e2e6c35305adff", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "eaffcd0e4fc6935cf8134da246e2e6c35305adff", "size": 23853, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.26.0.tgz", "integrity": "sha512-Bn4lTeTH3qxeM+u71GQVrY4AxQlqDT9jkapmEby7o6X9giHAS4U4ar/bzjkCocKAEPjP+77GmVxiYScExkiHyA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mime-db-1.26.0.tgz_1484453096877_0.39498970191925764"}, "directories": {}, "publish_time": 1484453099107, "_hasShrinkwrap": false, "_cnpm_publish_time": 1484453099107, "_cnpmcore_publish_time": "2021-12-13T12:35:13.452Z"}, "1.25.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.25.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "3.4.6", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.1.7", "eslint": "3.9.1", "eslint-config-standard": "6.2.1", "eslint-plugin-promise": "3.3.0", "eslint-plugin-standard": "2.0.1", "gnode": "0.1.2", "istanbul": "0.4.5", "mocha": "1.21.5", "raw-body": "2.1.7", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "9a2c710e347b4a7f030aae0d15afc0a06d1c8a37", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.25.0", "_shasum": "c18dbd7c73a5dbf6f44a024dc0d165a1e7b1c392", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "c18dbd7c73a5dbf6f44a024dc0d165a1e7b1c392", "size": 23677, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.25.0.tgz", "integrity": "sha512-5k547tI4Cy+Lddr/hdjNbBEWBwSl8EBc5aSdKvedav8DReADgWJzcYiktaRIw3GtGC1jjwldXtTzvqJZmtvC7w=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mime-db-1.25.0.tgz_1478915345127_0.22604371700435877"}, "directories": {}, "publish_time": 1478915347167, "_hasShrinkwrap": false, "_cnpm_publish_time": 1478915347167, "_cnpmcore_publish_time": "2021-12-13T12:35:14.199Z"}, "1.24.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.24.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.4.6", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.1.7", "gnode": "0.1.2", "istanbul": "0.4.5", "mocha": "1.21.5", "raw-body": "2.1.7", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "9dd00b34556a8cdd6f3385f09d4989298c4b86e1", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.24.0", "_shasum": "e2d13f939f0016c6e4e9ad25a8652f126c467f0c", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e2d13f939f0016c6e4e9ad25a8652f126c467f0c", "size": 23590, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.24.0.tgz", "integrity": "sha512-0XGpuLCNPqkv3vYiRjh1w6h4RbIGWyCh8OnXejta9INkFX0M8ENYth8O0As8rSGDxzEO1PafhiaqQdtqhtA2lw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-db-1.24.0.tgz_1474198792761_0.7161959335207939"}, "directories": {}, "publish_time": 1474198794651, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474198794651, "_cnpmcore_publish_time": "2021-12-13T12:35:14.893Z"}, "1.23.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.23.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.3.5", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.1.0", "gnode": "0.1.2", "istanbul": "0.4.3", "mocha": "1.21.5", "raw-body": "2.1.6", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "ba0d99fd05b3bfdc2ebcd78f858c25cb7db6af41", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.23.0", "_shasum": "a31b4070adaea27d732ea333740a64d0ec9a6659", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a31b4070adaea27d732ea333740a64d0ec9a6659", "size": 23272, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.23.0.tgz", "integrity": "sha512-lsX3UhcJITPHDXGOXSglBSPoI2UbcsWMmgX1VTaeXJ11TjjxOSE/DHrCl23zJk75odJc8MVpdZzWxdWt1Csx5Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-db-1.23.0.tgz_1462163798086_0.43938886746764183"}, "directories": {}, "publish_time": 1462163799170, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462163799170, "_cnpmcore_publish_time": "2021-12-13T12:35:15.613Z"}, "1.22.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.22.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.3.1", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.0.1", "gnode": "0.1.2", "istanbul": "0.4.2", "mocha": "1.21.5", "raw-body": "2.1.5", "stream-to-array": "2.2.1"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "ed88d32405582a5aaff6225d1210005d6be2623e", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.22.0", "_shasum": "ab23a6372dc9d86d3dc9121bd0ebd38105a1904a", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ab23a6372dc9d86d3dc9121bd0ebd38105a1904a", "size": 22947, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.22.0.tgz", "integrity": "sha512-n4fQVRPur8KsKpF9faaInRX3ZJnJ5FvuKKHKpJ5rTAQsgxbNHcG/JmSoopUTaHrjO+OqWIM5HLLVhhqAaEybFg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/mime-db-1.22.0.tgz_1455558813990_0.7830642955377698"}, "directories": {}, "publish_time": 1455558817862, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455558817862, "_cnpmcore_publish_time": "2021-12-13T12:35:16.310Z"}, "1.21.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.21.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "3.1.1", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.0.1", "gnode": "0.1.1", "istanbul": "0.4.1", "mocha": "1.21.5", "raw-body": "2.1.5", "stream-to-array": "2.2.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "9ab92f0a912a602408a64db5741dfef6f82c597f", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.21.0", "_shasum": "9b5239e3353cf6eb015a00d890261027c36d4bac", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9b5239e3353cf6eb015a00d890261027c36d4bac", "size": 22746, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.21.0.tgz", "integrity": "sha512-ZUMwgLSDVGB4nNEh59wa+ntQMXlxFhmXHlXi25NViUqe+SiIijPD8k2DC3/0GnRi1ohl5ohQbpkecoW3sab/Jw=="}, "directories": {}, "publish_time": 1452101152120, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452101152120, "_cnpmcore_publish_time": "2021-12-13T12:35:17.068Z"}, "1.20.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.20.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.10.0", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.0.0", "gnode": "0.1.1", "istanbul": "0.4.0", "mocha": "1.21.5", "raw-body": "2.1.4", "stream-to-array": "2.2.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "20c99312645c05ab8466701ede01bd5cd3ac7bc4", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.20.0", "_shasum": "496f90fd01fe0e031c8823ec3aa9450ffda18ed8", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "496f90fd01fe0e031c8823ec3aa9450ffda18ed8", "size": 22483, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.20.0.tgz", "integrity": "sha512-E<PERSON>+5Pcf8fs1yDZSisvAB68AvA67McCHqSKB9UB1nIrCX9efN6gKInT5gQX0HyWf/SZlWhTfoZQ+2sYF2TkI4A=="}, "directories": {}, "publish_time": 1447219810962, "_hasShrinkwrap": false, "_cnpm_publish_time": 1447219810962, "_cnpmcore_publish_time": "2021-12-13T12:35:17.802Z"}, "1.19.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.19.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.10.0", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.0.0", "gnode": "0.1.1", "istanbul": "0.3.20", "mocha": "1.21.5", "raw-body": "2.1.3", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "46a40f0524a01fb3075a7ecde92e8e04fc93d599", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.19.0", "_shasum": "496a18198a7ce8244534e25bb102b74fb420fd56", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "496a18198a7ce8244534e25bb102b74fb420fd56", "size": 22336, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.19.0.tgz", "integrity": "sha512-pY28EWl2RLz+bklnyeNegNYxKvzySdwTFWc8g1Ex0k1V1HfJ2wu2E+Mc7/5X1TDjdBEaMNl3Rw0By+OgpO+1Eg=="}, "directories": {}, "publish_time": 1442549452533, "_hasShrinkwrap": false, "_cnpm_publish_time": 1442549452533, "_cnpmcore_publish_time": "2021-12-13T12:35:18.537Z"}, "1.18.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.18.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.9.34", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.0.0", "gnode": "0.1.1", "istanbul": "0.3.19", "mocha": "1.21.5", "raw-body": "2.1.2", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "c48209a8786e61f20499ba14167252ad67638c5f", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.18.0", "_shasum": "5317e28224c08af1d484f60973dd386ba8f389e0", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5317e28224c08af1d484f60973dd386ba8f389e0", "size": 22280, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.18.0.tgz", "integrity": "sha512-uGdQeU9fAlL6Ku8iwYwapDOiSnF2s0liraQhGiJd5GlJLqnQsWBdH3nlBPCK3B2r4AMIscHP8KOqDVYDvX3RgQ=="}, "directories": {}, "publish_time": 1441295066171, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441295066171, "_cnpmcore_publish_time": "2021-12-13T12:35:19.350Z"}, "1.17.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.17.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.9.34", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.0.0", "gnode": "0.1.1", "istanbul": "0.3.17", "mocha": "1.21.5", "raw-body": "2.1.2", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "6525b89bd6d8f901d3c5b072741f0fbc4a4d60c3", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.17.0", "_shasum": "95bdc044092d2bcc3189dd19fbed6ed3a3f3df2a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "95bdc044092d2bcc3189dd19fbed6ed3a3f3df2a", "size": 22140, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.17.0.tgz", "integrity": "sha512-Ujt8KfNJPY39kygBhminaeHD2gppMc6KcXLQj30z5Fmj98gXrstdUl5yvWrXhNGOn5DjPUTyn1ZxYy0+ZJHQ3A=="}, "directories": {}, "publish_time": 1439520743446, "_hasShrinkwrap": false, "_cnpm_publish_time": 1439520743446, "_cnpmcore_publish_time": "2021-12-13T12:35:20.166Z"}, "1.16.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.16.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.9.34", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "0.1.4", "gnode": "0.1.1", "istanbul": "0.3.17", "mocha": "1.21.5", "raw-body": "2.1.2", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "81c7d528a1e9711084f64adbb99b70c24e8fb8c9", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.16.0", "_shasum": "e83dce4f81ca5455d29048e6c3422e9de3154f70", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e83dce4f81ca5455d29048e6c3422e9de3154f70", "size": 22047, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.16.0.tgz", "integrity": "sha512-EEJYXBPlkfM9BJZyRjtTHk3qd0t+MO6o0X231jTJrXOR7AMCtFp5UtyXPWTEgBrot15Vo/ePbRhk/pYZDChYZA=="}, "directories": {}, "publish_time": 1438186773504, "_hasShrinkwrap": false, "_cnpm_publish_time": 1438186773504, "_cnpmcore_publish_time": "2021-12-13T12:35:20.962Z"}, "1.15.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.15.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.9.33", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "0.1.3", "gnode": "0.1.1", "istanbul": "0.3.17", "mocha": "1.21.5", "raw-body": "2.1.2", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "96922b79fcaacf8c2a95ce3368739ec71c9471a2", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.15.0", "_shasum": "d219e6214bbcae23a6fa69c0868c4fadc1405e8a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d219e6214bbcae23a6fa69c0868c4fadc1405e8a", "size": 22029, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.15.0.tgz", "integrity": "sha512-u6JnCbeyuqShRSBzgr/pWdyQqMn9Zwc4RLmd9v5RgtnWDJZ6EV5BwCH+WuykJHY8G30yQzVdZDOfhjQQzkXO5A=="}, "directories": {}, "publish_time": 1436824596379, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436824596379, "_cnpmcore_publish_time": "2021-12-13T12:35:21.841Z"}, "1.14.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.14.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.9.30", "co": "4.5.4", "cogent": "1.0.1", "csv-parse": "0.1.3", "gnode": "0.1.1", "istanbul": "0.3.16", "mocha": "1.21.5", "raw-body": "2.1.1", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "9803c407b6621daba9363f534cfab18255c945a8", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.14.0", "_shasum": "d561f10b6ee66db51f94ae657a2951a74217ed83", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d561f10b6ee66db51f94ae657a2951a74217ed83", "size": 22009, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.14.0.tgz", "integrity": "sha512-01GmsNWrAeYVvfoUXOe1D/ABwa8RlJoGo+F6T4qu2JDv2epBlpuIeZOtIm75bMZaXbut2LMN1CqUHCVg5sbh1A=="}, "directories": {}, "publish_time": 1435284723398, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435284723398, "_cnpmcore_publish_time": "2021-12-13T12:35:22.728Z"}, "1.13.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.13.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.9.27", "co": "4.5.4", "cogent": "1.0.1", "csv-parse": "0.1.2", "gnode": "0.1.1", "istanbul": "0.3.14", "mocha": "1.21.5", "raw-body": "2.1.0", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "cd78635e4f8baf85d91b4edcd071f77f94a08c53", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.13.0", "_shasum": "fd6808168fe30835e7ea2205fc981d3b633e4e34", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fd6808168fe30835e7ea2205fc981d3b633e4e34", "size": 21980, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.13.0.tgz", "integrity": "sha512-SEBVmyKnXAZWWrcsUxZNylS5E9ZZnqqi4RuJAvVLtwFZWcSKLinYQuLBtsTsDEuq6WpcfSH858N9sz5z8tWBtQ=="}, "directories": {}, "publish_time": 1433723243098, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433723243098, "_cnpmcore_publish_time": "2021-12-13T12:35:23.632Z"}, "1.12.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.12.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.9.27", "co": "4.5.4", "cogent": "1.0.1", "csv-parse": "0.1.2", "gnode": "0.1.1", "istanbul": "0.3.9", "mocha": "1.21.5", "raw-body": "2.1.0", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "cf35cbba6b22f4a3b3eef9a32129ea5b7f0f91ee", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.12.0", "_shasum": "3d0c63180f458eb10d325aaa37d7c58ae312e9d7", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3d0c63180f458eb10d325aaa37d7c58ae312e9d7", "size": 21510, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.12.0.tgz", "integrity": "sha512-5aMAW7I4jZoZB27fXRuekqc4DVvJ7+hM8UcWrNj2mqibE54gXgPSonBYBdQW5hyaVNGmiYjY0ZMqn9fBefWYvA=="}, "directories": {}, "publish_time": 1433560777308, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433560777308, "_cnpmcore_publish_time": "2021-12-13T12:35:24.583Z"}, "1.11.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.11.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "2.9.27", "co": "4.5.4", "cogent": "1.0.1", "csv-parse": "0.1.2", "gnode": "0.1.1", "istanbul": "0.3.9", "mocha": "1.21.5", "raw-body": "2.1.0", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "70f7dadc39f262e1a8bdff27cefe996d0ed4e043", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.11.0", "_shasum": "658e1563b52d733a78161224b835166b457069ab", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "658e1563b52d733a78161224b835166b457069ab", "size": 21443, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.11.0.tgz", "integrity": "sha512-SvKY2AKL2bRUOrkZo/bcUkbvJurXKX2SnexKtc9zpG7yMl/0M1Hmebg0eVNOTypyPZQR4bnagtYVOyuGXhEnMw=="}, "directories": {}, "publish_time": 1433131425970, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433131425970, "_cnpmcore_publish_time": "2021-12-13T12:35:25.481Z"}, "1.10.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.10.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "~2.9.20", "co": "4.5.4", "cogent": "1.0.1", "csv-parse": "0.1.1", "gnode": "0.1.1", "istanbul": "0.3.9", "mocha": "1.21.5", "raw-body": "2.0.1", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "260552f9177fe78986b92699999f81999c7fe43c", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.10.0", "_shasum": "e6308063c758ebd12837874c3d1ea9170766b03b", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e6308063c758ebd12837874c3d1ea9170766b03b", "size": 21313, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.10.0.tgz", "integrity": "sha512-DjK7/0WjxJfKPgOZN4wWwK+ecI9rR/rEYfrJuZr5Z12BUCFCrB5dXwqa1VNxFt0Ul7NSBH6X9w65AodEV3DN1Q=="}, "directories": {}, "publish_time": 1432090935863, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432090935863, "_cnpmcore_publish_time": "2021-12-13T12:35:26.290Z"}, "1.9.1": {"name": "mime-db", "description": "Media Type Database", "version": "1.9.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "~2.9.20", "co": "4.5.1", "cogent": "1", "csv-parse": "0.1.0", "gnode": "0.1.1", "istanbul": "0.3.8", "mocha": "~1.21.4", "raw-body": "~1.3.3", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "590a8b6afceeee64b424fcd2d0d73a3bebc81685", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.9.1", "_shasum": "1431049a71791482c29f37bafc8ea2cb3a6dd3e8", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1431049a71791482c29f37bafc8ea2cb3a6dd3e8", "size": 21211, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.9.1.tgz", "integrity": "sha512-PtNmUNBDYEHKwjdF8Ma1ObpcQSMITKJe+5jJcglR7Mvl4bo9A+Z+PaJa/aLUniIP5srtxmPWtxB8oP2AZw9y6w=="}, "directories": {}, "publish_time": 1429495615370, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429495615370, "_cnpmcore_publish_time": "2021-12-13T12:35:27.137Z"}, "1.9.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.9.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "~2.9.20", "co": "4.5.1", "cogent": "1", "csv-parse": "0.1.0", "gnode": "0.1.1", "istanbul": "0.3.8", "mocha": "~1.21.4", "raw-body": "~1.3.3", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "3ab05638863bf1973b733c8a652581ea4c6b6613", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.9.0", "_shasum": "dba46663957551864f8142cc22ef364b2a9b4797", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dba46663957551864f8142cc22ef364b2a9b4797", "size": 21188, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.9.0.tgz", "integrity": "sha512-bTOKQiN3FUB+L6gobA57O6Y+yL3MdTafI6AFoVubxobl/Qg/tolKm1UwUiIqG9BjZyVKqvHf3K+Pzw2Y4ugw6A=="}, "directories": {}, "publish_time": 1429493892189, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429493892189, "_cnpmcore_publish_time": "2021-12-13T12:35:27.883Z"}, "1.8.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.8.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"bluebird": "~2.9.14", "co": "~4.4.0", "cogent": "1", "csv-parse": "0.0.9", "gnode": "0.1.1", "istanbul": "0.3.7", "mocha": "~1.21.4", "raw-body": "~1.3.3", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "cd5730a475ff03d2ef49fc571d5510a548b63494", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.8.0", "_shasum": "82a9b385f22b0f5954dec4d445faba0722c4ad25", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "82a9b385f22b0f5954dec4d445faba0722c4ad25", "size": 21102, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.8.0.tgz", "integrity": "sha512-a3JzHUfHB7xd3D8GSFi1bdq6HtQ6tS559/tqNyw5XUpLxhls8MHDTTtH6GEKi4UEsoPmtn60zIjCyDd9vTNT+g=="}, "directories": {}, "publish_time": 1426292696612, "_hasShrinkwrap": false, "_cnpm_publish_time": 1426292696612, "_cnpmcore_publish_time": "2021-12-13T12:35:28.814Z"}, "1.7.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "4", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.5", "mocha": "~1.21.4", "raw-body": "~1.3.2", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "update": "npm run fetch && npm run build"}, "gitHead": "972cc3ed48530ab7aca7a155bf2dbd1b13aa8f86", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.7.0", "_shasum": "36cf66a6c52ea71827bde287f77c254f5ef1b8d3", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "36cf66a6c52ea71827bde287f77c254f5ef1b8d3", "size": 21002, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.7.0.tgz", "integrity": "sha512-AxBDWDiuUdwOdZcMfJM0Mp+TC64U+VWCU5uGC4wVZvpG/xvl5HlptFyXBmgoNabjLpaVhxU0GdAU0dkK12n/Sg=="}, "directories": {}, "publish_time": 1423446733439, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423446733439, "_cnpmcore_publish_time": "2021-12-13T12:35:30.150Z"}, "1.6.1": {"name": "mime-db", "description": "Media Type Database", "version": "1.6.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "4", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.5", "mocha": "~1.21.4", "raw-body": "~1.3.2", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "7f07ff87267625b73dcf73b97b2530a37a85d079", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.6.1", "_shasum": "6e85cd87c961d130d6ebd37efdfc2c0e06fdfcd3", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6e85cd87c961d130d6ebd37efdfc2c0e06fdfcd3", "size": 21056, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.6.1.tgz", "integrity": "sha512-aSCBBINk3+H/xngoOj2y6aKL2RfjIVbyxp7FjAEWLcMW2jG3DsnAmKzdl9l+q2c3xcQfAuaXzVrd1lIlkIvtCA=="}, "directories": {}, "publish_time": 1423158622349, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423158622349, "_cnpmcore_publish_time": "2021-12-13T12:35:31.090Z"}, "1.6.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "4", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.5", "mocha": "~1.21.4", "raw-body": "~1.3.2", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "ca3a8540fc873694ad85dab835463c1ca3e0152a", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.6.0", "_shasum": "7453d2097b080cad8044f04e856b3408f31e1ff3", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7453d2097b080cad8044f04e856b3408f31e1ff3", "size": 20826, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.6.0.tgz", "integrity": "sha512-sSLIDySmGtQ6XwiQctsRLmLu4WTjh1EXj0bSSz+jUXlh1ZJqrT2bRrI7vAU/Af5sxboeZwcLCvklq8RO6Qg67w=="}, "directories": {}, "publish_time": 1422589766689, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422589766689, "_cnpmcore_publish_time": "2021-12-13T12:35:31.976Z"}, "1.5.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "4", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.5", "mocha": "~1.21.4", "raw-body": "~1.3.1", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "262fafb4a696cae208d6148c6642ee45bce07cb3", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.5.0", "_shasum": "bd80b576157991c3b46c71be7041fc6d5402a6ee", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bd80b576157991c3b46c71be7041fc6d5402a6ee", "size": 20738, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.5.0.tgz", "integrity": "sha512-3xQBpzQiUm7Xkdkb+86e7tR0LGclaeVogP6Q0HDogEOefew60Iklx7sJx+jFooaoBDFNMJElkhJ8qzB/i6cVXg=="}, "directories": {}, "publish_time": 1419970698941, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419970698941, "_cnpmcore_publish_time": "2021-12-13T12:35:32.939Z"}, "1.4.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.5", "mocha": "~1.21.4", "raw-body": "~1.3.1", "stream-to-array": "2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "3a452a9ab72ff6f3af7954bec77657c2d0699717", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.4.0", "_shasum": "58573481b042aca7da48bd22510891b1dbf11b03", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "58573481b042aca7da48bd22510891b1dbf11b03", "size": 20696, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.4.0.tgz", "integrity": "sha512-lzmX8CQJqVVgern1JPgE4MC7krhHfwCJgyMoizYZiAjTzdBTPybYqB20YIY8FohPWLtrB0AohSx7s9EkXKILyQ=="}, "directories": {}, "publish_time": 1419229410189, "_hasShrinkwrap": false, "_cnpm_publish_time": 1419229410189, "_cnpmcore_publish_time": "2021-12-13T12:35:33.978Z"}, "1.3.1": {"name": "mime-db", "description": "Media Type Database", "version": "1.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.5", "mocha": "~1.21.4", "stream-to-array": "2"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "98a91c9ddaf83e5c9b197d1ad26981a66e074c26", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.3.1", "_shasum": "b1cd51ee8c4a674c49e03a96d67565fc768ce941", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b1cd51ee8c4a674c49e03a96d67565fc768ce941", "size": 20022, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.3.1.tgz", "integrity": "sha512-iKx8a4LeyzujFpmHAVf4LTNNC+eoeuvlaKeAUOZVmbuwQiEXsirdfA05GuyF+eA/YzWecniLmCizVK1Ic62eqw=="}, "directories": {}, "publish_time": 1418752291299, "_hasShrinkwrap": false, "_cnpm_publish_time": 1418752291299, "_cnpmcore_publish_time": "2021-12-13T12:35:34.942Z"}, "1.3.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.4", "mocha": "~1.21.4", "stream-to-array": "2"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "dc3a4d4948e9e6814404712d0f3560f1fffe7d73", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.3.0", "_shasum": "5fefeb25dd9b097c5d45091c60f8149b98d749ec", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5fefeb25dd9b097c5d45091c60f8149b98d749ec", "size": 20002, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.3.0.tgz", "integrity": "sha512-+dkwAhvPOIHKIJypaHKfE0lqmgsqwJuxYs5eMHrq+ovj5ma5dzYf7vQIcjnSSqQa95Qui9aXQbzpqvD8rkKdFA=="}, "directories": {}, "publish_time": 1418012055910, "_hasShrinkwrap": false, "_cnpm_publish_time": 1418012055910, "_cnpmcore_publish_time": "2021-12-13T12:35:35.853Z"}, "1.2.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.2", "mocha": "~1.21.4", "stream-to-array": "2"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "caf5b253569f0267e31044602d2f1078028e2361", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.2.0", "_shasum": "76b92e7ecac673f5dab066a10b66faea1be2f01f", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "76b92e7ecac673f5dab066a10b66faea1be2f01f", "size": 19904, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.2.0.tgz", "integrity": "sha512-ZwZvp628p0pIt334fP34tLUl7zceMyemCQxf0NyjAIR1LrJnM08wCXxfwNGUVSfBDqegYud21GzhxvgdflFGIA=="}, "directories": {}, "publish_time": 1415556087065, "_hasShrinkwrap": false, "_cnpm_publish_time": 1415556087065, "_cnpmcore_publish_time": "2021-12-13T12:35:36.725Z"}, "1.1.2": {"name": "mime-db", "description": "Media Type Database", "version": "1.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.2", "mocha": "~1.21.4", "stream-to-array": "2"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "24ffc1c4bef8465cae0253448bc48437dda3124e", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.1.2", "_shasum": "893d6c510f7e3f64fc75d8a23a48401f669e7fdb", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "893d6c510f7e3f64fc75d8a23a48401f669e7fdb", "size": 19862, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.1.2.tgz", "integrity": "sha512-4NCo22vfuOzv0iHBEBirBZn4GJIM0PgT0eS3Mc1OAa4tjLvSUgrLy44iPxVW+Rgiu0AnSOVQGsJnFV0+OtQzNQ=="}, "directories": {}, "publish_time": 1414130259626, "_hasShrinkwrap": false, "_cnpm_publish_time": 1414130259626, "_cnpmcore_publish_time": "2021-12-13T12:35:37.777Z"}, "1.1.1": {"name": "mime-db", "description": "Media Type Database", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.2", "mocha": "~1.21.4", "stream-to-array": "2"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "edc213cbc9828807ea29ea3cc290b4fd485eae91", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.1.1", "_shasum": "0fc890cda05d0edadefde73d241ef7e28d110a98", "_from": ".", "_npmVersion": "2.1.4", "_nodeVersion": "0.11.14", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0fc890cda05d0edadefde73d241ef7e28d110a98", "size": 19850, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.1.1.tgz", "integrity": "sha512-5bNeht18e1RuKLVigz8aEVC7yhwFp7+ucIt7uZaHxUDnj/6QrngRfBh5CQcZvdCO6wIuugNNIxN+noZhwZ9KEA=="}, "directories": {}, "publish_time": 1413828718366, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413828718366, "_cnpmcore_publish_time": "2021-12-13T12:35:38.940Z"}, "1.1.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.2", "mocha": "~1.21.4", "stream-to-array": "2"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "7ed17588ab9c87b7f8f77f68326ef689bdfb4b61", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.1.0", "_shasum": "4613f418ab995450bf4bda240cd0ab38016a07a9", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4613f418ab995450bf4bda240cd0ab38016a07a9", "size": 19863, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.1.0.tgz", "integrity": "sha512-NlvZ2Wh2YjXEtuqLzdfmE61uFMS3pnOdVTA0xNmO3d3kJxM31r0gCSBJE56H2fBM3o7k6/IkYlPXuPJrTn4B4g=="}, "directories": {}, "publish_time": 1411941288591, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411941288591, "_cnpmcore_publish_time": "2021-12-13T12:35:39.921Z"}, "1.0.3": {"name": "mime-db", "description": "Media Type Database", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.2", "mocha": "~1.21.4", "stream-to-array": "2"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "0be986cf71b58b78dee1580f600811c0ddf2a358", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.0.3", "_shasum": "5680b12300b3cecea5620f20f269ee80f2632d81", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5680b12300b3cecea5620f20f269ee80f2632d81", "size": 19843, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.0.3.tgz", "integrity": "sha512-3uvomBfddD/PLXlNwQpXNhnS0TKQLe2Qk0sdp7krxLPJ3y8u+bIx6IBD//70o3t2cXQwSTvjNzAu/Fuz0ZY3Dw=="}, "directories": {}, "publish_time": 1411631666345, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411631666345, "_cnpmcore_publish_time": "2021-12-13T12:35:41.015Z"}, "1.0.2": {"name": "mime-db", "description": "Media Type Database", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.1.0", "istanbul": "0.3.2", "mocha": "~1.21.4", "stream-to-array": "2"}, "engine": {"node": ">= 0.6"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "7e73ecca04a6b1af7ceacb6e6434fc81a03e5620", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.0.2", "_shasum": "dfb351df979c2b59bfcab5c1e187469b6da1bba4", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dfb351df979c2b59bfcab5c1e187469b6da1bba4", "size": 19834, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.0.2.tgz", "integrity": "sha512-OCpkl8t9DgqDh95oFehR+NhmR90AnuEVEqE6enU2oy+8RJ+j11Bulou6HPfnMC3aZ9QPI6IkAzEWAj5D2QrxKA=="}, "directories": {}, "publish_time": 1411631267870, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411631267870, "_cnpmcore_publish_time": "2021-12-13T12:35:42.167Z"}, "1.0.1": {"name": "mime-db", "description": "Media Type Database", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.0.8", "istanbul": "0.3.0", "mocha": "1", "stream-to-array": "2"}, "engine": {"node": ">= 0.6.0"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "6c9ee137430015b52887901377ac2f33e21f4078", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.0.1", "_shasum": "35d99b0965967253bb30633a7d07a8de9975a952", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "35d99b0965967253bb30633a7d07a8de9975a952", "size": 19797, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.0.1.tgz", "integrity": "sha512-PtV/sUuFj50vT1hYmieMBrO6HlCy+me13yM6pnqKq0BAYbEjyWM7A2yL/n0Zz+MK4Q+KpQwWLHrOrsMfOXQB4A=="}, "directories": {}, "publish_time": 1409399865485, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409399865485, "_cnpmcore_publish_time": "2021-12-13T12:35:43.257Z"}, "1.0.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"co": "3", "cogent": "1", "csv-parse": "0", "gnode": "0.0.8", "istanbul": "0.3.0", "mocha": "1", "stream-to-array": "2"}, "engine": {"node": ">= 0.6.0"}, "files": ["LICENSE", "db.json", "index.js"], "scripts": {"update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "gitHead": "5671cb9358a9113859df3066c536ebcb1376f46a", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@1.0.0", "_shasum": "05786d9d41fd2b93065f05c7378d6be273602c06", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "05786d9d41fd2b93065f05c7378d6be273602c06", "size": 19805, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.0.0.tgz", "integrity": "sha512-Gn48in9/k1yRg8q7l5/RqEngmOl9R2zhqfyFEiGVEkv6C03IYvpGmJAEPvzgxOMaBpPHPGTiUzLztLiJdcX4Lg=="}, "directories": {}, "publish_time": 1409398425622, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409398425622, "_cnpmcore_publish_time": "2021-12-13T12:35:44.306Z"}, "0.0.0": {"name": "mime-db", "description": "Media Type Database", "version": "0.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jshttp/mime-db"}, "devDependencies": {"stream-to-array": "2", "csv-parse": "0", "cogent": "1", "mocha": "1", "co": "3"}, "scripts": {"update": "node --harmony-generators scripts/extensions; node --harmony-generators scripts/types; node scripts/build", "clean": "rm src/*", "test": "mocha --reporter spec"}, "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "main": "db.json", "files": ["db.json"], "gitHead": "482c13c51e9a564cd8e2c6e24071b562fbdc116e", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db", "_id": "mime-db@0.0.0", "_shasum": "e0d8c5ca1d3905e1287a87701cd2e003911ac4ed", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "e0d8c5ca1d3905e1287a87701cd2e003911ac4ed", "size": 18411, "noattachment": false, "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-0.0.0.tgz", "integrity": "sha512-hxzl8atSyUfOSKL+jXfqb4yhWEtIPPGot6GIkhNrRZCj4/G7lKGLK9/frwKf5nG7wcNLN3sblrWpMCNW2hJ5rQ=="}, "directories": {}, "publish_time": 1408054655560, "_hasShrinkwrap": false, "_cnpm_publish_time": 1408054655560, "_cnpmcore_publish_time": "2021-12-13T12:35:45.401Z"}, "1.52.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.52.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.16.3", "eslint": "7.32.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.1", "eslint-plugin-standard": "4.1.0", "gnode": "0.1.2", "media-typer": "1.1.0", "mocha": "9.2.1", "nyc": "15.1.0", "raw-body": "2.5.0", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "gitHead": "ebb6bf92ea39d3168a50942295d5edfdcdce641a", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_id": "mime-db@1.52.0", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "shasum": "bbabcdc02859f4987301c856e3387ce5ec43bf70", "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "fileCount": 6, "unpackedSize": 205539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiE+r/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryQA//aGj6C9ZNStdRHpyTrK4bxJL16qcl2Hp3j4wvduMNUzRj0zlk\r\nhdvMYRgl3wRE8kMMaAUUxDnoYqnFK6qRVRHkYr33uhG3UBt499yW2/XNs5fB\r\npsI1vRlVafu4zuV+kwb7KBVYCPUO2H/VYT9eZvN5mx71YQqFXlp5pv0GijdG\r\nAIs7f5w84vmZhnd0dPj0XDrOjidb37vSUKTfJKm2efBIe3wGygfHkH1hsuYQ\r\nDAgvHg3LN7auRBNU7kYZgrQK6xf2QBa3s/ZZUg/6QphdVbzyPLXf29eeswCL\r\nBnxo0C3YAYdhDLwUB17z4VYY1Lu7L5uLCGVyi5srA9JrVdUvzcmUJQWN5dfw\r\nzfW3RRQqcnArxDkWGKZUcatM8Zkn6HhrvCt//oHcw1t1vWvX+IWzkrDAX+pT\r\n3gHclJCw4y+tl9JTmFfK4XiIbHLq6Jf39kdlGZji68iz396+Z5a/fdaA6+hw\r\nqS/KvZ5QIghkuCT08r9nkqt8WXNqZlqNAWPKNG6h4sSfPNg8rkD9Ok52mxtm\r\nkj7vX9WZmbHlJzCbBTqH/jp4ELAiNnLxDSs+aodBwB2+eDYYhlqYT7UfpkDQ\r\n36orkkhUzWb86JtCQFuyy3wM3wte4SLhB4dmo4Iw/kdVrB9M5/5COw7nFHJI\r\nZoxzqeIpttW7q7BdtngI20KZqraXPJYbDI8=\r\n=XP6F\r\n-----END PGP SIGNATURE-----\r\n", "size": 26992}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.52.0_1645472510942_0.2866362639680471"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-21T19:41:59.026Z"}, "1.53.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.53.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"csv-parse": "4.16.3", "eslint": "8.32.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "got": "11.8.6", "media-typer": "1.1.0", "mocha": "10.2.0", "nyc": "15.1.0", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && node scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "_id": "mime-db@1.53.0", "gitHead": "03458dc67eac6e8a5823a6a1663fffaf59577ecf", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_nodeVersion": "22.2.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-oHlN/w+3MQ3rba9rqFr6V/ypF10LSkdwUysQL7GkXoTgIWeV+tcXGA852TBxH+gsh8UWoyhR1hKcoMJTuWflpg==", "shasum": "3cb63cd820fc29896d9d4e8c32ab4fcd74ccb447", "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.53.0.tgz", "fileCount": 6, "unpackedSize": 219029, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDW37ackGGTnZ6QRhv1MoGfPXqynQDsVBj0TLdQ5RgNIQIgSG9NR6i4osYh+ZoQwoiWHWGQLqtktvHP3IMuGb9maL4="}], "size": 28377}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-db_1.53.0_1720816500491_0.9021472348936239"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-07-12T20:35:00.644Z", "publish_time": 1720816500644, "_source_registry_name": "default"}, "1.54.0": {"name": "mime-db", "description": "Media Type Database", "version": "1.54.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "devDependencies": {"csv-parse": "4.16.3", "eslint": "8.32.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "media-typer": "1.1.0", "mocha": "10.2.0", "nyc": "15.1.0", "stream-to-array": "2.3.0", "undici": "7.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && node scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "_id": "mime-db@1.54.0", "gitHead": "5207a32f76e77ed2f63421641449f8addeacb0a5", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "homepage": "https://github.com/jshttp/mime-db#readme", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==", "shasum": "cddb3ee4f9c64530dff640236661d42cb6a314f5", "tarball": "https://registry.npmmirror.com/mime-db/-/mime-db-1.54.0.tgz", "fileCount": 6, "unpackedSize": 225566, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCQNJ57UlaKKgaBxzUo5WoRsITrTNeknrN05f96Z6VrCwIhANtVnf0QvkzIzRBhGoaP7MY007vjoouE5wcTJmBxirCB"}], "size": 29535}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/mime-db_1.54.0_1742310404154_0.19473775997612774"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-18T15:06:44.354Z", "publish_time": 1742310404354, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "homepage": "https://github.com/jshttp/mime-db#readme", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "_source_registry_name": "default"}