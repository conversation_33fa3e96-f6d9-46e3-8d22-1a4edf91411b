{"_attachments": {}, "_id": "data-uri-to-buffer", "_rev": "379-61f14471b677e08f51138020", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "description": "Create an ArrayBuffer instance from a Data URI string", "dist-tags": {"latest": "6.0.2"}, "license": "MIT", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "name": "data-uri-to-buffer", "readme": "data-uri-to-buffer\n==================\n### Create an ArrayBuffer instance from a [Data URI][rfc] string\n\nThis module accepts a [\"data\" URI][rfc] String of data, and returns\nan `ArrayBuffer` instance with the decoded data.\n\nThis module is intended to work on a large variety of JavaScript\nruntimes, including Node.js and web browsers.\n\nExample\n-------\n\n```typescript\nimport { dataUriToBuffer } from 'data-uri-to-buffer';\n\n// plain-text data is supported\nlet uri = 'data:,Hello%2C%20World!';\nlet parsed = dataUriToBuffer(uri);\nconsole.log(new TextDecoder().decode(parsed.buffer));\n// 'Hello, World!'\n\n// base64-encoded data is supported\nuri = 'data:text/plain;base64,SGVsbG8sIFdvcmxkIQ%3D%3D';\nparsed = dataUriToBuffer(uri);\nconsole.log(new TextDecoder().decode(parsed.buffer));\n// 'Hello, World!'\n```\n\n\nAPI\n---\n\n```typescript\nexport interface ParsedDataURI {\n\ttype: string;\n\ttypeFull: string;\n\tcharset: string;\n\tbuffer: ArrayBuffer;\n}\n```\n\n### dataUriToBuffer(uri: string | URL) → ParsedDataURI\n\nThe `type` property gets set to the main type portion of\nthe \"mediatype\" portion of the \"data\" URI, or defaults to `\"text/plain\"` if not\nspecified.\n\nThe `typeFull` property gets set to the entire\n\"mediatype\" portion of the \"data\" URI (including all parameters), or defaults\nto `\"text/plain;charset=US-ASCII\"` if not specified.\n\nThe `charset` property gets set to the Charset portion of\nthe \"mediatype\" portion of the \"data\" URI, or defaults to `\"US-ASCII\"` if the\nentire type is not specified, or defaults to `\"\"` otherwise.\n\n*Note*: If only the main type is specified but not the charset, e.g.\n`\"data:text/plain,abc\"`, the charset is set to the empty string. The spec only\ndefaults to US-ASCII as charset if the entire type is not specified.\n\n[rfc]: http://tools.ietf.org/html/rfc2397\n", "time": {"created": "2022-01-26T12:54:09.116Z", "modified": "2024-02-12T18:38:33.135Z", "4.0.0": "2021-09-27T10:11:39.112Z", "3.0.1": "2020-05-30T00:51:45.700Z", "3.0.0": "2019-09-27T21:51:15.948Z", "2.0.2": "2019-09-26T21:10:06.304Z", "2.0.1": "2019-04-04T18:31:54.601Z", "2.0.0": "2017-07-19T01:26:36.607Z", "1.2.0": "2017-07-19T01:25:17.266Z", "1.1.0": "2017-07-18T01:29:01.904Z", "1.0.0": "2017-06-10T00:35:08.216Z", "0.0.4": "2015-06-29T20:04:15.846Z", "0.0.3": "2014-01-09T01:12:26.219Z", "0.0.2": "2014-01-09T00:19:25.592Z", "0.0.1": "2014-01-03T00:53:10.915Z", "4.0.1": "2023-01-13T00:47:12.963Z", "5.0.0": "2023-05-04T20:33:19.663Z", "5.0.1": "2023-05-05T22:04:10.448Z", "6.0.0": "2023-09-30T14:29:51.307Z", "6.0.1": "2023-09-30T14:49:57.761Z", "6.0.2": "2024-02-12T18:24:17.563Z"}, "versions": {"4.0.0": {"name": "data-uri-to-buffer", "version": "4.0.0", "description": "Generate a Buffer instance from a Data URI string", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "engines": {"node": ">= 12"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/es6-promisify": "^5.0.0", "@types/mocha": "^9.0.0", "@types/node": "^10.5.3", "jest": "^27.2.2", "ts-jest": "^27.0.5", "typescript": "^4.4.3"}, "jest": {"preset": "ts-jest", "globals": {"ts-jest": {"diagnostics": false, "isolatedModules": true}}, "verbose": false, "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.test.ts"]}, "gitHead": "099460306e59ee2cbd9117e2b430e808ad19f5c0", "_id": "data-uri-to-buffer@4.0.0", "_nodeVersion": "12.22.6", "_npmVersion": "7.24.1", "dist": {"shasum": "b5db46aea50f6176428ac05b73be39a57701a64b", "size": 5677, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-4.0.0.tgz", "integrity": "sha512-Vr3mLBA8qWmcuschSLAOogKgQ/Jwxulv3RNE4FXnYWRGujzrRWQI4m12fQqRkwX06C0KanhLr4hK+GydchZsaA=="}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_4.0.0_1632737498941_0.6380803188362318"}, "_hasShrinkwrap": false, "publish_time": 1632737499112, "_cnpm_publish_time": 1632737499112, "_cnpmcore_publish_time": "2021-12-13T13:55:04.730Z"}, "3.0.1": {"name": "data-uri-to-buffer", "version": "3.0.1", "description": "Generate a Buffer instance from a Data URI string", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "engines": {"node": ">= 6"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/es6-promisify": "^5.0.0", "@types/mocha": "^5.2.7", "@types/node": "^10.5.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "typescript": "^3.5.3"}, "gitHead": "e39ad289e71726621466ba2cc7154a8eef452c23", "_id": "data-uri-to-buffer@3.0.1", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"shasum": "594b8973938c5bc2c33046535785341abc4f3636", "size": 3503, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-3.0.1.tgz", "integrity": "sha512-WboRycPNsVw3B3TL559F7kuBUM4d8CgMEvk6xEJlOp7OBPjt6G7z8WMWlD2rOFZLk6OYfFIUGsCOWzcQH9K2og=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_3.0.1_1590799905558_0.6459117411174389"}, "_hasShrinkwrap": false, "publish_time": 1590799905700, "_cnpm_publish_time": 1590799905700, "_cnpmcore_publish_time": "2021-12-13T13:55:05.034Z"}, "3.0.0": {"name": "data-uri-to-buffer", "version": "3.0.0", "description": "Generate a Buffer instance from a Data URI string", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "engines": {"node": ">= 6"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/buffer-from": "^1.1.0", "@types/es6-promisify": "^5.0.0", "@types/mocha": "^5.2.7", "@types/node": "^10.5.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "typescript": "^3.5.3"}, "dependencies": {"buffer-from": "^1.1.1"}, "gitHead": "e673786bdc3709a7337e1296fce0370a2ea6fed6", "_id": "data-uri-to-buffer@3.0.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "8a3088a5efd3f53c3682343313c6895d498eb8d7", "size": 5569, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-3.0.0.tgz", "integrity": "sha512-MJ6mFTZ+nPQO+39ua/ltwNePXrfdF3Ww0wP1Od7EePySXN1cP9XNqRQOG3FxTfipp8jx898LUCgBCEP11Qw/ZQ=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_3.0.0_1569621075826_0.47339569396666925"}, "_hasShrinkwrap": false, "publish_time": 1569621075948, "_cnpm_publish_time": 1569621075948, "_cnpmcore_publish_time": "2021-12-13T13:55:05.388Z"}, "2.0.2": {"name": "data-uri-to-buffer", "version": "2.0.2", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/node": "^8.0.7", "mocha": "^3.4.2"}, "gitHead": "49ca2a1b68b0080b6e037712a73cb423679a5ec6", "_id": "data-uri-to-buffer@2.0.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"shasum": "d296973d5a4897a5dbe31716d118211921f04770", "size": 4422, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-2.0.2.tgz", "integrity": "sha512-ND9qDTLc6diwj+Xe5cdAgVTbLVdXbtxTJRXRhli8Mowuaan+0EJOtdqJ0QCHNSSPyoXGx9HX2/VMnKeC34AChA=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_2.0.2_1569532206206_0.4679690854249954"}, "_hasShrinkwrap": false, "publish_time": 1569532206304, "_cnpm_publish_time": 1569532206304, "_cnpmcore_publish_time": "2021-12-13T13:55:05.770Z"}, "2.0.1": {"name": "data-uri-to-buffer", "version": "2.0.1", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "dependencies": {"@types/node": "^8.0.7"}, "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "84d6d828d8eaca38813c8ba7f11607fa558b7b2f", "_id": "data-uri-to-buffer@2.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "ca8f56fe38b1fd329473e9d1b4a9afcd8ce1c045", "size": 4407, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-2.0.1.tgz", "integrity": "sha512-OkVVLrerfAKZlW2ZZ3Ve2y65jgiWqBKsTfUIAFbn8nVbPcCZg6l6gikKlEYv0kXcmzqGm6mFq/Jf2vriuEkv8A=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_2.0.1_1554402714500_0.7907742068800752"}, "_hasShrinkwrap": false, "publish_time": 1554402714601, "_cnpm_publish_time": 1554402714601, "_cnpmcore_publish_time": "2021-12-13T13:55:06.147Z"}, "2.0.0": {"name": "data-uri-to-buffer", "version": "2.0.0", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "dependencies": {"@types/node": "^8.0.7"}, "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "ef77360329db07c3867dc5fcccfc43576f17fdf4", "_id": "data-uri-to-buffer@2.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "0ba23671727349828c32cfafddea411908d13d23", "size": 4420, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-2.0.0.tgz", "integrity": "sha512-YbKCNLPPP4inc0E5If4OaalBc7gpaM2MRv77Pv2VThVComLKfbGYtJcdDCViDyp1Wd4SebhHLz94vp91zbK6bw=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer-2.0.0.tgz_1500427596539_0.10986626683734357"}, "directories": {}, "publish_time": 1500427596607, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500427596607, "_cnpmcore_publish_time": "2021-12-13T13:55:06.505Z"}, "1.2.0": {"name": "data-uri-to-buffer", "version": "1.2.0", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "9b6aadea00c5f08d0a13246a4e3655bcd9bf86a0", "_id": "data-uri-to-buffer@1.2.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "77163ea9c20d8641b4707e8f18abdf9a78f34835", "size": 3883, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-1.2.0.tgz", "integrity": "sha512-vKQ9DTQPN1FLYiiEEOQ6IBGFqvjCa5rSK3cWMy/Nespm5d/x3dGFT9UBZnkLxCwua/IXBi2TYnwTEpsOvhC4UQ=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer-1.2.0.tgz_1500427517195_0.7155292946845293"}, "directories": {}, "publish_time": 1500427517266, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500427517266, "_cnpmcore_publish_time": "2021-12-13T13:55:06.863Z"}, "1.1.0": {"name": "data-uri-to-buffer", "version": "1.1.0", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "dependencies": {"@types/node": "^8.0.7"}, "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "08e292d692ae5e6afeecda322346f1e6d61b3cde", "_id": "data-uri-to-buffer@1.1.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "1895c5ed83cce455e382ce8d4b7301a6f4fc2915", "size": 4311, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-1.1.0.tgz", "integrity": "sha512-svaf3LJmZcURiC4637DpcWjdmO/PxCqDsSk9KGWBFsw7tlhHUSDPHGDiXFIhOnnqt2qfS22k2b2Mj/oZFxcfdA=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer-1.1.0.tgz_1500341341802_0.7833561815787107"}, "directories": {}, "publish_time": 1500341341904, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500341341904, "_cnpmcore_publish_time": "2021-12-13T13:55:07.226Z"}, "1.0.0": {"name": "data-uri-to-buffer", "version": "1.0.0", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "d02b63c39c8492fef3f22f064c4245dbdfc2f74a", "_id": "data-uri-to-buffer@1.0.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "af963806bc2bb6253f75442f13dfbee68a2a5897", "size": 3742, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-1.0.0.tgz", "integrity": "sha512-P2HdAgBLrJhnutGJkHLXqsfFOyVPWx3DTzr4WBVgv3UQVOcZmxwENbN1Zgt7QrBOGOPXoQe7/LSQMV2GMRNaWg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer-1.0.0.tgz_1497054908119_0.13749182294122875"}, "directories": {}, "publish_time": 1497054908216, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497054908216, "_cnpmcore_publish_time": "2021-12-13T13:55:07.660Z"}, "0.0.4": {"name": "data-uri-to-buffer", "version": "0.0.4", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "2"}, "gitHead": "387e133e2b016f8a62eb7df0d150455775f97ef8", "_id": "data-uri-to-buffer@0.0.4", "_shasum": "46e13ab9da8e309745c8d01ce547213ebdb2fe3f", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "46e13ab9da8e309745c8d01ce547213ebdb2fe3f", "size": 3632, "noattachment": false, "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-0.0.4.tgz", "integrity": "sha512-nntmCbCupHk2zFSWe64pTt0LJ2U6Bt3K1MWgwXiEAj9IEaowSXbGLYN7m8xCb4hbpQl8QSCRBkKT9tFRUMkU7A=="}, "publish_time": 1435608255846, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435608255846, "_cnpmcore_publish_time": "2021-12-13T13:55:08.073Z"}, "0.0.3": {"name": "data-uri-to-buffer", "version": "0.0.3", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "~1.16.2"}, "readmeFilename": "README.md", "_id": "data-uri-to-buffer@0.0.3", "dist": {"tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-0.0.3.tgz", "shasum": "18ae979a6a0ca994b0625853916d2662bbae0b1a", "size": 3143, "noattachment": false, "integrity": "sha512-Cp+jOa8QJef5nXS5hU7M1DWzXPEIoVR3kbV0dQuVGwROZg8bGf1DcCnkmajBTnvghTtSNMUdRrPjgaT6ZQucbw=="}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "publish_time": 1389229946219, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389229946219, "_cnpmcore_publish_time": "2021-12-13T13:55:08.498Z"}, "0.0.2": {"name": "data-uri-to-buffer", "version": "0.0.2", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "~1.16.2"}, "readmeFilename": "README.md", "_id": "data-uri-to-buffer@0.0.2", "dist": {"tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-0.0.2.tgz", "shasum": "63c213f89191f8ee0e728d8cc0d257935e1bac90", "size": 3059, "noattachment": false, "integrity": "sha512-df5kSO158tRSyLxnoVfUXMARPwA1j3PYOr3JucmcnDXJ6Ro1AyD0T1SIT14uKcq6g9g2KivrwTQB9AVJSTpUkg=="}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "publish_time": 1389226765592, "_hasShrinkwrap": false, "_cnpm_publish_time": 1389226765592, "_cnpmcore_publish_time": "2021-12-13T13:55:08.882Z"}, "0.0.1": {"name": "data-uri-to-buffer", "version": "0.0.1", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "~1.16.2"}, "readmeFilename": "README.md", "_id": "data-uri-to-buffer@0.0.1", "dist": {"tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-0.0.1.tgz", "shasum": "b4b10d29d88025ea71f0a815bc006613ca259469", "size": 2669, "noattachment": false, "integrity": "sha512-QPhQBQxTNXHesz2vmBsmfebX3UKWpGm8WMAkhZtglEVPLdfyaQ5126GrswcjLuhnui9Pt6Ix6ZzIOQDhMmcwsA=="}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "publish_time": 1388710390915, "_hasShrinkwrap": false, "_cnpm_publish_time": 1388710390915, "_cnpmcore_publish_time": "2021-12-13T13:55:09.295Z"}, "4.0.1": {"name": "data-uri-to-buffer", "version": "4.0.1", "description": "Generate a Buffer instance from a Data URI string", "type": "module", "exports": "./dist/index.js", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "engines": {"node": ">= 12"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^12.20.36", "jest": "^27.3.1", "ts-jest": "^27.0.7", "typescript": "^4.4.4"}, "jest": {"preset": "ts-jest", "globals": {"ts-jest": {"diagnostics": false, "isolatedModules": true}}, "verbose": false, "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.test.ts"]}, "gitHead": "85cd8c854aefbf1bb636789d80364cfac8ea1583", "_id": "data-uri-to-buffer@4.0.1", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==", "shasum": "d8feb2b2881e6a4f58c2e08acfd0e2834e26222e", "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "fileCount": 6, "unpackedSize": 10049, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB8aF0pXlnwJZiGMoMGmXhPR+2h2Tkzq9N7In8oH37z6AiEAlBfobbDNJtTxMBDTaJ2ox0kSO3yvaNzS+StuDuythUA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwKoQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPZxAAhSSUvsUlqSLH3I5/sCG2JgnzGNJ4Nu53GptVmWBh473olUtW\r\n15C1IahGUqMn/tu/lMgpmoMS7i9PNx6lkKABQyGQMoTIT5qlZ+4y6eGbS0eK\r\nEiofd9FZDbP3K9mUNpBk8oudK2Onp7L8z6c4K9pFEbHBIzNeUp3Q8dMY5LK4\r\nu5Ruq2U52AQrk8h2BSVkXjhGdJW9saEtbx17OIO1G3CcIxK4KVwjrg/59nV9\r\neY60Ba2Z+vxmByrycZJFtXiwTmj+lACTY6WQKN5LKoL9Jmfwtv7ouAwwWlkD\r\n0btf8CyGtgU20705j/OjVnKdp7yJyrzRNsRAMXX4iAGCA0sFn+ddwYTCb8sd\r\n16idSDog1GR4WLhwsfOrdouJy4qTi4vBYj//wa+jfF8kkaDwVv6i0wQ3kx6L\r\n3COFNtpKd6Jdw8kwCeBKfT5H0Bp/5Ws4iWQ/ObxV5cfhMM46k1uIz/qMeYXH\r\nG1PV3+Im4sTKsyhh07+raP5wvPSrEQqqdVdkIXKIlwLvW2CRex7K8NUn9dkn\r\n845cYU26vNXBSrboL6Hkr+uOMoZCn+nrpErbLk69/A+qmkRIxSQ2uvkZGY2M\r\n9d5R8WbQ+EqqiUdAlhQuX+ol0kWtAxB69pDNrHOqhnaWBLxEbM/o3Y6JXpNM\r\n7P5aJmNwCf4A2HJWAk7keW3m6BhKU4Sbl0E=\r\n=PPVo\r\n-----END PGP SIGNATURE-----\r\n", "size": 3680}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_4.0.1_1673570832777_0.48692261036527995"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-13T00:47:12.963Z", "publish_time": 1673570832963}, "5.0.0": {"name": "data-uri-to-buffer", "version": "5.0.0", "description": "Generate a Buffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.43", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "workspace:*", "typescript": "^5.0.4"}, "gitHead": "4d75e6cc974b2614a575e6f7a3d3c79741727dbc", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@5.0.0", "_nodeVersion": "16.20.0", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-RqYkPrQOBOGLS/OYpNwPuoyYvjKFmCePfA8SOrbCd1ohhtGNT/GSheWZzNTJlPvUWQYI9N4bcM0wbsp08AJyWw==", "shasum": "7b4e06b6292a279401c8868c047329f5d42c54ad", "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-5.0.0.tgz", "fileCount": 7, "unpackedSize": 10163, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKt4vn5uG2MGQzK26j3FJPBSZSl6kJ2mhNrpyuWzJWagIhAMInB+sxcgQwZbSCwQMH09X8SzWXwEtG8/0RNYJPV+01"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUCg//QYUFmz2mPuHhnTS2GmBol90DkqMsd4L4TaqnvwjnUOg6+IBi\r\nBSRXkTFKEObucIibTbvHnRtCHFmE950iTF7gSj7IHMaDMlQe1brCbzrfxUqL\r\njeDQfiOF1yM1k2xMoRwXZYTyXLZGj4Ony94iZrh1IbbXymUCh2pBbdFN4Us6\r\n/oEPWuXRe7VtN+TaGm56M2rugsaE3VSRskssmqC+DpNxkIPmKcTzi2sGs8aU\r\nk2k5vsr8mrKmavBIkcbfhqCbXU+ps/ShE0iN6oy/xDPboz8ehrfWXhgWaIJ6\r\nOl8c4Ta/ykE7qUmM8Pb3f9Q5mqEf6HUlIUydolSN+abdZT67biMQagnnCwNJ\r\nrRdt7duwH4lTedk0LmiM7n/cd+KPzaFd0krggPlXp6V7Js4Eijr4kkkQ+gF3\r\nOMZ7J6IwE+6UBPdod03bpDa3I86ffY9qnm0jy4Zg7medKwSHfhqA4eGMbqDZ\r\nVN884tFmgZHP0XhDpcvltC2Uh1ODDAdlhe7FFMTSLPavKz6fEv9/Q9QYz1Wm\r\ngJUnU+urPVf7zm54MSgruQWAioiKzt7CqHZlyUxGTI7fQUViwo/WtD1wg1+X\r\nOOM3qGxr3qeUlJQaQz9i9hNVj0HBr+CSccghmBewg6z9w/xZ/etHWz1bUAne\r\nS2cAHoBv3l+pXQxL3BkI7v8kT0dqf+kKbBo=\r\n=rlgJ\r\n-----END PGP SIGNATURE-----\r\n", "size": 3472}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_5.0.0_1683232399483_0.4102046430354689"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-04T20:33:19.663Z", "publish_time": 1683232399663, "_source_registry_name": "default"}, "5.0.1": {"name": "data-uri-to-buffer", "version": "5.0.1", "description": "Generate a Buffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@5.0.1", "_integrity": "sha512-a9l6T1qqDogvvnw0nKlfZzqsyikEBZBClF39V3TFoKhDtGBqHu2HkuomJc02j5zft8zrUaXEuoicLeW54RkzPg==", "_resolved": "/tmp/dace3c93ff0dd76faa9aa2a8cba1fb78/data-uri-to-buffer-5.0.1.tgz", "_from": "file:data-uri-to-buffer-5.0.1.tgz", "_nodeVersion": "20.1.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-a9l6T1qqDogvvnw0nKlfZzqsyikEBZBClF39V3TFoKhDtGBqHu2HkuomJc02j5zft8zrUaXEuoicLeW54RkzPg==", "shasum": "db89a9e279c2ffe74f50637a59a32fb23b3e4d7c", "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-5.0.1.tgz", "fileCount": 7, "unpackedSize": 10156, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJ+KvG2S3LWHkzgprmfPeRzEaLmzb1NFkQx86DgymoTAiEAjH12cTptj1o4ie5cFs3LRb8ZGkZwgUiMDNBvIjH1BM4="}], "size": 3497}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_5.0.1_1683324250263_0.6802099700141306"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-05T22:04:10.448Z", "publish_time": 1683324250448, "_source_registry_name": "default"}, "6.0.0": {"name": "data-uri-to-buffer", "version": "6.0.0", "description": "Create an ArrayBuffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@6.0.0", "_integrity": "sha512-pMT6awii4zyQu/fUdCVM798DjDxiIamF8h9WUrmRC87TCnxDyxyHoVmOH0EbGAmbCrC3aw+PfPTXK6UTSQc26g==", "_resolved": "/tmp/fde915e75b6737256e48822001ac96da/data-uri-to-buffer-6.0.0.tgz", "_from": "file:data-uri-to-buffer-6.0.0.tgz", "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-pMT6awii4zyQu/fUdCVM798DjDxiIamF8h9WUrmRC87TCnxDyxyHoVmOH0EbGAmbCrC3aw+PfPTXK6UTSQc26g==", "shasum": "80d4c58a7a9e3145dd623cfd138b5b4fec9cbc38", "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-6.0.0.tgz", "fileCount": 8, "unpackedSize": 14349, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH2iOk2diMyLCeEqRWfrBKkP4IYQbLWvTnCrbKFl/rjDAiAJjxe0ivVwX0PNHWRsIPJkgTOukU5Z7iT1klQ7tTS7Pg=="}], "size": 4217}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_6.0.0_1696084191053_0.5843435140881559"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-30T14:29:51.307Z", "publish_time": 1696084191307, "_source_registry_name": "default"}, "6.0.1": {"name": "data-uri-to-buffer", "version": "6.0.1", "description": "Create an ArrayBuffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@6.0.1", "_integrity": "sha512-MZd3VlchQkp8rdend6vrx7MmVDJzSNTBvghvKjirLkD+WTChA3KUf0jkE68Q4UyctNqI11zZO9/x2Yx+ub5Cvg==", "_resolved": "/tmp/30694727d39c5c4314bc18d1dd4d19de/data-uri-to-buffer-6.0.1.tgz", "_from": "file:data-uri-to-buffer-6.0.1.tgz", "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-MZd3VlchQkp8rdend6vrx7MmVDJzSNTBvghvKjirLkD+WTChA3KUf0jkE68Q4UyctNqI11zZO9/x2Yx+ub5Cvg==", "shasum": "540bd4c8753a25ee129035aebdedf63b078703c7", "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-6.0.1.tgz", "fileCount": 8, "unpackedSize": 14317, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcBtG+obRfoXg5RGkUXcVjgtQ+ih1HxvjMgUsYJdXusQIgOXCiFINv/1sw6/AQB5Wrx9LBgQxjEQhsUKOFfAcHmOw="}], "size": 4197}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_6.0.1_1696085397616_0.6014027081142856"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-30T14:49:57.761Z", "publish_time": 1696085397761, "_source_registry_name": "default"}, "6.0.2": {"name": "data-uri-to-buffer", "version": "6.0.2", "description": "Create an ArrayBuffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"node": "./dist/node.js", "default": "./dist/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@6.0.2", "_integrity": "sha512-7hvf7/GW8e86rW0ptuwS3OcBGDjIi6SZva7hCyWC0yYry2cOPmLIjXAUHI6DK2HsnwJd9ifmt57i8eV2n4YNpw==", "_resolved": "/tmp/fb3865a21fe72e0dc250a7262903a94e/data-uri-to-buffer-6.0.2.tgz", "_from": "file:data-uri-to-buffer-6.0.2.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-7hvf7/GW8e86rW0ptuwS3OcBGDjIi6SZva7hCyWC0yYry2cOPmLIjXAUHI6DK2HsnwJd9ifmt57i8eV2n4YNpw==", "shasum": "8a58bb67384b261a38ef18bea1810cb01badd28b", "tarball": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-6.0.2.tgz", "fileCount": 16, "unpackedSize": 16799, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRRrwO+BS1tCn0U6jL1RKkjkALy0vPnMA5m9jKvkOIEgIhALe4GtXXnoFrnEKrvzC/29aAmCi97lfoV050TkdlB6Dt"}], "size": 5039}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_6.0.2_1707762257392_0.9218302669200353"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-12T18:24:17.563Z", "publish_time": 1707762257563, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "_source_registry_name": "default"}