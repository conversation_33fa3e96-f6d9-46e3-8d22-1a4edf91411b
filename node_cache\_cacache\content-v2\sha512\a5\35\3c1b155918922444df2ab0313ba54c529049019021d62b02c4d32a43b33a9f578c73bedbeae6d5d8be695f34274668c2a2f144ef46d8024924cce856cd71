{"_attachments": {}, "_id": "agent-base", "_rev": "2945-61f14a58963ca28f5ee47354", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "description": "Turn a function into an `http.Agent` instance", "dist-tags": {"latest": "7.1.3"}, "license": "MIT", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "name": "agent-base", "readme": "agent-base\n==========\n### Turn a function into an [`http.Agent`][http.Agent] instance\n\nThis module is a thin wrapper around the base `http.Agent` class.\n\nIt provides an abstract class that must define a `connect()` function,\nwhich is responsible for creating the underlying socket that the HTTP\nclient requests will use.\n\nThe `connect()` function may return an arbitrary `Duplex` stream, or\nanother `http.Agent` instance to delegate the request to, and may be\nasynchronous (by defining an `async` function).\n\nInstances of this agent can be used with the `http` and `https`\nmodules. To differentiate, the options parameter in the `connect()`\nfunction includes a `secureEndpoint` property, which can be checked\nto determine what type of socket should be returned.\n\n#### Some subclasses:\n\nHere are some more interesting uses of `agent-base`.\nSend a pull request to list yours!\n\n * [`http-proxy-agent`][http-proxy-agent]: An HTTP(s) proxy `http.Agent` implementation for HTTP endpoints\n * [`https-proxy-agent`][https-proxy-agent]: An HTTP(s) proxy `http.Agent` implementation for HTTPS endpoints\n * [`pac-proxy-agent`][pac-proxy-agent]: A PAC file proxy `http.Agent` implementation for HTTP and HTTPS\n * [`socks-proxy-agent`][socks-proxy-agent]: A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS\n\nExample\n-------\n\nHere's a minimal example that creates a new `net.Socket` or `tls.Socket`\nbased on the `secureEndpoint` property. This agent can be used with both\nthe `http` and `https` modules.\n\n```ts\nimport * as net from 'net';\nimport * as tls from 'tls';\nimport * as http from 'http';\nimport { Agent } from 'agent-base';\n\nclass MyAgent extends Agent {\n  connect(req, opts) {\n    // `secureEndpoint` is true when using the \"https\" module\n    if (opts.secureEndpoint) {\n      return tls.connect(opts);\n    } else {\n      return net.connect(opts);\n    }\n  }\n});\n\n// Keep alive enabled means that `connect()` will only be\n// invoked when a new connection needs to be created\nconst agent = new MyAgent({ keepAlive: true });\n\n// Pass the `agent` option when creating the HTTP request\nhttp.get('http://nodejs.org/api/', { agent }, (res) => {\n  console.log('\"response\" event!', res.headers);\n  res.pipe(process.stdout);\n});\n```\n\n[http-proxy-agent]: ../http-proxy-agent\n[https-proxy-agent]: ../https-proxy-agent\n[pac-proxy-agent]: ../pac-proxy-agent\n[socks-proxy-agent]: ../socks-proxy-agent\n[http.Agent]: https://nodejs.org/api/http.html#http_class_http_agent\n", "time": {"created": "2022-01-26T13:19:20.708Z", "modified": "2024-12-08T04:04:57.052Z", "6.0.2": "2020-10-23T19:33:15.354Z", "6.0.1": "2020-07-02T23:57:24.943Z", "6.0.0": "2020-01-23T20:29:12.352Z", "5.1.1": "2019-12-11T05:16:13.169Z", "5.1.0": "2019-12-11T03:57:45.316Z", "5.0.0": "2019-12-10T19:40:42.838Z", "4.3.0": "2019-06-05T20:17:16.954Z", "4.2.1": "2018-07-05T20:45:34.771Z", "4.2.0": "2018-01-15T23:46:08.065Z", "4.1.2": "2017-11-20T17:55:50.004Z", "4.1.1": "2017-07-21T01:28:27.403Z", "4.1.0": "2017-06-27T00:15:13.928Z", "4.0.1": "2017-06-13T20:20:26.000Z", "4.0.0": "2017-06-06T23:32:31.040Z", "3.0.0": "2017-06-02T21:34:18.841Z", "2.1.1": "2017-05-30T22:08:41.402Z", "2.1.0": "2017-05-26T16:37:21.989Z", "2.0.1": "2015-09-10T18:55:12.806Z", "2.0.0": "2015-07-10T22:19:46.188Z", "1.0.2": "2015-06-28T01:24:07.948Z", "1.0.1": "2013-09-10T04:20:58.341Z", "1.0.0": "2013-09-09T23:12:46.790Z", "0.0.1": "2013-07-09T20:14:41.054Z", "7.0.0": "2023-05-04T20:33:04.125Z", "7.0.1": "2023-05-05T22:04:10.433Z", "7.0.2": "2023-05-18T19:31:24.134Z", "7.1.0": "2023-05-24T22:09:57.729Z", "7.1.1": "2024-03-30T01:31:40.164Z", "7.1.2": "2024-12-07T03:32:07.648Z", "7.1.3": "2024-12-08T04:04:11.531Z"}, "versions": {"6.0.2": {"name": "agent-base", "version": "6.0.2", "description": "Turn a function into an `http.Agent` instance", "main": "dist/src/index", "typings": "dist/src/index", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dependencies": {"debug": "4"}, "devDependencies": {"@types/debug": "4", "@types/mocha": "^5.2.7", "@types/node": "^14.0.20", "@types/semver": "^7.1.0", "@types/ws": "^6.0.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "async-listen": "^1.2.0", "cpy-cli": "^2.0.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "rimraf": "^3.0.0", "semver": "^7.1.2", "typescript": "^3.5.3", "ws": "^3.0.0"}, "engines": {"node": ">= 6.0.0"}, "gitHead": "c4b8ea2e1a11bae023bb09b708050a50418204e9", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@6.0.2", "_nodeVersion": "15.0.1", "_npmVersion": "7.0.3", "dist": {"shasum": "49fff58577cfee3f37176feab4c22e00f86d7f77", "size": 9273, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_6.0.2_1603481595243_0.4891369499229372"}, "_hasShrinkwrap": false, "publish_time": 1603481595354, "_cnpm_publish_time": 1603481595354, "_cnpmcore_publish_time": "2021-12-13T19:13:34.209Z"}, "6.0.1": {"name": "agent-base", "version": "6.0.1", "description": "Turn a function into an `http.Agent` instance", "main": "dist/src/index", "typings": "dist/src/index", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dependencies": {"debug": "4"}, "devDependencies": {"@types/debug": "4", "@types/mocha": "^5.2.7", "@types/node": "^12.12.17", "@types/semver": "^7.1.0", "@types/ws": "^6.0.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "async-listen": "^1.2.0", "cpy-cli": "^2.0.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "rimraf": "^3.0.0", "semver": "^7.1.2", "typescript": "^3.5.3", "ws": "^3.0.0"}, "engines": {"node": ">= 6.0.0"}, "gitHead": "ad9a326a01d1423390fc882b0d827918243f2093", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@6.0.1", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "dist": {"shasum": "808007e4e5867decb0ab6ab2f928fbdb5a596db4", "size": 9203, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-6.0.1.tgz", "integrity": "sha512-01q25QQDwLSsyfhrKbn8yuur+JNw0H+0Y4JiGIKd3z9aYk/w/2kxD/Upc+t2ZBBSUNff50VjPsSW2YxM8QYKVg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_6.0.1_1593734244794_0.18562676500612962"}, "_hasShrinkwrap": false, "publish_time": 1593734244943, "_cnpm_publish_time": 1593734244943, "_cnpmcore_publish_time": "2021-12-13T19:13:34.449Z"}, "6.0.0": {"name": "agent-base", "version": "6.0.0", "description": "Turn a function into an `http.Agent` instance", "main": "dist/src/index", "typings": "dist/src/index", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dependencies": {"debug": "4"}, "devDependencies": {"@types/debug": "4", "@types/mocha": "^5.2.7", "@types/node": "^12.12.17", "@types/ws": "^6.0.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "async-listen": "^1.2.0", "cpy-cli": "^2.0.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "rimraf": "^3.0.0", "typescript": "^3.5.3", "ws": "^3.0.0"}, "engines": {"node": ">= 6.0.0"}, "gitHead": "428a7b7df1d42d609c43acc072fb898fb56376d5", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@6.0.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.13.4", "dist": {"shasum": "5d0101f19bbfaed39980b22ae866de153b93f09a", "size": 7678, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-6.0.0.tgz", "integrity": "sha512-j1Q7cSCqN+AwrmDd+pzgqc0/NpC655x2bUf5ZjRIO77DcNBFmh+OgRNzF6OKdCC9RSCb19fGd99+bhXFdkRNqw=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_6.0.0_1579811352189_0.537918813505835"}, "_hasShrinkwrap": false, "publish_time": 1579811352352, "_cnpm_publish_time": 1579811352352, "_cnpmcore_publish_time": "2021-12-13T19:13:34.632Z"}, "5.1.1": {"name": "agent-base", "version": "5.1.1", "description": "Turn a function into an `http.Agent` instance", "main": "dist/src/index", "typings": "dist/src/index", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"@types/mocha": "^5.2.7", "@types/node": "^10.5.3", "@types/ws": "^6.0.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "async-listen": "^1.2.0", "cpy-cli": "^2.0.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "rimraf": "^3.0.0", "typescript": "^3.5.3", "ws": "^3.0.0"}, "engines": {"node": ">= 6.0.0"}, "gitHead": "dc309f8a5dbfc9cbc7a860118324759f7ab0818c", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@5.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "e8fb3f242959db44d63be665db7a8e739537a32c", "size": 7414, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-5.1.1.tgz", "integrity": "sha512-TMeqbNl2fMW0nMjTEPOwe3J/PRFP4vqeoNuQMG0HlMrtm5QxKqdvAkZ1pRBQ/ulIyDD5Yq0nJ7YbdD8ey0TO3g=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_5.1.1_1576041372880_0.4870527925173762"}, "_hasShrinkwrap": false, "publish_time": 1576041373169, "_cnpm_publish_time": 1576041373169, "_cnpmcore_publish_time": "2021-12-13T19:13:34.886Z"}, "5.1.0": {"name": "agent-base", "version": "5.1.0", "description": "Turn a function into an `http.Agent` instance", "main": "dist/src/index", "typings": "dist/src/index", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"@types/mocha": "^5.2.7", "@types/node": "^10.5.3", "@types/ws": "^6.0.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "async-listen": "^1.2.0", "cpy-cli": "^2.0.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "rimraf": "^3.0.0", "typescript": "^3.5.3", "ws": "^3.0.0"}, "engines": {"node": ">= 6.0.0"}, "gitHead": "3d122284f21c59917946458e56086f78ecc5add5", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@5.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "57a72a28613bcd5d2935a13dc7bd049c403ceb75", "size": 7390, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-5.1.0.tgz", "integrity": "sha512-7Fpt67pAkCNkDZZOKDKJJWPVaq2qCiDgU0n0abvGPWr3IiSuCrPtLrDPqUKWsGoUxQ0Lh7AEZ7VWZxgDyZ6uRA=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_5.1.0_1576036665194_0.28542412483549007"}, "_hasShrinkwrap": false, "publish_time": 1576036665316, "_cnpm_publish_time": 1576036665316, "_cnpmcore_publish_time": "2021-12-13T19:13:35.120Z"}, "5.0.0": {"name": "agent-base", "version": "5.0.0", "description": "Turn a function into an `http.Agent` instance", "main": "dist/src/index", "typings": "dist/src/index", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"@types/es6-promisify": "^5.0.0", "@types/mocha": "^5.2.7", "@types/node": "^10.5.3", "@types/ws": "^6.0.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "async-listen": "^1.2.0", "cpy-cli": "^2.0.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "rimraf": "^3.0.0", "typescript": "^3.5.3", "ws": "^3.0.0"}, "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 6.0.0"}, "gitHead": "512557bf07f6088594b8deb3315a4f934c81af47", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@5.0.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.13.1", "dist": {"shasum": "9b1fbf3e74b43f3cf42691d855f46886d7f80a60", "size": 6961, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-5.0.0.tgz", "integrity": "sha512-bx4LotUmXlmHABuGlTIwhcVP5U8FDqFVNGzAscDDIDf+2jfpTt219tj0XigIa9K8z1zuPCYjU6emS9YW/yExBQ=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_5.0.0_1576006842669_0.3213814953446261"}, "_hasShrinkwrap": false, "publish_time": 1576006842838, "_cnpm_publish_time": 1576006842838, "_cnpmcore_publish_time": "2021-12-13T19:13:35.311Z"}, "4.3.0": {"name": "agent-base", "version": "4.3.0", "description": "Turn a function into an `http.Agent` instance", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"@types/es6-promisify": "^5.0.0", "@types/node": "^10.5.3", "mocha": "^3.4.2", "ws": "^3.0.0"}, "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "560f111674af84dec46a4c3070ddf3b22edd3e76", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@4.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "8165f01c436009bccad0b1d122f05ed770efc6ee", "size": 10668, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-4.3.0.tgz", "integrity": "sha512-salcGninV0nPrwpGNn4VTXBb1SOuXQBiqbrNXoeizJsHrsL6ERFM2Ne3JUSBWRE6aeNJI2ROP/WEEIDUiDe3cg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_4.3.0_1559765836773_0.2712931340824465"}, "_hasShrinkwrap": false, "publish_time": 1559765836954, "_cnpm_publish_time": 1559765836954, "_cnpmcore_publish_time": "2021-12-13T19:13:35.488Z"}, "4.2.1": {"name": "agent-base", "version": "4.2.1", "description": "Turn a function into an `http.Agent` instance", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "^3.4.2", "ws": "^3.0.0"}, "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "7ea2dde4c21f2f5cfe071e99335d35b9c0a1403e", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@4.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "d89e5999f797875674c07d87f260fc41e83e8ca9", "size": 9952, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-4.2.1.tgz", "integrity": "sha512-JVwXMr9nHYTUXsBFKUqhJwvlcYU/blreOEUkhNR2eXZIvwd+c+o5V4MgDPKWnMS/56awN3TRzIP+KoPn+roQtg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_4.2.1_1530823534659_0.9208504260347914"}, "_hasShrinkwrap": false, "publish_time": 1530823534771, "_cnpm_publish_time": 1530823534771, "_cnpmcore_publish_time": "2021-12-13T19:13:35.689Z"}, "4.2.0": {"name": "agent-base", "version": "4.2.0", "description": "Turn a function into an `http.Agent` instance", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "^3.4.2", "ws": "^3.0.0"}, "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "35b49daefc0e0cb165dd1b235d8d125413fc4dfe", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@4.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "9838b5c3392b962bad031e6a4c5e1024abec45ce", "size": 9869, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-4.2.0.tgz", "integrity": "sha512-c+R/U5X+2zz2+UCrCFv6odQzJdoqI+YecuhnAJLa1zYaMc13zPfwMwZrr91Pd1DYNo/yPRbiM4WVf9whgwFsIg=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base-4.2.0.tgz_1516059967997_0.4326384493615478"}, "directories": {}, "publish_time": 1516059968065, "_hasShrinkwrap": false, "_cnpm_publish_time": 1516059968065, "_cnpmcore_publish_time": "2021-12-13T19:13:35.899Z"}, "4.1.2": {"name": "agent-base", "version": "4.1.2", "description": "Turn a function into an `http.Agent` instance", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "^3.4.2", "ws": "^3.0.0"}, "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "1b3c8c9bf228ee4f3fe085a454e4912cee55b739", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@4.1.2", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "80fa6cde440f4dcf9af2617cf246099b5d99f0c8", "size": 9518, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-4.1.2.tgz", "integrity": "sha512-VE6QoEdaugY86BohRtfGmTDabxdU5sCKOkbcPA6PXKJsRzEi/7A3RCTxJal1ft/4qSfPht5/iQLhMh/wzSkkNw=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base-4.1.2.tgz_1511200549900_0.9417378406506032"}, "directories": {}, "publish_time": 1511200550004, "_hasShrinkwrap": false, "_cnpm_publish_time": 1511200550004, "_cnpmcore_publish_time": "2021-12-13T19:13:36.120Z"}, "4.1.1": {"name": "agent-base", "version": "4.1.1", "description": "Turn a function into an `http.Agent` instance", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "^3.4.2", "ws": "^3.0.0"}, "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "e66f64cb58f2132d390711ce2dda8c05825cfecc", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@4.1.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "92d8a4fc2524a3b09b3666a33b6c97960f23d6a4", "size": 9461, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-4.1.1.tgz", "integrity": "sha512-yWGUUmCZD/33IRjG2It94PzixT8lX+47Uq8fjmd0cgQWITCMrJuXFaVIMnGDmDnZGGKAGdwTx8UGeU8lMR2urA=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base-4.1.1.tgz_1500600507309_0.9737169749569148"}, "directories": {}, "publish_time": 1500600507403, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500600507403, "_cnpmcore_publish_time": "2021-12-13T19:13:36.332Z"}, "4.1.0": {"name": "agent-base", "version": "4.1.0", "description": "Turn a function into an `http.Agent` instance", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "^3.4.2", "ws": "^3.0.0"}, "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "6df3dba945c63d5b57a09b9b559942bd8e2b6946", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@4.1.0", "_shasum": "20e17401cd49b3c076bf56a4bc6c5b436ffa8d55", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "20e17401cd49b3c076bf56a4bc6c5b436ffa8d55", "size": 9352, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-4.1.0.tgz", "integrity": "sha512-BzHx9oIyF/K2RsLeWgi+C55/GZU2R9YoREachgWOHPVSJloKuCw9n9Aqlhz5bOe16UgWwoRBK/yQp4hERFwyKw=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base-4.1.0.tgz_1498522512817_0.28704919456504285"}, "directories": {}, "publish_time": 1498522513928, "_hasShrinkwrap": false, "_cnpm_publish_time": 1498522513928, "_cnpmcore_publish_time": "2021-12-13T19:13:36.531Z"}, "4.0.1": {"name": "agent-base", "version": "4.0.1", "description": "Turn a function into an `http.Agent` instance", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "^3.4.2", "ws": "^3.0.0"}, "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "c4b3bb7381eadf04272d684c258c4b337e9675f4", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@4.0.1", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "b478185cc6774fdc8c4f70ee8caadf856afd1b34", "size": 8883, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-4.0.1.tgz", "integrity": "sha512-VA96h2SVV7dBuKed5kvWTdpY6HVcBwIjiOZquseX/3xVg23GbUHUSuiJCN0+8KBjdhyJEkO5oBWyXhekUiMPdw=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base-4.0.1.tgz_1497385225896_0.1452008062042296"}, "directories": {}, "publish_time": 1497385226000, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497385226000, "_cnpmcore_publish_time": "2021-12-13T19:13:36.736Z"}, "4.0.0": {"name": "agent-base", "version": "4.0.0", "description": "Turn a function into an `http.Agent` instance", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "3", "ws": "0.8.0"}, "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}, "gitHead": "757cc8a6c589775c0af4e6c50c20603f81480362", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@4.0.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "8c318459c1eae6561396e7bfed70b27b13e8957a", "size": 8789, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-4.0.0.tgz", "integrity": "sha512-ccZqgUwkDCqy4vA9D9H5zFTjvEqgOZ/+A240u01qkQDMK2g+iv/U2TVkVIMqzHrqizbUA+dGVak282PR/Tbckw=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base-4.0.0.tgz_1496791950846_0.8519535600207746"}, "directories": {}, "publish_time": 1496791951040, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496791951040, "_cnpmcore_publish_time": "2021-12-13T19:13:36.944Z"}, "3.0.0": {"name": "agent-base", "version": "3.0.0", "description": "Turn a function into an `http.Agent` instance", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "3", "ws": "0.8.0"}, "dependencies": {"es6-promisify": "^5.0.0", "extend": "~3.0.0", "semver": "~5.0.1"}, "engines": {"node": ">= 0.12.0"}, "gitHead": "9fa40bab2f665e0688cd0e4155f579644ebd9114", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@3.0.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "fae2fa0429e960af7885d4c1278a0084412cddaa", "size": 9168, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-3.0.0.tgz", "integrity": "sha512-M9K9N6u3MdyZ4b46CTYaANA5P1vmjW+Hay6gvleP8RH3Kk1qO6ClrqRgUGTHjUx9VhQVz1odhRy6fYCoIQc9wA=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base-3.0.0.tgz_1496439258689_0.737877310719341"}, "directories": {}, "publish_time": 1496439258841, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496439258841, "_cnpmcore_publish_time": "2021-12-13T19:13:37.127Z"}, "2.1.1": {"name": "agent-base", "version": "2.1.1", "description": "Turn a function into an `http.Agent` instance", "main": "agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "2", "ws": "0.8.0"}, "dependencies": {"extend": "~3.0.0", "semver": "~5.0.1"}, "gitHead": "b6eecacecb3708181992c31bf7e6fcc96bbedc06", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@2.1.1", "_shasum": "d6de10d5af6132d5bd692427d46fc538539094c7", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "d6de10d5af6132d5bd692427d46fc538539094c7", "size": 8320, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-2.1.1.tgz", "integrity": "sha512-oDtZV740o3fr5oJtPLOsgH2hl2TRPscNXIx4VzzBwVlXVkv8RHm7XXqGAYg8t20+Gwu6LNDnx8HRMGqVGPZ8Vw=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base-2.1.1.tgz_1496182121313_0.05445550032891333"}, "directories": {}, "publish_time": 1496182121402, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496182121402, "_cnpmcore_publish_time": "2021-12-13T19:13:37.338Z"}, "2.1.0": {"name": "agent-base", "version": "2.1.0", "description": "Turn a function into an `http.Agent` instance", "main": "agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "2", "ws": "0.8.0"}, "dependencies": {"extend": "~3.0.0", "semver": "~5.0.1"}, "gitHead": "5b981ee88def6dee042c10bbfdc02585f2af2faf", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@2.1.0", "_shasum": "193455e4347bca6b05847cb81e939bb325446da8", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "193455e4347bca6b05847cb81e939bb325446da8", "size": 8392, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-2.1.0.tgz", "integrity": "sha512-pV5UClQPXnjQ5WOnMYYr6PKp9yuuNBhVKedxqIuW1e4MHVZwVxCx44y5CzdPB9vdZ/PBaMa3ioOhJsw9nlVoaw=="}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base-2.1.0.tgz_1495816641892_0.3964533843100071"}, "directories": {}, "publish_time": 1495816641989, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495816641989, "_cnpmcore_publish_time": "2021-12-13T19:13:37.528Z"}, "2.0.1": {"name": "agent-base", "version": "2.0.1", "description": "Turn a function into an `http.Agent` instance", "main": "agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "2"}, "dependencies": {"extend": "~3.0.0", "semver": "~5.0.1"}, "gitHead": "b46938339bcecd261939dc55798270d0398ad8f0", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@2.0.1", "_shasum": "bd8f9e86a8eb221fffa07bd14befd55df142815e", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "bd8f9e86a8eb221fffa07bd14befd55df142815e", "size": 7459, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-2.0.1.tgz", "integrity": "sha512-9FEVRFHQZjAD2eP+9nBfnOTT3ts3BnnkqAR+szAPWd9Blque8VmlyoyLsEahb/rvFRb4nWCIFBrguF2amz53FQ=="}, "directories": {}, "publish_time": 1441911312806, "_hasShrinkwrap": false, "_cnpm_publish_time": 1441911312806, "_cnpmcore_publish_time": "2021-12-13T19:13:37.739Z"}, "2.0.0": {"name": "agent-base", "version": "2.0.0", "description": "Turn a function into an `http.Agent` instance", "main": "agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "2"}, "dependencies": {"extend": "~3.0.0", "semver": "~4.3.6"}, "gitHead": "5598d7a64b59479135172670b91cec013b8b4037", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@2.0.0", "_shasum": "1120e1f8efed7a6b2fe60ea60ea4a52a9d5c80e1", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.6", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "1120e1f8efed7a6b2fe60ea60ea4a52a9d5c80e1", "size": 7422, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-2.0.0.tgz", "integrity": "sha512-SQHLdjIjfq64aWzOtHF4hNSsaqmCirZ7xWvw5qQPFCPeBDksBxGhxhJwo0MqiiB+hN9/043+svfdbdYJimJ09g=="}, "directories": {}, "publish_time": 1436566786188, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436566786188, "_cnpmcore_publish_time": "2021-12-13T19:13:38.018Z"}, "1.0.2": {"name": "agent-base", "version": "1.0.2", "description": "Turn a function into an `http.Agent` instance", "main": "agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "2"}, "gitHead": "7be263ca09bc9b0f78384bb248006fe01fcbe21a", "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "_id": "agent-base@1.0.2", "_shasum": "6890d3fb217004b62b70f8928e0fae5f8952a706", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "6890d3fb217004b62b70f8928e0fae5f8952a706", "size": 5821, "noattachment": false, "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-1.0.2.tgz", "integrity": "sha512-IrdRInle5l28T2DjBsOojXniN91mXYkt9piDyPbPEoA/X+f7kjd0qiIb18vZThIZCJdLk2Zq/ukXxZp8NkcFsw=="}, "directories": {}, "publish_time": 1435454647948, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435454647948, "_cnpmcore_publish_time": "2021-12-13T19:13:38.191Z"}, "1.0.1": {"name": "agent-base", "version": "1.0.1", "description": "Turn a function into an `http.Agent` instance", "main": "agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "~1.12.0"}, "readmeFilename": "README.md", "_id": "agent-base@1.0.1", "dist": {"tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-1.0.1.tgz", "shasum": "806dbee16f2f27506730e2eb78f537192706ccc3", "size": 5091, "noattachment": false, "integrity": "sha512-1cEV+azwttRTWAxkcCiqUiVyGxfOTIahKuHbHMfMQtQTc+QGXRKK7Ls0JUMat92Tdvow+TAXbT35/0s5Lk91zA=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378786858341, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378786858341, "_cnpmcore_publish_time": "2021-12-13T19:13:38.384Z"}, "1.0.0": {"name": "agent-base", "version": "1.0.0", "description": "Turn a function into an `http.Agent` instance", "main": "agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "~1.12.0"}, "readmeFilename": "README.md", "_id": "agent-base@1.0.0", "dist": {"tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-1.0.0.tgz", "shasum": "e4e0b974fe9d250340d3f7b7aaa48284076b2f8b", "size": 4790, "noattachment": false, "integrity": "sha512-JQT6IZTOMohZ86n0YDDoSTSAaD2K8VbSnsxTpHm6jSxNqi011Ht6/9gLBgjK/jxv4X3dLsBqEejhR1keGNkAiQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378768366790, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378768366790, "_cnpmcore_publish_time": "2021-12-13T19:13:38.611Z"}, "0.0.1": {"name": "agent-base", "version": "0.0.1", "description": "Barebone `http.Agent` implementation", "main": "agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-agent-base.git"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "devDependencies": {"mocha": "~1.12.0"}, "readmeFilename": "README.md", "_id": "agent-base@0.0.1", "dist": {"tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-0.0.1.tgz", "shasum": "6821bd0b228447562378e3560a923ace3eedc3c5", "size": 2960, "noattachment": false, "integrity": "sha512-lWxlsr/w2jOOeN2GsYsZdhXjyvWZ6waQRFhqIoxQbrGICUzK9SdSiyjSnX1YMMF4ueoJwwmp+0CwQ5o/4k+JhA=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1373400881054, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373400881054, "_cnpmcore_publish_time": "2021-12-13T19:13:38.847Z"}, "7.0.0": {"name": "agent-base", "version": "7.0.0", "description": "Turn a function into an `http.Agent` instance", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "@types/semver": "^7.3.13", "@types/ws": "^6.0.4", "async-listen": "^2.1.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "workspace:*", "typescript": "^5.0.4", "ws": "^3.3.3"}, "engines": {"node": ">= 14"}, "gitHead": "4d75e6cc974b2614a575e6f7a3d3c79741727dbc", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "agent-base@7.0.0", "_nodeVersion": "16.20.0", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-awaqsf16R0tAUMxWiVikaBDKrbt0im7XdzPMh3I8TFC097G4ZowjGgLBfXt+tGPsE+1U1FyLBGuWMd/EPVblWg==", "shasum": "3424dd96658fbbcc0b090e41aaca2deb9a89f9cf", "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-7.0.0.tgz", "fileCount": 15, "unpackedSize": 23482, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJD7uU+y+uy2jUMTunFNhkxisqymPyHxzQYucUPZB2rgIgcDF1oqhgsU/EvhKy4ug/o58cgP9f4PHdft18pnJW7kk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZJxAAmgyiKkyKMUKOoPB+I0Js0vDWUxkQOSWvKNxxER0iD8so1+Pa\r\nCu9XXb9fesW+iG3KqAdQRrolhdNJGLrvBsqcFoFc0k4nzDdT54aFsSwtmj1j\r\nM9Y2980QEiaLF+aSVsbEEpmckNQZxZQMnzlRt6KxoBGDTzHtkmObgOfqRS7n\r\nvW6sSn5d5KpwZsWkMy5hzwuRL+9PTN/DnJlYUqUGRRFMafGPZFhS8s091Sy4\r\nSgYwZU3OWQi9Tk9j2xdcf6be7BJ9NF7LhK6x96OT0vjDGTmaghGBTRFLwXkv\r\nNKATfu4TrKeQ2eFGhkGUWYZa98QXx2+NXsEXDb8SlAZFpuXzAtNUff1OsNSB\r\nsdYSq9uuCR2pg0KSprw1tvmSRyfMR8hZetHHRhQR6ROWG+CSevIVGarBJ6vB\r\nUMKB4T2Ix4T/fsO7rjnubbnLfY4culUenQ9FYYzVuPh0Tt8T46VQBtBo8BLi\r\nVBJLfNgE9dKKBg26++Pji0t+nTXb9zZ9DSK+sN+6TyeXXOm34V15ESx5EQGe\r\nkMMIaB3Q07YXNGNBZPD6UwRkCpDoSBoLxpxcO+m5pGNfelYdQqqQjy7Zwt+t\r\n+sRYqj69WP4TYkmFE20Fj+IGWb3UiVcJYK513PJc0COWHPZTkvX/qN7HSYTn\r\nS4kpeUU/0+SKZj9BlE2+CbAJ/IHceATyhng=\r\n=39vP\r\n-----END PGP SIGNATURE-----\r\n", "size": 6612}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_7.0.0_1683232383919_0.7242806518001645"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-04T20:33:04.125Z", "publish_time": 1683232384125, "_source_registry_name": "default"}, "7.0.1": {"name": "agent-base", "version": "7.0.1", "description": "Turn a function into an `http.Agent` instance", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/semver": "^7.3.13", "@types/ws": "^6.0.4", "async-listen": "^2.1.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "ws": "^3.3.3", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "agent-base@7.0.1", "_integrity": "sha512-V9to8gr2GK7eA+xskWGAFUX/TLSQKuH2TI06c/jGLL6yLp3oEjtnqM7a5tPV9fC1rabLeAgThZeBwsYX+WWHpw==", "_resolved": "/tmp/04dd9e5f64167b47be3df4a78e581305/agent-base-7.0.1.tgz", "_from": "file:agent-base-7.0.1.tgz", "_nodeVersion": "20.1.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-V9to8gr2GK7eA+xskWGAFUX/TLSQKuH2TI06c/jGLL6yLp3oEjtnqM7a5tPV9fC1rabLeAgThZeBwsYX+WWHpw==", "shasum": "ec4df4e6406bdf71490ade302ea45f86bf365ea9", "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-7.0.1.tgz", "fileCount": 11, "unpackedSize": 20416, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCub43qOrgL1ECB9peiOsrbviOLLcQhA9hWMR/kLM3pCQIgcUfHmBMsJ9eLYNAYQMMFYDUZDZqsncl4cmtztuH5t4c="}], "size": 5846}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_7.0.1_1683324250254_0.9390646513749472"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-05T22:04:10.433Z", "publish_time": 1683324250433, "_source_registry_name": "default"}, "7.0.2": {"name": "agent-base", "version": "7.0.2", "description": "Turn a function into an `http.Agent` instance", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/semver": "^7.3.13", "@types/ws": "^6.0.4", "async-listen": "^2.1.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "ws": "^3.3.3", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "agent-base@7.0.2", "_integrity": "sha512-k2/tQ1+8Zf50dEUJWklUP80LcE/+Ph+OJ6cf2Ff2fD/c/TtCe6ofnCoNMz9UnyxOQYlaAALZtEWETzn+1JjfHg==", "_resolved": "/tmp/7068fe54e9376f4e4643a69370d03902/agent-base-7.0.2.tgz", "_from": "file:agent-base-7.0.2.tgz", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-k2/tQ1+8Zf50dEUJWklUP80LcE/+Ph+OJ6cf2Ff2fD/c/TtCe6ofnCoNMz9UnyxOQYlaAALZtEWETzn+1JjfHg==", "shasum": "d6c854c21fe5b8c8f1c69ac12a7d21a3d1be2859", "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-7.0.2.tgz", "fileCount": 11, "unpackedSize": 23174, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBHy3g7QH1m/A/E1Lvd+HSsiJRLcXdNCoR+CSan8btZhAiEA2PBA2H3C7gZPtXNlcQILGrmEw2GsCAt05q+yP9q537w="}], "size": 6390}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_7.0.2_1684438283925_0.9084338374824403"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-18T19:31:24.134Z", "publish_time": 1684438284134, "_source_registry_name": "default"}, "7.1.0": {"name": "agent-base", "version": "7.1.0", "description": "Turn a function into an `http.Agent` instance", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/semver": "^7.3.13", "@types/ws": "^6.0.4", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "ws": "^3.3.3", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "agent-base@7.1.0", "_integrity": "sha512-o/zjMZRhJxny7OyEF+Op8X+efiELC7k7yOjMzgfzVqOzXqkBkWI79YoTdOtsuWd5BWhAGAuOY/Xa6xpiaWXiNg==", "_resolved": "/tmp/c6754f3ac9048574dd63be70310eecbe/agent-base-7.1.0.tgz", "_from": "file:agent-base-7.1.0.tgz", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-o/zjMZRhJxny7OyEF+Op8X+efiELC7k7yOjMzgfzVqOzXqkBkWI79YoTdOtsuWd5BWhAGAuOY/Xa6xpiaWXiNg==", "shasum": "536802b76bc0b34aa50195eb2442276d613e3434", "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-7.1.0.tgz", "fileCount": 11, "unpackedSize": 23496, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG59UY1uwFDbQsfoWZdqXmc183pdjBzFVU/NIEjHtYbKAiEAvswRuxPXjS6p46waDQ1b1Sj/B3fEaMtnn4xWFodqJGI="}], "size": 6411}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_7.1.0_1684966197571_0.20313114786357644"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-24T22:09:57.729Z", "publish_time": 1684966197729, "_source_registry_name": "default"}, "7.1.1": {"name": "agent-base", "version": "7.1.1", "description": "Turn a function into an `http.Agent` instance", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/semver": "^7.3.13", "@types/ws": "^6.0.4", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "ws": "^3.3.3", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "agent-base@7.1.1", "_integrity": "sha512-H0TSyFNDMomMNJQBn8wFV5YC/2eJ+VXECwOadZJT554xP6cODZHPX3H9QMQECxvrgiSOP1pHjy1sMWQVYJOUOA==", "_resolved": "/tmp/49926a570127159653e7b70d101bcbba/agent-base-7.1.1.tgz", "_from": "file:agent-base-7.1.1.tgz", "_nodeVersion": "20.11.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-H0TSyFNDMomMNJQBn8wFV5YC/2eJ+VXECwOadZJT554xP6cODZHPX3H9QMQECxvrgiSOP1pHjy1sMWQVYJOUOA==", "shasum": "bdbded7dfb096b751a2a087eeeb9664725b2e317", "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-7.1.1.tgz", "fileCount": 12, "unpackedSize": 31249, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICukUv7At8DBG9zvB3grqS9q18Bw9PPJwQIZpQq0tHZaAiBhiCvJ0ot1d9WsVT3+zqexp8BR4hufEl57A+jsa6gIsQ=="}], "size": 7797}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_7.1.1_1711762299983_0.6642759080604241"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-30T01:31:40.164Z", "publish_time": 1711762300164, "_source_registry_name": "default"}, "7.1.2": {"name": "agent-base", "version": "7.1.2", "description": "Turn a function into an `http.Agent` instance", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/semver": "^7.3.13", "@types/ws": "^6.0.4", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "ws": "^5.2.4", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "agent-base@7.1.2", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-JVzqkCNRT+VfqzzgPWDPnwvDheSAUdiMUn3NoLXpDJF5lRqeJqyC9iGsAxIOAW+mzIdq+uP1TvcX6bMtrH0agg==", "_resolved": "/tmp/fe81417e0d3b4ac9647c9bdce788daff/agent-base-7.1.2.tgz", "_from": "file:agent-base-7.1.2.tgz", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-JVzqkCNRT+VfqzzgPWDPnwvDheSAUdiMUn3NoLXpDJF5lRqeJqyC9iGsAxIOAW+mzIdq+uP1TvcX6bMtrH0agg==", "shasum": "c83b029791b07a5301dce3ef825e6a328b5391cd", "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-7.1.2.tgz", "fileCount": 12, "unpackedSize": 31595, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDohLCl4KKVFq1r4bUIymtlvn51BqBKTdYjL0WWu3hXsgIhAIA71jk7fB3Xc+7zgK+ciYf+qNv3SFnqexl+1XUqW5rh"}], "size": 7850}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_7.1.2_1733542327483_0.35557837083053245"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-07T03:32:07.648Z", "publish_time": 1733542327648, "_source_registry_name": "default"}, "7.1.3": {"name": "agent-base", "version": "7.1.3", "description": "Turn a function into an `http.Agent` instance", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/semver": "^7.3.13", "@types/ws": "^6.0.4", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "ws": "^5.2.4", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "agent-base@7.1.3", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==", "_resolved": "/tmp/0708188976c1b804614f97606b4b448d/agent-base-7.1.3.tgz", "_from": "file:agent-base-7.1.3.tgz", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==", "shasum": "29435eb821bc4194633a5b89e5bc4703bafc25a1", "tarball": "https://registry.npmmirror.com/agent-base/-/agent-base-7.1.3.tgz", "fileCount": 12, "unpackedSize": 31548, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGPXvJWij+sL0uBz+jG5jqDD/Cq6tbJ47abDAe1nuBMJAiEAyYLnJ1AjcCuGMxYYrsmaFfrdKAk52YVoZqK886o44PA="}], "size": 7840}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_7.1.3_1733630651319_0.4296383757785176"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-12-08T04:04:11.531Z", "publish_time": 1733630651531, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["http", "agent", "base", "barebones", "https"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "_source_registry_name": "default"}