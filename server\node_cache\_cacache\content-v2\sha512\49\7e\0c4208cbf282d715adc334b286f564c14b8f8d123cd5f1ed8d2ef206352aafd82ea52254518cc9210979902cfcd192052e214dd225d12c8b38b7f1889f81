{"_attachments": {}, "_id": "abbrev", "_rev": "2250-61f148a1b677e08f51145bbb", "author": {"name": "GitHub Inc."}, "description": "Like ruby's abbrev module, but in js", "dist-tags": {"latest": "3.0.1"}, "license": "ISC", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "name": "abbrev", "readme": "# abbrev-js\n\nJust like [ruby's Abbrev](http://apidock.com/ruby/Abbrev).\n\nUsage:\n\n    var abbrev = require(\"abbrev\");\n    abbrev(\"foo\", \"fool\", \"folding\", \"flop\");\n    \n    // returns:\n    { fl: 'flop'\n    , flo: 'flop'\n    , flop: 'flop'\n    , fol: 'folding'\n    , fold: 'folding'\n    , foldi: 'folding'\n    , foldin: 'folding'\n    , folding: 'folding'\n    , foo: 'foo'\n    , fool: 'fool'\n    }\n\nThis is handy for command-line scripts, or other cases where you want to be able to accept shorthands.\n", "time": {"created": "2022-01-26T13:12:01.704Z", "modified": "2025-05-14T20:10:23.112Z", "1.1.1": "2017-09-28T02:47:13.220Z", "1.1.0": "2017-02-14T06:33:20.235Z", "1.0.9": "2016-06-15T18:41:01.215Z", "1.0.7": "2015-05-30T22:57:54.685Z", "1.0.6": "2015-05-21T00:58:16.778Z", "1.0.5": "2014-04-17T20:09:12.523Z", "1.0.4": "2013-01-09T00:01:24.135Z", "1.0.3": "2011-03-21T22:21:11.183Z", "2.0.0": "2022-11-01T16:39:45.574Z", "3.0.0": "2024-09-24T19:01:56.100Z", "3.0.1": "2025-04-10T13:58:55.680Z"}, "versions": {"1.1.1": {"name": "abbrev", "version": "1.1.1", "description": "Like ruby's abbrev module, but in js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "abbrev.js", "scripts": {"test": "tap test.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+ssh://**************/isaacs/abbrev-js.git"}, "license": "ISC", "devDependencies": {"tap": "^10.1"}, "files": ["abbrev.js"], "gitHead": "a9ee72ebc8fe3975f1b0c7aeb3a8f2a806a432eb", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "homepage": "https://github.com/isaacs/abbrev-js#readme", "_id": "abbrev@1.1.1", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f8f2c887ad10bf67f634f005b6987fed3179aac8", "size": 2301, "noattachment": false, "tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abbrev-1.1.1.tgz_1506566833068_0.05750026390887797"}, "directories": {}, "publish_time": 1506566833220, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506566833220, "_cnpmcore_publish_time": "2021-12-13T08:11:44.024Z"}, "1.1.0": {"name": "abbrev", "version": "1.1.0", "description": "Like ruby's abbrev module, but in js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "abbrev.js", "scripts": {"test": "tap test.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+ssh://**************/isaacs/abbrev-js.git"}, "license": "ISC", "devDependencies": {"tap": "^10.1"}, "files": ["abbrev.js"], "gitHead": "7136d4d95449dc44115d4f78b80ec907724f64e0", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "homepage": "https://github.com/isaacs/abbrev-js#readme", "_id": "abbrev@1.1.0", "_shasum": "d0554c2256636e2f56e7c2e5ad183f859428d81f", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "d0554c2256636e2f56e7c2e5ad183f859428d81f", "size": 1838, "noattachment": false, "tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-1.1.0.tgz", "integrity": "sha512-c92Vmq5hfBgXyoUaHqF8P5+7THGjvxAlB64tm3PiFSAcDww34ndmrlSOd3AUaBZoutDwX0dHz9nUUFoD1jEw0Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/abbrev-1.1.0.tgz_1487054000015_0.9229173036292195"}, "directories": {}, "publish_time": 1487054000235, "_hasShrinkwrap": false, "_cnpm_publish_time": 1487054000235, "_cnpmcore_publish_time": "2021-12-13T08:11:44.321Z"}, "1.0.9": {"name": "abbrev", "version": "1.0.9", "description": "Like ruby's abbrev module, but in js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "abbrev.js", "scripts": {"test": "tap test.js --cov"}, "repository": {"type": "git", "url": "git+ssh://**************/isaacs/abbrev-js.git"}, "license": "ISC", "devDependencies": {"tap": "^5.7.2"}, "files": ["abbrev.js"], "gitHead": "c386cd9dbb1d8d7581718c54d4ba944cc9298d6f", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "homepage": "https://github.com/isaacs/abbrev-js#readme", "_id": "abbrev@1.0.9", "_shasum": "91b4792588a7738c25f35dd6f63752a2f8776135", "_from": ".", "_npmVersion": "3.9.1", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "91b4792588a7738c25f35dd6f63752a2f8776135", "size": 1800, "noattachment": false, "tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-1.0.9.tgz", "integrity": "sha512-L<PERSON>yx4aLEC3x6T0UguF6YILf+ntvmOaWsVfENmIW0E9H09vKlLDGelMjjSm0jkDHALj8A8quZ/HapKNigzwge+Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/abbrev-1.0.9.tgz_1466016055839_0.7825860097073019"}, "directories": {}, "publish_time": 1466016061215, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466016061215, "_cnpmcore_publish_time": "2021-12-13T08:11:44.702Z"}, "1.0.7": {"name": "abbrev", "version": "1.0.7", "description": "Like ruby's abbrev module, but in js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "abbrev.js", "scripts": {"test": "tap test.js --cov"}, "repository": {"type": "git", "url": "git+ssh://**************/isaacs/abbrev-js.git"}, "license": "ISC", "devDependencies": {"tap": "^1.2.0"}, "gitHead": "821d09ce7da33627f91bbd8ed631497ed6f760c2", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "homepage": "https://github.com/isaacs/abbrev-js#readme", "_id": "abbrev@1.0.7", "_shasum": "5b6035b2ee9d4fb5cf859f08a9be81b208491843", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "5b6035b2ee9d4fb5cf859f08a9be81b208491843", "size": 2425, "noattachment": false, "tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-1.0.7.tgz", "integrity": "sha512-eJTPIs0mc8P5gniSqIq74DCfeFiBp1CqIdkhWvso16Xed4BlQ6WyfmuNueOka9VXIcrnmm4AEdYuayjNo1EHIg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1433026674685, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433026674685, "_cnpmcore_publish_time": "2021-12-13T08:11:45.030Z"}, "1.0.6": {"name": "abbrev", "version": "1.0.6", "description": "Like ruby's abbrev module, but in js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "abbrev.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/isaacs/abbrev-js.git"}, "license": "ISC", "gitHead": "648a6735d9c5a7a04885e3ada49eed4db36181c2", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "homepage": "https://github.com/isaacs/abbrev-js#readme", "_id": "abbrev@1.0.6", "_shasum": "b6d632b859b3fa2d6f7e4b195472461b9e32dc30", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b6d632b859b3fa2d6f7e4b195472461b9e32dc30", "size": 2256, "noattachment": false, "tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-1.0.6.tgz", "integrity": "sha512-7+0vBaPMygAKGRDelipd+cGQPprUGb9ZEw3zINnbKuXwrUV9bYiak9BS/4iAIA+mUgBeGYcFxJYGSM3PpPFtDQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1432169896778, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432169896778, "_cnpmcore_publish_time": "2021-12-13T08:11:45.415Z"}, "1.0.5": {"name": "abbrev", "version": "1.0.5", "description": "Like ruby's abbrev module, but in js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "abbrev.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/abbrev-js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/abbrev-js/raw/master/LICENSE"}, "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "homepage": "https://github.com/isaacs/abbrev-js", "_id": "abbrev@1.0.5", "_shasum": "5d8257bd9ebe435e698b2fa431afde4fe7b10b03", "_from": ".", "_npmVersion": "1.4.7", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "5d8257bd9ebe435e698b2fa431afde4fe7b10b03", "size": 2465, "noattachment": false, "tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-1.0.5.tgz", "integrity": "sha512-Sg+CLYf4W/aL/aN6jF7KJ7U8NLK0Dlewx93tRLjB2G6MPlqwWJYN+pypKISr0sbzIfSJVCkn6tYlgKBM41RYpA=="}, "directories": {}, "publish_time": 1397765352523, "_hasShrinkwrap": false, "_cnpm_publish_time": 1397765352523, "_cnpmcore_publish_time": "2021-12-13T08:11:45.786Z"}, "1.0.4": {"name": "abbrev", "version": "1.0.4", "description": "Like ruby's abbrev module, but in js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/abbrev.js", "scripts": {"test": "node lib/abbrev.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/abbrev-js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/abbrev-js/raw/master/LICENSE"}, "readmeFilename": "README.md", "_id": "abbrev@1.0.4", "dist": {"tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-1.0.4.tgz", "shasum": "bd55ae5e413ba1722ee4caba1f6ea10414a59ecd", "size": 2295, "noattachment": false, "integrity": "sha512-xoV1ALZYWdMEsoOSazVe4J2/0Tim1ZPKvz2xvmpxjpErBkF5o7VQWivVr8VxlAhOdesjvKHKm62l0gNEeL4+2A=="}, "_npmVersion": "1.1.70", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1357689684135, "_hasShrinkwrap": false, "_cnpm_publish_time": 1357689684135, "_cnpmcore_publish_time": "2021-12-13T08:11:46.249Z"}, "1.0.3": {"name": "abbrev", "version": "1.0.3", "description": "Like ruby's abbrev module, but in js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "./lib/abbrev.js", "scripts": {"test": "node lib/abbrev.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/abbrev-js.git"}, "_id": "abbrev@1.0.3", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.0rc7", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-1.0.3.tgz", "shasum": "aa049c967f999222aa42e14434f0c562ef468241", "size": 1526, "noattachment": false, "integrity": "sha512-s07HMJf6O5iTLVDx9cH7c9VbOdrmzxE+AzWz9CPi94zVNBQQA3jIwIZKTrHQj4dGR1T/MdwMnVJzSpjaVEXtXw=="}, "directories": {}, "publish_time": 1300746071183, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_hasShrinkwrap": false, "_cnpm_publish_time": 1300746071183, "_cnpmcore_publish_time": "2021-12-13T08:11:46.748Z"}, "2.0.0": {"name": "abbrev", "version": "2.0.0", "description": "Like ruby's abbrev module, but in js", "author": {"name": "GitHub Inc."}, "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/npm/abbrev-js.git"}, "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.8.0", "tap": "^16.3.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.8.0"}, "gitHead": "6eaa998b4291757a34d55d815290314c4776a30a", "bugs": {"url": "https://github.com/npm/abbrev-js/issues"}, "homepage": "https://github.com/npm/abbrev-js#readme", "_id": "abbrev@2.0.0", "_nodeVersion": "18.12.0", "_npmVersion": "9.0.1", "dist": {"integrity": "sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ==", "shasum": "cf59829b8b4f03f89dda2771cb7f3653828c89bf", "tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-2.0.0.tgz", "fileCount": 4, "unpackedSize": 4827, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAqmMUR8ZhXSrjGlpIqySNBD9Pa236Qja5gsIWoqGhRaAiBss1eUkBZQkv4pCtZXOXp5D1pZDUNnz0y/bz7oDuH3uQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYUvRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmHg//TaKWbk0RyOVd76RxaU48TKH2GwNGZqy4pxlrLKUFedm49eG6\r\nlmZm0XN3FmnCMBfj/aQ+vf4IF/NKaZpHpYbcq3wlfOdEeDisKauxheH0Hia+\r\n8KDwi7kPod8WVFt3GfrYzKscZybn4xTMkLMDe3CKRa6tzQeJUm8je8a8eRD2\r\nS8iTV7GjKPCePsS5mxakvZ7pigDFsjPAM2NzpUksiP1SS3dVVNnJoG+i4Lcg\r\nSjc7PdIszYpsk1L/SWG6Habsg7bembsTAS2a+Kl2pveU/Xp/UReEf2B+DKtp\r\nWs5O691i6xVeMICVNJFF525xPpq1kLYPknsx+SObk/b9tOhdByvUPi4ig5UZ\r\nXT3JZhv0S2pZgYXdW9KA086Jrln0X4yoHXHjfx44JLURNIjOT5wAw+Y/IRCg\r\ntg/vMwX187IfB8qG3LRrVNbxXxIl9FMCj/YBtW4TmNh/T0SDyItNSkresswh\r\nSTdNg2cE8jglnKk2/G2aKX/UnaJ5COzMOdUtF91oVjrOBZQ7HN7otS8ccdXe\r\nIGH14mamCJOx0OBS2D47gO+bpiyNJVhaOaQX1Q3QU9CHhY5DMb6PPzooMiGG\r\nGBgWlTSwzZSMJ4N1XK2thLTHmJMncQTdUWTEG9e9IGHG6Tcc40C/wjbWuocn\r\n5u2Lbqn50mw/xdtk0ZJUl0rsJoSOgu2RXVw=\r\n=u41A\r\n-----END PGP SIGNATURE-----\r\n", "size": 2365}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abbrev_2.0.0_1667320785391_0.08801030116430786"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-01T16:52:12.838Z"}, "3.0.0": {"name": "abbrev", "version": "3.0.0", "description": "Like ruby's abbrev module, but in js", "author": {"name": "GitHub Inc."}, "main": "lib/index.js", "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/abbrev-js.git"}, "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.3.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}, "_id": "abbrev@3.0.0", "gitHead": "89e72e322083708922f259dc5f3635237527c41e", "bugs": {"url": "https://github.com/npm/abbrev-js/issues"}, "homepage": "https://github.com/npm/abbrev-js#readme", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-+/kfrslGQ7TNV2ecmQwMJj/B65g5KVq1/L3SGVZ3tCYGqlzFuFCGBZJtMP99wH3NpEUyAjn0zPdPUg0D+DwrOA==", "shasum": "c29a6337e167ac61a84b41b80461b29c5c271a27", "tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-3.0.0.tgz", "fileCount": 4, "unpackedSize": 4897, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/abbrev@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlbGvGlGAD7SWzkgrlTWH2/YR/vy/HJmmsvNCONQaPDQIgEtgNm0m2mLoBk+NHNRuuUH7UANu1T9ie99I+aCGNl54="}], "size": 2405}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abbrev_3.0.0_1727204515937_0.4472956498597629"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-24T19:01:56.100Z", "publish_time": 1727204516100, "_source_registry_name": "default"}, "3.0.1": {"name": "abbrev", "version": "3.0.1", "description": "Like ruby's abbrev module, but in js", "author": {"name": "GitHub Inc."}, "main": "lib/index.js", "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/abbrev-js.git"}, "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.24.3", "tap": "^16.3.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.24.3", "publish": true}, "_id": "abbrev@3.0.1", "gitHead": "21ff20fdb92525008b4fc4d90857873b20c23b40", "bugs": {"url": "https://github.com/npm/abbrev-js/issues"}, "homepage": "https://github.com/npm/abbrev-js#readme", "_nodeVersion": "22.14.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-AO2ac6pjRB3SJmGJo+v5/aK6Omggp6fsLrs6wN9bd35ulu4cCwaAU9+7ZhXjeqHVkaHThLuzH0nZr0YpCDhygg==", "shasum": "8ac8b3b5024d31464fe2a5feeea9f4536bf44025", "tarball": "https://registry.npmmirror.com/abbrev/-/abbrev-3.0.1.tgz", "fileCount": 4, "unpackedSize": 4961, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/abbrev@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCuVlUH0YBHS1kAP8/rUvo5TQXhgOEcGg8lmjMUi7JAQgIhAJcn5pht2CBOzjZj+pC4ALa09qpZz0m7hPWlQTssPaSM"}], "size": 2426}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/abbrev_3.0.1_1744293535403_0.038353096831895694"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-10T13:58:55.680Z", "publish_time": 1744293535680, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/npm/abbrev-js/issues"}, "homepage": "https://github.com/npm/abbrev-js#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/abbrev-js.git"}, "_source_registry_name": "default"}