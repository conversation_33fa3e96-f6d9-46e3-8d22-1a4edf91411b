{"_attachments": {}, "_id": "ansi-regex", "_rev": "253-61f14435963ca28f5ee351a5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "description": "Regular expression for matching ANSI escape codes", "dist-tags": {"latest": "6.1.0"}, "license": "MIT", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "name": "ansi-regex", "readme": "# ansi-regex\n\n> Regular expression for matching [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code)\n\n## Install\n\n```sh\nnpm install ansi-regex\n```\n\n## Usage\n\n```js\nimport ansiRegex from 'ansi-regex';\n\nansiRegex().test('\\u001B[4mcake\\u001B[0m');\n//=> true\n\nansiRegex().test('cake');\n//=> false\n\n'\\u001B[4mcake\\u001B[0m'.match(ansiRegex());\n//=> ['\\u001B[4m', '\\u001B[0m']\n\n'\\u001B[4mcake\\u001B[0m'.match(ansiRegex({onlyFirst: true}));\n//=> ['\\u001B[4m']\n\n'\\u001B]8;;https://github.com\\u0007click\\u001B]8;;\\u0007'.match(ansiRegex());\n//=> ['\\u001B]8;;https://github.com\\u0007', '\\u001B]8;;\\u0007']\n```\n\n## API\n\n### ansiRegex(options?)\n\nReturns a regex for matching ANSI escape codes.\n\n#### options\n\nType: `object`\n\n##### onlyFirst\n\nType: `boolean`\\\nDefault: `false` *(Matches any ANSI escape codes in a string)*\n\nMatch only the first ANSI escape.\n\n## FAQ\n\n### Why do you test for codes not in the ECMA 48 standard?\n\nSome of the codes we run as a test are codes that we acquired finding various lists of non-standard or manufacturer specific codes. We test for both standard and non-standard codes, as most of them follow the same or similar format and can be safely matched in strings without the risk of removing actual string content. There are a few non-standard control codes that do not follow the traditional format (i.e. they end in numbers) thus forcing us to exclude them from the test because we cannot reliably match them.\n\nOn the historical side, those ECMA standards were established in the early 90's whereas the VT100, for example, was designed in the mid/late 70's. At that point in time, control codes were still pretty ungoverned and engineers used them for a multitude of things, namely to activate hardware ports that may have been proprietary. Somewhere else you see a similar 'anarchy' of codes is in the x86 architecture for processors; there are a ton of \"interrupts\" that can mean different things on certain brands of processors, most of which have been phased out.\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n", "time": {"created": "2022-01-26T12:53:09.267Z", "modified": "2024-09-09T14:18:33.359Z", "5.0.1": "2021-09-14T15:55:19.540Z", "6.0.1": "2021-09-10T20:25:05.514Z", "6.0.0": "2021-04-16T06:02:15.282Z", "5.0.0": "2019-10-04T11:29:13.830Z", "4.1.0": "2019-03-08T06:14:40.169Z", "4.0.0": "2018-09-18T08:18:58.129Z", "3.0.0": "2017-06-20T19:03:33.464Z", "2.1.1": "2017-01-14T03:09:39.887Z", "2.0.0": "2015-06-30T16:07:19.279Z", "1.1.1": "2015-02-22T09:24:51.185Z", "1.1.0": "2014-08-30T12:38:30.166Z", "1.0.0": "2014-08-13T13:29:14.050Z", "0.2.1": "2014-06-20T16:44:03.241Z", "0.2.0": "2014-06-14T01:12:53.550Z", "0.1.0": "2014-06-03T16:59:22.332Z", "4.1.1": "2022-03-12T03:08:58.035Z", "3.0.1": "2022-03-27T13:29:44.595Z", "6.1.0": "2024-09-09T13:57:56.873Z"}, "versions": {"5.0.1": {"name": "ansi-regex", "version": "5.0.1", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "^2.4.0", "tsd": "^0.9.0", "xo": "^0.25.3"}, "types": "./index.d.ts", "readmeFilename": "readme.md", "gitHead": "a9babce885cf19c363cf1d1c645f834592c3f7a4", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@5.0.1", "_nodeVersion": "16.0.0", "_npmVersion": "7.23.0", "dist": {"shasum": "082cb2c89c9fe8659a311a53bd6a4dc5301db304", "size": 2768, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_5.0.1_1631634919356_0.29996150981120184"}, "_hasShrinkwrap": false, "publish_time": 1631634919540, "_cnpm_publish_time": 1631634919540, "_cnpmcore_publish_time": "2021-12-13T06:41:54.866Z"}, "6.0.1": {"name": "ansi-regex", "version": "6.0.1", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "funding": "https://github.com/chalk/ansi-regex?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "types": "./index.d.ts", "gitHead": "d908492e0070f26552fad1b25e339aff9011ae8b", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@6.0.1", "_nodeVersion": "16.0.0", "_npmVersion": "7.23.0", "dist": {"shasum": "3183e38fae9a65d7cb5e53945cd5897d0260a06a", "size": 2765, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.0.1.tgz", "integrity": "sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA=="}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_6.0.1_1631305505393_0.35989069175371236"}, "_hasShrinkwrap": false, "publish_time": 1631305505514, "_cnpm_publish_time": 1631305505514, "_cnpmcore_publish_time": "2021-12-13T06:41:55.163Z"}, "6.0.0": {"name": "ansi-regex", "version": "6.0.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "funding": "https://github.com/chalk/ansi-regex?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "c1b5e45f7c65a332ffb03ac8e5804ad37c579cdc", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@6.0.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ecc7f5933cbe5ac7b33e209a5ff409ab1669c6b2", "size": 2780, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.0.0.tgz", "integrity": "sha512-tAaOSrWCHF+1Ear1Z4wnJCXA9GGox4K6Ic85a5qalES2aeEwQGr7UC93mwef49536PkCYjzkp0zIxfFvexJ6zQ=="}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_6.0.0_1618552935087_0.14381997678067826"}, "_hasShrinkwrap": false, "publish_time": 1618552935282, "_cnpm_publish_time": 1618552935282, "_cnpmcore_publish_time": "2021-12-13T06:41:55.442Z"}, "5.0.0": {"name": "ansi-regex", "version": "5.0.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "^2.4.0", "tsd": "^0.9.0", "xo": "^0.25.3"}, "gitHead": "2b56fb0c7a07108e5b54241e8faec160d393aedb", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@5.0.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "388539f55179bf39339c81af30a654d69f87cb75", "size": 2821, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.0.tgz", "integrity": "sha512-bY6fj56OUQ0hU1KjFNDQuJFezqKdrAyFdIevADiqrWHwSlbmBNMHp5ak2f40Pm8JTFyM2mqxkG6ngkHO11f/lg=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_5.0.0_1570188553674_0.9267473348647308"}, "_hasShrinkwrap": false, "publish_time": 1570188553830, "_cnpm_publish_time": 1570188553830, "_cnpmcore_publish_time": "2021-12-13T06:41:55.729Z"}, "4.1.0": {"name": "ansi-regex", "version": "4.1.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "^0.25.0", "xo": "^0.23.0"}, "gitHead": "a079ab2d30cfb752a3f247dcf358d0a591c288c5", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@4.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8b9f8f08cf1acb843756a839ca8c7e3168c51997", "size": 2749, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-4.1.0.tgz", "integrity": "sha512-1apePfXM1UOSqw0o9IiFAovVz9M5S1Dg+4TrDwfMewQ6p/rmMueb7tWZjQ1rx4Loy1ArBggoqGpfqqdI4rondg=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_4.1.0_1552025680031_0.26097113418100015"}, "_hasShrinkwrap": false, "publish_time": 1552025680169, "_cnpm_publish_time": 1552025680169, "_cnpmcore_publish_time": "2021-12-13T06:41:56.034Z"}, "4.0.0": {"name": "ansi-regex", "version": "4.0.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "^0.25.0", "xo": "^0.23.0"}, "gitHead": "a1d92466388fc8766b63bfab27ddc1e1df897dda", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@4.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "70de791edf021404c3fd615aa89118ae0432e5a9", "size": 2416, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-4.0.0.tgz", "integrity": "sha512-iB5Dda8t/UqpPI/IjsejXu5jOGDrzn41wJyljwPH65VCIbk6+1BzFIMJGFwTNrYXT1CrD+B4l19U7awiQ8rk7w=="}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_4.0.0_1537258737952_0.3982474354635459"}, "_hasShrinkwrap": false, "publish_time": 1537258738129, "_cnpm_publish_time": 1537258738129, "_cnpmcore_publish_time": "2021-12-13T06:41:56.321Z"}, "3.0.0": {"name": "ansi-regex", "version": "3.0.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "0a8cc19946c03c38520fe8c086b8adb66f9cce0b", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@3.0.0", "_shasum": "ed0317c322064f79466c02966bddb605ab37d998", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ed0317c322064f79466c02966bddb605ab37d998", "size": 2264, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha512-wFUFA5bg5dviipbQQ32yOQhl6gcJaJXiHE7dvR8VYPG97+J/GNC5FKGepKdEDUFeXRzDxPF1X/Btc8L+v7oqIQ=="}, "maintainers": [{"name": "dthree", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex-3.0.0.tgz_1497985412590_0.5700640194118023"}, "directories": {}, "publish_time": 1497985413464, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497985413464, "_cnpmcore_publish_time": "2021-12-13T06:41:56.619Z"}, "2.1.1": {"name": "ansi-regex", "version": "2.1.1", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava --verbose", "view-supported": "node fixtures/view-codes.js"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "0.17.0", "xo": "0.16.0"}, "xo": {"rules": {"guard-for-in": 0, "no-loop-func": 0}}, "gitHead": "7c908e7b4eb6cd82bfe1295e33fdf6d166c7ed85", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@2.1.1", "_shasum": "c3b33ab5ee360d86e0e628f0468ae7ef27d654df", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "c3b33ab5ee360d86e0e628f0468ae7ef27d654df", "size": 2340, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA=="}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ansi-regex-2.1.1.tgz_1484363378013_0.4482989883981645"}, "directories": {}, "publish_time": 1484363379887, "_hasShrinkwrap": false, "_cnpm_publish_time": 1484363379887, "_cnpmcore_publish_time": "2021-12-13T06:41:56.918Z"}, "2.0.0": {"name": "ansi-regex", "version": "2.0.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/ansi-regex"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha test/test.js", "view-supported": "node test/viewCodes.js"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"mocha": "*"}, "gitHead": "57c3f2941a73079fa8b081e02a522e3d29913e2f", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "homepage": "https://github.com/sindresorhus/ansi-regex", "_id": "ansi-regex@2.0.0", "_shasum": "c5061b6e0ef8a81775e50f5d66151bf6bf371107", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c5061b6e0ef8a81775e50f5d66151bf6bf371107", "size": 1665, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-2.0.0.tgz", "integrity": "sha512-jCcLjwL2jOaTcRIaJkoRteMwNXg8nfJvwT/9K91kwZhH7bf4lsprqZ2+Qa7tSp8BYtejobOCBkDreC07q0KmZw=="}, "directories": {}, "publish_time": 1435680439279, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435680439279, "_cnpmcore_publish_time": "2021-12-13T06:41:57.252Z"}, "1.1.1": {"name": "ansi-regex", "version": "1.1.1", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/ansi-regex"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha test/test.js", "view-supported": "node test/viewCodes.js"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"mocha": "*"}, "gitHead": "47fb974630af70998157b30fad6eb5e5bd7c7cd6", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "homepage": "https://github.com/sindresorhus/ansi-regex", "_id": "ansi-regex@1.1.1", "_shasum": "41c847194646375e6a1a5d10c3ca054ef9fc980d", "_from": ".", "_npmVersion": "2.1.16", "_nodeVersion": "0.10.35", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "dist": {"shasum": "41c847194646375e6a1a5d10c3ca054ef9fc980d", "size": 1802, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-1.1.1.tgz", "integrity": "sha512-q5i8bFLg2wDfsuR56c1NzlJFPzVD+9mxhDrhqOGigEFa87OZHlF+9dWeGWzVTP/0ECiA/JUGzfzRr2t3eYORRw=="}, "directories": {}, "publish_time": 1424597091185, "_hasShrinkwrap": false, "_cnpm_publish_time": 1424597091185, "_cnpmcore_publish_time": "2021-12-13T06:41:57.586Z"}, "1.1.0": {"name": "ansi-regex", "version": "1.1.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-regex"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha test/test.js", "view-supported": "node test/viewCodes.js"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "homepage": "https://github.com/sindresorhus/ansi-regex", "_id": "ansi-regex@1.1.0", "_shasum": "67792c5d6ad05c792d6cd6057ac8f5e69ebf4357", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "dist": {"shasum": "67792c5d6ad05c792d6cd6057ac8f5e69ebf4357", "size": 1075, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-1.1.0.tgz", "integrity": "sha512-+VAkk48zFJGuS3ru8ycFCrX1wif67Tbn+yJHAo5xxFY5pFVp8Fy9WbZqOKYxGCP9dEr4ny8aGbjMMpcTGPQzMA=="}, "directories": {}, "publish_time": 1409402310166, "_hasShrinkwrap": false, "_cnpm_publish_time": 1409402310166, "_cnpmcore_publish_time": "2021-12-13T06:41:58.002Z"}, "1.0.0": {"name": "ansi-regex", "version": "1.0.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-regex"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"mocha": "*"}, "gitHead": "4210f11027ddd7937f9e25a9a1570aee6d0594f5", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "homepage": "https://github.com/sindresorhus/ansi-regex", "_id": "ansi-regex@1.0.0", "_shasum": "54c7ce13af71e436348666484c44516ab9bc144e", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "dist": {"shasum": "54c7ce13af71e436348666484c44516ab9bc144e", "size": 1024, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-1.0.0.tgz", "integrity": "sha512-wzNmkzoFDDHQR1zwctGAX0KOZjX8rwjXG+5j4EeLqr1dHyGwcvH7/AIXk5yYB2Abuh2Ge3H44Fn83csDLLUqQA=="}, "directories": {}, "publish_time": 1407936554050, "_hasShrinkwrap": false, "_cnpm_publish_time": 1407936554050, "_cnpmcore_publish_time": "2021-12-13T06:41:58.406Z"}, "0.2.1": {"name": "ansi-regex", "version": "0.2.1", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-regex"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "homepage": "https://github.com/sindresorhus/ansi-regex", "_id": "ansi-regex@0.2.1", "_shasum": "0d8e946967a3d8143f93e24e298525fc1b2235f9", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0d8e946967a3d8143f93e24e298525fc1b2235f9", "size": 1023, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-0.2.1.tgz", "integrity": "sha512-sGwIGMjhYdW26/IhwK2gkWWI8DRCVO6uj3hYgHT+zD+QL1pa37tM3ujhyfcJIYSbsxp7Gxhy7zrRW/1AHm4BmA=="}, "directories": {}, "publish_time": 1403282643241, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403282643241, "_cnpmcore_publish_time": "2021-12-13T06:41:58.790Z"}, "0.2.0": {"name": "ansi-regex", "version": "0.2.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-regex"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "homepage": "https://github.com/sindresorhus/ansi-regex", "_id": "ansi-regex@0.2.0", "_shasum": "3c48015ac52bcae430b08b822b87522644eb0de7", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3c48015ac52bcae430b08b822b87522644eb0de7", "size": 1015, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-0.2.0.tgz", "integrity": "sha512-Dl8j95am19SdSL9f3jc7rJN9qykOF10PwqRcJX9iXZfxXDHaXHuiYrZDsRWJCEGdsJwyama5HBZONykdFmvf4w=="}, "directories": {}, "publish_time": 1402708373550, "_hasShrinkwrap": false, "_cnpm_publish_time": 1402708373550, "_cnpmcore_publish_time": "2021-12-13T06:41:59.200Z"}, "0.1.0": {"name": "ansi-regex", "version": "0.1.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-regex"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re"], "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "homepage": "https://github.com/sindresorhus/ansi-regex", "_id": "ansi-regex@0.1.0", "_shasum": "55ca60db6900857c423ae9297980026f941ed903", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "55ca60db6900857c423ae9297980026f941ed903", "size": 806, "noattachment": false, "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-0.1.0.tgz", "integrity": "sha512-sKASJ0JEeHkkUN5A7GBQ1JH5EmBGi8dhl9KChCXV40kGmykzW4uLkVB8XbjV7598iK+I/HQOlqJGLCFA+1ZmzQ=="}, "directories": {}, "publish_time": 1401814762332, "_hasShrinkwrap": false, "_cnpm_publish_time": 1401814762332, "_cnpmcore_publish_time": "2021-12-13T06:41:59.637Z"}, "4.1.1": {"name": "ansi-regex", "version": "4.1.1", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "^0.25.0", "xo": "^0.23.0"}, "readmeFilename": "readme.md", "gitHead": "64735d25eb839b55bc9fae3877edb702b4c92ca2", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@4.1.1", "_nodeVersion": "17.3.1", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==", "shasum": "164daac87ab2d6f6db3a29875e2d1766582dabed", "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-4.1.1.tgz", "fileCount": 4, "unpackedSize": 5172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiLA7KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3gQ//a+k/IMT0gBGfY6uA0Cgu+o/GhIow5TQbqkJER9T27TO8Po7h\r\n/07GR3KJTtxgFn+wkRlNLgXxdCRRjXe5df4eHzbMM52Ag49iqrYecm3xC28i\r\nCJArVdmZ0djATnvThwWImD/dHN5cj76MHQzJjWEdWPXXim/cRHHHcHXkWEsv\r\nN64mWjz06J8NqidAS0ARvYGc6QmUeWENzuhUO7Pa2rymBrkccTQkYNJUoTCf\r\nJ+DAtQZpUm9jFHOKoKsNtyBkb3BlPLZVjnfF6TNv0Ya2KiW5N3FIQEeS8h54\r\nM9l82ED8sopqO7Z6Vsps2W7xdCTRvKPht+cZJhet4hmcxMQE/lNtH6R4C1hR\r\n0MzhnSYwvvsDfqDbjNaerMe3/yq/EkUj8DTDqnNBPeODWlLgG3WF3tTlA7aQ\r\nIqGk8bVfd8Fxr6EJ4JKjn4bUCbmTSzqK7lAiDX/pDXMOONiDvRwQaDJbT5y5\r\nycep5aL9mlYGkSOf2dPRp7B71KHKQAmGTl3OupYMrWxSVNAoIk3T4r5j6IX4\r\n+8LlAtusnCJ4mGWo2M4rXhQ1o+5gWyLgyf470hdCkgH24Yp/8348NEMRZD+z\r\nGv3sPclHhjVZzS0GhEIvHUYYGUuSRxM6Vd6g1lgv8e9LI3/yqvf830l/FHQo\r\nJwEpTS3Q8YrM9t2YpodilybqqpuTbu8uoB4=\r\n=Hgo9\r\n-----END PGP SIGNATURE-----\r\n", "size": 2748}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_4.1.1_1647054537685_0.02257829120511201"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-12T03:09:06.031Z"}, "3.0.1": {"name": "ansi-regex", "version": "3.0.1", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ava": "*", "xo": "*"}, "readmeFilename": "readme.md", "gitHead": "f545bdb80048f527889eddb9ac1a851c6f2a2241", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_id": "ansi-regex@3.0.1", "_nodeVersion": "17.3.1", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==", "shasum": "123d6479e92ad45ad897d4054e3c7ca7db4944e1", "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-3.0.1.tgz", "fileCount": 4, "unpackedSize": 4054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQGbIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqy/Q//Vh9oKpgDxgXcW+UYZ9DZKYScVldnKzuT5VNYLrGT8mMMPxAC\r\nws4wVRyeWo8WX4jwQqpZaDxr5WlH+X2tbxM+PmwncwkOYTtbTGutmr+cwc16\r\nXaKNwQZt4F5fA32N39Jxyu05sLqje517n3rRFvN6HqzOw21DYIL6N7UpUXeD\r\nFiwNMFOi2S3RMUJgf6EDVNB59MNX7X6W85cvJrjRK8xfUJB50ODx1WfUNG1X\r\nc96t6swskGBdq0riRxtmUTyJgxkgaL4eHnYMpLSad1Xx5rVyHckL9CFajYPo\r\np64kmi2+UHAswdfzDYuQTuIt2vKzpKCGGP74FSZ5trTsQNuljvjhemFePlAw\r\nXOoq2sf/yTGiNu9W+ved2NPpR/nSQO9UnBp7P/pKcfY92rPOwRc3ypBx4JDC\r\nQHANx8/fM7TtRrVUyVJoyEYzMnDxDQnzeGRrYfSneg1oI7oci62HXmgrfxvh\r\n8TPDgsKALJixmkDc6FQgENjmMrblrmU7ZuK2H1meidi+62MJEoTj5lwXKp9d\r\nhay1RKGB4qKHEO5PVIBrYdvkoZIaUyIrN4KgcqHHjrOKWhcWAZDu3A9wwyLD\r\nAOMLQbPqJccQj2FrSMOGrc1ego70swRFGX21xLW0xqr8p65PTLGBKp0+OgC9\r\nlcz8F/xfg1PkqVRZw4TGuEcBE3EHcDFSrr0=\r\n=xF18\r\n-----END PGP SIGNATURE-----\r\n", "size": 2246}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_3.0.1_1648387784373_0.8659992768445368"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-27T13:29:47.468Z"}, "6.1.0": {"name": "ansi-regex", "version": "6.1.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "funding": "https://github.com/chalk/ansi-regex?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ansi-escapes": "^5.0.0", "ava": "^3.15.0", "tsd": "^0.21.0", "xo": "^0.54.2"}, "_id": "ansi-regex@6.1.0", "gitHead": "f338e1814144efb950276aac84135ff86b72dc8e", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_nodeVersion": "18.20.4", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==", "shasum": "95ec409c69619d6cb1b8b34f14b660ef28ebd654", "tarball": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.1.0.tgz", "fileCount": 5, "unpackedSize": 5412, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDGQvwQxWIDhiEnJTo1Tb35X3bxVWxMjBoPaj+qTxlfnAiA3pztqy3OFgYbMqWZC5GdWT1yG2frnePf72yHSrhynBw=="}], "size": 2658}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_6.1.0_1725890276733_0.3140624451173344"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-09T13:57:56.873Z", "publish_time": 1725890276873, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "_source_registry_name": "default"}