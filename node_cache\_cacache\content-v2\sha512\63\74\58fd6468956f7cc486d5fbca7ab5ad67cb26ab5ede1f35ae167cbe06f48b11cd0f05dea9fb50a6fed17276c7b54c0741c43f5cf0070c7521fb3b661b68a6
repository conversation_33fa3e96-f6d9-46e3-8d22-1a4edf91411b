{"_attachments": {}, "_id": "web-streams-polyfill", "_rev": "895-61f1456ab677e08f5113c27c", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "description": "Web Streams, based on the WHATWG spec reference implementation", "dist-tags": {"latest": "4.1.0", "next": "4.0.0-beta.3"}, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "name": "web-streams-polyfill", "readme": "# web-streams-polyfill\n\nWeb Streams, based on the WHATWG spec reference implementation.  \n\n[![build status](https://api.travis-ci.com/MattiasBuelens/web-streams-polyfill.svg?branch=master)](https://travis-ci.com/MattiasBuelens/web-streams-polyfill)\n[![npm version](https://img.shields.io/npm/v/web-streams-polyfill.svg)](https://www.npmjs.com/package/web-streams-polyfill)\n[![license](https://img.shields.io/npm/l/web-streams-polyfill.svg)](https://github.com/MattiasBuelens/web-streams-polyfill/blob/master/LICENSE)\n\n## Links\n\n - [Official spec][spec]\n - [Reference implementation][ref-impl]\n\n## Usage\n\nThis library comes in multiple variants:\n* `web-streams-polyfill`: a [ponyfill] that provides the stream implementations \n  without replacing any globals, targeting ES2015+ environments.\n  Recommended for use in Node 6+ applications, or in web libraries supporting modern browsers.\n* `web-streams-polyfill/es5`: a ponyfill targeting ES5+ environments.\n  Recommended for use in legacy Node applications, or in web libraries supporting older browsers.\n* `web-streams-polyfill/polyfill`: a polyfill that replaces the native stream implementations,\n  targeting ES2015+ environments.\n  Recommended for use in web apps supporting modern browsers through a `<script>` tag.\n* `web-streams-polyfill/polyfill/es5`: a polyfill targeting ES5+ environments.\n  Recommended for use in web apps supporting older browsers through a `<script>` tag.\n\nEach variant also includes TypeScript type definitions, compatible with the DOM type definitions for streams included in TypeScript.\nThese type definitions require TypeScript version 4.7 or higher.\n\nIn version 4, the list of variants was reworked to have more modern defaults and to reduce the download size of the package.\nSee the [migration guide][migrating] for more information.\n\nUsage as a polyfill:\n```html\n<!-- option 1: hosted by unpkg CDN -->\n<script src=\"https://unpkg.com/web-streams-polyfill/dist/polyfill.js\"></script>\n<!-- option 2: self hosted -->\n<script src=\"/path/to/web-streams-polyfill/dist/polyfill.js\"></script>\n<script>\nvar readable = new ReadableStream();\n</script>\n```\nUsage as a Node module:\n```js\nvar streams = require(\"web-streams-polyfill\");\nvar readable = new streams.ReadableStream();\n```\nUsage as a ponyfill from within a ES2015 module:\n```js\nimport { ReadableStream } from \"web-streams-polyfill\";\nconst readable = new ReadableStream();\n```\nUsage as a polyfill from within an ES2015 module:\n```js\nimport \"web-streams-polyfill/polyfill\";\nconst readable = new ReadableStream();\n```\n\n## Compatibility\n\nThe `polyfill` and `ponyfill` variants work in any ES2015-compatible environment.\n\nThe `polyfill/es5` and `ponyfill/es5` variants work in any ES5-compatible environment that has a global `Promise`.\nIf you need to support older browsers or Node versions that do not have a native `Promise` implementation\n(check the [support table][promise-support]), you must first include a `Promise` polyfill\n(e.g. [promise-polyfill][promise-polyfill]).\n\n[Async iterable support for `ReadableStream`][rs-asynciterator] is available in all variants, but requires an ES2018-compatible environment or a polyfill for `Symbol.asyncIterator`.\n\n[`WritableStreamDefaultController.signal`][ws-controller-signal] is available in all variants, but requires a global `AbortController` constructor. If necessary, consider using a polyfill such as [abortcontroller-polyfill].\n\n[Reading with a BYOB reader][mdn-byob-read] is available in all variants, but requires `ArrayBuffer.prototype.transfer()` or `structuredClone()` to exist in order to correctly transfer the given view's buffer. If not available, then the buffer won't be transferred during the read.\n\n### Tooling compatibility\n\nThis package uses [subpath exports](https://nodejs.org/api/packages.html#subpath-exports) for its variants. As such, you need Node 12 or higher in order to `import` or `require()` such a variant.\n\nWhen using TypeScript, make sure your [`moduleResolution`](https://www.typescriptlang.org/tsconfig#moduleResolution) is set to `\"node16\"`, `\"nodenext\"` or `\"bundler\"`.\n\n## Compliance\n\nThe polyfill implements [version `fa4891a` (3 Dec 2024)][spec-snapshot] of the streams specification.\n\nThe polyfill is tested against the same [web platform tests][wpt] that are used by browsers to test their native implementations.\nThe polyfill aims to pass all tests, although it allows some exceptions for practical reasons:\n* The default (ES2015) variant passes all of the tests, except for the [test for the prototype of `ReadableStream`'s async iterator][wpt-async-iterator-prototype].\n  Retrieving the correct `%AsyncIteratorPrototype%` requires using an async generator (`async function* () {}`), which is invalid syntax before ES2018.\n  Instead, the polyfill [creates its own version][stub-async-iterator-prototype] which is functionally equivalent to the real prototype.\n* The ES5 variant passes the same tests as the ES2015 variant, except for various tests about specific characteristics of the constructors, properties and methods.\n  These test failures do not affect the run-time behavior of the polyfill.\n  For example:\n  * The `name` property of down-leveled constructors is incorrect.\n  * The `length` property of down-leveled constructors and methods with optional arguments is incorrect.\n  * Not all properties and methods are correctly marked as non-enumerable.\n  * Down-leveled class methods are not correctly marked as non-constructable.\n\n## Contributors\n\nThanks to these people for their work on [the original polyfill][creatorrr-polyfill]:\n\n - Diwank Singh Tomer ([creatorrr](https://github.com/creatorrr))\n - Anders Riutta ([ariutta](https://github.com/ariutta))\n\n[spec]: https://streams.spec.whatwg.org\n[ref-impl]: https://github.com/whatwg/streams\n[ponyfill]: https://github.com/sindresorhus/ponyfill\n[migrating]: https://github.com/MattiasBuelens/web-streams-polyfill/blob/master/MIGRATING.md\n[promise-support]: https://kangax.github.io/compat-table/es6/#test-Promise\n[promise-polyfill]: https://www.npmjs.com/package/promise-polyfill\n[rs-asynciterator]: https://streams.spec.whatwg.org/#rs-asynciterator\n[ws-controller-signal]: https://streams.spec.whatwg.org/#ws-default-controller-signal\n[abortcontroller-polyfill]: https://www.npmjs.com/package/abortcontroller-polyfill\n[mdn-byob-read]: https://developer.mozilla.org/en-US/docs/Web/API/ReadableStreamBYOBReader/read\n[spec-snapshot]: https://streams.spec.whatwg.org/commit-snapshots/fa4891a35ff05281ff8ed66f8ad447644ea7cec3/\n[wpt]: https://github.com/web-platform-tests/wpt/tree/7ef95a1c3f1c178e455b21569eddb31af7c3691f/streams\n[wpt-async-iterator-prototype]: https://github.com/web-platform-tests/wpt/blob/7ef95a1c3f1c178e455b21569eddb31af7c3691f/streams/readable-streams/async-iterator.any.js#L24\n[stub-async-iterator-prototype]: https://github.com/MattiasBuelens/web-streams-polyfill/blob/v4.0.0/src/lib/readable-stream/async-iterator.ts#L143-L147\n[creatorrr-polyfill]: https://github.com/creatorrr/web-streams-polyfill\n", "time": {"created": "2022-01-26T12:58:18.710Z", "modified": "2025-01-05T20:44:59.602Z", "3.2.0": "2021-11-06T15:05:35.960Z", "4.0.0-beta.1": "2021-09-06T20:14:22.133Z", "3.1.1": "2021-09-05T22:21:14.167Z", "3.1.0": "2021-07-21T21:56:44.376Z", "3.0.3": "2021-04-09T17:12:48.510Z", "3.0.2": "2021-02-09T23:46:12.139Z", "3.0.1": "2020-11-12T00:08:03.392Z", "3.0.0": "2020-07-20T19:19:03.695Z", "2.1.1": "2020-04-11T08:44:54.533Z", "2.1.0": "2020-02-23T11:54:11.636Z", "2.0.6": "2019-11-08T12:21:41.985Z", "2.0.5": "2019-10-08T12:43:19.570Z", "2.0.4": "2019-08-01T18:24:23.106Z", "2.0.3": "2019-04-04T22:02:37.198Z", "2.0.2": "2019-03-17T16:52:29.614Z", "2.0.1": "2019-03-16T12:20:24.835Z", "2.0.0": "2019-03-10T14:23:06.906Z", "1.3.2": "2016-12-14T14:24:30.374Z", "1.3.1": "2016-12-14T07:53:14.473Z", "1.3.0": "2016-08-07T08:45:04.848Z", "1.2.2": "2016-07-16T08:52:07.238Z", "1.2.1": "2016-07-16T08:32:16.074Z", "1.2.0": "2016-07-16T07:53:34.453Z", "1.1.1": "2016-04-03T06:35:16.278Z", "1.1.0": "2016-04-03T06:26:58.870Z", "1.0.1": "2016-03-03T22:17:52.632Z", "1.0.0": "2016-03-02T18:29:50.194Z", "3.2.1": "2022-04-07T20:29:00.636Z", "4.0.0-beta.2": "2022-04-11T22:37:10.568Z", "4.0.0-beta.3": "2022-05-24T19:52:20.773Z", "3.3.0": "2024-01-04T15:23:42.365Z", "3.3.1": "2024-01-04T17:40:05.287Z", "3.3.2": "2024-01-04T17:51:22.720Z", "3.3.3": "2024-02-16T21:15:47.924Z", "4.0.0": "2024-02-28T21:53:04.107Z", "4.1.0": "2025-01-05T20:44:21.625Z"}, "versions": {"3.2.0": {"name": "web-streams-polyfill", "version": "3.2.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@rollup/plugin-strip": "^2.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/node": "^14.14.37", "@typescript-eslint/eslint-plugin": "^4.21.0", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "eslint": "^7.23.0", "jasmine": "^3.7.0", "micromatch": "^4.0.2", "rollup": "^2.44.0", "rollup-plugin-terser": "^7.0.2", "ts-morph": "^10.0.2", "tslib": "^2.2.0", "typescript": "^4.2.4", "wpt-runner": "^3.2.1"}, "gitHead": "224d8538eb2df5950a29de97c7409f32e2b2981f", "_id": "web-streams-polyfill@3.2.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "a6b74026b38e4885869fb5c589e90b95ccfc7965", "size": 1335250, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.2.0.tgz", "integrity": "sha512-EqPmREeOzttaLRm5HS7io98goBgZ7IVz79aDvqjD0kYXLtFZTc0T/U6wHTPKyIjb+MdN7DFIIX6hgdBEpWmfPA=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.2.0_1636211135718_0.6988981602649902"}, "_hasShrinkwrap": false, "publish_time": 1636211135960, "_cnpm_publish_time": 1636211135960, "_cnpmcore_publish_time": "2021-12-13T14:48:10.243Z"}, "4.0.0-beta.1": {"name": "web-streams-polyfill", "version": "4.0.0-beta.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/ponyfill.js", "module": "dist/ponyfill.mjs", "exports": {".": {"import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./polyfill": "./dist/polyfill.js", "./polyfill/es5": "./dist/polyfill.es5.js", "./dist/*": "./dist/*", "./types/*": "./types/*", "./package": "./package.json", "./package.json": "./package.json"}, "types": "types/ponyfill.d.ts", "typesVersions": {">=3.6": {".": ["./types/ponyfill.d.ts"], "./es5": ["./types/ponyfill.d.ts"], "./polyfill": ["./types/polyfill.d.ts"], "./polyfill/es5": ["./types/polyfill.d.ts"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundlers", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:bundlers": "npm run test:rollup && npm run test:webpack", "test:rollup": "cd test/rollup && npm ci && npm test", "test:webpack": "cd test/webpack && npm ci && npm test", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local", "prepare": "npm run build"}, "engines": {"node": ">= 12"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.18.7", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-strip": "^2.1.0", "@rollup/plugin-typescript": "^8.2.5", "@types/node": "^14.17.12", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "@ungap/promise-all-settled": "^1.1.2", "eslint": "^7.32.0", "jasmine": "^3.9.0", "micromatch": "^4.0.4", "rollup": "^2.56.3", "rollup-plugin-terser": "^7.0.2", "tslib": "^2.3.0", "typescript": "~4.3.5", "wpt-runner": "^3.2.1"}, "readmeFilename": "README.md", "gitHead": "3a3536ffc092f420c9ee1c2945bc7e5e0a1e78bf", "_id": "web-streams-polyfill@4.0.0-beta.1", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "3b19b9817374b7cee06d374ba7eeb3aeb80e8c95", "size": 87097, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.1.tgz", "integrity": "sha512-3ux37gEX670UUphBF9AMCq8XM6iQ8Ac6A+DSRRjDoRBm1ufCkaCDdNVbaqq60PsEkdNlLKrGtv/YBP4EJXqNtQ=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_4.0.0-beta.1_1630959261984_0.21839278606178159"}, "_hasShrinkwrap": false, "publish_time": 1630959262133, "_cnpm_publish_time": 1630959262133, "_cnpmcore_publish_time": "2021-12-13T14:48:10.682Z"}, "3.1.1": {"name": "web-streams-polyfill", "version": "3.1.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@rollup/plugin-strip": "^2.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/node": "^14.14.37", "@typescript-eslint/eslint-plugin": "^4.21.0", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "eslint": "^7.23.0", "jasmine": "^3.7.0", "micromatch": "^4.0.2", "rollup": "^2.44.0", "rollup-plugin-terser": "^7.0.2", "ts-morph": "^10.0.2", "tslib": "^2.2.0", "typescript": "^4.2.4", "wpt-runner": "^3.2.1"}, "gitHead": "270791329d8d9c56769ff9806e430344ce18dbec", "_id": "web-streams-polyfill@3.1.1", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "1516f2d4ea8f1bdbfed15eb65cb2df87098c8364", "size": 1325197, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.1.1.tgz", "integrity": "sha512-Czi3fG883e96T4DLEPRvufrF2ydhOOW1+1a6c3gNjH2aIh50DNFBdfwh2AKoOf1rXvpvavAoA11Qdq9+BKjE0Q=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.1.1_1630880473934_0.1325941418478258"}, "_hasShrinkwrap": false, "publish_time": 1630880474167, "_cnpm_publish_time": 1630880474167, "_cnpmcore_publish_time": "2021-12-13T14:48:11.451Z"}, "3.1.0": {"name": "web-streams-polyfill", "version": "3.1.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@rollup/plugin-strip": "^2.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/node": "^14.14.37", "@typescript-eslint/eslint-plugin": "^4.21.0", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "eslint": "^7.23.0", "jasmine": "^3.7.0", "micromatch": "^4.0.2", "rollup": "^2.44.0", "rollup-plugin-terser": "^7.0.2", "ts-morph": "^10.0.2", "tslib": "^2.2.0", "typescript": "^4.2.4", "wpt-runner": "^3.2.1"}, "gitHead": "80a46678d3b0062a1f5db3b36e436facbe9f5614", "_id": "web-streams-polyfill@3.1.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"shasum": "86f983b4f44745502b0d8563d9ef3afc609d4465", "size": 1324677, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.1.0.tgz", "integrity": "sha512-wO9r1YnYe7kFBLHyyVEhV1H8VRWoNiNnuP+v/HUUmSTaRF8F93Kmd3JMrETx0f11GXxRek6OcL2QtjFIdc5WYw=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.1.0_1626904604156_0.990376894300866"}, "_hasShrinkwrap": false, "publish_time": 1626904604376, "_cnpm_publish_time": 1626904604376, "_cnpmcore_publish_time": "2021-12-13T14:48:12.292Z"}, "3.0.3": {"name": "web-streams-polyfill", "version": "3.0.3", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@rollup/plugin-strip": "^2.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/node": "^14.14.37", "@typescript-eslint/eslint-plugin": "^4.21.0", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "eslint": "^7.23.0", "jasmine": "^3.7.0", "micromatch": "^4.0.2", "rollup": "^2.44.0", "rollup-plugin-terser": "^7.0.2", "ts-morph": "^10.0.2", "tslib": "^2.2.0", "typescript": "^4.2.4", "wpt-runner": "^3.2.1"}, "gitHead": "69422f7be3d3f59e9b3cc37ead8b474e67d78f95", "_id": "web-streams-polyfill@3.0.3", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.8", "dist": {"shasum": "f49e487eedeca47a207c1aee41ee5578f884b42f", "size": 1232068, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.0.3.tgz", "integrity": "sha512-d2H/t0eqRNM4w2WvmTdoeIvzAUSpK7JmATB8Nr2lb7nQ9BTIJVjbQ/TRFVEh2gUH1HwclPdoPtfMoFfetXaZnA=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.0.3_1617988368328_0.6748844068144739"}, "_hasShrinkwrap": false, "publish_time": 1617988368510, "_cnpm_publish_time": 1617988368510, "_cnpmcore_publish_time": "2021-12-13T14:48:12.965Z"}, "3.0.2": {"name": "web-streams-polyfill", "version": "3.0.2", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.9.2", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.3.3", "@rollup/plugin-strip": "^2.0.0", "@rollup/plugin-typescript": "^5.0.2", "@types/node": "^12.12.50", "@typescript-eslint/eslint-plugin": "^3.6.1", "@typescript-eslint/parser": "^3.6.1", "@ungap/promise-all-settled": "^1.1.0", "eslint": "^7.5.0", "jasmine": "^3.5.0", "micromatch": "^4.0.2", "rollup": "^2.22.1", "rollup-plugin-terser": "^6.1.0", "ts-morph": "^7.1.2", "tslib": "^2.0.0", "typescript": "^3.9.7", "wpt-runner": "^3.2.0"}, "gitHead": "6915a01aa6c92b4d3cb306fb7a66299e881a7bcc", "_id": "web-streams-polyfill@3.0.2", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.8", "dist": {"shasum": "402061089a61a2465457938abaa1b9e4db1bcc0f", "size": 1229434, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.0.2.tgz", "integrity": "sha512-JTNkNbAKoSo8NKiqu2UUaqRFCDWWZaCOsXuJEsToWopikTA0YHKKUf91GNkS/SnD8JixOkJjVsiacNlrFnRECA=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.0.2_1612914371546_0.6477250639036616"}, "_hasShrinkwrap": false, "publish_time": 1612914372139, "_cnpm_publish_time": 1612914372139, "_cnpmcore_publish_time": "2021-12-13T14:48:13.618Z"}, "3.0.1": {"name": "web-streams-polyfill", "version": "3.0.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.9.2", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.3.3", "@rollup/plugin-strip": "^2.0.0", "@rollup/plugin-typescript": "^5.0.2", "@types/node": "^12.12.50", "@typescript-eslint/eslint-plugin": "^3.6.1", "@typescript-eslint/parser": "^3.6.1", "@ungap/promise-all-settled": "^1.1.0", "eslint": "^7.5.0", "jasmine": "^3.5.0", "micromatch": "^4.0.2", "rollup": "^2.22.1", "rollup-plugin-terser": "^6.1.0", "ts-morph": "^7.1.2", "tslib": "^2.0.0", "typescript": "^3.9.7", "wpt-runner": "^3.2.0"}, "gitHead": "6e93b8f0b09c47e1702bf2b5273696279f211de4", "_id": "web-streams-polyfill@3.0.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.8", "dist": {"shasum": "1f836eea307e8f4af15758ee473c7af755eb879e", "size": 1228371, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.0.1.tgz", "integrity": "sha512-M+EmTdszMWINywOZaqpZ6VIEDUmNpRaTOuizF0ZKPjSDC8paMRe/jBBwFv0Yeyn5WYnM5pMqMQa82vpaE+IJRw=="}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.0.1_1605139683130_0.8118750364520677"}, "_hasShrinkwrap": false, "publish_time": 1605139683392, "_cnpm_publish_time": 1605139683392, "_cnpmcore_publish_time": "2021-12-13T14:48:14.275Z"}, "3.0.0": {"name": "web-streams-polyfill", "version": "3.0.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.9.2", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.3.3", "@rollup/plugin-strip": "^2.0.0", "@rollup/plugin-typescript": "^5.0.2", "@types/node": "^12.12.50", "@typescript-eslint/eslint-plugin": "^3.6.1", "@typescript-eslint/parser": "^3.6.1", "@ungap/promise-all-settled": "^1.1.0", "eslint": "^7.5.0", "jasmine": "^3.5.0", "micromatch": "^4.0.2", "rollup": "^2.22.1", "rollup-plugin-terser": "^6.1.0", "ts-morph": "^7.1.2", "tslib": "^2.0.0", "typescript": "^3.9.7", "wpt-runner": "^3.2.0"}, "gitHead": "787ec35cbc2eca4d87a398aea08e0b2aeb7b61d9", "_id": "web-streams-polyfill@3.0.0", "_nodeVersion": "12.17.0", "_npmVersion": "6.14.5", "dist": {"shasum": "c8bcb1e2ec1820088ca201674486a0f1feae848b", "size": 1098705, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.0.0.tgz", "integrity": "sha512-tcZlIJ+VBxuDXdRFF3PCZTJ3yUISGklG4hkl3CDGOlZ8XwpN90L5YsJNoSPH72wZ4nbsatE/OfIaxfM3p+6W7w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.0.0_1595272743491_0.1276916544321085"}, "_hasShrinkwrap": false, "publish_time": 1595272743695, "_cnpm_publish_time": 1595272743695, "_cnpmcore_publish_time": "2021-12-13T14:48:14.907Z"}, "2.1.1": {"name": "web-streams-polyfill", "version": "2.1.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.7.13", "@rollup/plugin-inject": "^4.0.1", "@rollup/plugin-replace": "^2.3.1", "@rollup/plugin-strip": "^1.3.2", "@rollup/plugin-typescript": "^4.0.0", "@types/node": "^12.12.30", "@typescript-eslint/eslint-plugin": "^2.23.0", "@typescript-eslint/parser": "^2.23.0", "eslint": "^6.8.0", "jasmine": "^3.5.0", "micromatch": "^4.0.2", "rollup": "^2.6.0", "rollup-plugin-terser": "^5.3.0", "ts-morph": "^6.0.3", "tslib": "^1.11.1", "typescript": "^3.8.3", "wpt-runner": "^2.8.0"}, "gitHead": "92aa5943d095c61570d7b63be7c1bd7a5570399a", "_id": "web-streams-polyfill@2.1.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.4", "dist": {"shasum": "2c82b6193849ccb9efaa267772c28260ef68d6d2", "size": 523056, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-2.1.1.tgz", "integrity": "sha512-dlNpL2aab3g8CKfGz6rl8FNmGaRWLLn2g/DtSc9IjB30mEdE6XxzPfPSig5BwGSzI+oLxHyETrQGKjrVVhbLCg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_2.1.1_1586594694397_0.06891971739233527"}, "_hasShrinkwrap": false, "publish_time": 1586594694533, "_cnpm_publish_time": 1586594694533, "_cnpmcore_publish_time": "2021-12-13T14:48:15.457Z"}, "2.1.0": {"name": "web-streams-polyfill", "version": "2.1.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project src --emitDeclarationOnly && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.7.0", "@types/node": "^12.12.17", "@typescript-eslint/eslint-plugin": "^2.11.0", "@typescript-eslint/parser": "^2.11.0", "eslint": "^6.7.2", "jasmine": "^3.5.0", "micromatch": "^4.0.2", "rollup": "^1.27.10", "rollup-plugin-inject": "^3.0.2", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-strip": "^1.2.2", "rollup-plugin-terser": "^5.1.3", "rollup-plugin-typescript2": "^0.25.3", "ts-morph": "^6.0.2", "tslib": "^1.10.0", "typescript": "^3.7.3", "wpt-runner": "^2.8.0"}, "gitHead": "2160765a54ba3177e3f8ac289bc3bba231ba5166", "_id": "web-streams-polyfill@2.1.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.13.6", "dist": {"shasum": "a4673244f92338e714bad632d2e0ef0004098271", "size": 275190, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-2.1.0.tgz", "integrity": "sha512-ZgJ7lsxTM0GR77Nwzt950NHd/CjWzVEct2DH48Dy8/u+3r4TD9o8Bo1VNFK04QajgcEjr0CZPlRhau8LtR3YHQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_2.1.0_1582458851474_0.9842460362145058"}, "_hasShrinkwrap": false, "publish_time": 1582458851636, "_cnpm_publish_time": 1582458851636, "_cnpmcore_publish_time": "2021-12-13T14:48:15.974Z"}, "2.0.6": {"name": "web-streams-polyfill", "version": "2.0.6", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project src --emitDeclarationOnly && api-extractor run --local", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.3.4", "@types/node": "^12.7.11", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "eslint": "^6.1.0", "jasmine": "^3.5.0", "micromatch": "^4.0.2", "rollup": "^1.18.0", "rollup-plugin-inject": "^3.0.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-strip": "^1.2.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.22.1", "tslib": "^1.10.0", "typescript": "^3.5.3", "wpt-runner": "^2.8.0"}, "gitHead": "a719b21d7cbb5536a0e0424aced3e914af7514e2", "_id": "web-streams-polyfill@2.0.6", "_nodeVersion": "10.16.0", "_npmVersion": "6.12.0", "dist": {"shasum": "76e0504b4f15a6310cfc9e78e27637b648f9741e", "size": 273924, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-2.0.6.tgz", "integrity": "sha512-nXOi4fBykO4LzyQhZX3MAGib635KGZBoNTkNXrNIkz0zthEf2QokEWxRb0H632xNLDWtHFb1R6dFGzksjYMSDw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_2.0.6_1573215701826_0.2716769871954485"}, "_hasShrinkwrap": false, "publish_time": 1573215701985, "_cnpm_publish_time": 1573215701985, "_cnpmcore_publish_time": "2021-12-13T14:48:16.507Z"}, "2.0.5": {"name": "web-streams-polyfill", "version": "2.0.5", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project src --emitDeclarationOnly && api-extractor run --local", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.3.4", "@types/node": "^12.7.11", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "eslint": "^6.1.0", "jasmine": "^3.5.0", "micromatch": "^4.0.2", "rollup": "^1.18.0", "rollup-plugin-inject": "^3.0.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-strip": "^1.2.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.22.1", "tslib": "^1.10.0", "typescript": "^3.5.3", "wpt-runner": "^2.8.0"}, "gitHead": "3adecd0a89706c40dd5fe1da3fa212eab6ae3dbe", "_id": "web-streams-polyfill@2.0.5", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.0", "dist": {"shasum": "63defc04cd3b56d6f8bf2c6cdf996b626f812821", "size": 273862, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-2.0.5.tgz", "integrity": "sha512-jECu/7ilpv3Q3bRP9yVtC+/DkEKM4imnIJMT7DD5Dx0TB9ylWpPtanpnQSxf4x2q1iyMr6kYEYD9hGipbRhh5w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_2.0.5_1570538599384_0.21601584840462507"}, "_hasShrinkwrap": false, "publish_time": 1570538599570, "_cnpm_publish_time": 1570538599570, "_cnpmcore_publish_time": "2021-12-13T14:48:17.080Z"}, "2.0.4": {"name": "web-streams-polyfill", "version": "2.0.4", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "scripts": {"test": "npm run test:types && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project src --emitDeclarationOnly && api-extractor run --local", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.3.4", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "eslint": "^6.1.0", "micromatch": "^4.0.2", "rollup": "^1.18.0", "rollup-plugin-inject": "^3.0.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-strip": "^1.2.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.22.1", "tslib": "^1.10.0", "typescript": "^3.5.3", "wpt-runner": "^2.8.0"}, "gitHead": "cf49b9643f2e840c4afa4f4521c5c1d034d1609b", "_id": "web-streams-polyfill@2.0.4", "_nodeVersion": "10.15.0", "_npmVersion": "6.8.0", "dist": {"shasum": "67ba73e42ae7c1d6dd64498e6e0a7952fb3993eb", "size": 266144, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-2.0.4.tgz", "integrity": "sha512-hsLbiIZou2pZoht4VxfITEbCUz09YhZaaYFNICaZo7SOJrtW3/35LsJoAXeB8HITOlKpuWU0tFvtItOS2PpbsA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_2.0.4_1564683862797_0.2947333946350075"}, "_hasShrinkwrap": false, "publish_time": 1564683863106, "_cnpm_publish_time": 1564683863106, "_cnpmcore_publish_time": "2021-12-13T14:48:17.552Z"}, "2.0.3": {"name": "web-streams-polyfill", "version": "2.0.3", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "scripts": {"test": "npm run test:types && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "lint": "eslint \"src/**/*.ts\"", "build": "rollup -c", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^1.4.2", "eslint": "^5.15.1", "micromatch": "^3.1.10", "rollup": "^1.6.0", "rollup-plugin-dts": "^0.13.0", "rollup-plugin-inject": "^2.2.0", "rollup-plugin-replace": "^2.1.1", "rollup-plugin-strip": "^1.2.1", "rollup-plugin-terser": "^4.0.4", "tslib": "^1.9.3", "typescript": "^3.3.3", "wpt-runner": "^2.7.1"}, "gitHead": "6b678f2fae982d08d60c907a96a1aa86259f4bbf", "_id": "web-streams-polyfill@2.0.3", "_nodeVersion": "10.15.0", "_npmVersion": "6.8.0", "dist": {"shasum": "0c396f069a5eedc96c711393b12f2c67cf283a00", "size": 525940, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-2.0.3.tgz", "integrity": "sha512-pOqiHmL3RBAGS+SgOR42RbPU6nc8/n15N2rsOXFYHLnTfs2Z8QHs8AizOeOaYEnhwPN4+hu3M2D9XvAqzvt6MA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_2.0.3_1554415356959_0.2688065309882972"}, "_hasShrinkwrap": false, "publish_time": 1554415357198, "_cnpm_publish_time": 1554415357198, "_cnpmcore_publish_time": "2021-12-13T14:48:18.123Z"}, "2.0.2": {"name": "web-streams-polyfill", "version": "2.0.2", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "scripts": {"test": "npm run test:types && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:types && npm run build:bundle", "build:bundle": "rollup -c", "build:types": "rollup -c rollup-types.config.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^1.4.2", "eslint": "^5.15.1", "micromatch": "^3.1.10", "rollup": "^1.6.0", "rollup-plugin-dts": "^0.13.0", "rollup-plugin-inject": "^2.2.0", "rollup-plugin-strip": "^1.2.1", "rollup-plugin-terser": "^4.0.4", "typescript": "^3.3.3", "wpt-runner": "^2.7.1"}, "gitHead": "216008e4778a3992fb9872c9f29fb25f7475e8a2", "_id": "web-streams-polyfill@2.0.2", "_nodeVersion": "10.15.3", "_npmVersion": "6.8.0", "dist": {"shasum": "de70800b0d08ec9be99b5cf6c7c628906e4af95d", "size": 522454, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-2.0.2.tgz", "integrity": "sha512-ieGrNJWWtDD4ywBWWxqBh2OvHJL6KKFag63cxTEHcXKHyeLRhLcH6wI7gVhMVk3rDSDZLpZjGmgkDbL1RRDkPQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_2.0.2_1552841549312_0.513413141185949"}, "_hasShrinkwrap": false, "publish_time": 1552841549614, "_cnpm_publish_time": 1552841549614, "_cnpmcore_publish_time": "2021-12-13T14:48:18.675Z"}, "2.0.1": {"name": "web-streams-polyfill", "version": "2.0.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "scripts": {"test": "npm run test:types && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:types && npm run build:bundle", "build:bundle": "rollup -c", "build:types": "rollup -c rollup-types.config.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^1.4.2", "eslint": "^5.15.1", "micromatch": "^3.1.10", "rollup": "^1.6.0", "rollup-plugin-dts": "^0.13.0", "rollup-plugin-inject": "^2.2.0", "rollup-plugin-strip": "^1.2.1", "rollup-plugin-terser": "^4.0.4", "typescript": "^3.3.3", "wpt-runner": "^2.7.1"}, "gitHead": "e70a76162c5de7dffe3b716131e6aeb6bdf826d4", "_id": "web-streams-polyfill@2.0.1", "_nodeVersion": "10.15.3", "_npmVersion": "6.8.0", "dist": {"shasum": "15b7f0e7337caa90645167d302e716747b18744b", "size": 509709, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-2.0.1.tgz", "integrity": "sha512-H<PERSON>OMKJ26i5bPVSDXsEPE7OU+twuqCWLC8ZSfhIedeQnjzGjCrC4S7n5w9Ymh0rqZyzsfdbRmOGlEmB069dmjuA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_2.0.1_1552738824639_0.9583898337163212"}, "_hasShrinkwrap": false, "publish_time": 1552738824835, "_cnpm_publish_time": 1552738824835, "_cnpmcore_publish_time": "2021-12-13T14:48:19.244Z"}, "2.0.0": {"name": "web-streams-polyfill", "version": "2.0.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "scripts": {"test": "npm run test:types && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:types && npm run build:bundle", "build:bundle": "rollup -c", "build:types": "rollup -c rollup-types.config.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^1.4.2", "eslint": "^5.15.1", "micromatch": "^3.1.10", "rollup": "^1.6.0", "rollup-plugin-dts": "^0.13.0", "rollup-plugin-inject": "^2.2.0", "rollup-plugin-strip": "^1.2.1", "rollup-plugin-terser": "^4.0.4", "typescript": "^3.3.3", "wpt-runner": "^2.7.1"}, "readmeFilename": "README.md", "gitHead": "8bf71918f87174ebe2a9ca7546f8afc83482efda", "_id": "web-streams-polyfill@2.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.8.0", "dist": {"shasum": "a7a58b042fdcd93c55c69c6524231e08b577ed02", "size": 500558, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-2.0.0.tgz", "integrity": "sha512-CZrboLwoXbJmdUokO7TN1Re4K8a+PcHDbsewQTVWQRUUZI30OOUpECeW3I3GidmalHkTdWsi4w3ftj6bK8Nwog=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_2.0.0_1552227786738_0.8309113432612656"}, "_hasShrinkwrap": false, "publish_time": 1552227786906, "_cnpm_publish_time": 1552227786906, "_cnpmcore_publish_time": "2021-12-13T14:48:19.826Z"}, "1.3.2": {"name": "web-streams-polyfill", "version": "1.3.2", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "jsnext:main": "index.es6.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test && cp ../../run-web-platform-tests-on-bundle.js ./ && node --expose_gc run-web-platform-tests-on-bundle.js && rm ./run-web-platform-tests-on-bundle.js)", "bundle": "npm-run-all bundle:*", "bundle:no-min": "browserify index.es6.js -d -s default -t [ babelify ] | derequire | exorcist ./dist/polyfill.js.map > ./dist/polyfill.js", "bundle:min": "browserify -g uglifyify index.es6.js -d -i ./spec/reference-implementation/lib/utils.js -s default -t [ babelify --plugins babel-plugin-unassert uglifyify ] | derequire | exorcist ./dist/polyfill.min.js.map > ./dist/polyfill.min.js", "build": "npm run bundle", "prebuild": "git submodule update --init", "prepublish": "npm run build"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.11.4", "babel-plugin-unassert": "^2.1.1", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.9.0", "babel-register": "^6.6.0", "babelify": "^7.3.0", "browserify": "^13.0.1", "derequire": "^2.0.3", "exorcist": "^0.4.0", "npm-run-all": "^2.3.0", "uglifyify": "^3.0.2", "yarn": "^0.17.0"}, "dependencies": {}, "gitHead": "385e96b0b2fd0a5afa9f216c2f3b803f2f1dab7a", "_id": "web-streams-polyfill@1.3.2", "_shasum": "3719245e909282d93967825f44bcd550e9c03995", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "dist": {"shasum": "3719245e909282d93967825f44bcd550e9c03995", "size": 110875, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.3.2.tgz", "integrity": "sha512-zU+H6o+0cRbwxHAA2rsHeZ3Cd9gPyWElxuPSKF/+o3vQcdoRa7fbONimwItgR8c48N4UHFcuimxLgAIamP0p4A=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.3.2.tgz_1481725468091_0.8838668977841735"}, "directories": {}, "publish_time": 1481725470374, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481725470374, "_cnpmcore_publish_time": "2021-12-13T14:48:20.394Z"}, "1.3.1": {"name": "web-streams-polyfill", "version": "1.3.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "jsnext:main": "index.es6.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test && cp ../../run-web-platform-tests-on-bundle.js ./ && node --expose_gc run-web-platform-tests-on-bundle.js && rm ./run-web-platform-tests-on-bundle.js)", "bundle": "npm-run-all bundle:*", "bundle:no-min": "browserify index.es6.js -d -s default -t [ babelify ] | derequire | exorcist ./dist/polyfill.js.map > ./dist/polyfill.js", "bundle:min": "browserify -g uglifyify index.es6.js -d -i ./spec/reference-implementation/lib/utils.js -s default -t [ babelify --plugins babel-plugin-unassert uglifyify ] | derequire | exorcist ./dist/polyfill.min.js.map > ./dist/polyfill.min.js", "build": "npm run bundle", "prebuild": "git submodule update --init", "prepublish": "npm run build"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.11.4", "babel-plugin-unassert": "^2.1.1", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.9.0", "babel-register": "^6.6.0", "babelify": "^7.3.0", "browserify": "^13.0.1", "derequire": "^2.0.3", "exorcist": "^0.4.0", "npm-run-all": "^2.3.0", "uglifyify": "^3.0.2", "yarn": "^0.17.0"}, "dependencies": {}, "gitHead": "213e356231918901d8e4cc54b3828956515255f2", "_id": "web-streams-polyfill@1.3.1", "_shasum": "bd890cfed004130d502eb08ea261e2240b0e5918", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "dist": {"shasum": "bd890cfed004130d502eb08ea261e2240b0e5918", "size": 110839, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.3.1.tgz", "integrity": "sha512-c5aLpuNFMAvhxX5PQgL7uRVkEDedHobcdA9S4T3lyY2Ye3WCK6/15JiCTltBt+9YwMiZ/1mhsWDiJsVtKWUmUA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.3.1.tgz_1481701992618_0.44352244632318616"}, "directories": {}, "publish_time": 1481701994473, "_hasShrinkwrap": false, "_cnpm_publish_time": 1481701994473, "_cnpmcore_publish_time": "2021-12-13T14:48:21.002Z"}, "1.3.0": {"name": "web-streams-polyfill", "version": "1.3.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "bundle": "browserify index.es6.js -s default -o dist/polyfill.js -t [ babelify ]", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "build": "npm run bundle && npm run minify"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.6.0", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.0", "babelify": "^7.3.0", "browserify": "^13.0.1", "uglify-js": "^2.6.2"}, "gitHead": "68f93d7240d925d27b175ce39133f57993c3f109", "_id": "web-streams-polyfill@1.3.0", "_shasum": "2ae58ef3ffb7a9ba3a8669eff236c12e8888e4ba", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "dist": {"shasum": "2ae58ef3ffb7a9ba3a8669eff236c12e8888e4ba", "size": 46369, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.3.0.tgz", "integrity": "sha512-SckEDFJTEHvfm7KqHOV66xV90f+RUsOKGjt4vVbDxpoxBIYvsG9ltoL6g3BRjszi0DQ2ApT9PNeIMaricienhg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.3.0.tgz_1470559504593_0.028606666019186378"}, "directories": {}, "publish_time": 1470559504848, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470559504848, "_cnpmcore_publish_time": "2021-12-13T14:48:21.634Z"}, "1.2.2": {"name": "web-streams-polyfill", "version": "1.2.2", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "bundle": "browserify index.es6.js -s default -o dist/polyfill.js -t [ babelify ]", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "build": "npm run bundle && npm run minify"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.6.0", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.0", "babelify": "^7.3.0", "browserify": "^13.0.1", "uglify-js": "^2.6.2"}, "gitHead": "77d2ed141fbc2d9170ebf9e2bcefb8b39a513be9", "_id": "web-streams-polyfill@1.2.2", "_shasum": "363dce238cd20bf57847403701b7cbd17c231ee6", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "dist": {"shasum": "363dce238cd20bf57847403701b7cbd17c231ee6", "size": 39974, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.2.2.tgz", "integrity": "sha512-9ZXPF7a2xfcxkKPZk2WPQEDu8QFdLlN8SMV+epwb3N+tcY/in0o6/RLrG1ovUcAfMbDwVEngQ2DUnlhYtNO/ZQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.2.2.tgz_1468659125303_0.8623745448421687"}, "directories": {}, "publish_time": 1468659127238, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468659127238, "_cnpmcore_publish_time": "2021-12-13T14:48:22.212Z"}, "1.2.1": {"name": "web-streams-polyfill", "version": "1.2.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "bundle": "browserify index.es6.js -o dist/polyfill.js -t [ babelify ]", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "build": "npm run bundle && npm run minify"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.6.0", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.0", "babelify": "^7.3.0", "browserify": "^13.0.1", "uglify-js": "^2.6.2"}, "gitHead": "22953f20cb48afdd1cbf02b762fcb702aade38d2", "_id": "web-streams-polyfill@1.2.1", "_shasum": "440e7a03c59812cb3ff2eeb77297b154121fcd23", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "dist": {"shasum": "440e7a03c59812cb3ff2eeb77297b154121fcd23", "size": 39890, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.2.1.tgz", "integrity": "sha512-OEkpnn8WIgIXXg4lKNqOXdhfkmghjYPjfGQBQqDuJTOz/wfcoSf/w5tpyB9iq7ivZdSXQyMIwiStRo3UkxfoCg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.2.1.tgz_1468657934300_0.29969602823257446"}, "directories": {}, "publish_time": 1468657936074, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468657936074, "_cnpmcore_publish_time": "2021-12-13T14:48:22.824Z"}, "1.2.0": {"name": "web-streams-polyfill", "version": "1.2.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "build": "npm run bundle && npm run transpile && npm run minify"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.6.0", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.0", "rollup": "^0.25.4", "uglify-js": "^2.6.2"}, "gitHead": "ca572561139bd5ee5e0eb9e31132c51a10c3712d", "_id": "web-streams-polyfill@1.2.0", "_shasum": "9c2de7b27e86e8ad98fb575766249835f1524184", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "dist": {"shasum": "9c2de7b27e86e8ad98fb575766249835f1524184", "size": 2254, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.2.0.tgz", "integrity": "sha512-7sSOIzzmKNArJ+tKHC66di3/lfrPm6HTG7QSYjmIy6DYua7ZoIhaencSM81tTkDXl/b5z0lqjsHKCqzvrWTQ6Q=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.2.0.tgz_1468655614205_0.11036600591614842"}, "directories": {}, "publish_time": 1468655614453, "_hasShrinkwrap": false, "_cnpm_publish_time": 1468655614453, "_cnpmcore_publish_time": "2021-12-13T14:48:23.383Z"}, "1.1.1": {"name": "web-streams-polyfill", "version": "1.1.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "build": "npm run bundle && npm run transpile && npm run minify"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.6.0", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.0", "rollup": "^0.25.4", "uglify-js": "^2.6.2"}, "gitHead": "c13da6314dcc45abc01dcc377d193923be7d7bd2", "_id": "web-streams-polyfill@1.1.1", "_shasum": "4ce0065390fa9865cfaaa52cb2722e06dc1e0e61", "_from": ".", "_npmVersion": "3.8.1", "_nodeVersion": "5.7.0", "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "dist": {"shasum": "4ce0065390fa9865cfaaa52cb2722e06dc1e0e61", "size": 44523, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.1.1.tgz", "integrity": "sha512-P71q/i52COL0tcovq8xPCT9ih5J43MJXaU4Orde5GqxFlt2vuibCG5wBqa9YEeI4EcNXGaljPhhzzpakTHW62w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.1.1.tgz_1459665315733_0.4818312537390739"}, "directories": {}, "publish_time": 1459665316278, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459665316278, "_cnpmcore_publish_time": "2021-12-13T14:48:23.993Z"}, "1.1.0": {"name": "web-streams-polyfill", "version": "1.1.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "build": "npm run bundle && npm run transpile && npm run minify"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.6.0", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.0", "rollup": "^0.25.4", "uglify-js": "^2.6.2"}, "gitHead": "29c7d91e9f062426eb423bb34af5d4193f4183c1", "_id": "web-streams-polyfill@1.1.0", "_shasum": "55d198a3c059e7dcdf4450b3512551baf6c094c1", "_from": ".", "_npmVersion": "3.8.1", "_nodeVersion": "5.7.0", "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "dist": {"shasum": "55d198a3c059e7dcdf4450b3512551baf6c094c1", "size": 44478, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.1.0.tgz", "integrity": "sha512-rLUQhnTnBOzbxlbWdeVebYPvRBl+ZiUHFBoS4JFczNxnsn8Wu4C1AFHQvGa2wTLgUD8B4FbqVGsn/Ze4+74kdw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.1.0.tgz_1459664818337_0.024501644540578127"}, "directories": {}, "publish_time": 1459664818870, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459664818870, "_cnpmcore_publish_time": "2021-12-13T14:48:24.598Z"}, "1.0.1": {"name": "web-streams-polyfill", "version": "1.0.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "build": "npm run bundle && npm run transpile && npm run minify"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.6.0", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.0", "rollup": "^0.25.4", "uglify-js": "^2.6.2"}, "gitHead": "c7cc6ba1b79b87841c31e40e656f10cec7fc40eb", "_id": "web-streams-polyfill@1.0.1", "_shasum": "2e261c60506000d68718e6953bcf20c9cf1cbd4e", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "dist": {"shasum": "2e261c60506000d68718e6953bcf20c9cf1cbd4e", "size": 32852, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.0.1.tgz", "integrity": "sha512-qk9IL43G3oIZXd/g2gx7+KOqbLclMrRk+F65bTV1IDl/dgY4syr05psFsua/52B2dQhduNZikS9cUd7r2N9GFA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.0.1.tgz_1457043469694_0.1993605128955096"}, "directories": {}, "publish_time": 1457043472632, "_hasShrinkwrap": false, "_cnpm_publish_time": 1457043472632, "_cnpmcore_publish_time": "2021-12-13T14:48:25.167Z"}, "1.0.0": {"name": "web-streams-polyfill", "version": "1.0.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill.js", "browser": "dist/polyfill.min.js", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "build": "npm run bundle && npm run transpile && npm run minify"}, "files": ["./dist"], "repository": {"type": "git", "url": "git+https://github.com/creatorrr/web-stream-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "devDependencies": {"babel-cli": "^6.6.0", "babel-polyfill": "^6.6.1", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.6.0", "rollup": "^0.25.4", "uglify-js": "^2.6.2"}, "gitHead": "d43894012bc72a4e086a698e4008aa74c3229726", "_id": "web-streams-polyfill@1.0.0", "_shasum": "afac5218e5b56c915d502bfc9b49af32659485f8", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "dist": {"shasum": "afac5218e5b56c915d502bfc9b49af32659485f8", "size": 1618, "noattachment": false, "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-1.0.0.tgz", "integrity": "sha512-MTVBbn2a1pmkJaIc/UsuaHfMV0RSGgMlAGYuOWzTPA3IBcfqYR6XBR4MJO5tK//8v4UG9ulcv7bmVRsNBYLvnA=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/web-streams-polyfill-1.0.0.tgz_1456943387426_0.8151088957674801"}, "directories": {}, "publish_time": 1456943390194, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456943390194, "_cnpmcore_publish_time": "2021-12-13T14:48:25.697Z"}, "3.2.1": {"name": "web-streams-polyfill", "version": "3.2.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "pretest:wpt": "git submodule update --init --recursive", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@rollup/plugin-strip": "^2.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/node": "^14.14.37", "@typescript-eslint/eslint-plugin": "^4.21.0", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "eslint": "^7.23.0", "jasmine": "^3.7.0", "micromatch": "^4.0.2", "rollup": "^2.44.0", "rollup-plugin-terser": "^7.0.2", "ts-morph": "^10.0.2", "tslib": "^2.2.0", "typescript": "^4.2.4", "wpt-runner": "^3.2.1"}, "gitHead": "d354a7457ca8a24030dbd0a135ee40baed7c774d", "_id": "web-streams-polyfill@3.2.1", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-e0MO3wdXWKrLbL0DgGnUV7WHVuw9OUvL4hjgnPkIeEvESk74gAITi5G606JtZPp39cd8HA9VQzCIvA49LpPN5Q==", "shasum": "71c2718c52b45fd49dbeee88634b3a60ceab42a6", "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.2.1.tgz", "fileCount": 41, "unpackedSize": 7614396, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxjmEcKQOCW2LCExN/9QlHW+3r1JwWYuS15x+COnk1FgIhAL7fbqsDXk8E651JFe7eGR71CEmeWUlJRE9TD7zwtYKa"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiT0mMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZ3w/8D02QSw+mnRW7LUJpnkHOzUZfLFTRlB10TQzM4nkf4GeZsXHB\r\nNqoAvelkv3iuFlq4NwSL5W5xhx62L1caah4QSl7KtFN4tCaaEaKFz3wTH4Xt\r\n+9cg8h3C5pMxEsYGI2AvE2wTe6H4KgmpJGb0y7hv1DiHdl30hCcJ5Ss8LYBj\r\n05eEvl5tzJ9R+eZJ1pSSXMKBbEH43n/OmGS/D2QmYe5VQVOARX/313Au+VHh\r\n+iEMqeTY4+dIdktulyFMnoobmPdIG7a5CuIkbKLGWwDV6+UETMuQoIqeatr7\r\nUsZnoU+vmAv5UqPYTV3gsoBP4uiHG8wnAt0/7DMu85MjiLswE5cmdsInDEru\r\nc9sJZ6Hl4jANnBum3FX6GwD+tII5SJ52am8HSQ23tJfxu46gqyLC/QGumjkg\r\nRL7Pw8n7KjXpqFUofb8rxH77N581+/xMszsM83x0OOPS/89CSbPaewBJZMAw\r\nF/d8SMffLVqb3JYPX6wslZmgl0C4xpcQ5lD64XZ9fzo+Z5zkxAhNn6aDPuV1\r\nASPIRyECKTmVwWBLPlIV3pBpbep1K9CxPLVhgEDFdn+xmqWkvQ1ONOzpzFja\r\nyQTpBqGc+pd4AKetNgey1ccGdz+cW1hJXR52OdJqD0uosGcJn5PRjI3ptDUo\r\nFQNo4OflgjrY0R4ObMpkwhWTYSIcugEv3cg=\r\n=UZnV\r\n-----END PGP SIGNATURE-----\r\n", "size": 1314882}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.2.1_1649363340359_0.8115155521118684"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-07T21:59:19.432Z"}, "4.0.0-beta.2": {"name": "web-streams-polyfill", "version": "4.0.0-beta.2", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/ponyfill.js", "module": "dist/ponyfill.mjs", "types": "types/ponyfill.d.ts", "exports": {".": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./polyfill": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.js"}, "./polyfill/es5": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.es5.js"}, "./dist/*": "./dist/*", "./types/*": "./types/*", "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundler", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:bundler": "npm run test:bundler:rollup && npm run test:bundler:webpack", "test:bundler:rollup": "cd test/rollup && npm ci && npm test", "test:bundler:webpack": "cd test/webpack && npm ci && npm test", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "node --experimental-import-meta-resolve ./node_modules/jasmine/bin/jasmine.js --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local", "prepare": "npm run build"}, "engines": {"node": ">= 14"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.21.2", "@rollup/plugin-inject": "^4.0.4", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-strip": "^2.1.0", "@rollup/plugin-typescript": "^8.3.1", "@types/jasmine": "^4.0.2", "@types/node": "^14.18.12", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "@ungap/promise-all-settled": "^1.1.2", "abort-controller": "^3.0.0", "eslint": "^8.13.0", "jasmine": "^4.0.2", "micromatch": "^4.0.5", "minimist": "^1.2.6", "playwright": "^1.20.2", "recursive-readdir": "^2.2.2", "rollup": "^2.70.1", "rollup-plugin-terser": "^7.0.2", "tslib": "^2.3.1", "typescript": "^4.7.0-beta", "wpt-runner": "^4.1.0"}, "readmeFilename": "README.md", "gitHead": "3d687503d1f939094ebe1404c43f2e7dd0f4df36", "_id": "web-streams-polyfill@4.0.0-beta.2", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-UHhhnoe2M40uh2r0KVdJTN7qjFytm6o0Yp3VcjwV3bfo6rz8uqvxNoE5yNmGF0y3eFfXaFeb6M09MDSwwLmq4w==", "shasum": "a534d1a8a4bbbb8d2bba07eb27722fde8ee8d077", "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.2.tgz", "fileCount": 15, "unpackedSize": 420006, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbnNqCr1+LPsQsbgaMfod8gUgJ9z22eibNf4gNI7dC7wIhALVEnlmac6j7P9eEJtBf8XYkxBnActb1Y6xO+yxyrZ9G"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVK2WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpITQ/6A4vQ3vrix554zW4e9dCBKBW1vq2uWIHm0MeJUq9phdHn4Swm\r\ntbmijJWEfk9jTUlXNVSNNkSs6XoaE0cQluvOX+n2zmPk59Ise4E9krEJtXv3\r\nethwNezctoZZJORKUnzi+wauwYDC/j/6gcyc41hm0AkmY44vqgIj7vDCRTmX\r\nyknhGa3GPkWD4fPepUxoy5yNp+ooT8fjsmTO1xJqXBJIqImM0IB7OYbVYHJq\r\nJuNbtiQ4seUa3l33LS5rwWp7CRt2Mhdxh6FW2y0KzvTuWH4Z8tx/5Py8Vxby\r\nHYBSXWCrr5JoHqD3/LDaqd+QVjjMCaKGehBpdQYxG+K4q92R7TMd7KJPs4Pm\r\nOgtpmO4qhLL1WoM4tgkuEbQcx0urpNXHM/kP/RzvQEehRVOPluqOXdHCiFYm\r\nFJ5grHA8iEcdCc8AOOMHEyhohmBaQTOE7f5gQl+ANavHp806nozIq+i0xVew\r\n/+oFHFWRHF4e+7V2UBoSbbJRdZuZZOIobwMLKxLAzsGRMHY0B95LP/xCc9u/\r\njBME2HRUZ4tvuFikOKgIUSJjEFj7PMSL87vZwJvLf1ix2djr9xoVSSl9kMTr\r\nW5i9Hev2Fp81R9xZl7qW+x4C3jGV7abXvfQr9mBtVdOS3Cf334cZ1T7LXjYR\r\nO26IxR0clSOUHEkIexU52tRNWEVaq58YPAc=\r\n=Tl7s\r\n-----END PGP SIGNATURE-----\r\n", "size": 91246}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_4.0.0-beta.2_1649716630401_0.3693867922909877"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-12T00:30:29.940Z"}, "4.0.0-beta.3": {"name": "web-streams-polyfill", "version": "4.0.0-beta.3", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/ponyfill.js", "module": "dist/ponyfill.mjs", "types": "types/ponyfill.d.ts", "exports": {".": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./polyfill": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.js"}, "./polyfill/es5": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.es5.js"}, "./dist/*": "./dist/*", "./types/*": "./types/*", "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundler", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:bundler": "npm run test:bundler:rollup && npm run test:bundler:webpack", "test:bundler:rollup": "cd test/rollup && npm ci && npm test", "test:bundler:webpack": "cd test/webpack && npm ci && npm test", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "node --experimental-import-meta-resolve ./node_modules/jasmine/bin/jasmine.js --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local", "prepare": "npm run build"}, "engines": {"node": ">= 14"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.21.2", "@rollup/plugin-inject": "^4.0.4", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-strip": "^2.1.0", "@rollup/plugin-typescript": "^8.3.1", "@types/jasmine": "^4.0.2", "@types/node": "^14.18.12", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "@ungap/promise-all-settled": "^1.1.2", "abort-controller": "^3.0.0", "eslint": "^8.13.0", "jasmine": "^4.0.2", "micromatch": "^4.0.5", "minimist": "^1.2.6", "playwright": "^1.20.2", "recursive-readdir": "^2.2.2", "rollup": "^2.70.1", "rollup-plugin-terser": "^7.0.2", "tslib": "^2.3.1", "typescript": "^4.7.0-beta", "wpt-runner": "^4.1.0"}, "readmeFilename": "README.md", "gitHead": "4444d642c39a03533bf62a111869b40822ccac35", "_id": "web-streams-polyfill@4.0.0-beta.3", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==", "shasum": "2898486b74f5156095e473efe989dcf185047a38", "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.3.tgz", "fileCount": 15, "unpackedSize": 422901, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHLYMZKqqtBu5SzMGygfj4b25sdzuqWJT6oLjrJB8Uh6AiA+llfxkPXTlU7jy+aMxdK3Ma+mU1F4hRIr9+wVQTC8Mg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijTd0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrB2RAAoUXW/l06KgbNYIQgIFA3d6MxqmLwzpVzHu65ve8+17kD45lZ\r\nY0F616CeakovEu2lSQj9jj4gzE7wweywna9hF02QAY41jTF6ol8qBU9Q3TIT\r\nL1A9zjj2R+XSbj3kzg8vH3Xjs+RqRZRRMgr+rERGVAwRxovaVwZob/PDSz/L\r\nNP81B9Q7uUSKu6w0vFjQY1AXvA1K8Eb/AxY18QubpMXbNkB2Cca5Rvds5/kP\r\nIwzSQ/3T1VjOogJGXySUkpqQN+rUUOduK6L4vsBXfW01QIws7gBcCjLzg6OL\r\nsRq1WyE6MJDiKy0y3Pm1xDmjHz19IjfIpFXf8ukucGzLMb524a4um64fjKlB\r\nq6OY4zWwbO+dXdfZbq9ZEtp9Mgj9tArkc2895FFil69/wSP7cunJSBxAB5ek\r\noka3E7XT523akq7f200FLMlPZxhkwsH3qThwrzGqZLfe3s17agrpDN18I65a\r\nb4Uh3tHTh+d6GGpJGIlA+vFSQ5+KlvdZRlauifxrcCzkSAuLits/ZOgilfvb\r\nTTZ3AOt28FI73ki5WoFca02gJkXIHykhK+ae6zRj/RUHxqzCx9TJW8150TPg\r\nzL3OrWJdPxq2LaHMea9MuwEKG/c775pM3/9Ol/gKR/mEbimKvGBZ4ZewGz8U\r\n4iY05UkTxcbyRdBfb6gjJj1SUuL187Gf6xA=\r\n=mJAz\r\n-----END PGP SIGNATURE-----\r\n", "size": 90751}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_4.0.0-beta.3_1653421940638_0.06233098248151969"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-05-24T20:21:48.571Z"}, "3.3.0": {"name": "web-streams-polyfill", "version": "3.3.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "types/polyfill.d.ts", "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local", "prepare": "npm run build"}, "engines": {"node": ">= 18"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.39.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-typescript": "^11.1.5", "@rollup/plugin-terser": "^0.4.4", "@types/node": "^18.19.4", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "@ungap/promise-all-settled": "^1.1.2", "eslint": "^8.56.0", "jasmine": "^5.1.0", "micromatch": "^4.0.5", "minimist": "^1.2.5", "playwright": "^1.14.1", "recursive-readdir": "^2.2.2", "rollup": "^4.9.2", "tslib": "^2.6.2", "typescript": "^5.3.3", "wpt-runner": "^5.0.0"}, "_id": "web-streams-polyfill@3.3.0", "gitHead": "78409d3cf3533af6c4be3258de9ba991aa163515", "_nodeVersion": "18.18.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-qGPA+g7LsFEF3dXQDJdZUSUBEuCONtE303GrFblnE+5BGTIim+h8CcOmYzylo/4in2GcdpirP/fBkM3/J6kWoQ==", "shasum": "d16f2dc29e1f23179771697c05b24ff3d157cb3c", "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.3.0.tgz", "fileCount": 41, "unpackedSize": 8998835, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfAJuAFOE0Oql7mej4RGGCJB0rCh5bsYkeU5TpDiXOcgIhAMrOg/eiT6eGoXmWdJmBn/1vNkA+utLlegW7BiWZ/2De"}], "size": 1569613}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.3.0_1704381822106_0.4831674733849718"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-04T15:23:42.365Z", "publish_time": 1704381822365, "_source_registry_name": "default"}, "3.3.1": {"name": "web-streams-polyfill", "version": "3.3.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "postbuild:types": "downlevel-dts ./dist/types/ts3.6/ ./dist/types/ --to=3.5 && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.39.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.5", "@types/node": "^18.19.4", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "@ungap/promise-all-settled": "^1.1.2", "downlevel-dts": "^0.11.0", "eslint": "^8.56.0", "jasmine": "^5.1.0", "micromatch": "^4.0.5", "minimist": "^1.2.5", "playwright": "^1.14.1", "recursive-readdir": "^2.2.2", "rollup": "^4.9.2", "ts-morph": "^10.0.2", "tslib": "^2.6.2", "typescript": "^5.3.3", "wpt-runner": "^5.0.0"}, "_id": "web-streams-polyfill@3.3.1", "gitHead": "de42d2a95e823f00fc3da21e8ff20bacbb6f34a6", "_nodeVersion": "18.18.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-hhvyQPaWKuZpyr19naOWHktLWtbkpOaR048vXDNR9EgYb2Al7rqCd7RBFx68eYZHgkIlQy9UtO01psg/ONg4cQ==", "shasum": "4d16e0804fd25c2a211f6cd722cf45b0b1cded36", "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.3.1.tgz", "fileCount": 41, "unpackedSize": 9032974, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB5FpSgNzvAu4eh+6Szjw3hyT2ueZkBnND57sRVJFho9AiEAom+0XlIaZ2W5xLLrPmQRabDTA0jdQHfDHK2McDh9mPU="}], "size": 1575736}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.3.1_1704390005058_0.39184024980433363"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-04T17:40:05.287Z", "publish_time": 1704390005287, "_source_registry_name": "default", "deprecated": "broken publish, upgrade to v3.3.2"}, "3.3.2": {"name": "web-streams-polyfill", "version": "3.3.2", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "postbuild:types": "downlevel-dts ./dist/types/ts3.6/ ./dist/types/ --to=3.5 && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.39.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.5", "@types/node": "^18.19.4", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "@ungap/promise-all-settled": "^1.1.2", "downlevel-dts": "^0.11.0", "eslint": "^8.56.0", "jasmine": "^5.1.0", "micromatch": "^4.0.5", "minimist": "^1.2.5", "playwright": "^1.14.1", "recursive-readdir": "^2.2.2", "rollup": "^4.9.2", "ts-morph": "^10.0.2", "tslib": "^2.6.2", "typescript": "^5.3.3", "wpt-runner": "^5.0.0"}, "_id": "web-streams-polyfill@3.3.2", "gitHead": "49c215e6106799816e9d3de0649c7a20981a6ab3", "_nodeVersion": "18.18.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-3pRGuxRF5gpuZc0W+EpwQRmCD7gRqcDOMt688KmdlDAgAyaB1XlN0zq2njfDNm44XVdIouE7pZ6GzbdyH47uIQ==", "shasum": "32e26522e05128203a7de59519be3c648004343b", "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.3.2.tgz", "fileCount": 43, "unpackedSize": 9035432, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCntMoSbFOPufGMy8o1PPuyHdON2/vw7Cp42Fri58v3aQIgaK9zAhEP7HWuNBfHb7qb2iGkxcNU+a3h4BJs0tv7ZK0="}], "size": 1576096}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.3.2_1704390682467_0.9624001986509192"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-01-04T17:51:22.720Z", "publish_time": 1704390682720, "_source_registry_name": "default"}, "3.3.3": {"name": "web-streams-polyfill", "version": "3.3.3", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/polyfill", "browser": "dist/polyfill.min.js", "module": "dist/polyfill.mjs", "types": "dist/types/polyfill.d.ts", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "postbuild:types": "downlevel-dts ./dist/types/ts3.6/ ./dist/types/ --to=3.5 && node ./build/downlevel-dts.js", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.39.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.5", "@types/node": "^18.19.4", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "@ungap/promise-all-settled": "^1.1.2", "downlevel-dts": "^0.11.0", "eslint": "^8.56.0", "jasmine": "^5.1.0", "micromatch": "^4.0.5", "minimist": "^1.2.5", "playwright": "^1.14.1", "recursive-readdir": "^2.2.2", "rollup": "^4.9.2", "ts-morph": "^10.0.2", "tslib": "^2.6.2", "typescript": "^5.3.3", "wpt-runner": "^5.0.0"}, "_id": "web-streams-polyfill@3.3.3", "gitHead": "ef5c9e9094341b721e8724eb9589a711733d0edb", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==", "shasum": "2073b91a2fdb1fbfbd401e7de0ac9f8214cecb4b", "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "fileCount": 43, "unpackedSize": 9036205, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/web-streams-polyfill@3.3.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC04mxSFnuq+iqYgHE4XYRl8o4ZBDG+PAWJ5ZIeD1IbPAiBzOBdQ89z82wvtMvE9ZSLXn6SKW3jfstUpSaiAzkiW2w=="}], "size": 1579083}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_3.3.3_1708118147627_0.37609771191882335"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-16T21:15:47.924Z", "publish_time": 1708118147924, "_source_registry_name": "default"}, "4.0.0": {"name": "web-streams-polyfill", "version": "4.0.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/ponyfill.js", "module": "dist/ponyfill.mjs", "types": "types/ponyfill.d.ts", "exports": {".": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./polyfill": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.js"}, "./polyfill/es5": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.es5.js"}, "./dist/*": "./dist/*", "./types/*": "./types/*", "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundlers", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:bundlers": "npm run test:bundler:rollup && npm run test:bundler:webpack", "test:bundler:rollup": "cd test/rollup && npm install && npm test", "test:bundler:webpack": "cd test/webpack && npm install && npm test", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "node --experimental-import-meta-resolve node_modules/jasmine/bin/jasmine.js --config=test/unit/jasmine.json", "lint": "eslint \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@microsoft/api-extractor": "^7.41.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@types/node": "^20.11.21", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@ungap/promise-all-settled": "^1.1.2", "eslint": "^8.57.0", "jasmine": "^5.1.0", "micromatch": "^4.0.5", "minimist": "^1.2.5", "playwright": "^1.42.0", "recursive-readdir": "^2.2.2", "rollup": "^4.12.0", "tslib": "^2.6.2", "typescript": "^5.3.3", "wpt-runner": "^5.0.0"}, "_id": "web-streams-polyfill@4.0.0", "gitHead": "9eeeb1355f46f1ffd116799d236a34987d1f4e45", "_nodeVersion": "20.11.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-0zJXHRAYEjM2tUfZ2DiSOHAa2aw1tisnnhU3ufD57R8iefL+DcdJyRBRyJpG+NUimDgbTI/lH+gAE1PAvV3Cgw==", "shasum": "74cedf168339ee6e709532f76c49313a8c7acdac", "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-4.0.0.tgz", "fileCount": 12, "unpackedSize": 441002, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/web-streams-polyfill@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFjh29qDVRWMXKoEyHWlD8+IwwDVW/oN2RT+0HU8RADAAiEAjQK2XxmavEHWR1HepsJEe8vl92tc2Y3LlEehSnTU31k="}], "size": 94710}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/web-streams-polyfill_4.0.0_1709157183947_0.217138679026474"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-02-28T21:53:04.107Z", "publish_time": 1709157184107, "_source_registry_name": "default"}, "4.1.0": {"name": "web-streams-polyfill", "version": "4.1.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/ponyfill.js", "module": "dist/ponyfill.mjs", "types": "types/ponyfill.d.ts", "exports": {".": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./polyfill": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.js"}, "./polyfill/es5": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.es5.js"}, "./dist/*": "./dist/*", "./types/*": "./types/*", "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundlers", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.mjs", "test:wpt:chromium": "node ./test/wpt/browser/run.mjs --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.mjs --browser firefox", "test:bundlers": "npm run test:bundler:rollup && npm run test:bundler:webpack", "test:bundler:rollup": "cd test/rollup && npm install && npm test", "test:bundler:webpack": "cd test/webpack && npm install && npm test", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"*.mjs\" \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@eslint/js": "^9.17.0", "@microsoft/api-extractor": "^7.48.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@stylistic/eslint-plugin": "^2.12.1", "@types/node": "^20.17.11", "eslint": "^9.17.0", "globals": "^15.14.0", "jasmine": "^5.5.0", "micromatch": "^4.0.8", "minimist": "^1.2.8", "playwright": "^1.49.1", "rollup": "^4.29.2", "st": "^3.0.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.19.0", "wpt-runner": "^6.0.0"}, "_id": "web-streams-polyfill@4.1.0", "gitHead": "ea1b0b7dcc6558192226ab6e2256611b138b1b9d", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-A7Jxrg7+eV+eZR/CIdESDnRGFb6/bcKukGvJBB5snI6cw3is1c2qamkYstC1bY1p08TyMRlN9eTMkxmnKJBPBw==", "shasum": "3ba095d0eb3ef6377cd126e8354b2cdba286e0d3", "tarball": "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-4.1.0.tgz", "fileCount": 12, "unpackedSize": 442126, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/web-streams-polyfill@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVjeDAmIHYM06I+G+reIXbuoaVvOrJIujVyEkFE/SHXAIhAJIV1TFVdorXBuqbSG/utgzmYH7wbnOLg76PJbP9gII5"}], "size": 94958}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/web-streams-polyfill_4.1.0_1736109861424_0.2548391284379421"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-01-05T20:44:21.625Z", "publish_time": 1736109861625, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "keywords": ["streams", "whatwg", "polyfill"], "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "_source_registry_name": "default"}