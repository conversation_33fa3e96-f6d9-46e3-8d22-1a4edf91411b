{"_attachments": {}, "_id": "ecdsa-sig-formatter", "_rev": "6125-61f154e1fbcaa28a75972a0b", "author": {"name": "D2L Corporation"}, "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "dist-tags": {"latest": "1.0.11"}, "license": "Apache-2.0", "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "name": "ecdsa-sig-formatter", "readme": "# ecdsa-sig-formatter\n\n[![Build Status](https://travis-ci.org/Brightspace/node-ecdsa-sig-formatter.svg?branch=master)](https://travis-ci.org/Brightspace/node-ecdsa-sig-formatter) [![Coverage Status](https://coveralls.io/repos/Brightspace/node-ecdsa-sig-formatter/badge.svg)](https://coveralls.io/r/Brightspace/node-ecdsa-sig-formatter)\n\nTranslate between JOSE and ASN.1/DER encodings for ECDSA signatures\n\n## Install\n```sh\nnpm install ecdsa-sig-formatter --save\n```\n\n## Usage\n```js\nvar format = require('ecdsa-sig-formatter');\n\nvar derSignature = '..'; // asn.1/DER encoded ecdsa signature\n\nvar joseSignature = format.derToJose(derSignature);\n\n```\n\n### API\n\n---\n\n#### `.derToJose(Buffer|String signature, String alg)` -> `String`\n\nConvert the ASN.1/DER encoded signature to a JOSE-style concatenated signature.\nReturns a _base64 url_ encoded `String`.\n\n* If _signature_ is a `String`, it should be _base64_ encoded\n* _alg_ must be one of _ES256_, _ES384_ or _ES512_\n\n---\n\n#### `.joseToDer(Buffer|String signature, String alg)` -> `Buffer`\n\nConvert the JOSE-style concatenated signature to an ASN.1/DER encoded\nsignature. Returns a `Buffer`\n\n* If _signature_ is a `String`, it should be _base64 url_ encoded\n* _alg_ must be one of _ES256_, _ES384_ or _ES512_\n\n## Contributing\n\n1. **Fork** the repository. Committing directly against this repository is\n   highly discouraged.\n\n2. Make your modifications in a branch, updating and writing new unit tests\n   as necessary in the `spec` directory.\n\n3. Ensure that all tests pass with `npm test`\n\n4. `rebase` your changes against master. *Do not merge*.\n\n5. Submit a pull request to this repository. Wait for tests to run and someone\n   to chime in.\n\n### Code Style\n\nThis repository is configured with [EditorConfig][EditorConfig] and\n[ESLint][ESLint] rules.\n\n[EditorConfig]: http://editorconfig.org/\n[ESLint]: http://eslint.org\n", "time": {"created": "2022-01-26T14:04:17.193Z", "modified": "2023-05-12T22:02:58.466Z", "1.0.11": "2019-01-25T21:32:13.447Z", "1.0.10": "2018-05-14T14:40:42.729Z", "1.0.9": "2016-12-05T15:40:24.823Z", "1.0.8": "2016-11-15T15:50:08.273Z", "1.0.7": "2016-06-18T15:26:07.961Z", "1.0.6": "2016-06-17T15:08:45.103Z", "1.0.5": "2016-01-20T01:20:23.559Z", "1.0.2": "2015-06-12T20:39:55.056Z", "1.0.1": "2015-06-10T20:54:59.800Z", "1.0.0": "2015-06-10T19:40:01.611Z"}, "versions": {"1.0.11": {"name": "ecdsa-sig-formatter", "version": "1.0.11", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "typings": "./src/ecdsa-sig-formatter.d.ts", "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3", "native-crypto": "^1.7.0"}, "gitHead": "c730235f4a54526f33f5309d80beb12ee298db6b", "_id": "ecdsa-sig-formatter@1.0.11", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "ae0f0fa2d85045ef14a817daa3ce9acd0489e5bf", "size": 7104, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "integrity": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ=="}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ecdsa-sig-formatter_1.0.11_1548451933314_0.5170227155509808"}, "_hasShrinkwrap": false, "publish_time": 1548451933447, "_cnpm_publish_time": 1548451933447, "_cnpmcore_publish_time": "2021-12-15T17:13:49.621Z"}, "1.0.10": {"name": "ecdsa-sig-formatter", "version": "1.0.10", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3", "native-crypto": "^1.7.0"}, "gitHead": "b07b82c51f9be13e1ca291239983d7b052863f77", "_id": "ecdsa-sig-formatter@1.0.10", "_shasum": "1c595000f04a8897dfb85000892a0f4c33af86c3", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "1c595000f04a8897dfb85000892a0f4c33af86c3", "size": 6936, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.10.tgz", "integrity": "sha512-0XqJH78hi7Cx141usy/GT+gSzwZs58W144EhBPCFWM5bQj5nDhC+CnGTAuJDIYkuXZei+m7/isuIBqhaf1XAEA=="}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ecdsa-sig-formatter_1.0.10_1526308842657_0.3204613548609856"}, "_hasShrinkwrap": false, "publish_time": 1526308842729, "_cnpm_publish_time": 1526308842729, "_cnpmcore_publish_time": "2021-12-15T17:13:49.827Z"}, "1.0.9": {"name": "ecdsa-sig-formatter", "version": "1.0.9", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64url": "^2.0.0", "safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "gitHead": "e32ea222f0ffdde667d8e97588135953a4c34555", "_id": "ecdsa-sig-formatter@1.0.9", "_shasum": "4bc926274ec3b5abb5016e7e1d60921ac262b2a1", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "4bc926274ec3b5abb5016e7e1d60921ac262b2a1", "size": 6857, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.9.tgz", "integrity": "sha512-J/mTMRuaEY85fUpQmLRXBg3tOk1zshbIIazSoUzdpjdD7hdiYKR/VpiL6JNWbuSjbSYdugmgBv8m/O1kK0CQRw=="}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ecdsa-sig-formatter-1.0.9.tgz_1480952424192_0.9221549024805427"}, "directories": {}, "publish_time": 1480952424823, "_hasShrinkwrap": false, "_cnpm_publish_time": 1480952424823, "_cnpmcore_publish_time": "2021-12-15T17:13:50.034Z"}, "1.0.8": {"name": "ecdsa-sig-formatter", "version": "1.0.8", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64-url": "^1.2.1", "safe-buffer": "^5.0.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "gitHead": "a711c0825818906f24993cffb6d8a02b97070537", "_id": "ecdsa-sig-formatter@1.0.8", "_shasum": "a9e3f5534e3755a56f8c982fb15a05a6364a1dab", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "a9e3f5534e3755a56f8c982fb15a05a6364a1dab", "size": 6861, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.8.tgz", "integrity": "sha512-ULDtFASgTauQ2zeSgA4DtdT9C1OVeoJNN1uuUjuAGSZw7z9UUcSGYcqPCfBP/dB+mtNv4WJpuB4AYqHryvXALQ=="}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/ecdsa-sig-formatter-1.0.8.tgz_1479225007727_0.3844668928068131"}, "directories": {}, "publish_time": 1479225008273, "_hasShrinkwrap": false, "_cnpm_publish_time": 1479225008273, "_cnpmcore_publish_time": "2021-12-15T17:13:50.266Z"}, "1.0.7": {"name": "ecdsa-sig-formatter", "version": "1.0.7", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "gitHead": "eed19df2c57d8ab04dee02f151ae7ed7bbdbba8f", "_id": "ecdsa-sig-formatter@1.0.7", "_shasum": "3137e976a1d6232517e2513e04e32f79bcbdf126", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.11.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "3137e976a1d6232517e2513e04e32f79bcbdf126", "size": 6833, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.7.tgz", "integrity": "sha512-eQGBiG9EKukpMONQkopQv3Xv3JN4fSL+aupxz8o1B4+TSB+fvuFQcfU0u7SMIbLacpOe4c8uS9qnXd/jy3OqOA=="}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ecdsa-sig-formatter-1.0.7.tgz_1466263566774_0.3799667169805616"}, "directories": {}, "publish_time": 1466263567961, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466263567961, "_cnpmcore_publish_time": "2021-12-15T17:13:50.499Z"}, "1.0.6": {"name": "ecdsa-sig-formatter", "version": "1.0.6", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.5.0", "coveralls": "^2.11.9", "elliptic": "^6.3.1", "eslint": "^2.12.0", "eslint-config-brightspace": "^0.2.1", "istanbul": "^0.4.3", "jwk-to-pem": "^1.2.5", "mocha": "^2.5.3"}, "gitHead": "f232d5cddb120b92b552635fca21167b7dd6e8e1", "_id": "ecdsa-sig-formatter@1.0.6", "_shasum": "19968e66a2f4366210ef3f791e5454d760f7722d", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.11.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "19968e66a2f4366210ef3f791e5454d760f7722d", "size": 6805, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.6.tgz", "integrity": "sha512-Bft5z8c4ga3uS6X47orWnfAKCeeRVP1qYrvo/ndyMTZ4yZxVeV3owSNtcNCYDWvp2CMNyUAcdmtRE/22NDvqIA=="}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ecdsa-sig-formatter-1.0.6.tgz_1466176123955_0.7629159067291766"}, "directories": {}, "publish_time": 1466176125103, "_hasShrinkwrap": false, "_cnpm_publish_time": 1466176125103, "_cnpmcore_publish_time": "2021-12-15T17:13:50.834Z"}, "1.0.5": {"name": "ecdsa-sig-formatter", "version": "1.0.5", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"check-style": "eslint .", "pretest": "npm run check-style", "test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.6", "chai": "^3.4.1", "coveralls": "^2.11.6", "elliptic": "^6.1.0", "eslint": "^1.10.3", "eslint-config-brightspace": "^0.1.0", "istanbul": "^0.4.2", "jwk-to-pem": "^1.2.4", "mocha": "^2.3.4"}, "gitHead": "a7bed5fbdc737798c0f499156cdbeeeaf7138542", "_id": "ecdsa-sig-formatter@1.0.5", "_shasum": "0d0f32b638611f6b8f36ffd305a3e512ea5444e6", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.4.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "dist": {"shasum": "0d0f32b638611f6b8f36ffd305a3e512ea5444e6", "size": 6820, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.5.tgz", "integrity": "sha512-+iTWaj9CQHnvYQ7keATosCbg6I60hVQoT3SGAva2dHwGuWe9enc3Irl60Bb6Mm9+eEZ9xukm1UMrEY1dSHC03A=="}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1453252823559, "_hasShrinkwrap": false, "_cnpm_publish_time": 1453252823559, "_cnpmcore_publish_time": "2021-12-15T17:13:51.042Z"}, "1.0.2": {"name": "ecdsa-sig-formatter", "version": "1.0.2", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"asn1.js": "^2.0.3", "base64-url": "^1.2.1"}, "devDependencies": {"bench": "^0.3.5", "chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.15", "mocha": "^2.2.5"}, "gitHead": "426ae016eadb72400f96a0b82a29b6500b4289e5", "_id": "ecdsa-sig-formatter@1.0.2", "_shasum": "2074b4bd06be5e7479c9f71e73358bc3deea4a9b", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "dist": {"shasum": "2074b4bd06be5e7479c9f71e73358bc3deea4a9b", "size": 7320, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.2.tgz", "integrity": "sha512-1GNuV5W8v1rU+5AxNrPTJxF8pkDk3U2a9kxVcVoer1jpF0ZjzjTcVw8zwbA78UJzObKWaswmwJvVZEglhboCKQ=="}, "directories": {}, "publish_time": 1434141595056, "_hasShrinkwrap": false, "_cnpm_publish_time": 1434141595056, "_cnpmcore_publish_time": "2021-12-15T17:13:51.253Z"}, "1.0.1": {"name": "ecdsa-sig-formatter", "version": "1.0.1", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-ecdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"asn1.js": "^2.0.3", "base64-url": "^1.2.1"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.15", "mocha": "^2.2.5"}, "gitHead": "28eef3ebc4315db2a2435b427300fd4bddf9074c", "_id": "ecdsa-sig-formatter@1.0.1", "_shasum": "369b867c40080ba35921530df5a26dad3390d4f1", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "dist": {"shasum": "369b867c40080ba35921530df5a26dad3390d4f1", "size": 6026, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.1.tgz", "integrity": "sha512-wsxwVpHRXqqxBcIBqlXncYrLDva8RbJ78GQBTAT0yhQp5+T4lDxqRjXk8VG2IJ4gz+eaWgx7BPs0RydVk/C9cA=="}, "directories": {}, "publish_time": 1433969699800, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433969699800, "_cnpmcore_publish_time": "2021-12-15T17:13:51.466Z"}, "1.0.0": {"name": "ecdsa-sig-formatter", "version": "1.0.0", "description": "Translate ECDSA signatures between ASN.1/DER and JOSE-style concatenation", "main": "src/ecdsa-sig-formatter.js", "scripts": {"test": "istanbul cover --root src _mocha -- spec", "report-cov": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/Brightspace/node-cdsa-sig-formatter.git"}, "keywords": ["ecdsa", "der", "asn.1", "jwt", "jwa", "jsonwebtoken", "jose"], "author": {"name": "D2L Corporation"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Brightspace/node-ecdsa-sig-formatter/issues"}, "homepage": "https://github.com/Brightspace/node-ecdsa-sig-formatter#readme", "dependencies": {"asn1.js": "^2.0.3", "base64-url": "^1.2.1"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.15", "mocha": "^2.2.5"}, "gitHead": "53a33d2cfc076f24030542692c8a73019f2bbfde", "_id": "ecdsa-sig-formatter@1.0.0", "_shasum": "2925b91c568c1c13d1e55b6fb45917f753f7ca46", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "d2l-travis-deploy", "email": "<EMAIL>"}, "maintainers": [{"name": "d2l-travis-deploy", "email": "<EMAIL>"}], "dist": {"shasum": "2925b91c568c1c13d1e55b6fb45917f753f7ca46", "size": 6034, "noattachment": false, "tarball": "https://registry.npmmirror.com/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.0.tgz", "integrity": "sha512-8oki6U/9WCO3Axr5Zcgy+JLrSI+s4vyBahJSYF9UghcZnJqB4bIBC0/yUFRN7XuR/1rcT3YWFKjweJAH1MOAjw=="}, "directories": {}, "publish_time": 1433965201611, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433965201611, "_cnpmcore_publish_time": "2021-12-15T17:13:51.647Z"}}, "_source_registry_name": "default"}