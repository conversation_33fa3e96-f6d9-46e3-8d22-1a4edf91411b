{"_attachments": {}, "_id": "minipass", "_rev": "2644-61f149d9fbcaa28a75955a85", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "description": "minimal implementation of a PassThrough stream", "dist-tags": {"latest": "7.1.2", "legacy-v4": "4.2.8"}, "license": "ISC", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "minipass", "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1K_HR5oh3r80b8WVMWCPPjfuWXUgfkmhlX7FGI6JJ8tY/edit?usp=sharing)\nfor objects, strings, and buffers.\n\nSupports `pipe()`ing (including multi-`pipe()` and backpressure\ntransmission), buffering data until either a `data` event handler\nor `pipe()` is added (so you don't lose the first chunk), and\nmost other cases where PassThrough is a good idea.\n\nThere is a `read()` method, but it's much more efficient to\nconsume data from this stream via `'data'` events or by calling\n`pipe()` into some other stream. Calling `read()` requires the\nbuffer to be flattened in some cases, which requires copying\nmemory.\n\nIf you set `objectMode: true` in the options, then whatever is\nwritten will be emitted. Otherwise, it'll do a minimal amount of\nBuffer copying to ensure proper Streams semantics when `read(n)`\nis called.\n\n`objectMode` can only be set at instantiation. Attempting to\nwrite something other than a String or Buffer without having set\n`objectMode` in the options will throw an error.\n\nThis is not a `through` or `through2` stream. It doesn't\ntransform the data, it just passes it right through. If you want\nto transform the data, extend the class, and override the\n`write()` method. Once you're done transforming the data however\nyou want, call `super.write()` with the transform output.\n\nFor some examples of streams that extend Minipass in various\nways, check out:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap-parser)\n- [treport](http://npm.im/treport)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n- [pacote](http://npm.im/pacote)\n- [make-fetch-happen](http://npm.im/make-fetch-happen)\n- [cacache](http://npm.im/cacache)\n- [ssri](http://npm.im/ssri)\n- [npm-registry-fetch](http://npm.im/npm-registry-fetch)\n- [minipass-json-stream](http://npm.im/minipass-json-stream)\n- [minipass-sized](http://npm.im/minipass-sized)\n\n## Usage in TypeScript\n\nThe `Minipass` class takes three type template definitions:\n\n- `RType` the type being read, which defaults to `Buffer`. If\n  `RType` is `string`, then the constructor _must_ get an options\n  object specifying either an `encoding` or `objectMode: true`.\n  If it's anything other than `string` or `Buffer`, then it\n  _must_ get an options object specifying `objectMode: true`.\n- `WType` the type being written. If `RType` is `Buffer` or\n  `string`, then this defaults to `ContiguousData` (Buffer,\n  string, ArrayBuffer, or ArrayBufferView). Otherwise, it\n  defaults to `RType`.\n- `Events` type mapping event names to the arguments emitted\n  with that event, which extends `Minipass.Events`.\n\nTo declare types for custom events in subclasses, extend the\nthird parameter with your own event signatures. For example:\n\n```js\nimport { Minipass } from 'minipass'\n\n// a NDJSON stream that emits 'jsonError' when it can't stringify\nexport interface Events extends Minipass.Events {\n  jsonError: [e: Error]\n}\n\nexport class NDJSONStream extends Minipass<string, any, Events> {\n  constructor() {\n    super({ objectMode: true })\n  }\n\n  // data is type `any` because that's WType\n  write(data, encoding, cb) {\n    try {\n      const json = JSON.stringify(data)\n      return super.write(json + '\\n', encoding, cb)\n    } catch (er) {\n      if (!er instanceof Error) {\n        er = Object.assign(new Error('json stringify failed'), {\n          cause: er,\n        })\n      }\n      // trying to emit with something OTHER than an error will\n      // fail, because we declared the event arguments type.\n      this.emit('jsonError', er)\n    }\n  }\n}\n\nconst s = new NDJSONStream()\ns.on('jsonError', e => {\n  // here, TS knows that e is an Error\n})\n```\n\nEmitting/handling events that aren't declared in this way is\nfine, but the arguments will be typed as `unknown`.\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different\nfrom (and in some ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with node-core\nstreams and intend to use Minipass streams in your programs.\n\nYou can avoid most of these differences entirely (for a very\nsmall performance penalty) by setting `{async: true}` in the\nconstructor options.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.\nThus, data is emitted as soon as it is available, always. It is\nbuffered until read, but no longer. Another way to look at it is\nthat Minipass streams are exactly as synchronous as the logic\nthat writes into them.\n\nThis can be surprising if your code relies on\n`PassThrough.write()` always providing data on the next tick\nrather than the current one, or being able to call `resume()` and\nnot have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no\nway for Minipass to achieve the speeds it does, or support the\nsynchronous use cases that it does. Simply put, waiting takes\ntime.\n\nThis non-deferring approach makes Minipass streams much easier to\nreason about, especially in the context of Promises and other\nflow-control mechanisms.\n\nExample:\n\n```js\n// hybrid module, either works\nimport { Minipass } from 'minipass'\n// or:\nconst { Minipass } = require('minipass')\n\nconst stream = new Minipass()\nstream.on('data', () => console.log('data event'))\nconsole.log('before write')\nstream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// data event\n// after write\n```\n\n### Exception: Async Opt-In\n\nIf you wish to have a Minipass stream with behavior that more\nclosely mimics Node.js core streams, you can set the stream in\nasync mode either by setting `async: true` in the constructor\noptions, or by setting `stream.async = true` later on.\n\n```js\n// hybrid module, either works\nimport { Minipass } from 'minipass'\n// or:\nconst { Minipass } = require('minipass')\n\nconst asyncStream = new Minipass({ async: true })\nasyncStream.on('data', () => console.log('data event'))\nconsole.log('before write')\nasyncStream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// after write\n// data event <-- this is deferred until the next tick\n```\n\nSwitching _out_ of async mode is unsafe, as it could cause data\ncorruption, and so is not enabled. Example:\n\n```js\nimport { Minipass } from 'minipass'\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nsetStreamSyncAgainSomehow(stream) // <-- this doesn't actually exist!\nstream.write('world')\nconsole.log('after writes')\n// hypothetical output would be:\n// before writes\n// world\n// after writes\n// hello\n// NOT GOOD!\n```\n\nTo avoid this problem, once set into async mode, any attempt to\nmake the stream sync again will be ignored.\n\n```js\nconst { Minipass } = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nstream.async = false // <-- no-op, stream already async\nstream.write('world')\nconsole.log('after writes')\n// actual output:\n// before writes\n// after writes\n// hello\n// world\n```\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer,\nreturning `true` on all writes until the limit is hit, even if\nthe data has nowhere to go. Then, they will not attempt to draw\nmore data in until the buffer size dips below a minimum value.\n\nMinipass streams are much simpler. The `write()` method will\nreturn `true` if the data has somewhere to go (which is to say,\ngiven the timing guarantees, that the data is already there by\nthe time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and\nthe data sits in a buffer, to be drained out immediately as soon\nas anyone consumes it.\n\nSince nothing is ever buffered unnecessarily, there is much less\ncopying data, and less bookkeeping about buffer capacity levels.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written\nall the way through the pipeline, and `write()` always returns\ntrue/false based on whether the data was fully flushed,\nbackpressure is communicated immediately to the upstream caller.\nThis minimizes buffering.\n\nConsider this case:\n\n```js\nconst { PassThrough } = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage,\nand multiple event deferrals happened, for an unblocked pipeline\nwhere it was perfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead\nsomeone reading the code to think an advisory maximum of 1KiB is\nbeing set for the pipeline. However, the actual advisory\nbuffering level is the _sum_ of `highWaterMark` values, since\neach one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data\nwritten, or _ever_ buffer data that can be flushed all the way\nthrough. Neither node-core streams nor Minipass ever fail to\nbuffer written data, but node-core streams do a lot of\nunnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less\nstuff and waits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing\nany data into it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't\nwant to potentially happen immediately (for example, closing file\ndescriptors, moving on to the next entry in an archive parse\nstream, etc.) then be sure to call `stream.pause()` on creation,\nand then `stream.resume()` once you are ready to respond to the\n`end` event.\n\nHowever, this is _usually_ not a problem because:\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not\nyet have had a chance to add a listener. In order to avoid this\nhazard, Minipass streams safely re-emit the `'end'` event if a\nnew listener is added after `'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream\nhas already emitted `end`, then it will call the handler right\naway. (You can think of this somewhat like attaching a new\n`.then(fn)` to a previously-resolved Promise.)\n\nTo prevent calling handlers multiple times who would not expect\nmultiple ends to occur, all listeners are removed from the\n`'end'` event whenever it is emitted.\n\n### Emit `error` When Asked\n\nThe most recent error object passed to the `'error'` event is\nstored on the stream. If a new `'error'` event handler is added,\nand an error was previously emitted, then the event handler will\nbe called immediately (or on `process.nextTick` in the case of\nasync streams).\n\nThis makes it much more difficult to end up trying to interact\nwith a broken stream, if the error handler is added after an\nerror was previously emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data\nthrough the pipeline when a new pipe destination is added, this\ncan have surprising effects, especially when a stream comes in\nfrom some other function and may or may not have data in its\nbuffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nOne solution is to create a dedicated tee-stream junction that\npipes to both locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nsrc.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners. The\nfirst one added will _immediately_ receive all of the data,\nleaving nothing for the second:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\nAll of the hazards in this section are avoided by setting `{\nasync: true }` in the Minipass constructor, or by setting\n`stream.async = true` afterwards. Note that this does add some\noverhead, so should only be done in cases where you are willing\nto lose a bit of performance in order to avoid having to refactor\nprogram logic.\n\n## USAGE\n\nIt's a stream! Use it like a stream and it'll most likely do what\nyou want.\n\n```js\nimport { Minipass } from 'minipass'\nconst mp = new Minipass(options) // options is optional\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n- `encoding` How would you like the data coming _out_ of the\n  stream to be encoded? Accepts any values that can be passed to\n  `Buffer.toString()`.\n- `objectMode` Emit data exactly as it comes in. This will be\n  flipped on by default if you write() something other than a\n  string or Buffer at any point. Setting `objectMode: true` will\n  prevent setting any encoding value.\n- `async` Defaults to `false`. Set to `true` to defer data\n  emission until next tick. This reduces performance slightly,\n  but makes Minipass streams use timing behavior closer to Node\n  core streams. See [Timing](#timing) for more details.\n- `signal` An `AbortSignal` that will cause the stream to unhook\n  itself from everything and become as inert as possible. Note\n  that providing a `signal` parameter will make `'error'` events\n  no longer throw if they are unhandled, but they will still be\n  emitted to handlers if any are attached.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and\n`Writable` streams.\n\n### Methods\n\n- `write(chunk, [encoding], [callback])` - Put data in. (Note\n  that, in the base Minipass class, the same data will come out.)\n  Returns `false` if the stream will buffer the next write, or\n  true if it's still in \"flowing\" mode.\n- `end([chunk, [encoding]], [callback])` - Signal that you have\n  no more data to write. This will queue an `end` event to be\n  fired when all the data has been consumed.\n- `pause()` - No more data for a while, please. This also\n  prevents `end` from being emitted for empty streams until the\n  stream is resumed.\n- `resume()` - Resume the stream. If there's data in the buffer,\n  it is all discarded. Any buffered events are immediately\n  emitted.\n- `pipe(dest)` - Send all output to the stream provided. When\n  data is emitted, it is immediately written to any and all pipe\n  destinations. (Or written on next tick in `async` mode.)\n- `unpipe(dest)` - Stop piping to the destination stream. This is\n  immediate, meaning that any asynchronously queued data will\n  _not_ make it to the destination when running in `async` mode.\n  - `options.end` - Boolean, end the destination stream when the\n    source stream ends. Default `true`.\n  - `options.proxyErrors` - Boolean, proxy `error` events from\n    the source stream to the destination stream. Note that errors\n    are _not_ proxied after the pipeline terminates, either due\n    to the source emitting `'end'` or manually unpiping with\n    `src.unpipe(dest)`. Default `false`.\n- `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are\n  EventEmitters. Some events are given special treatment,\n  however. (See below under \"events\".)\n- `promise()` - Returns a Promise that resolves when the stream\n  emits `end`, or rejects if the stream emits `error`.\n- `collect()` - Return a Promise that resolves on `end` with an\n  array containing each chunk of data that was emitted, or\n  rejects if the stream emits `error`. Note that this consumes\n  the stream data.\n- `concat()` - Same as `collect()`, but concatenates the data\n  into a single Buffer object. Will reject the returned promise\n  if the stream is in objectMode, or if it goes into objectMode\n  by the end of the data.\n- `read(n)` - Consume `n` bytes of data out of the buffer. If `n`\n  is not provided, then consume all of it. If `n` bytes are not\n  available, then it returns null. **Note** consuming streams in\n  this way is less efficient, and can lead to unnecessary Buffer\n  copying.\n- `destroy([er])` - Destroy the stream. If an error is provided,\n  then an `'error'` event is emitted. If the stream has a\n  `close()` method, and has not emitted a `'close'` event yet,\n  then `stream.close()` will be called. Any Promises returned by\n  `.promise()`, `.collect()` or `.concat()` will be rejected.\n  After being destroyed, writing to the stream will emit an\n  error. No more data will be emitted if the stream is destroyed,\n  even if it was previously buffered.\n\n### Properties\n\n- `bufferLength` Read-only. Total number of bytes buffered, or in\n  the case of objectMode, the total number of objects.\n- `encoding` Read-only. The encoding that has been set.\n- `flowing` Read-only. Boolean indicating whether a chunk written\n  to the stream will be immediately emitted.\n- `emittedEnd` Read-only. Boolean indicating whether the end-ish\n  events (ie, `end`, `prefinish`, `finish`) have been emitted.\n  Note that listening on any end-ish event will immediateyl\n  re-emit it if it has already been emitted.\n- `writable` Whether the stream is writable. Default `true`. Set\n  to `false` when `end()`\n- `readable` Whether the stream is readable. Default `true`.\n- `pipes` An array of Pipe objects referencing streams that this\n  stream is piping into.\n- `destroyed` A getter that indicates whether the stream was\n  destroyed.\n- `paused` True if the stream has been explicitly paused,\n  otherwise false.\n- `objectMode` Indicates whether the stream is in `objectMode`.\n- `aborted` Readonly property set when the `AbortSignal`\n  dispatches an `abort` event.\n\n### Events\n\n- `data` Emitted when there's data to read. Argument is the data\n  to read. This is never emitted while not flowing. If a listener\n  is attached, that will resume the stream.\n- `end` Emitted when there's no more data to read. This will be\n  emitted immediately for empty streams when `end()` is called.\n  If a listener is attached, and `end` was already emitted, then\n  it will be emitted again. All listeners are removed when `end`\n  is emitted.\n- `prefinish` An end-ish event that follows the same logic as\n  `end` and is emitted in the same conditions where `end` is\n  emitted. Emitted after `'end'`.\n- `finish` An end-ish event that follows the same logic as `end`\n  and is emitted in the same conditions where `end` is emitted.\n  Emitted after `'prefinish'`.\n- `close` An indication that an underlying resource has been\n  released. Minipass does not emit this event, but will defer it\n  until after `end` has been emitted, since it throws off some\n  stream libraries otherwise.\n- `drain` Emitted when the internal buffer empties, and it is\n  again suitable to `write()` into the stream.\n- `readable` Emitted when data is buffered and ready to be read\n  by a consumer.\n- `resume` Emitted when stream changes state from buffering to\n  flowing mode. (Ie, when `resume` is called, `pipe` is called,\n  or a `data` event listener is added.)\n\n### Static Methods\n\n- `Minipass.isStream(stream)` Returns `true` if the argument is a\n  stream, and false otherwise. To be considered a stream, the\n  object must be either an instance of Minipass, or an\n  EventEmitter that has either a `pipe()` method, or both\n  `write()` and `end()` methods. (Pretty much any stream in\n  node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass\nstreams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(\n  () => {\n    // stream is finished\n  },\n  er => {\n    // stream emitted an error\n  }\n)\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one\nchunk for you, but if you're going to do it yourself anyway, it's\nconvenient this way:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in\nplatforms that support it.\n\nSynchronous iteration will end when the currently available data\nis consumed, even if the `end` event has not been reached. In\nstring and buffer mode, the data is concatenated, so unless\nmultiple writes are occurring in the same tick as the `read()`,\nsync iteration loops will generally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written,\nwith no flattening, create the stream with the `{ objectMode:\ntrue }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i-- > 0) mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume() {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write(chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end(chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(\n    new (class extends Minipass {\n      emit(ev, ...data) {\n        // let's also log events, because debugging some weird thing\n        console.log('EMIT', ev)\n        return super.emit(ev, ...data)\n      }\n      write(chunk, encoding, callback) {\n        console.log('WRITE', chunk, encoding)\n        return super.write(chunk, encoding, callback)\n      }\n      end(chunk, encoding, callback) {\n        console.log('END', chunk, encoding)\n        return super.end(chunk, encoding, callback)\n      }\n    })()\n  )\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit(ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n      return true\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write(obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end(obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor(options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write(chunk, encoding, cb) {\n    if (\n      typeof chunk === 'string' &&\n      typeof encoding === 'string' &&\n      encoding !== 'utf8'\n    ) {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk)) {\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      try {\n        // JSON.parse can throw, emit an error on that\n        super.write(JSON.parse(jsonData[i]))\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb) cb()\n  }\n}\n```\n", "time": {"created": "2022-01-26T13:17:13.873Z", "modified": "2025-06-04T11:17:32.822Z", "3.1.6": "2021-12-06T19:45:29.457Z", "3.1.5": "2021-09-14T19:36:39.296Z", "3.1.4": "2021-09-14T14:39:09.622Z", "3.1.3": "2020-05-13T01:00:37.001Z", "3.1.2": "2020-05-09T20:59:12.842Z", "3.1.1": "2019-10-24T21:34:57.398Z", "3.1.0": "2019-10-20T04:53:28.706Z", "3.0.1": "2019-10-02T16:34:57.711Z", "3.0.0": "2019-09-30T20:16:05.884Z", "2.9.0": "2019-09-24T23:43:01.864Z", "2.8.6": "2019-09-24T16:22:27.580Z", "2.8.5": "2019-09-24T07:56:04.688Z", "2.8.4": "2019-09-24T01:03:32.894Z", "2.8.3": "2019-09-23T18:48:30.530Z", "2.8.2": "2019-09-23T16:57:17.916Z", "2.8.1": "2019-09-23T00:04:43.683Z", "2.8.0": "2019-09-22T23:56:33.134Z", "2.7.0": "2019-09-22T06:39:19.777Z", "2.6.5": "2019-09-17T22:18:31.028Z", "2.6.4": "2019-09-17T16:20:15.752Z", "2.6.3": "2019-09-17T14:36:56.519Z", "2.6.2": "2019-09-16T21:58:13.409Z", "2.6.1": "2019-09-16T20:55:48.943Z", "2.6.0": "2019-09-16T06:12:45.936Z", "2.5.1": "2019-09-09T21:34:00.679Z", "2.5.0": "2019-08-28T23:20:03.661Z", "2.4.0": "2019-08-23T16:36:02.314Z", "2.3.5": "2018-10-23T21:46:18.167Z", "2.3.4": "2018-08-10T16:25:21.783Z", "2.3.3": "2018-05-22T18:59:35.084Z", "2.3.2": "2018-05-22T03:42:41.482Z", "2.3.1": "2018-05-18T23:25:47.557Z", "2.3.0": "2018-05-06T17:52:27.992Z", "2.2.4": "2018-03-20T16:44:28.516Z", "2.2.3": "2018-03-20T16:26:35.853Z", "2.2.2": "2018-03-20T16:23:26.657Z", "2.2.1": "2017-07-10T05:09:48.146Z", "2.2.0": "2017-07-09T05:17:47.759Z", "2.1.1": "2017-06-14T16:26:50.686Z", "2.1.0": "2017-06-14T16:17:30.707Z", "2.0.2": "2017-05-10T17:07:21.058Z", "2.0.1": "2017-05-04T20:46:53.600Z", "2.0.0": "2017-05-04T07:59:52.477Z", "1.2.0": "2017-04-30T04:14:30.434Z", "1.1.11": "2017-04-30T02:36:01.406Z", "1.1.10": "2017-04-28T00:40:45.977Z", "1.1.9": "2017-04-22T03:02:25.768Z", "1.1.8": "2017-04-10T17:52:57.826Z", "1.1.7": "2017-04-03T20:21:06.787Z", "1.1.6": "2017-03-29T06:46:51.682Z", "1.1.5": "2017-03-29T06:18:55.673Z", "1.1.4": "2017-03-29T01:25:23.560Z", "1.1.3": "2017-03-29T00:58:09.871Z", "1.1.2": "2017-03-28T08:14:58.366Z", "1.1.1": "2017-03-28T07:06:23.059Z", "1.1.0": "2017-03-28T06:13:10.262Z", "1.0.2": "2017-03-22T04:40:04.601Z", "1.0.1": "2017-03-22T00:26:16.857Z", "1.0.0": "2017-03-14T00:11:57.420Z", "3.2.0": "2022-06-08T17:23:00.538Z", "3.2.1": "2022-06-10T18:42:30.081Z", "3.3.0": "2022-06-20T02:16:02.422Z", "3.3.1": "2022-06-20T02:52:10.767Z", "3.3.2": "2022-06-20T02:57:26.134Z", "3.3.3": "2022-06-20T03:38:50.485Z", "3.3.4": "2022-06-28T18:43:35.980Z", "3.3.5": "2022-07-24T22:23:31.633Z", "3.3.6": "2022-11-25T07:54:48.420Z", "4.0.0": "2022-11-27T00:17:30.622Z", "4.0.1": "2023-01-30T15:27:33.931Z", "4.0.2": "2023-02-05T17:49:25.951Z", "4.0.3": "2023-02-07T21:58:13.198Z", "4.1.0": "2023-02-22T04:20:08.792Z", "4.2.0": "2023-02-22T05:38:13.922Z", "4.2.1": "2023-02-24T04:48:58.574Z", "4.2.2": "2023-02-26T07:18:40.877Z", "4.2.3": "2023-02-26T07:45:46.439Z", "4.2.4": "2023-02-26T07:51:00.557Z", "4.2.5": "2023-03-11T19:27:30.190Z", "4.2.6": "2023-04-09T21:00:10.988Z", "4.2.7": "2023-04-09T21:24:19.901Z", "5.0.0": "2023-04-09T21:54:10.390Z", "4.2.8": "2023-04-11T16:00:11.170Z", "6.0.0": "2023-05-15T04:41:09.361Z", "6.0.1": "2023-05-15T22:26:16.226Z", "6.0.2": "2023-05-17T21:16:50.370Z", "7.0.0": "2023-07-08T00:14:43.854Z", "7.0.1": "2023-07-08T00:23:19.663Z", "7.0.2": "2023-07-11T05:17:30.315Z", "7.0.3": "2023-08-12T19:30:03.611Z", "7.0.4": "2023-09-28T23:58:33.597Z", "7.1.0": "2024-05-04T02:00:38.139Z", "7.1.1": "2024-05-09T13:49:27.137Z", "7.1.2": "2024-05-24T00:42:21.149Z"}, "versions": {"3.1.6": {"name": "minipass", "version": "3.1.6", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^15.0.9", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "f55015f024cfaf1a27b595ddcedebd99c38dc189", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.6", "_nodeVersion": "16.5.0", "_npmVersion": "8.1.3", "dist": {"integrity": "sha512-rty5kpw9/z8SX9dmxblFA6edItUmwJgMeYDZRrwlIVN27i8gysGbznJwUggw2V/FVqFSDdWy040ZPS811DYAqQ==", "shasum": "3b8150aa688a711a1521af5e8779c1d3bb4f45ee", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.1.6.tgz", "fileCount": 4, "unpackedSize": 37791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrmhZCRA9TVsSAnZWagAA6B8P/AyQZ7FDRbeYF8pdHbha\nn7//BY0OyQ+yq0vgh6kEoMqnUIfSrANz5kwZehLKjARw2vcUG+e/2s5P4ATN\nLd5SWpG/L71IfPe0n/guuSKJW1IwH7nYj0FjMBkWSOnUnw3SE8rOhg8K1Smt\n4TdO9Lpp7QLkkBmrU35MVXg55YN4n57BeV5wmK5kGkeybYV1bzs2xR5OinYa\nvxi4ySKFFFhoX38SwtN8XTms/Wuw1D4y3ejBlSGoqhlHHHbp+iJDKx0xDe2b\n6IwAu1yzj0kiJLJ8bVM/Bz84y+JzKVN+GIatDXdA3kYs6BBGuVkqNDh/ckRG\nYHO3jFg0Eo7AnJrzAdCUn+pcj0ISHKuoDwuTsMnD05JMVFXmD5UWUwkYPxqf\nfWqn2q48+8XkHMCguWf2F1d5Ri+lr5ck/o43KRyeXRIj5ClTpGuGOpEYO8cw\nOHLVDxEdefVlUVBcE88ztw6kt9pNZ2Tmfk0/qyfW2+CQlpTaxv+nKgaj5CoC\nOk0wEvWb4uQn3r7t3IrTCd7AD7guwRAe6Xnn2ccSkmhPjaa59x9yRnrxsGer\nxolHdV48bxcdKZ4DTGZ7sipRM/or7gNY/8ELrfdkI1vqnGMDTa6KlFnn/rfJ\ncpEl9NPUPsEdDfiXXYRukE9s5G/pCC13WaKabhaTKDJxmc2r5f47qTmHuX1v\n0DXq\r\n=HYuS\r\n-----END PGP SIGNATURE-----\r\n", "size": 12495, "noattachment": false}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.6_1638819929280_0.34760831556769944"}, "_hasShrinkwrap": false, "publish_time": 1638819929457, "_cnpm_publish_time": 1638819929457, "_cnpmcore_publish_time": "2021-12-13T14:42:06.095Z"}, "3.1.5": {"name": "minipass", "version": "3.1.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^15.0.9", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "9bfcf550f7f71667294c0f3a75458347020754ff", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.5", "_nodeVersion": "16.5.0", "_npmVersion": "7.23.0", "dist": {"shasum": "71f6251b0a33a49c01b3cf97ff77eda030dff732", "size": 12448, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.1.5.tgz", "integrity": "sha512-+8NzxD82XQoNKNrl1d/FSi+X8wAEWR+sbYAfIvub4Nz0d22plFG72CEVVaufV8PNf4qSslFTD8VMOxNVhHCjTw=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.5_1631648199111_0.4963903964208507"}, "_hasShrinkwrap": false, "publish_time": 1631648199296, "_cnpm_publish_time": 1631648199296, "_cnpmcore_publish_time": "2021-12-13T14:42:06.431Z"}, "3.1.4": {"name": "minipass", "version": "3.1.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^15.0.9", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "bb90f266f391b77b07c0a241384cbe0f705eb38e", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.4", "_nodeVersion": "16.5.0", "_npmVersion": "7.23.0", "dist": {"shasum": "bd5a9b097a326977e84a6ff1a824f322c516ab84", "size": 12403, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.1.4.tgz", "integrity": "sha512-0EWfzWLOeFe013Jz0k5iaza6l7/hjSozD+sHgKY9LClEKZY+0jbIwd6CnkOcEFDxlZVwcG5oPUWDbzXzJ0k8nA=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.4_1631630349451_0.8024518838544734"}, "_hasShrinkwrap": false, "publish_time": 1631630349622, "_cnpm_publish_time": 1631630349622, "_cnpmcore_publish_time": "2021-12-13T14:42:06.752Z"}, "3.1.3": {"name": "minipass", "version": "3.1.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "a67764b3ea5c8c2679167390897da2b46d0b8022", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.3", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "7d42ff1f39635482e15f9cdb53184deebd5815fd", "size": 12388, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.1.3.tgz", "integrity": "sha512-Mgd2GdMVzY+x3IJ+oHnVM+KG3lA5c8tnabyJKmHSaG2kAGpudxuOf8ToDkhumF7UzME7DecbQE9uOZhNm7PuJg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.3_1589331636888_0.7787979174732615"}, "_hasShrinkwrap": false, "publish_time": 1589331637001, "_cnpm_publish_time": 1589331637001, "_cnpmcore_publish_time": "2021-12-13T14:42:07.040Z"}, "3.1.2": {"name": "minipass", "version": "3.1.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "41ec3d09cb8034cbfc471b802269dc4a684aa0c3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.2", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"shasum": "74bce7f1b9624236dc8d374a49910181f9eb3600", "size": 12181, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.1.2.tgz", "integrity": "sha512-1UelkoRxUOd3d3VOKu2YIgwqhnLaBRpPyqiCpLFOesz5gqEMS8ryTnrzbge1J6C4LBKecr9eKb1FD6INXvWssw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.2_1589057952720_0.49020973461025363"}, "_hasShrinkwrap": false, "publish_time": 1589057952842, "_cnpm_publish_time": 1589057952842, "_cnpmcore_publish_time": "2021-12-13T14:42:07.417Z"}, "3.1.1": {"name": "minipass", "version": "3.1.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "0e866597838dadaf8dcfd7872ab8951f3c8ba3c0", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.1", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.0", "dist": {"shasum": "7607ce778472a185ad6d89082aa2070f79cedcd5", "size": 12192, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.1.1.tgz", "integrity": "sha512-UFqVihv6PQgwj8/yTGvl9kPz7xIAY+R5z6XYjRInD3Gk3qx6QGSD6zEcpeG4Dy/lQnv1J6zv8ejV90hyYIKf3w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.1_1571952897280_0.4106563063011017"}, "_hasShrinkwrap": false, "publish_time": 1571952897398, "_cnpm_publish_time": 1571952897398, "_cnpmcore_publish_time": "2021-12-13T14:42:07.732Z"}, "3.1.0": {"name": "minipass", "version": "3.1.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "4a5f1c26a1881fb3370d9c28ea9bdefc6a3eb402", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.0", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.0", "dist": {"shasum": "c08b140a0d5e8b6c6034056f7a168a3f5441df3b", "size": 12142, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.1.0.tgz", "integrity": "sha512-wVCobyF3/vj8KTVCp7+XKmorSiBCKvIKJDsB3VjC1m/pUCxhvInRUpnqLcaETKlqZig0KNP6EYjqBZxC42GUBg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.0_1571547208516_0.163407317672416"}, "_hasShrinkwrap": false, "publish_time": 1571547208706, "_cnpm_publish_time": 1571547208706, "_cnpmcore_publish_time": "2021-12-13T14:42:08.093Z"}, "3.0.1": {"name": "minipass", "version": "3.0.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "603b8116ea11af26b43f2f2d0888edad77696b4f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.0.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "b4fec73bd61e8a40f0b374ddd04260ade2c8ec20", "size": 12114, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.0.1.tgz", "integrity": "sha512-2y5okJ4uBsjoD2vAbLKL9EUQPPkC0YMIp+2mZOXG3nBba++pdfJWRxx2Ewirc0pwAJYu4XtWg2EkVo1nRXuO/w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.0.1_1570034097465_0.9922015990068342"}, "_hasShrinkwrap": false, "publish_time": 1570034097711, "_cnpm_publish_time": 1570034097711, "_cnpmcore_publish_time": "2021-12-13T14:42:08.513Z"}, "3.0.0": {"name": "minipass", "version": "3.0.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "76ac42b7ffa1bd3cc2f669d828ec5aedf74ad6f5", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.0.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "adb830268348df8b32217ceda3fc48684faff232", "size": 12106, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.0.0.tgz", "integrity": "sha512-FKNU4XrAPDX0+ynwns7njVu4RolyG1mUKSlT6n6GwGXLtYSYh2Znc0S83Rl6zEr1zgFfXvAzIBabnmItm+n19g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.0.0_1569874565676_0.7731705094606287"}, "_hasShrinkwrap": false, "publish_time": 1569874565884, "_cnpm_publish_time": 1569874565884, "_cnpmcore_publish_time": "2021-12-13T14:42:08.832Z"}, "2.9.0": {"name": "minipass", "version": "2.9.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "readmeFilename": "README.md", "gitHead": "86068755a48b4d58d21376c0c30c1ecc44fe4a8e", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.9.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "e713762e7d3e32fed803115cf93e04bca9fcc9a6", "size": 12218, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.9.0.tgz", "integrity": "sha512-wxfUjg9WebH+CUDX/CdbRlh5SmfZiy/hpkxaRI16Y9W56Pa75sWgd/rvFilSgrauD9NyFymP/+JFV3KwzIsJeg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.9.0_1569368581649_0.9807254959544474"}, "_hasShrinkwrap": false, "publish_time": 1569368581864, "_cnpm_publish_time": 1569368581864, "_cnpmcore_publish_time": "2021-12-13T14:42:09.179Z"}, "2.8.6": {"name": "minipass", "version": "2.8.6", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "8243e5efd6a058d28479a66e40ab3aba0207cb76", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.6", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "620d889ace26356391d010ecb9458749df9b6db5", "size": 11967, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.8.6.tgz", "integrity": "sha512-lFG7d6g3+/UaFDCOtqPiKAC9zngWWsQZl1g5q6gaONqrjq61SX2xFqXMleQiFVyDpYwa018E9hmlAFY22PCb+A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.6_1569342147428_0.013183884532681267"}, "_hasShrinkwrap": false, "publish_time": 1569342147580, "_cnpm_publish_time": 1569342147580, "_cnpmcore_publish_time": "2021-12-13T14:42:09.545Z"}, "2.8.5": {"name": "minipass", "version": "2.8.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "5718d456b6bc535febe3b3f52168fa8b2acada04", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.5", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "072f3c67b1f05fe4703f58a3c38e186c03a17692", "size": 10154, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.8.5.tgz", "integrity": "sha512-D5+szmZBoOAfbLjOXY4Ve5bWylyTdrUOmbJPgYmgTF5ovCnCFFTY+I16Ccm/UjSNNAekXtIEDvoCDioFzRz18Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.5_1569311764545_0.23634394433582018"}, "_hasShrinkwrap": false, "publish_time": 1569311764688, "_cnpm_publish_time": 1569311764688, "_cnpmcore_publish_time": "2021-12-13T14:42:09.972Z"}, "2.8.4": {"name": "minipass", "version": "2.8.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "f193f5cf35b3b04d434c0b1cea954928842b3770", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "a73bdc84cad62e8e6c8d56eba1302a5fe04c5910", "size": 10092, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.8.1.tgz", "integrity": "sha512-QCG523ParRcE2+9A6wYh9UI3uy2FFLw4DQaVYQrY5HPfszc5M6VDD+j0QCwHm19LI2imes4RB+NBD8cOJccyCg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.1_1569197083493_0.4502391094175884"}, "_hasShrinkwrap": false, "publish_time": 1569197083683, "_cnpm_publish_time": 1569197083683, "_cnpmcore_publish_time": "2021-12-13T14:42:11.580Z", "deprecated": "This version of Minipass has a serious regression which was fixed in v2.8.5.  Please update ASAP. ([WARNING] Use 2.8.1 instead of 2.8.4, reason: https://github.com/isaacs/minipass/issues/9)"}, "2.8.3": {"name": "minipass", "version": "2.8.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "f193f5cf35b3b04d434c0b1cea954928842b3770", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "a73bdc84cad62e8e6c8d56eba1302a5fe04c5910", "size": 10092, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.8.1.tgz", "integrity": "sha512-QCG523ParRcE2+9A6wYh9UI3uy2FFLw4DQaVYQrY5HPfszc5M6VDD+j0QCwHm19LI2imes4RB+NBD8cOJccyCg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.1_1569197083493_0.4502391094175884"}, "_hasShrinkwrap": false, "publish_time": 1569197083683, "_cnpm_publish_time": 1569197083683, "_cnpmcore_publish_time": "2021-12-13T14:42:11.580Z", "deprecated": "[WARNING] Use 2.8.1 instead of 2.8.3, reason: https://github.com/isaacs/minipass/issues/9"}, "2.8.2": {"name": "minipass", "version": "2.8.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "f193f5cf35b3b04d434c0b1cea954928842b3770", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "a73bdc84cad62e8e6c8d56eba1302a5fe04c5910", "size": 10092, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.8.1.tgz", "integrity": "sha512-QCG523ParRcE2+9A6wYh9UI3uy2FFLw4DQaVYQrY5HPfszc5M6VDD+j0QCwHm19LI2imes4RB+NBD8cOJccyCg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.1_1569197083493_0.4502391094175884"}, "_hasShrinkwrap": false, "publish_time": 1569197083683, "_cnpm_publish_time": 1569197083683, "_cnpmcore_publish_time": "2021-12-13T14:42:11.580Z", "deprecated": "[WARNING] Use 2.8.1 instead of 2.8.2, reason: https://github.com/isaacs/minipass/issues/9"}, "2.8.1": {"name": "minipass", "version": "2.8.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "f193f5cf35b3b04d434c0b1cea954928842b3770", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "a73bdc84cad62e8e6c8d56eba1302a5fe04c5910", "size": 10092, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.8.1.tgz", "integrity": "sha512-QCG523ParRcE2+9A6wYh9UI3uy2FFLw4DQaVYQrY5HPfszc5M6VDD+j0QCwHm19LI2imes4RB+NBD8cOJccyCg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.1_1569197083493_0.4502391094175884"}, "_hasShrinkwrap": false, "publish_time": 1569197083683, "_cnpm_publish_time": 1569197083683, "_cnpmcore_publish_time": "2021-12-13T14:42:11.580Z"}, "2.8.0": {"name": "minipass", "version": "2.8.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "8cfe8502b45c48bdd3df7ced13a726f0e69f345a", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "63eb51b46697cbaeb75158ae00cf4f95fb3d88e5", "size": 10088, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.8.0.tgz", "integrity": "sha512-zsFPWDGtVTM6szptnZFwpFwXIj2cQW+BhrGmR87In8/Of5dnYnIlsb6Pujb9BpQMCSURRopBg3o2HFHFlfXOPA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.0_1569196592923_0.383813049498406"}, "_hasShrinkwrap": false, "publish_time": 1569196593134, "_cnpm_publish_time": 1569196593134, "_cnpmcore_publish_time": "2021-12-13T14:42:12.042Z"}, "2.7.0": {"name": "minipass", "version": "2.7.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "6e1486f21bc805f98221d1ab07969b39a498a79d", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.7.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "c01093a82287c8331f08f1075499fef124888796", "size": 9323, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.7.0.tgz", "integrity": "sha512-+CbZuJ4uEiuTL9s5Z/ULkuRg1O9AvVqVvceaBrhbYHIy1R3dPO7FMmG0nZLD0//ZzZq0MUOjwdBQvk+w1JHUqQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.7.0_1569134359593_0.6757777043008619"}, "_hasShrinkwrap": false, "publish_time": 1569134359777, "_cnpm_publish_time": 1569134359777, "_cnpmcore_publish_time": "2021-12-13T14:42:12.487Z"}, "2.6.5": {"name": "minipass", "version": "2.6.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "eb03b7ad760791ebb727a393a1056aab490e45ba", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.6.5", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "1c245f9f2897f70fd4a219066261ce6c29f80b18", "size": 6700, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.6.5.tgz", "integrity": "sha512-ewSKOPFH9blOLXx0YSE+mbrNMBFPS+11a2b03QZ+P4LVrUHW/GAlqeYC7DBknDyMWkHzrzTpDhUvy7MUxqyrPA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.6.5_1568758710901_0.00493985783646278"}, "_hasShrinkwrap": false, "publish_time": 1568758711028, "_cnpm_publish_time": 1568758711028, "_cnpmcore_publish_time": "2021-12-13T14:42:12.951Z"}, "2.6.4": {"name": "minipass", "version": "2.6.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "e3b7320555e1d7a258bda1987e205ed72c623305", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.6.4", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "c15b8e86d1ecee001652564a2c240c0b6e58e817", "size": 6695, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.6.4.tgz", "integrity": "sha512-D/+wBy2YykFsCcWvaIslCKKus5tqGQZ8MhEzNx4mujLNgHhXWaaUOZkok6/kztAlTt0QkYLEyIShrybNmzoeTA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.6.4_1568737215637_0.8029580992745486"}, "_hasShrinkwrap": false, "publish_time": 1568737215752, "_cnpm_publish_time": 1568737215752, "_cnpmcore_publish_time": "2021-12-13T14:42:13.552Z"}, "2.6.3": {"name": "minipass", "version": "2.6.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "0e5cd3265fd37d33ffd2474868d20fd529e40519", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.6.3", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "3ac4ae0fc8835946fdf6d2480659e98e17e88424", "size": 6604, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.6.3.tgz", "integrity": "sha512-TQJDWx0rizi7qvXGydyOx+it271jnk/zxV7/mCkTecpMrSksvZ6zTXxWgJS2gSzVmYG1tBufz5r5NaBVkJEERQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.6.3_1568731016405_0.9743197614232673"}, "_hasShrinkwrap": false, "publish_time": 1568731016519, "_cnpm_publish_time": 1568731016519, "_cnpmcore_publish_time": "2021-12-13T14:42:14.061Z"}, "2.6.2": {"name": "minipass", "version": "2.6.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "43f76e2a9dfaccd1b6cc1f93c4130a849df29523", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.5.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "cf435a9bf9408796ca3a3525a8b851464279c9b8", "size": 5372, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.5.1.tgz", "integrity": "sha512-dmpSnLJtNQioZFI5HfQ55Ad0DzzsMAb+HfokwRTNXwEQjepbTkl5mtIlSVxGIkOkxlpX7wIn5ET/oAd9fZ/Y/Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.5.1_1568064840586_0.8526816449859596"}, "_hasShrinkwrap": false, "publish_time": 1568064840679, "_cnpm_publish_time": 1568064840679, "_cnpmcore_publish_time": "2021-12-13T14:42:16.101Z", "deprecated": "[WARNING] Use 2.5.1 instead of 2.6.2, reason: https://github.com/isaacs/minipass/issues/8"}, "2.6.1": {"name": "minipass", "version": "2.6.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "43f76e2a9dfaccd1b6cc1f93c4130a849df29523", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.5.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "cf435a9bf9408796ca3a3525a8b851464279c9b8", "size": 5372, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.5.1.tgz", "integrity": "sha512-dmpSnLJtNQioZFI5HfQ55Ad0DzzsMAb+HfokwRTNXwEQjepbTkl5mtIlSVxGIkOkxlpX7wIn5ET/oAd9fZ/Y/Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.5.1_1568064840586_0.8526816449859596"}, "_hasShrinkwrap": false, "publish_time": 1568064840679, "_cnpm_publish_time": 1568064840679, "_cnpmcore_publish_time": "2021-12-13T14:42:16.101Z", "deprecated": "[WARNING] Use 2.5.1 instead of 2.6.1, reason: https://github.com/isaacs/minipass/issues/8"}, "2.6.0": {"name": "minipass", "version": "2.6.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "43f76e2a9dfaccd1b6cc1f93c4130a849df29523", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.5.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "cf435a9bf9408796ca3a3525a8b851464279c9b8", "size": 5372, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.5.1.tgz", "integrity": "sha512-dmpSnLJtNQioZFI5HfQ55Ad0DzzsMAb+HfokwRTNXwEQjepbTkl5mtIlSVxGIkOkxlpX7wIn5ET/oAd9fZ/Y/Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.5.1_1568064840586_0.8526816449859596"}, "_hasShrinkwrap": false, "publish_time": 1568064840679, "_cnpm_publish_time": 1568064840679, "_cnpmcore_publish_time": "2021-12-13T14:42:16.101Z", "deprecated": "[WARNING] Use 2.5.1 instead of 2.6.0, reason: https://github.com/isaacs/minipass/issues/8"}, "2.5.1": {"name": "minipass", "version": "2.5.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "43f76e2a9dfaccd1b6cc1f93c4130a849df29523", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.5.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "cf435a9bf9408796ca3a3525a8b851464279c9b8", "size": 5372, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.5.1.tgz", "integrity": "sha512-dmpSnLJtNQioZFI5HfQ55Ad0DzzsMAb+HfokwRTNXwEQjepbTkl5mtIlSVxGIkOkxlpX7wIn5ET/oAd9fZ/Y/Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.5.1_1568064840586_0.8526816449859596"}, "_hasShrinkwrap": false, "publish_time": 1568064840679, "_cnpm_publish_time": 1568064840679, "_cnpmcore_publish_time": "2021-12-13T14:42:16.101Z"}, "2.5.0": {"name": "minipass", "version": "2.5.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "fab29874f70b04730c864ac52136a7a01bd1801c", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.5.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.2", "dist": {"shasum": "dddb1d001976978158a05badfcbef4a771612857", "size": 5342, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.5.0.tgz", "integrity": "sha512-9FwMVYhn6ERvMR8XFdOavRz4QK/VJV8elU1x50vYexf9lslDcWe/f4HBRxCPd185ekRSjU6CfYyJCECa/CQy7Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.5.0_1567034403527_0.5492616475869285"}, "_hasShrinkwrap": false, "publish_time": 1567034403661, "_cnpm_publish_time": 1567034403661, "_cnpmcore_publish_time": "2021-12-13T14:42:16.668Z"}, "2.4.0": {"name": "minipass", "version": "2.4.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^12.0.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "565ebb363ae2b6720fe4d0795aed96ba90f059d7", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.4.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.1", "dist": {"shasum": "38f0af94f42fb6f34d3d7d82a90e2c99cd3ff485", "size": 5169, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.4.0.tgz", "integrity": "sha512-6PmOuSP4NnZXzs2z6rbwzLJu/c5gdzYg1mRI/WIYdx45iiX7T+a4esOzavD6V/KmBzAaopFSTZPZcUx73bqKWA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.4.0_1566578162152_0.5234555613255545"}, "_hasShrinkwrap": false, "publish_time": 1566578162314, "_cnpm_publish_time": 1566578162314, "_cnpmcore_publish_time": "2021-12-13T14:42:17.221Z"}, "2.3.5": {"name": "minipass", "version": "2.3.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^12.0.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "d362cb255c2b2ad24b0b6bf8cef35c522ba29019", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.5", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "cacebe492022497f656b0f0f51e2682a9ed2d848", "size": 5167, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.3.5.tgz", "integrity": "sha512-Gi1W4k059gyRbyVUZQ4mEqLm0YIUiGYfvxhF6SIlk3ui1WVxMTGfGdQ2SInh3PDrRTVvPKgULkpJtT4RH10+VA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.5_1540331177990_0.793403052371799"}, "_hasShrinkwrap": false, "publish_time": 1540331178167, "_cnpm_publish_time": 1540331178167, "_cnpmcore_publish_time": "2021-12-13T14:42:17.893Z"}, "2.3.4": {"name": "minipass", "version": "2.3.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^12.0.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "6b2713ffe4871849d232caca4f05d390cfcb93dc", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.4", "_npmVersion": "6.3.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "4768d7605ed6194d6d576169b9e12ef71e9d9957", "size": 5171, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.3.4.tgz", "integrity": "sha512-mlouk1OHlaUE8Odt1drMtG1bAJA4ZA6B/ehysgV0LUIrDHdKgo1KorZq3pK0b/7Z7LJIQ12MNM6aC+Tn6lUZ5w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.4_1533918321682_0.4793462682470073"}, "_hasShrinkwrap": false, "publish_time": 1533918321783, "_cnpm_publish_time": 1533918321783, "_cnpmcore_publish_time": "2021-12-13T14:42:18.510Z"}, "2.3.3": {"name": "minipass", "version": "2.3.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^12.0.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "eb6835c4199e205fae9ae48c5340ff4b3edca60e", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.3", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "a7dcc8b7b833f5d368759cce544dccb55f50f233", "size": 4685, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.3.3.tgz", "integrity": "sha512-/jAn9/tEX4gnpyRATxgHEOV6xbcyxgT7iUnxo9Y3+OB0zX00TgKIv/2FZCf5brBbICcwbLqVv2ImjvWWrQMSYw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.3_1527015575005_0.7372253058813618"}, "_hasShrinkwrap": false, "publish_time": 1527015575084, "_cnpm_publish_time": 1527015575084, "_cnpmcore_publish_time": "2021-12-13T14:42:19.082Z"}, "2.3.2": {"name": "minipass", "version": "2.3.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.1", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^11.1.4", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "77971fac31a26cada594071e82cadfdfdd4949f3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.2", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "2bb00e836f1f8d44573f96b08c4f440b330072e4", "size": 4615, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.3.2.tgz", "integrity": "sha512-4AwVB38b0GIVYTfI3+Y9A1yynJkUHA3SGH+nx4YSzjwKXIOeJ9GVdvN88Rl7bivL0DaFkYng6hAC7vbb6+eZ5w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.2_1526960561378_0.7850614237575084"}, "_hasShrinkwrap": false, "publish_time": 1526960561482, "_cnpm_publish_time": 1526960561482, "_cnpmcore_publish_time": "2021-12-13T14:42:19.781Z"}, "2.3.1": {"name": "minipass", "version": "2.3.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.1", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^11.1.4", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "df22eac41ed1d11e9f7bd2903f88f021cc34f27f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.1", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "4e872b959131a672837ab3cb554962bc84b1537d", "size": 4609, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.3.1.tgz", "integrity": "sha512-liT0Gjaz7OHXg2qsfefVFfryBE9uAsqVFWQ6wVf4KNMzI2edsrCDjdGDpTxRaykbxhSKHu/SDtRRcMEcCcTQ2g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.1_1526685947485_0.8626022137673797"}, "_hasShrinkwrap": false, "publish_time": 1526685947557, "_cnpm_publish_time": 1526685947557, "_cnpmcore_publish_time": "2021-12-13T14:42:20.431Z"}, "2.3.0": {"name": "minipass", "version": "2.3.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.1", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^11.1.4", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "bcb7f538847de6d30af0c8e83933af7cade2cc58", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "2e11b1c46df7fe7f1afbe9a490280add21ffe384", "size": 4624, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.3.0.tgz", "integrity": "sha512-jWC2Eg+Np4bxah7llu1IrUNSJQxtLz/J+pOjTM0nFpJXGAaV18XBWhUn031Q1tAA/TJtA1jgwnOe9S2PQa4Lbg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.0_1525629147927_0.7791628235820471"}, "_hasShrinkwrap": false, "publish_time": 1525629147992, "_cnpm_publish_time": 1525629147992, "_cnpmcore_publish_time": "2021-12-13T14:42:21.044Z"}, "2.2.4": {"name": "minipass", "version": "2.2.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.1", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "73cf8431e3678b27083e353b8ef0a6bfab84650c", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.4", "_npmVersion": "5.7.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "03c824d84551ec38a8d1bb5bc350a5a30a354a40", "size": 3548, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.2.4.tgz", "integrity": "sha512-hzXIWWet/BzWhYs2b+u7dRHlruXhwdgvlTMDKC6Cb1U7ps6Ac6yQlR39xsbjWJE377YTCtKwIXIpJ5oP+j5y8g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.2.4_1521564268128_0.1298490999080657"}, "_hasShrinkwrap": false, "publish_time": 1521564268516, "_cnpm_publish_time": 1521564268516, "_cnpmcore_publish_time": "2021-12-13T14:42:21.698Z"}, "2.2.3": {"name": "minipass", "version": "2.2.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "8af41a48a2ca01dc857003d650dd74765e950e03", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.3", "_npmVersion": "5.7.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "cf0ec79c88cd3880da991a9c376a77fe1140ed63", "size": 3395, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.2.3.tgz", "integrity": "sha512-kpUYlSURoUeOQ823yjhFyNiEGUYfa3J8ufxOYkvp8ilVFZrukGPHWrDn5xOtfueJS56+Z1msB8D2qVcaaJhlkQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.2.3_1521563195774_0.8260329475137804"}, "_hasShrinkwrap": false, "publish_time": 1521563195853, "_cnpm_publish_time": 1521563195853, "_cnpmcore_publish_time": "2021-12-13T14:42:22.315Z"}, "2.2.2": {"name": "minipass", "version": "2.2.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ba027488f63fc7de95ccf1ef770901693ae14c2f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.2", "_npmVersion": "5.7.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "26feb237462d235ed8b6542abf36fe019490063a", "size": 58757, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.2.2.tgz", "integrity": "sha512-PhtCbGQmUep5DSJW19ixmEpomjGv1xW4fpG0w4PbbrGWy1YJ5Duau8VxZxeuiHdlbpukJvHCzrVHJu900kcwLA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.2.2_1521563006537_0.0684973195384373"}, "_hasShrinkwrap": false, "publish_time": 1521563006657, "_cnpm_publish_time": 1521563006657, "_cnpmcore_publish_time": "2021-12-13T14:42:23.198Z"}, "2.2.1": {"name": "minipass", "version": "2.2.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "7b7d9ad23f01dd0a6347f66a8441cb2dbc0abcec", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.1", "_npmVersion": "5.1.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "5ada97538b1027b4cf7213432428578cb564011f", "size": 59381, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.2.1.tgz", "integrity": "sha512-u1aUllxPJUI07cOqzR7reGmQxmCqlH88uIIsf6XZFEWgw7gXKpJdR+5R9Y3KEDmWYkdIz9wXZs3C0jOPxejk/Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass-2.2.1.tgz_1499663387972_0.7067792634479702"}, "directories": {}, "publish_time": 1499663388146, "_hasShrinkwrap": false, "_cnpm_publish_time": 1499663388146, "_cnpmcore_publish_time": "2021-12-13T14:42:24.049Z"}, "2.2.0": {"name": "minipass", "version": "2.2.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "277abefa0edbac568083d05b2d7130bc768ed552", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.0", "_npmVersion": "5.1.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "c0db6c9d8ec7609e5a98b40a01bb229b803c961d", "size": 59338, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.2.0.tgz", "integrity": "sha512-2Cy9UnruqC1KHTuOyu00TmCgt8YzEQLN58gshpt6JaL8Vq3ir1ArIZ1rU8V1oJzrHpPmoKjlm7eH61R57dc+9Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass-2.2.0.tgz_1499577467556_0.7973325287457556"}, "directories": {}, "publish_time": 1499577467759, "_hasShrinkwrap": false, "_cnpm_publish_time": 1499577467759, "_cnpmcore_publish_time": "2021-12-13T14:42:24.844Z"}, "2.1.1": {"name": "minipass", "version": "2.1.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "cc4d17e603adf90567fc0e0da2cf0b9cfb43d72b", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.1.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "c80c80a3491c180d4071d8f219bd1b4b0284999d", "size": 59076, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.1.1.tgz", "integrity": "sha512-xZjdNWL+9Z5Ut0Ay+S/2JJranFcuJJMmXIRKbFEpzETZITghn5w3Gf524kwfrpB7Jm8QplXwKJnkDn/pdF3/7Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass-2.1.1.tgz_1497457610522_0.42575242393650115"}, "directories": {}, "publish_time": 1497457610686, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497457610686, "_cnpmcore_publish_time": "2021-12-13T14:42:25.676Z"}, "2.1.0": {"name": "minipass", "version": "2.1.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "7c67ec39540408cc404bac8d96e8bdf572cc641f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.1.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "a25f21dd72a2b8e715ea5c784aa0dbd703759ef7", "size": 60682, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.1.0.tgz", "integrity": "sha512-DmPo3Ry1ilNYyQshqcGo45jrMZ4Yq8lBd7eW5o8tqju8E6rryj6qAf8ijh5oq6V3iTJDlA00wfsrEarx/wp18Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass-2.1.0.tgz_1497457050581_0.37674622470512986"}, "directories": {}, "publish_time": 1497457050707, "_hasShrinkwrap": false, "_cnpm_publish_time": 1497457050707, "_cnpmcore_publish_time": "2021-12-13T14:42:26.451Z"}, "2.0.2": {"name": "minipass", "version": "2.0.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "b18627e376d16f9188cd4049efb8febacf8b2fa2", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.0.2", "_npmVersion": "5.0.0-beta.44", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "fae5c78124051f56fd2007df0012e0dac7a752ce", "size": 5540, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.0.2.tgz", "integrity": "sha512-DtFmHGmqDzlw/iUypeGvwFSbP3n7R6S0wcJ1GiQkKxM1aQigmCUaQLOT2fGQGNwZCKdqxTtC2NN5FzEWFiz+KA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-2.0.2.tgz_1494436039082_0.553761099698022"}, "directories": {}, "publish_time": 1494436041058, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494436041058, "_cnpmcore_publish_time": "2021-12-13T14:42:27.210Z"}, "2.0.1": {"name": "minipass", "version": "2.0.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "d029efb88726070e7c18c61259021e46efb1b8b1", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.0.1", "_npmVersion": "5.0.0-beta.33", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "9ce4b0ce5b065eb2e5e2e20687e7e1d43579ef78", "size": 5383, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.0.1.tgz", "integrity": "sha512-uXQyMf0GrY/RDfekKG1CzpY99cj/6X7VLxBXPt12+84KaPAeWWo3lEwI+DCJInrhSpO6IA4HCbCXJptsh6gM0Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-2.0.1.tgz_1493930811196_0.312193572986871"}, "directories": {}, "publish_time": 1493930813600, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493930813600, "_cnpmcore_publish_time": "2021-12-13T14:42:28.019Z"}, "2.0.0": {"name": "minipass", "version": "2.0.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "81ddd48d09008cea36adaa7b2d307cb181901140", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.0.0", "_shasum": "e095385c8e8a89be8b2577c7b09ffa91e091fa02", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e095385c8e8a89be8b2577c7b09ffa91e091fa02", "size": 5130, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-2.0.0.tgz", "integrity": "sha512-bD6zy8L6TIJ39RMrNb1Fs7BADKc0Vo5bQLKytqIQXmuJrxtD81hxt/739uNERaBEKK5Ik7qjP6FVytIK01QU7w=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-2.0.0.tgz_1493884792228_0.924768059514463"}, "directories": {}, "publish_time": 1493884792477, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493884792477, "_cnpmcore_publish_time": "2021-12-13T14:42:28.835Z"}, "1.2.0": {"name": "minipass", "version": "1.2.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "e133c4120ae4ab18acc2cd468bd5668f499054cd", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.2.0", "_shasum": "1f4af9c2803921d3035de8aa19443aa5860fea7c", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "1f4af9c2803921d3035de8aa19443aa5860fea7c", "size": 5498, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.2.0.tgz", "integrity": "sha512-XA57AfZj07foIQ6hKuI6KC/+gL5DxbEKm9r1Pn2mJP2cxXQdOOuiM++etKT8M9dO6AyuGJcnMYIlnb+gwOJRGQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.2.0.tgz_1493525670102_0.469406632008031"}, "directories": {}, "publish_time": 1493525670434, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493525670434, "_cnpmcore_publish_time": "2021-12-13T14:42:29.649Z"}, "1.1.11": {"name": "minipass", "version": "1.1.11", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ce4800e752946d0dd11b4779123da0d9f5bb6930", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.11", "_shasum": "0780ea6f54a04253e680eace2602302fc123af53", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "0780ea6f54a04253e680eace2602302fc123af53", "size": 4772, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.11.tgz", "integrity": "sha512-KZ93E4uya83zC53wbYjLP3YB9gIt+WW5pfpROmYJsnnPm9QeNzZces2cwRoQbkHc/IYz4pi7UTz5Z+Xj85Vf0Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.11.tgz_1493519761127_0.9377385785337538"}, "directories": {}, "publish_time": 1493519761406, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493519761406, "_cnpmcore_publish_time": "2021-12-13T14:42:30.494Z"}, "1.1.10": {"name": "minipass", "version": "1.1.10", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "939c3e089af81de898e715151b11ac4cdb3f2887", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.10", "_shasum": "cb0f3810d3380435597763069693b1c72f219eb2", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "cb0f3810d3380435597763069693b1c72f219eb2", "size": 25942, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.10.tgz", "integrity": "sha512-3nLEdSnN220giZ2CrXxq5arjdfoNBEnUYs5GH6/tavWLPhxYgPRQZIPx/ivlPwPA5yemQ0BjQFsJWsbTWdg/VA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-1.1.10.tgz_1493340043913_0.4495376900304109"}, "directories": {}, "publish_time": 1493340045977, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493340045977, "_cnpmcore_publish_time": "2021-12-13T14:42:31.301Z"}, "1.1.9": {"name": "minipass", "version": "1.1.9", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "374dee67e86ca47b79573ed70a642ab20ae50853", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.9", "_shasum": "5fc44fce0b7f1a1f0c27418ec2fa4127f31bb1dc", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "5fc44fce0b7f1a1f0c27418ec2fa4127f31bb1dc", "size": 25822, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.9.tgz", "integrity": "sha512-VAK8wAzHddkF+F/reqmFaoms7S5lueIm1oJyimFdKHjgvhnxv6s10u1Oa5yiMC3e4o8ER/34TtmhOG/Ey/6k3g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.9.tgz_1492830145534_0.7150215269066393"}, "directories": {}, "publish_time": 1492830145768, "_hasShrinkwrap": false, "_cnpm_publish_time": 1492830145768, "_cnpmcore_publish_time": "2021-12-13T14:42:32.318Z"}, "1.1.8": {"name": "minipass", "version": "1.1.8", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "cfc08d331585f233a0a513a15f9eb0804cdc814c", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.8", "_shasum": "70f2d1ec93c9f11613b6150529e9e1571e562b5c", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "70f2d1ec93c9f11613b6150529e9e1571e562b5c", "size": 25742, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.8.tgz", "integrity": "sha512-289//bRHb97eux2cDy7I5v0P+G+mx77V8JHbxeDmzXeiNqTkdex35yU5E3gNqXoQfjaRFxNJ49pu80mWUdlfxg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.8.tgz_1491846777505_0.5472147725522518"}, "directories": {}, "publish_time": 1491846777826, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491846777826, "_cnpmcore_publish_time": "2021-12-13T14:42:33.289Z"}, "1.1.7": {"name": "minipass", "version": "1.1.7", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "88b390364333585399e82bcac5878a56b42b9e90", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.7", "_shasum": "8e800c24c61e49e1424d8c367855509df550f453", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "8e800c24c61e49e1424d8c367855509df550f453", "size": 25250, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.7.tgz", "integrity": "sha512-M8LOXSP2yDpf36Vt5jGL3tn8JFmH4w4Zxhc+4RwUL6QPWAEYh8x1f+EX9Pla/ioM7Vlcv6Rc/zHuXKPtxO13Ug=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.7.tgz_1491250866481_0.022621459094807506"}, "directories": {}, "publish_time": 1491250866787, "_hasShrinkwrap": false, "_cnpm_publish_time": 1491250866787, "_cnpmcore_publish_time": "2021-12-13T14:42:34.169Z"}, "1.1.6": {"name": "minipass", "version": "1.1.6", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "8e1550a1fc92eea4cf4266782daf5c66682e22a9", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.6", "_shasum": "0dde63c08a781ad3f72ee2ba68f1cc23012b8944", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "0dde63c08a781ad3f72ee2ba68f1cc23012b8944", "size": 25127, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.6.tgz", "integrity": "sha512-d43+cD5WXq/h7NbyaOYZlYeBpdwL6yWIoisVh8Bibyxajiy9dg5ernS9oFjfZDsxRWSOWCEZhjvfJEfuazfiWg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.6.tgz_1490770011445_0.6162307255435735"}, "directories": {}, "publish_time": 1490770011682, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490770011682, "_cnpmcore_publish_time": "2021-12-13T14:42:35.055Z"}, "1.1.5": {"name": "minipass", "version": "1.1.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "0232f2585e43297c6b5c15b36453ab6d43183be3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.5", "_shasum": "b7a2293b9cc7a123f4878581b8e139693a39891b", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b7a2293b9cc7a123f4878581b8e139693a39891b", "size": 25017, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.5.tgz", "integrity": "sha512-cAf0ktsJ0t48JgfzmKDNpMqA5coPcOELWVM8u/4BS8MNc+lNrsfiE6Bfd3mgV74V98EcyWKef34Zcd6MAJIQdA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.5.tgz_1490768335439_0.06365093612112105"}, "directories": {}, "publish_time": 1490768335673, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490768335673, "_cnpmcore_publish_time": "2021-12-13T14:42:35.856Z"}, "1.1.4": {"name": "minipass", "version": "1.1.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "b9cb84b174cd7295eada68d071a590d4f0c47642", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.4", "_shasum": "c8801d72c80981c0e29e59d10e18cd15cadc927d", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "c8801d72c80981c0e29e59d10e18cd15cadc927d", "size": 24208, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.4.tgz", "integrity": "sha512-QgpWnmIwn+ifdbFD9G9nUm52aZMsxDd/o5dw9ns6NRSgsOalZhBOa4lMfQizIjHeNmubdsm3+KgUeaLHaFfVPw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.4.tgz_1490750723304_0.5657442165538669"}, "directories": {}, "publish_time": 1490750723560, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490750723560, "_cnpmcore_publish_time": "2021-12-13T14:42:36.993Z"}, "1.1.3": {"name": "minipass", "version": "1.1.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "852e03965a06911ff7f650b9d9d502e1a649c785", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.3", "_shasum": "ed16a0d8585a2e67fe0762352e8cf9110c125427", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ed16a0d8585a2e67fe0762352e8cf9110c125427", "size": 24046, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.3.tgz", "integrity": "sha512-U4tryzwDkt5Pwmbv5qXELpASJkmdW6p9FqZy+bH1BNRvkdf2Y6uA4balj4yv8lEKFqwjxjqnvCZTxun5SLZpwQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-1.1.3.tgz_1490749088000_0.1479788450524211"}, "directories": {}, "publish_time": 1490749089871, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490749089871, "_cnpmcore_publish_time": "2021-12-13T14:42:38.051Z"}, "1.1.2": {"name": "minipass", "version": "1.1.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "a226cbecaec3f354e7d3d3c68918d8d4ad552690", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.2", "_shasum": "83b490887b77c95f3049a1083403e16d3c0be6fa", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "83b490887b77c95f3049a1083403e16d3c0be6fa", "size": 23984, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.2.tgz", "integrity": "sha512-puyFE3xPVp4RHAb0qeMu/LxrTXf8RY21MaV+wruRK04ZMeXMr1Cppizw6QSPzzHN5W9V1JuvMa710Mu/jTF0og=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.2.tgz_1490688898041_0.4253221561666578"}, "directories": {}, "publish_time": 1490688898366, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490688898366, "_cnpmcore_publish_time": "2021-12-13T14:42:39.003Z"}, "1.1.1": {"name": "minipass", "version": "1.1.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "27bbc0c5d090f3deff5836aeb43b20e115e874fd", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.1", "_shasum": "e5c6a6a64562e6038330d2d46694f660e7ebeed1", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e5c6a6a64562e6038330d2d46694f660e7ebeed1", "size": 23883, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.1.tgz", "integrity": "sha512-rgITvLMgK/g/XqwQVjuSSTkmxppX5NgcYLGcgU4oUyar5ZRwezLI78+a4WDEl+IYIrQ7er3L0Cp5wp+mbi27XA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-1.1.1.tgz_1490684781086_0.2492500205989927"}, "directories": {}, "publish_time": 1490684783059, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490684783059, "_cnpmcore_publish_time": "2021-12-13T14:42:40.078Z"}, "1.1.0": {"name": "minipass", "version": "1.1.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "5ff9f9e0394a3f997072e347204b0c2deea48d58", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.0", "_shasum": "a0bac558ae88e5f2652b2dea6b80e77aecdbe5be", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "a0bac558ae88e5f2652b2dea6b80e77aecdbe5be", "size": 22127, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.1.0.tgz", "integrity": "sha512-qsTMszSyFH8/TtMCPWC+++Izcynuz+c7QN3JAIdv7GINQkpRB8Wseod70VwglwDi1lmF5srlU/HaddCNhXg7OQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.0.tgz_1490681589971_0.4406989624258131"}, "directories": {}, "publish_time": 1490681590262, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490681590262, "_cnpmcore_publish_time": "2021-12-13T14:42:41.025Z"}, "1.0.2": {"name": "minipass", "version": "1.0.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "bbd2607d80a77b3b2e880f065552ada47089f6f2", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.0.2", "_shasum": "c2169e3f3ac2119f6e128ad860c679bf9854c0ec", "_from": ".", "_npmVersion": "4.4.2", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "c2169e3f3ac2119f6e128ad860c679bf9854c0ec", "size": 22081, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.0.2.tgz", "integrity": "sha512-sZK3hco/615FTe2WBEoUUEs+jKIWVU3qYR9FPOPFY3ODiLrrBgZNy7pFA0waXPH5K4IYR07ZCesBywg6D6xdfw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.0.2.tgz_1490157604381_0.3243618570268154"}, "directories": {}, "publish_time": 1490157604601, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490157604601, "_cnpmcore_publish_time": "2021-12-13T14:42:41.934Z"}, "1.0.1": {"name": "minipass", "version": "1.0.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "2ee0d11bc4ed89dda4892cffe92718190e4aa4ef", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.0.1", "_shasum": "ca57afde318b5ba9e059a7e64844baa53cdb4034", "_from": ".", "_npmVersion": "4.4.2", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ca57afde318b5ba9e059a7e64844baa53cdb4034", "size": 21937, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.0.1.tgz", "integrity": "sha512-+UewrOmdZYgtPN6SyipT8WZiwl39tPaMPqPdrKtc6tvvjL3YQV/XrP6n6w1u03JEnj5TXAReZWTMnmdZUeo/Nw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.0.1.tgz_1490142376611_0.3365593715570867"}, "directories": {}, "publish_time": 1490142376857, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490142376857, "_cnpmcore_publish_time": "2021-12-13T14:42:42.758Z"}, "1.0.0": {"name": "minipass", "version": "1.0.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "6e91377e7c076f768c2cfd556f365090717d249b", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.0.0", "_shasum": "0ca84f7235109ffe3a8e459cb14d7d6bc3119a67", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "0ca84f7235109ffe3a8e459cb14d7d6bc3119a67", "size": 20349, "noattachment": false, "tarball": "https://registry.npmmirror.com/minipass/-/minipass-1.0.0.tgz", "integrity": "sha512-15/j8qP71EAWMHeZ58xKaBCZwGRTnr/qEs8ZabDTx0KHDLuPn0TAYVuKF9NGiQWfypykZIfMnhW/TszUu99SXg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-1.0.0.tgz_1489450315564_0.20009294734336436"}, "directories": {}, "publish_time": 1489450317420, "_hasShrinkwrap": false, "_cnpm_publish_time": 1489450317420, "_cnpmcore_publish_time": "2021-12-13T14:42:43.560Z"}, "3.2.0": {"name": "minipass", "version": "3.2.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^16.2.0", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "b5962821660cfa51f570fa5c2aeb8373d98b2270", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.2.0", "_nodeVersion": "18.2.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-rosVvUUjMkTW1UoqXVHzNw937MAKv1ewomUBIqYk0IXPYk+LpVCOV1+kBpzAiQrKGjG3Ta81ZNzk/EcL28zABw==", "shasum": "cee61ecabea6634b5706139b0195794103e70af9", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.2.0.tgz", "fileCount": 4, "unpackedSize": 41954, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIN7FPYfROiOXZL37Gg4ijX/iQSFKctot/ZIj5SpLRPQIhANC6f2gQLh1sOh0qZ935JhWO1eHOfCtnEGFnbdOJerUo"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioNr0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXSg/+I/JQcflKVqRbDY3M/HRsmE0MhnMTDC01kcdSRLe2I8g1PoXS\r\nrclPpOyl4X3hxMawy9Sh7HhdiGoY/zEbq8SwnklfpBN3FpuVjNWKcqqTJ7N2\r\nk4S9xT09rFfa9ey8w2dfJ/TPp01+Ou5bKHNE5qgGwXhoqC3JRhncPkQ6IpfY\r\nunNQE1JyWBXqb0kgc/c1C3X7LIJFCtGI5L1XtNq3BsmlLeRvKSDJ3WYcGvrN\r\nx8oLVhdDA464x4sodRLq5KFN02sOlljKyE8NDJv7ZLc1G1guT0sdigfuqmNR\r\nR9G8LAyvrLPImjK+GIg68tVmC522ZES4tLT4o7WMpLgXWuAvSaTmI9f3wus8\r\nuXJmvyiVgrG20p8oEbRO0IQeKaMiJKtHwNj1JxBAToA9SmqPwPZgGkv0ETqT\r\nSkGscrAgrLPXOH/EM/p/p45WcS5h4q9szTD5MlPTlTGEc0dK1KEDu1+TYIND\r\nI+ay2WdHVm1Tc47oh+2D+AzFnGZxwhm7qfZ/lgbsRxvPcbb1jI1Xf/XvqdOA\r\nLbmAbZZwgxy2jU5TaWi0DMQRptCBC7ofj1nB7tyrqZmHlGztE1vxU0eFsoYn\r\nm4ity+ozp8nN7Fy+GrgaWivxyRpUeagkkuOGQB1ghJOjCLH3hk2JdpsSYh1L\r\nzZoB7YSWr6FkSulUdXDwmJ+1kwANURSMyWQ=\r\n=6AK4\r\n-----END PGP SIGNATURE-----\r\n", "size": 13479}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.2.0_1654708980407_0.8034251288290672"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-08T17:58:27.476Z"}, "3.2.1": {"name": "minipass", "version": "3.2.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^16.2.0", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readmeFilename": "README.md", "gitHead": "547db2981c1c301c9552f3158ccad13c1a106cfc", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.2.1", "_nodeVersion": "18.2.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-v5cqJP4WxUVXYXhOOdPiOZEDoF7omSpLivw2GMCL1v/j+xh886bPXKh6SzyA6sa45e4NRQ46IRBEkAazvb6I6A==", "shasum": "12ac0ab289be638db0ad8887b28413b773355c13", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.2.1.tgz", "fileCount": 4, "unpackedSize": 42013, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwL9Wd5Nga9fJ1MuLHwazQSiIpFAiB1OmKiG2ojWbGrAIhAON3wsp54NrJpTDBIKHD82DfxXRGvfs9v/C/2eHYET/B"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio5CWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYzg/+JnHdrJEaOlLAWBd4U/+MNb25emW45tM2psHG4SxxfW1V8m9f\r\njOMQOdfU53n0x55rF7CvFK5Xuqt/F5zA+V3DDgOa3y5uVsw8OdpaGUhFvJtW\r\nw8sIrEmZvpHi4JhC5pWAfsNSJLSEuAl7fWbNlv3IadLqRIvPTqRJqreLgvXU\r\ntpVeitWn00WPIHInqE2vqQI/bdZ3GGXorHCWWGBRWU+7jklXTHYyOCJmPbqZ\r\nuoLQnpABp1LbvEmFtNH66psoZLZHD2Hjxj0qgEHt+94dj+sLWlKylt7b689q\r\nDOCBXfHp2PDRvCgRyY9Gq3n2HcPns/VSdygdVnWek6ROqgGwHDMpKk/1gXUF\r\ndZuQIJXgW8GUsDayr0nxfixTmM0BduJ3YGJ0mVIxu3tHbTPumQ6S/y32WDcf\r\nFK7lCIAhsxiOMiX/1Flkgl279Bhua64Pm7586MqRXuG4uZ39vuVgoYWlfsYf\r\nM3S1sfUprwXa8puLU5YBCDTTWKinRKgxskQ3K0cQPSnz2iZ3MAdPXc9V8Mig\r\nU1B3yTuIHxbeIsfIUHp3zAotJKaea+TxgB7eKx1xJh7HvfjH/n5sjV3CoefR\r\nsGCEyhB/PFw26lSCxYX7urIQAQL4vU48aX8Wi0cczyM+P9gzetrDtk0OVGpb\r\nhyiS0nBnulWmrQRe6jxD83Ik/pRzySq71KE=\r\n=+fQZ\r\n-----END PGP SIGNATURE-----\r\n", "size": 13485}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.2.1_1654886549880_0.8924112603311831"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-10T19:09:23.023Z"}, "3.3.0": {"name": "minipass", "version": "3.3.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "readmeFilename": "README.md", "gitHead": "80662a0ddf9e3795ec0e4d773aa2c9f34bc0dbd1", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.0", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-rIjEM0fvMW1i+txLUOo+ZwoW+cB1dJrhy62iM9ptwhYSaZ7yoHtkO3m+Wpq9kYO/pPKOK6MuziXawNC7OyJcjA==", "shasum": "9bb11578125d483c45c8debd14f2a1be8ea82bab", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.3.0.tgz", "fileCount": 4, "unpackedSize": 43834, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIErJ8wrEDbauQxWgisK7yOMI7w3mivel2mbUevqr/FC5AiBeWQhwU91QwetdvuEJE09lZKFnRKBUy7I3xfOHIdKcNw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJir9hiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmof5A//fEYxjEECGBP0+R3fAMm5PlZptMEs/l1Wd1cVdI0nxPycf1OP\r\nEucE1f6Gus2Z1Oqoa39QqjqVqcf6eA8Mf2ncrJUWrKsWFIaB0tGonp5T7vzz\r\ner4nDXnCl4/g36lwH9zCjUC+83hw50Tt+CEpMujINJ/g+HRMJPDFErfMv1Eu\r\nWNc3Bx5dHGNinmowdpQbxLt9CFnfjvyzhzsaKqQPNdhfA6HOB6vnnNJsktFB\r\nPWBs9LXgWawecux20QCHiBQfMD1QO/XKVjuZh6C7+ycbf0PKSm/RGfMwoUvG\r\npwT36QQZGG3mBQ3sxj7V/n7Q1pXppg5Ne/crqQsDcV4ANB66weIltIdFQouM\r\n6D8STYUyLqX+PMEILTCKSiZXRZSm7caoPr4Q3LrI5lqnFQLQiw+w+H548ZUv\r\nVLdjNBNED5IPyJsWoGoK+kMOPe9OQaYWNl3j5zJGbfh622q/05QiT1bVcBam\r\nd6mgvuMNl8bMNyRBP/DCI1jUo3ywM+JayDRpRwYm8kU8EGmXF/GXxYXM8k88\r\nTtzSgu0TUn3Dvqr0pD43oN34VWuwQORJR4M7vmOP9SJ/t6uI6dL1GL+CEeUF\r\nM7NUECaMM16goKlA45a+Q7xQNOVjwAzjRtIkMwe5mZu4TgKQM0JZdEmnGRuu\r\nXbjJ8Q2EUVKRbR84ox2hol/EJelc0y5SuDI=\r\n=sKBO\r\n-----END PGP SIGNATURE-----\r\n", "size": 14061}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.0_1655691362014_0.542472729959655"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-20T02:16:08.598Z"}, "3.3.1": {"name": "minipass", "version": "3.3.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "readmeFilename": "README.md", "gitHead": "d65917f5e5f592f0a9454b057f69c6abcff974ec", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.1", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-BwHdcCb8ouar1yk+z3Nxu2SQvDcE3yrrrzDkOzavvbdlPFY+DC5wngUMZkkg+QtNIupbdUO4hgB24ySi8WJ1gg==", "shasum": "8959f676c7ed669334a2db4d8dd980c2c6d8e55c", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.3.1.tgz", "fileCount": 5, "unpackedSize": 47890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICzrXaTHR/Wn7M2U/OKdaRnRwLxpQZKRUy5gTx+h7FR2AiEA0I470Kd98O4nSPoAgLix4W7p5n4N+rlGefkHekQM2FQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJir+DaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8whAAheh6EnaaB3jdj6W09QaakNiHsMCf3Lw8FOczkCXMpR30IP6X\r\nYcgwsYvj3ewi7LJbyDlqVChrE7aPpSfRHSg7zX+HaVJzsADYtppka/bHuAUn\r\nc2B6u6PVgwTI6OHQdeSTfXcZ/quP+XRRNlBKWz8FHrLpgsX4Etp2AX2iT7xm\r\nJoLAak5LvW6fgyYeTy82COxAvaL+LjEFICZw4rUxWXNSgBChnVPS3TmboaI2\r\nEoD/46+l3aKalA14kSJ6MTDoUxG8NcCAIHmE51V/Wm1Rh77CX0TqbOr2HFRz\r\nV865+m+YeLjjecn+YSvbrnHTrWTNTD49d8DLhfJuvZdakEcjd1v7BTPAk8Rt\r\n22ZacFaopqOx34DO4h3DK7/4T/vigM0ybBJtspFsAqKPSH+i15U0DoFAeaZV\r\nmgFkoixu/IQWOK3DfTi4UkOgqC5J0qSMBwstwWlsVhB4Cu1xcckv3FMP5h76\r\nc9aehYai3GUcjSjSZLJHJ2J4nld1Ve6OpYylfAHb5ZrDqbb19U0tzFo6kReG\r\nnTXXl7WkpBKu5wYzJWTVPPaUiWPsyPYD0vPrpA+JiBoVzTX1sz6/m25/xLyE\r\nM0i27LSWQSW6MMLMCQFAm5ju5Y/B9cLmX/4GrNhdxRfCVX5VPucZV+X8TS64\r\n5em4lweqifLopV7/V+wZVhxsKiLe7rXBYwQ=\r\n=JvGN\r\n-----END PGP SIGNATURE-----\r\n", "size": 15299}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.1_1655693530623_0.9527395009725081"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-20T02:52:15.917Z"}, "3.3.2": {"name": "minipass", "version": "3.3.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "readmeFilename": "README.md", "gitHead": "3802694369561391f1908f93c56420e5a8b1cca5", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.2", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-Z2BWOv2d19zOhU6Roua5LXHzdPkLJsq0REESIf+kJy27EIgCRxRvYcf6Ww0OD8HlYATBzkeXL/0CCt7hrmUe2w==", "shasum": "7be1929b2963b08f889b8426098f9af92e08f279", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.3.2.tgz", "fileCount": 5, "unpackedSize": 47903, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC0Yuz59pk/x7RpVdhCtiXi8F7hA9A72bjI8XU4XvZg8AiBck4mGABOomFFy5BtmobG1DzU+oNL82/YD1tCM1mRU9A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJir+IWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooOA/8CHKqMjL3iHYpp93xSqp4l3g7FdUaQMbRqLhRBKIlxw5RWjEt\r\nSRS/m46PNdEv5V4ajclIXJGv7/RmXzLvLM2WRvlGEHwldMFX40q/ZONruoUy\r\nt+R6O6QCbxFCjC+wTxPTajFdLPq5ORslHpCKM4TddVx/YGGsRfwTwO5BbLwH\r\ni9l1+RSvyYvE0IczWS/ni125dIbu3PRXZtyNeINqK091XoWXYmK0T5makWYV\r\nR3MdDl9U04NUhenSdPNHO7Fof6arTUJ7xKEmdXYre6pYiDJfdJBUqqTh5l9s\r\n967HIEyU7a5Bh6b0hOelOMJc3MQvVX3wAPMeuq0LO+OuejIWcwhEkZDkqLsK\r\nCVeFQzh6ZbivoOh6cI1pC4mOteYio9GPR7t96QYETsUxH0qX7mULfgIljqIu\r\nGwz8D06RXjqiklMLJoZ5v/oPHRG4AZOS97qpb6Pv17rMGtBnTE129bllbm+M\r\n8Dql8oJkmsifRvSLDCD9n0xQVUJ4CsVOkL5wMRF+PHV2YBp/5SyRTtXlETli\r\nEwP1nP6Sy0RZn6N2GcFdcIiNzywsBja0TwYrblCdYsWktkwvOy9K0C9DLdqz\r\n3G9jTX/hGAiFmExEho1deock8/RQAmQoxQtwZtoHjrH8izM+gVX8JFEfeG0W\r\nNyXneV1Ta+BxgP9XlQWRMj9bzaxIjMVjmag=\r\n=jPo0\r\n-----END PGP SIGNATURE-----\r\n", "size": 15305}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.2_1655693845957_0.25637305163514035"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-20T02:57:36.686Z"}, "3.3.3": {"name": "minipass", "version": "3.3.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "readmeFilename": "README.md", "gitHead": "af6d2aeaa9254f547675f82cbde18aebf0126960", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.3", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-N0BOsdFAlNRfmwMhjAsLVWOk7Ljmeb39iqFlsV1At+jqRhSUP9yeof8FyJu4imaJiSUp8vQebWD/guZwGQC8iA==", "shasum": "fd1f0e6c06449c10dadda72618b59c00f3d6378d", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.3.3.tgz", "fileCount": 5, "unpackedSize": 47855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDDnWccgb0rXc1WUKSijySM+RbciGs9osa0I1+D4Re0uAiB+uyxoBvm3dZqczxAy6H0ggOZS+HIUwvmIy2CekThijQ=="}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJir+vKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQGA/+M2HBrPmabDpxxbQRwN+faLEBOT2YgtvogcS15bQIvg2v/isl\r\nzFGeVqs8NiHD4VJHCxAdZyrmQaLmdwNp0tcX09UVEDz9V/66RgpvdYaKSriV\r\nbfnHzMYKxaRDQln+KPS1mgLWNy2B3MfC71h5hSx2JmOSSdeyxR9iXPcyXU3K\r\nn0jniIsRDQszOKSesPl70gUqEg9KRKkLdPJAZZy1NFDPVYDJJGeUgakm+3NY\r\nPuBM29ZTevNy1nDRfpqqrID2mB2KlFGpZw+24fOl/fosss/5wuMrQdxbSPEK\r\n9nEVGIU/nd8IRo+BETj1/aCTLAq2LLMuvN+kF0yS/ztFmrFgvx5MX8I9nONr\r\ntqBppMzDk3348YiBCzWg3e/7M41hLG0yVDd7lP8jbiGsFwxhGnkxH5fYItbq\r\nmiOArnOfLLmFjIqgymmlO6t9W0fm+GVcqtuB3REU7h+S1gPYGU4cX7MxongU\r\nsRlfu36yjp6VWRPn8EosXt7eh1NdkbQw5lF2gNATqH4r/FwPJPm6kvSZnkpZ\r\nxYm3G7M1ev3rxINUWf9rhiwQ1KRSMbxF7BTMQU/DTUvect8pR2GZYxWZ7Bsu\r\nnv0c4isV4Yw7Gmas9SndFOAni2KSDAerOH5VgFIZkh3JY6DryDtXmVPNVZeJ\r\n/Ed36yZV6uAJjO8H+w4inVh0DK+N4dxNBCs=\r\n=3hUS\r\n-----END PGP SIGNATURE-----\r\n", "size": 15290}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.3_1655696330262_0.271118571703173"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-20T03:47:46.202Z"}, "3.3.4": {"name": "minipass", "version": "3.3.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "readmeFilename": "README.md", "gitHead": "66a65348ec33823db3f8dc90e5a60348eb2da600", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.4", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-I9WPbWHCGu8W+6k1ZiGpPu0GkoKBeorkfKNuAFBNS1HNFJvke82sxvI5bzcCNpWPorkOO5QQ+zomzzwRxejXiw==", "shasum": "ca99f95dd77c43c7a76bf51e6d200025eee0ffae", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.3.4.tgz", "fileCount": 5, "unpackedSize": 47841, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzu8dyxvuvoBZRJx0TYoui0pa24MCiVPTC0YZRxgORxgIgPhSmUVHj8eGzHddEJkX8CQPyekQeCJ+aU2NLk/2P/Do="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiu0vYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzNg/+KkwAdyjrPW06Rxy4FMxyxup2vUBa8ZvlltxErvbH7OhuysHj\r\nrI+M5BF1oN5N8j8IFi2vORLxR7RyX6ZRN60ou7RtOHTJqUAlIPXfIrXcBVV+\r\ngdAunwjogPLhGfUP6zmcKKuqmqceE5r+dJaJnQrYDq3G81bDjKuyxuMGswvm\r\nZa2Y6AjvJqVsrRhPCRsVexQdFYQGx2gdRhX4VouteU9ZZusg4nDb97G7lBG/\r\n7+ojAhH3uraOqiwH2+QsBto0QXhhXDsNoKVk7Mgtd9m3znwScf6K03g9yiyn\r\nPsld7GhYXLfjIIz+KP9wS2HhvzRtYJX33HazEjcDYh1mOzSz2YqpXktarWky\r\nEfRz5maSx9qKtzg0FaBQCteb/6r1fTTFtjk00lT3GzUw2Bd7b1hNfgtI5gCl\r\nP3DbYo2Ss/8vwUzWv2/RZyWvD+qTylH+KKKwyggphP5JuFAK9i6X0amhQHME\r\nMRUvWvoV6ApOQMjJAl1MPD9mvEzt66xcINHD3OjqMB3JnQF8NuYsiP7DaYKv\r\n9EwI5H5LQ7/FXOuN+0l/FXk3Ey/AeqdRTxV05f2TmZJEEudPLQGFg1wQBeSq\r\ng0HudVSKT/mTpE88e/fj7NJtQl4G1dInERH/S1vt4tNmHIkvKYeu20T/xlpt\r\nWP49BuClFU4jnlb1lEDSQvDm4u0OHuLJLB8=\r\n=P8AF\r\n-----END PGP SIGNATURE-----\r\n", "size": 15288}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.4_1656441815769_0.8491380440336691"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-28T18:46:42.565Z"}, "3.3.5": {"name": "minipass", "version": "3.3.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "readmeFilename": "README.md", "gitHead": "e5b768d2b89c5a5be776362e913e35a6707c6df7", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.5", "_nodeVersion": "18.4.0", "_npmVersion": "8.13.2", "dist": {"integrity": "sha512-rQ/p+KfKBkeNwo04U15i+hOwoVBVmekmm/HcfTkTN2t9pbQKCMm4eN5gFeqgrrSp/kH/7BYYhTIHOxGqzbBPaA==", "shasum": "6da7e53a48db8a856eeb9153d85b230a2119e819", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.3.5.tgz", "fileCount": 5, "unpackedSize": 48119, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/RkRbZq9sY4+Nkv3kzsjmiaF2sDHe/zB0801c2MNepQIhAK6wIUFsrwKnS2H6Gy6X7LfbGK/KXEbkrb7sw1wV2DlW"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3cZjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0bw//T4iPy194D7TcF+SrVH1fDKGKluOt7ZJpUgumIfU6Rh8p1aeM\r\npYN1m9q42Z4C6/id4MTg5Ehx2bw9VgZHoOHMknbaVt+l9kreQSdXr+xGC1tg\r\nnDh0Ots4T8moMLSyLe8ejBtaSGwmSjTZ48Gg3cWrCdH3x2yWzsinsRYx6cUC\r\ncx3sA2dWkevRvO0jr4bpGwCzDL33NReyJAuTcKypJKtkcvyhkrY2bZ5sJIFO\r\nVbr3ORHRk6W0w0msm2th8mpbP1vr7+34QTYJAmHahyRxs/kmz6QiJU8lKR2M\r\nGOjjMFixOmbCvechYkAy/thZvdX4kmnSEZRkEUSodmC0CWjxxgyr6WGWQkel\r\nfekv8X418+ZvrwxmzVF2kO+6Y03EOVBwNE+W9W7IwxwS4DeFCOVZlZDZYgd3\r\nB1MiMnujS34sod1a+RbwQ5ohYt8WVVj2y9ZEVH5FsFMeb0l4Wgq/RbFd+ebV\r\nWuPkrUg8Bfuv+/1/53/kzyRUWxBlhtNP579kOSn+NM0tgF3RN7pkzkai3fsc\r\neIJtxL3Zsbya9sAdwIAwwmRNsgt6ciCAH5ue9ypypw4pPRhsrE3DhFOFU0Fy\r\n/+OWUiB7IZ/iQJRCyI1sUR8i2rmzo3QdMRMav7xJ9Ya7RoqEUO2PlK1OPmUq\r\npES2Tt115bh3jEZSXzabU2MB4RJCXK2q9tI=\r\n=jmBv\r\n-----END PGP SIGNATURE-----\r\n", "size": 15346}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.5_1658701411484_0.19872163702440693"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-24T22:23:34.914Z"}, "3.3.6": {"name": "minipass", "version": "3.3.6", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "52ab642fa447419dca139ce29fad780dd61a27af", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.6", "_nodeVersion": "18.12.0", "_npmVersion": "9.1.1", "dist": {"integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "shasum": "7bba384db3a1520d18c9c0e5251c3444e95dd94a", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-3.3.6.tgz", "fileCount": 5, "unpackedSize": 48090, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICmszgh/M4TJhGylTbFqjQUzPzClnto3r6OliGcTp3y+AiAiNeWHb8PcbXkrt2SUIzL/A8WTAjcux3k70LkTdjdkIQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjgHTIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoj8w//fGj3vpiV9bJwZssFywHgmSLaZkdfFJ0hyuXS8M5+8mSYkQEs\r\nWx3vT+Z1f6Q4/UB0ZaFFlSOlE29hTjfTBoRp7yTH42ImYgnqJh9jlBnQuiB9\r\nbcGpEEvLm+xzNjqSryFPJ0AaZApeufHjFMW5aDgiFsTFH37BB4r/WB5WYUsi\r\nzVMPQWvdMMY8zKENQFxRYmOEgARHq7InF8F/YXkxIOwafGSReo90k8DFiIo9\r\ncK2gMR/TieF0NW51Ji1WjgJJlz2PeSfNveufKVgaMm7psECm0gpVWuu159io\r\nX8xQZhX8pd8grOx9UTL70Eas6010MPeoNKERFxDvVdQ+pE3At34SXluKd6+b\r\nKGT+xvrFruyQkajHRrUOY3Xto8+D48T2pj2BYyA+djYRkodwXVkKrJQMVog+\r\n8STrMhYF3SmVpLJT8etQ5dHDmkDbM4xQg5wxrAuzTiw63yOfZK3/YDVfcRmA\r\nRrqTjc5u29vpZxiSHCajI6ODDACNQa7m2TdE0j1LvgCHb0CD7heqSrXVdS2M\r\nUp8oH27tnYQEq11C711ULFPs+47ArnHDgNRjseOe1VhLWtNGBGuXIa7TJZj9\r\n5mHNO+13DDXtfQRiTrz9jRQgUUime8LAum3Z39v/BytWBSfVS93F7vVFwmef\r\nu+hUditRmZHNTHHRs93XAtdlz3GrdsGnFlw=\r\n=S0p7\r\n-----END PGP SIGNATURE-----\r\n", "size": 15335}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.6_1669362888281_0.589549775531871"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-25T07:56:15.085Z"}, "4.0.0": {"name": "minipass", "version": "4.0.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "94124ea6c999e9f7ff76551950ff1ed79431151f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.0.0", "_nodeVersion": "18.12.0", "_npmVersion": "9.1.1", "dist": {"integrity": "sha512-g2Uuh2jEKoht+zvO6vJqXmYpflPqzRBT+Th2h01DKh5z7wbY/AZ2gCQ78cP70YoHPyFdY30YBV5WxgLOEwOykw==", "shasum": "7cebb0f9fa7d56f0c5b17853cbe28838a8dbbd3b", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.0.0.tgz", "fileCount": 5, "unpackedSize": 48333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPDIJ4ateOm0BMJL2Wn4F/jna49gMZpJhOPoupYQbMGAiBza1iyVp25ZRg+Uzm86RTlcA4kjBBDY5fU6ymQLa6ayg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjgqyaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6hg//XhmGUtjrY3sZIwsT2Gzfg2dDXs9NPfch+PKnn6sxo8jKEtwu\r\nnMC5v5Cmm6EkGJCHfrjO7L/AXA/VeF3rw//SBbginMtSJiXGheeYTOlX+dic\r\nvmbUC88bAfg2SMjzXKIsslwXMzlWQY72lNwpiaUA7dXDor8QB6hOGhwyJv9F\r\ntktuy1Rb9S5pPis96G/cYbDWanrM3D6I8Kd4zpRWk18Ja8X6z0Bu/sBrNBgb\r\ntR23AGZDMvtAnsw6oN5/Yd/0gGzB8m8XX+yoPthcRWfFJlQPHWsFurL6QVoE\r\nJQ+imfIfmvQ6CUtRjVgXa2tT1VgDtPzS/GzuYpxJs/1YJPmx0fcvkqU12VEl\r\njyi045os4td9RuZLcClLPIVM8AiEwsBEZ5Decxtai3sc9zjURa7x+wz7IDxC\r\n78GRs9umpoJ26dKlyb54sS/xDTqdDmJJFQh3JsQOdVBKv0pnkEyTP59gC757\r\nUC6eG0cHi/tD8PCeN45rw5ZzAPeettyMCYQIyxgnlNGaGjRKoZigcHWhNOfo\r\no80xSGll6Cte+D776rCrk1tTnlo4OPWkvTGI0QIZVZgYiHz9F6RGn53Xp1Gb\r\nw6wR+hZpZ6D7d885ynCqtPkVa3TMbm0yWMm3+UFWHiZtDf+sF/KsiQbEipJt\r\nLPmFNGnT7n/PO0jXahoZDChTAFVKCIAJB/8=\r\n=Snz0\r\n-----END PGP SIGNATURE-----\r\n", "size": 15415}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.0.0_1669508250476_0.8615478666925138"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-27T00:17:35.647Z"}, "4.0.1": {"name": "minipass", "version": "4.0.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "7c89949841a2a7ee24909f5775a1fcdd5a7a4e22", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.0.1", "_nodeVersion": "19.4.0", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-V9esFpNbK0arbN3fm2sxDKqMYgIp7XtVdE4Esj+PE4Qaaxdg1wIw48ITQIOn1sc8xXSmUviVL3cyjMqPlrVkiA==", "shasum": "2b9408c6e81bb8b338d600fb3685e375a370a057", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.0.1.tgz", "fileCount": 5, "unpackedSize": 48335, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAZDcR+xEG3rSE4EcV3rr3Uge2bsL57LwypcV31nFUhcAiEAzEa98YRV1TDbnz4SoG/wfLt9TqoPKHEcEDARJHh5SbA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1+HlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiLw//WMpEs9CNL2DOb1A+/8ZjlHPVhseqIj/oCIADRmLfNeJgiX8i\r\nB7nlQRoEbivCeCNgfKivjJUn0UF8BoiapXCbwmhLsjH2WXpYGUmKqSwKVdwN\r\ncUHTqUmCirRhPJvjHTAroC4zgDYCvq/iXE9EB86jqqyZAyp0j7Ivuqi/w8Yw\r\nEkIfQdwuRVjkQtT/VzSS6xjFG1pf10uaVyk4dgxnSwm3LfuTxxe+vIGJhEj5\r\nso/ypytE+o8TxrN46AwaFIaQZ7Obg6o9bo6zgCMd3RGnDNR2pS8TLlkr0fhH\r\nyHyNPOxdi12snlhS7yiPa+rg8zfoKOAdNqmbLHKM3UNd1rQHoWOOj/P63AKD\r\n3MdBXQ5PU0EnB5cxhFA3uxSR68dTikk4wRuqKYdu9XHVZF/ayTDJMSAW81/u\r\ne+FfkkZ20/v9lGYg612Dw21uwyevyZ2wV9OAwdObiNgKAOWOkgjRkdnMAa+n\r\nU+oPFfcpjN3FchXwhklhJnbrqM7fSFTzTFyrvwvfY1a21NbHQ8vq006FYpzZ\r\nx2UtyA6hMyAiof10LB1FaImI/aneSMHviEGCCSyJnJeFrd40zezg4dUqBEG6\r\n7QZJMKKNfk/126mm7AitqnZw8GK89tPVTlLHEHCQeviRLShslxdAnCxQ9c+Q\r\n8H/8+N5cSfBShc5qlsS8ie5pfWklgBIJR70=\r\n=Yo6j\r\n-----END PGP SIGNATURE-----\r\n", "size": 15429}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.0.1_1675092453781_0.36220475255847173"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-30T15:27:33.931Z", "publish_time": 1675092453931}, "4.0.2": {"name": "minipass", "version": "4.0.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "c3ecced436ad9c884e45220454fc17df0db38daa", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.0.2", "_nodeVersion": "18.14.0", "_npmVersion": "9.3.1", "dist": {"integrity": "sha512-4Hbzei7ZyBp+1aw0874YWpKOubZd/jc53/XU+gkYry1QV+VvrbO8icLM5CUtm4F0hyXn85DXYKEMIS26gitD3A==", "shasum": "26fc3364d5ea6cb971c6e5259eac67a0887510d1", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.0.2.tgz", "fileCount": 5, "unpackedSize": 48947, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBZhwn6V5NxPoFs5Mhjk6dBbE7b0GAdDLScEioXXns7kAiAdn3DcGINaKKGacvq8ZLx4LowS9dcrOU7sTDYVDT2p+Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3+wlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp29hAAgbb5Gic3Ed10yyL5vdRlLoIYW7Bv5YEm62rk5t6qeI4/TUHK\r\n6arbnNTipx6AmnJmGtca4oFxHVlOtNJBlPLwEjLU5JLjYEYgyM4kQT3q7get\r\nfl5waybCilOyZ/HPHkpGfTDqgjLiU5F6eIEvBvES7CR/2k+kjos1/qeoII0w\r\ndDEQ8EXv3CJaoFvIIf95t2P1xiPw/+tmeOEimQrdHBGvIYHyMKd8Q6GFT79n\r\nTTzrmLV0MidebxFRj8HY+Hs5tIw8oycOI7LsGv9XXG0sNYTJsuiO1g6jyeW9\r\n5/ol9Rc8n+p0dkppoLVZiuF3j8cMnKp9JwL5hBLeBvHRFfsX6v18OHRh/88q\r\nwRY4zy7sFE37PtVrM9mMSp/JQU6MNkE5F8ec9vKLpWh+GjYe3rz3QTMhXDsL\r\nxfxA3sAwrud5QOv0Pgdp/4Z46zcNHB+zwzkjMAX8zS67wSxOPCAgBFSUk5b8\r\nu2FCxstlGU1n9eoTBNzcQxjrcBvfm3gdcAqipLQ8c/QiHouUMziVaYOTSRsK\r\nWZAc6sdiHrmDA+F2Tjx75tm9M5YvQ1xcX27Zwot4t/HbzywERWnN+JU3G5MN\r\nKxTCWhPTRo9nbn6ME+OZb1rdEgvmQ8MAnLRlBaIPtRNtIoAQ9Sranv8Gcp8B\r\nxflpiCad5lkXyHeWKx3IDfLDwLJWkBuB4qs=\r\n=UuTi\r\n-----END PGP SIGNATURE-----\r\n", "size": 15601}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.0.2_1675619365690_0.8194046899768483"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-05T17:49:25.951Z", "publish_time": 1675619365951}, "4.0.3": {"name": "minipass", "version": "4.0.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "d9099429d9d1ee28e753e608d497a7f5d2300490", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.0.3", "_nodeVersion": "18.14.0", "_npmVersion": "9.3.1", "dist": {"integrity": "sha512-OW2r4sQ0sI+z5ckEt5c1Tri4xTgZwYDxpE54eqWlQloQRoWtXjqt9udJ5Z4dSv7wK+nfFI7FRXyCpBSft+gpFw==", "shasum": "00bfbaf1e16e35e804f4aa31a7c1f6b8d9f0ee72", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.0.3.tgz", "fileCount": 5, "unpackedSize": 48953, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICuPLljEchGhhVxX6VvWTJTvKbW5KXUlkA8WNO2bWAKmAiBG0cQiMfTNcIIu8LbBK5C2p9jRfHvRhDi+CJyB9uJPhg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4sl1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokEQ/6A/sOsPN6S5RHsKB3mHglMo7YsoDg8ftBjLK+T8+kP8ttIJGV\r\n53t2ffJfCv9vcG6ZXAUxVZtFMGe6w0CcAnD88KtHHRh0zW/Y5wxtzPHH8hWn\r\nR567H+lZ/U3ZWRpx+2ugFUzFKZOs/87i8fVwpVRk3UI/W6PmjwRsCKdp1a62\r\neeCEF9/uSb1oQkO2wXywIiBxIH86iNNAhdiBhwjZ5eMXAWCT1i6RK2E7Nc9x\r\nTvP2tFmsdcSXqnReV25hJK6bVDNRCfSZoMiZv62Rh5Hpi79Nz4CZjv+bccPH\r\nbmfLeZIS/mrIGMScerFcn0To3DvmCw3nJON4V6bKPAXYXw5u6neYvm5AfFyl\r\nikRA4/Iv8rVj15b4NJAcizTvc8mu+HkCLRhp4jVFqIXtWbJohLrt8nryVNyf\r\nTZh0I/HZpV6EbA3bVYGe9LSnQy9+mgvVPcvq8SAqOg3Q115VDcKIif9S810w\r\nQrS5f8jT3140MhGybfAcdeaKmB9vjdYd1HjYEaNWE+t2szVUMcO0AY+vMM/R\r\nNOz1LADZmeEAGgHtRUIlDNeAdC9K77lBz1ipSHL8t/OutDuyKLTAs3ZFLRPv\r\n7+sgcefub1DBE3PiZUza9oKos3nr222ZfQgnmwaeUFcbk4D9jEW3XPAnxEjH\r\nh6DOKURcbXfvZR49XzdKQsoyBFT5ID4gJJ8=\r\n=3JHs\r\n-----END PGP SIGNATURE-----\r\n", "size": 15607}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.0.3_1675807092995_0.583231517344913"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-07T21:58:13.198Z", "publish_time": 1675807093198}, "4.1.0": {"name": "minipass", "version": "4.1.0", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.mjs", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "1eb74cf5efce01c555afe55a2c21afc09ea75a1a", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.1.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.4.2", "dist": {"integrity": "sha512-WHxk07h4cIpCokP6qy2YPIJLk/5ELgqW7c0uxzhZIDXteqRd2YevFi0+ZjTPQ2Y8Z6w1FUW3HA9QzJ1UdaC8rA==", "shasum": "572e5b64ffee9ff8abe7a48d01906160c1ce9e08", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.1.0.tgz", "fileCount": 6, "unpackedSize": 67225, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH/KGTwKe8FG9WWwmLwbIkrdMgol9h1Li0zRqSwadTmaAiBt8aaCQbh/9v1PX7Qf205kj7oWZyVgBxe5nlG7oFQx/g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9Zf4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZjQ/+NO3xOwRpMX3seYQ1GbgezXXPDi1drzI5yny8/UZUwAQKiidH\r\nyQdvpDMPo5PfFL14igIACa/coDHPaKEWvOhCIJcwkHU6lOJxB5EH4qe75qhN\r\n5C1V0q6C00LnhL+z4trYa+ag8JenxYFkDqENTt8HQB0Frpvh24JabA8j6zC/\r\ne1kj9dSEQ/LUB0VyKa/8iWjzND0/32qrvwwPfAuk/1/sPI7RgXM2JCEnKj+M\r\nuJZ7z9r+aRfGC49yg8e/FojPtbYldsaAPLH9n6OTh9hPOqpWxxeYb2iO/mlD\r\nCLw5BgNaXRgwIhL9Q0n0Ct0CwBjBJjtdFzs4XfjOfBJfz62R27QNHAftm5ER\r\n9KFvyLiPGUr7k/k3eX8VI0kUT4/ypWVRSc8E71OmnDWZ6Acrub4Tl/uX3+o5\r\nGfifq0cdfasV+akAyABKpHwpFoQ3fy+FAP0JmYLfMqMNSPlXLmn9vu6zsYLG\r\nwZwMu/sd7k7VlZgF5jkjoe0AzFyCbgx8NQdE5LLVFoORbfCtX5evvIA22SlH\r\nb/DnYqVuJ7jiRuzC81YyNSMpYTgSVQ3iREYp7OpCtVCuaqdC1VyDdRSerboJ\r\nkh9BH5gYzHrpYep51pcAP+WezheauS9B7jZwaKrJZQuENu2VhX2OFTT/xjor\r\nH/cSNS4pJT3tIk+lw7jsxsJy33qSh3zsSXo=\r\n=5VqH\r\n-----END PGP SIGNATURE-----\r\n", "size": 20010}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.1.0_1677039608570_0.43776795906501187"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-22T04:20:08.792Z", "publish_time": 1677039608792}, "4.2.0": {"name": "minipass", "version": "4.2.0", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.mjs", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "0b77950c7b87a41e58aee0983429d23f51e77e08", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.4.2", "dist": {"integrity": "sha512-ExlilAIS7zJ2EWUMaVXi14H+FnZ18kr17kFkGemMqBx6jW0m8P6XfqwYVPEG53ENlgsED+alVP9ZxC3JzkK23Q==", "shasum": "4bf124d8c87c14e99846f9a27c3219d956998c0e", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.2.0.tgz", "fileCount": 6, "unpackedSize": 70644, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+ith3Wb0Fla7RTCtzg4mHVqY3fv91EsuixRqAJP3ZtgIgTqIA3hEJF/kVyS90PQMTFUNKzOSiejTiBxUF2OzGAME="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9apFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8dw/+PotqTr1yCIPjJXclHAKNmOX7wjS+3XQWE18L+e6HA91jcClz\r\nWXCunj0/uHIft32pjVNfZk0/0vEg0bBKW44Oecj8zLKLSs7c4lQk+FgOZR3n\r\nt3qD/cbMzsr8PbEansYx0lRVJd+kzOdyX3k4Ii5PnC8QhRNDHYa9NcbMvt4y\r\n6ENSE6G10EOiNrVnTgb4x44OWPodAsX1gyxJUbfzpC7uSXnsI81Heqielvta\r\nIimjmSONMR3TUgTePvLExTflvSkceMfS1Zw13PyA83oP5U06GxU3lwjb7MLj\r\nBh7uwh3fbWYa0d+/vv8aIyF9g4hCy7kw8DYTbggCSqdmHmzT9F7EjgDWZyh+\r\n2yAxJUWxSDzKZjIdRDeql6z7cRAc+IKyCNx91PTnrPZB0ccXH3rYtASFsiDi\r\nuUgqvfvlgHJrKtzhnL6IfR45oPV7DXrs5p8fLlz3Y1vZMQbwdCQX2CU70cIK\r\ngunyu66l5BJvBJdsv7HuQybIBS25NYPaDyTzp0HeXakX72UpNonbzr4LBlSw\r\nXker6fYIbXzPijk5JignsLTEVFK6a+OuiJvmGv2lhXEcvYpTq6u0VBCiNJpN\r\nfAxnLBHTcLe/fys4BsxMeh+TyblSSSsFxZwxzayWyJ3AevSKhODRipJhqKJL\r\nRVrtIxeYq/fqdDfGmEqnuatxHSrRit4B5cI=\r\n=RNqi\r\n-----END PGP SIGNATURE-----\r\n", "size": 20667}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.0_1677044293765_0.8526737717005475"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-22T05:38:13.922Z", "publish_time": 1677044293922}, "4.2.1": {"name": "minipass", "version": "4.2.1", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "ef1f48933b9260b2664a942b1bc7ca9d8b640991", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.1", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-KS4CHIsDfOZetnT+u6fwxyFADXLamtkPxkGScmmtTW//MlRrImV+LtbmbJpLQ86Hw7km/utbfEfndhGBrfwvlA==", "shasum": "084031141113657662d40f66f9c2329036892128", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.2.1.tgz", "fileCount": 6, "unpackedSize": 70577, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFeaYcULYtuv7cQ9joXv6myvvjTDFETiJRdMuuXNaDPCAiB2wr4mluIM1BJXWUlVz9M8dJaot3n2X9MrDXMnetIlVw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+EG6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCVw/6A+6sbk+no1ptWXJpysHf9qTbVuactq4RWE4vIy+2eTZA8c4Z\r\nv99ZPI0F6d7gDEzZf7LQB0GomddxusWERGNwwU82DjLunk6WhTOFuHADQ4gB\r\n1PCh3b/+M2tcqwkCE1Vyyq3QzF0rsMeIsOTkxI1LVqBD6ZVl5XdIlJguRwMg\r\nvn6EtffS/QQfla+UJP1829QhTw/UtAbqI6eeng33r1e0sEbtuUiVg64tGhKs\r\njfysQIg1QeCVCMgjagPgaZrE7aOTPr9P2+nkoFr8ILRaXU+hQeMDZchZVxH7\r\nL5wq4ZmRZqmupcN0CMCYHhhoaYxGmv1k5kGAibkIQXVvDU9U6K/B1MEzF2Q/\r\nKyBWIQHLwae6aednzAwjeyojOWWD8rJV3yqdPgGo7ZXrQhSbtRTwGVykM4ow\r\n+qcpUtSnzaH+j9jjxzEOTMTL5SIPDir32JM6K1Y93MJqUAzoVnpBRJKPx+Rn\r\nGddXKjnvOPIESrv385cYhwqmAlZRyLF0p2z3Q63YCkUzSNe3gMmeXgAjim2e\r\nrl4s6IH9TTYHCgLbN/kLo1J0BDYpKlbOxKBJ9Ul6ORJrlEOFmpRlCG3qOABL\r\nObCRvMQ/qGcLfi+veGiBLZRuQMCxTKrBBTbZDAPsydKVxL5DBEmu3AsrOhmg\r\no3ADCGOnlMlp/ydERkytmvjamT/o9xC3epc=\r\n=mHh5\r\n-----END PGP SIGNATURE-----\r\n", "size": 20637}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.1_1677214138419_0.3678700408590496"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-24T04:48:58.574Z", "publish_time": 1677214138574}, "4.2.2": {"name": "minipass", "version": "4.2.2", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "16837bea69197dcfb3b3534d0747d062e17ac473", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.2", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-CK/S6dX/gmBq0YF4FGPlvsv5O0WH6YVwEc28xJiTNjkstRGPYA4S7lfrGTqE61YydECWC68pYSUt9aP8yC70PQ==", "shasum": "4f35a099272b23d0cfe26c0dcea2a7b772aeb809", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.2.2.tgz", "fileCount": 6, "unpackedSize": 68732, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDUO0VdLd8d7DGGWDh4+jDOex28jlMnPpRizgQIWFuFBAiBVlM+7HbltBTE+gJHHaqj6XX5mxvqOUw5JMg0PuNfXUw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+wfQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmospRAAmDESxQiok1WWGiZxXkQT6lniXQGvgFXntqTsVD3FE6P3EJtR\r\nc5E1faThPP4TjcPt/Turw1vVGtyKD0zvY+48I1LDVLqWkB/YA6VPvPNoiK8Z\r\n04gTR43uowXMhH1BsYr7iF1hXMqKNzZfby74shVEzSMoXCeBLj2DinY86pNL\r\n6JiZWWSTzu4Vz/5UM5EqLqydJqNSAUtGo+hfKub7IAAFRbmB+0n2ZxllpUNc\r\nMHUdbY/d+cSTeMSihGaFSg32JAREi6RlfSvcimuMdmr/2TFOLKl13WR1AefQ\r\nx7LY6qmxdaThqLLKsyxopoLL6s2i8jjZ5UDjXpMdLn5QBY6Si8rdfpeeqBOZ\r\ntZ2EhqC/0aR0QDEW5/sYWUvBbY0w3TBWUiKtCG0tVPgI+yjhhfb6cpGeLgc5\r\n2HvZcONI3w0CZSIdfCWoLJJYMrOwhQk/xkueta4hZOOsvy6rVQDle4zmKtlG\r\n7FzK3MnWXkTP7kqbBVa8RZoe2F2Q9CZ2wR8owQS9X1EyDAszmk8uEwrjphVo\r\nk5waArRofMaEkMQrrxG4tE57twsQrtPv9TIozKE0QKqrShhUG2Lz0KFJXVOC\r\nf2BDF3v6Ei2O3uxR0I4/EfWfpAqlVuX8za8w7UP8CVGG+4PdgzrZGPaYROvw\r\nGbE3Rj5KBqtZ2UeGdEPAVv1kZ30T9UXcuq0=\r\n=nuI7\r\n-----END PGP SIGNATURE-----\r\n", "size": 20459}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.2_1677395920672_0.6444427884982831"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-26T07:18:40.877Z", "publish_time": 1677395920877}, "4.2.3": {"name": "minipass", "version": "4.2.3", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "3d63d733bdcb1eed77ed7947977d85643287af60", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.3", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-iYSQ8k1lVu79HJdl033DCtz73XFBUO1cg45QS8nalhkvV3KE+G3LYj/2NBZjjILpqhshq8rfJfshJJnjdeJl8A==", "shasum": "5ee9b1ad67dfc916ce7cec86e5260fc61da64376", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.2.3.tgz", "fileCount": 6, "unpackedSize": 68804, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBEUjFBAqopAIfx87mj57/qwjDfGRStD+vBVFES65xOHAiEA5LRdmtmL0heguC/qiFS7FISNj7oK4Z7Yts/4jSoUgLI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+w4qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG7g//QxfYGSXkuh8drWcrBeRPUJ263oe8NVtVkUhEQVgLbKt79Bfa\r\nxev5FPMskX6HgAcZ1hiHLz/TZtuNm15zDK6u3tyGjCOVTJbZM+usgO8YOh5t\r\nxoJZyFSsdwvrLnCkRtvdrcixoZkZ4OKRXOoZxhzZbw1SPkRdbf7wHGh/92cv\r\nqvmNrylD4bQQK45XpK8KVjCGNeuILXdl0elaWUG5RzTf07qfLc6jUpxQe8+n\r\naKHMcjemY+7Fk4aKFdBI2VzlMGGQ/MY+cc+pIFQ0atrOZloam1zgHuUDxl8y\r\nrdaHFiKpLVOCYut5eSDDrVHRpqIwfTx5a/7lALZzQQp+fxdEJzN0vaaQDd6G\r\nSsO6+xvrZZzixzq3XTtrIyXOuJOLzY1hvhf+fnGpxdJMAR4Ce11qfqgOdxD5\r\nEoLlJMdaP278dowNjq3UaZ4EnmQOzzag9nL14ooYgUBK9xqh4llpA2SNeP+H\r\nrK9xqLNun17s2FwsIzIydKhwkqUo5lBUligVCDvUqrOEY1I6IqYVCrOdUObG\r\ng4UsYHYlciIrOwZwAZ1JGtjSrEG7t3AjXn9YHyTBtlRmEF9CKRsQFrcZ8Qfg\r\nQEroxoJaxzIT1LiSuDDaIRZ0p2eGnWAI5l4zBUgqNRfZNkMTbS5N2l7wcqRs\r\n+naqXvKZo0LS7pOqT1oaT6fbxSNp2TLCi74=\r\n=RcLq\r\n-----END PGP SIGNATURE-----\r\n", "size": 20474}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.3_1677397546259_0.8851282963016653"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-26T07:45:46.439Z", "publish_time": 1677397546439}, "4.2.4": {"name": "minipass", "version": "4.2.4", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "8a5e3921179c0ca58683678858f9496d30bddbcc", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.4", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-lwycX3cBMTvcejsHITUgYj6Gy6A7Nh4Q6h9NP4sTHY1ccJlC7yKzDmiShEHsJ16Jf1nKGDEaiHxiltsJEvk0nQ==", "shasum": "7d0d97434b6a19f59c5c3221698b48bbf3b2cd06", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.2.4.tgz", "fileCount": 6, "unpackedSize": 68932, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAtSJW37qXfjz8QSrskkBSajAq7oVZErjBvgBnzcpCg6AiEAx9ZwWEE7uzzqn13qc1XvP4mABHeEDpbWB2Vzol0w6Xc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+w9kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5TBAAgfKwb7vEVSu4YQETeXlBI6Z2kwgNybfPYVYDZLxlJSB1y2hx\r\nZMmePBgm5aCFc/aYm4jaVV+8Oej74vWawR7JY9UhwdNp1K8bwp2C3xSvtAGw\r\nQHHiA8qp82u7mflg0c4CAw3p+oJq7S8WH8hacCEvURTEuWnPGqFV7EBfvLwE\r\nFIcwm/vEJUEymbQ6CJZ1OWbiO2V4wmMFyDFBrOrJWhhJnZLY/kVweaOoja9y\r\nWpZrJnHwe2K26HXuhj39ZSEe6ZwLDxxAnNPT3rIAj5G4cPi8WA26hoA2mzLU\r\nZcFObomkG5DlMEcLx4UBczl6Seyc/r3KLjII9oNvd1BwVlSGfWSll+MxCCy9\r\njNqqnuB83uvfe+SGY7Gb6kNJkiLHJVTmUeoU8pUV84Qk06GLVCHbDWLOdgUD\r\n8bfx5rqL0/Y7xl8/Qy4UXMR2eUfHI39ZpzD4Ga8Of7LQmpK9xu1V3GTQn6Po\r\ny/9HTDNKAkksKHKlXwoammGKmvi31iWOcy2ZnZ6DxyW431gSsTdSen8snpZ8\r\nZetDgiofd02PuhG2cOsa0n29+dI058yzEYCwUTJC+mBUXQaPd1uaPx35HYyg\r\nCYTWkUROX3InkWjDNMNKUESC8R3no58DEAiHgfhA8xsRfjQ9MUcFxpn+eJjp\r\nhUD0Fh+SbxTlptbBg48HmoH+DtbHe5LRqbU=\r\n=m/1i\r\n-----END PGP SIGNATURE-----\r\n", "size": 20491}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.4_1677397860387_0.5042330666279724"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-02-26T07:51:00.557Z", "publish_time": 1677397860557}, "4.2.5": {"name": "minipass", "version": "4.2.5", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "e3b98a071233fd4f4054ab7437f1d9a3bab71ce3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.5", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-+yQl7SX3bIT83Lhb4BVorMAHVuqsskxRdlmO9kTpyukp8vsm2Sn/fUOV9xlnG8/a5JsypJzap21lz/y3FBMJ8Q==", "shasum": "9e0e5256f1e3513f8c34691dd68549e85b2c8ceb", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.2.5.tgz", "fileCount": 6, "unpackedSize": 69380, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHwHdkGTpCWWnPtaHirVQkfvMHVwUMg/F4HpBAIf2ot/AiEAr7CHZjPHwHx3xh7yfmUBDFk9Pbvpol9zQhUxF8ChYv4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDNYiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUVQ/+PRTy7bfR4mObrOvtgacZLKRo8jCZot085aJrva9inVOip35C\r\n1263ejxV2/ljcNA2MnQecqCvwRbwXu7CfWPNYFDFxu/SJg1GOYjtf5pAz1qy\r\nc+/P7YKrNloDevbfZ3JjXheEeGb+cMigtbiPGvFpQx3irzKZVgPiRowrkIrW\r\n7A/jeFtnsVCfQRKJqx4fA1yuN55MeI8896VPZnoXngXQAH5nrQjhGbY7EDj8\r\nqOrcYECZ2y3ANb4fnqv3e7/U+g5T0yewQwWwC49rzBXQGYLiJYZXk/eXJ8wo\r\n0rPl0YhyyNyCifPVnXkWUMyInk/OXJMd5jGPuAVf7HVwCXN71LfK1+AjjX+3\r\nVgBeBZbc5SqL8M9ug7sMUFhhwU9kgXqJ12+djZQvEc3ph36K6Cu5WyX5IHAw\r\nWHnflo7/hx8EaIj+OxxCm7v48QbDNE/PISyqOyfwf4c+hkwgUvl3K2/nHOJ/\r\n88Xk2jV+1+T0MVcoVfhIieeGf+5BokvXeElBm/kIT2wVX3aJHs7338d7NhfI\r\nlNy12gtxMi0Qub7ndWqQoxd7dRWseXLwOe4U080uaKr9/WC7zMT3tZaWvSOa\r\nSL3LDmNe/v3BcXamg3iAaws4NVofo4YFegQZgNqMKS9J4FtDk2X0P/nP1AdZ\r\nmrKFrSJ2j3UdsMboR2uvQaE1v+3BtQe8dF0=\r\n=5Lps\r\n-----END PGP SIGNATURE-----\r\n", "size": 20537}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.5_1678562849994_0.47969510867769394"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-11T19:27:30.190Z", "publish_time": 1678562850190}, "4.2.6": {"name": "minipass", "version": "4.2.6", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "4f37bc74563af7f5c2ff131648112e5ed9e1d72d", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.6", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.3", "dist": {"integrity": "sha512-99el+wjSnfeQqHTP/mKgFh15BXIk347QvNZ//yBGDbkYtyS4ZeOoIuAf16v+R4oCmuaYavIopwW0KEQEuUMn2w==", "shasum": "43c56f3890214d24b5d63f70e8ee97b2fb632df3", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.2.6.tgz", "fileCount": 6, "unpackedSize": 69463, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDS+nXq7/96s4C4hYXXasyrAtt6oTKrWcMc/x7bEM6TdQIgcl0gHxUaBHgBp3f5OZkW+Mh7+TCGjulAYMiUD0/Nyjk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMydbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLZhAAoLDXZ2dMjUXVHeVQC1xHHBd+kkkGmTboAiF58ixSxySCvM2J\r\nY5iCZKA3FYNk4nS+3dMh5BqeddkBBPsOfy1EGvu/rnxGGVES7eEK3wvdMsae\r\nptgEdLS1+9Y/cgGWXtZ6y2uR5xPr8/hH/SxO6QcXp+FYdIn7kHhToNeDO7JS\r\nVsHJagbrMtPjFW0LF/E17hKSEoafS89zPlB+ZHkudHhiyn3I32rfSHJetkOs\r\nZXFObmCAYDNAhkS98rbNnzQtLcRXqlm7pQevEQw85cjOwClvMNKUrd3k+aKU\r\nfw97VBEg6UDVpXspNPuNKfJjB3W2NYgM7ypX71TH9NH1BzBclLCijAxy4J2z\r\nFNkh23+3a4psvCHuTQ7kMdC/2WvNmQpiySoRusZ77Gp5TICtI80M6gSd1POp\r\nk4NyjC1VO4AizacovzhDT1rxc4t0VyxuHrNEIR7tGnkOfd7bl2vukJFqXXcv\r\nQEbFXLSmEtq9Mg+zPK0gFETE/lZe0b11i2XroZfBH7/hjNrgp15RQPumJosA\r\ng+8WspRk287rKfmZeGBVBnWPn1VzZ0+m2biTWXSd34B8J03hVAUTuKW9N07D\r\nb0/5vZgjtS3sxrUxm67BtbWtswORcc7r6NS+Wh9ue0fghsqkhmtKITrebc4s\r\nSXeg4NuqswUtbxmY0ujiH0BJi3IkyAhRs9U=\r\n=JsQ+\r\n-----END PGP SIGNATURE-----\r\n", "size": 20566}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.6_1681074010848_0.5148388380615421"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-09T21:00:10.988Z", "publish_time": 1681074010988}, "4.2.7": {"name": "minipass", "version": "4.2.7", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "815a2efb09546d48f98c0817d0656aa7b6e83e99", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.7", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.3", "dist": {"integrity": "sha512-ScVIgqHcXRMyfflqHmEW0bm8z8rb5McHyOY3ewX9JBgZaR77G7nxq9L/mtV96/QbAAwtbCAHVVLzD1kkyfFQEw==", "shasum": "14c6fc0dcab54d9c4dd64b2b7032fef04efec218", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.2.7.tgz", "fileCount": 7, "unpackedSize": 69783, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC12j7PnxfPVGl/5busvBG2ZyudGoodfyAtbl4Pw7XznAIgLbcLXa2G/4htnCJRV8F37Pa2FAykycqfP92Xcnx5+c8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMy0DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYVA/+IFj1xozLOCch1Y8HIZoKp5fHNXkz+sIVp4ZEjAQFPDo0hRka\r\nME9RghrlecqcGKTgum3ysxuj2dFVnCDMs8lXyRwkqrzeWTxIWAlqQubtzYfV\r\nNklLpucy7gBllNjf0YNAWJVFqF5arxb/VKccgzdqTE9WzER7ugjSDQH56TaM\r\nZMQRoh0r/EOdzvSPIT6hnvQLfbjGKHgWGM6Ly3Twnax/CkcyjGWFGnSCnSJA\r\nPWJ4aw4QqAn5yDFZDid6KGVS/NPPVxAfGjR1ddpWg5n3+DbtPbjn//l2FBna\r\naWnqerkrUNjwQmcwX5l751+saYAqJ70H95TMFAjO3GLoK8ghkvUM0dKL02j1\r\nnmwMwT1roih8TQ+NEc1S8jRB3Plc0PCYonUgFmwx73IacBebgT6t90ZmMURy\r\nTct1jZQXQ+GSqhg8TDZrnZ9D4Ym/m6xR6DU2PdkhAQuDJ1zDdnu0TDIAGcqy\r\nY3qu58Py7mWfTEpiO37xQ2+BCbAJBW6uACbliuqFlbMMBxjajjzrRR4BYwdn\r\nn2RBPVLep/IRD/RHuX8En15aU1eGJzuFvu7ds7tEkDt8dRdBhgq41PytFzul\r\nvcOiLwO8iYZ7WxO6vWlQ/BI5JDvL8v4SkmXcafVHdnYF9DoT5n4ANLBWtJys\r\nQVB0zLxCMdwtInNvfrtuLGrV23JSXrfiTJ4=\r\n=PqeM\r\n-----END PGP SIGNATURE-----\r\n", "size": 20663}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.7_1681075459725_0.7272249058668407"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-09T21:24:19.901Z", "publish_time": 1681075459901}, "5.0.0": {"name": "minipass", "version": "5.0.0", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "3066600b811753bd9c85831a8ecd5c6ca248f2aa", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@5.0.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.3", "dist": {"integrity": "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==", "shasum": "3e9788ffb90b694a5d0ec94479a45b5d8738133d", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-5.0.0.tgz", "fileCount": 6, "unpackedSize": 69475, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGG9d/n9j/SiJwfKLK58G4W+KXXHLm/aIadRa/mJxsnFAiAMhpcZ9419dsyFB+8n8uTkyWOkalIM/OsqBG4PrBM2lA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMzQCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbsQ//brt9nqdgncqvwVSLdtzC7rp53x+Q7gIYj9/fPCRFwJvFG98V\r\nRwhWWlSbFcbHyZDb8qCTK63KLICw4d6ha3SjKV3ZNX/+Xd7Qr4HbCp+ELyRs\r\n1+8EyQQdtcJcMAYnUBhG/wV3+h9gLgc8AY47iFColB5GJahS38Ua7lcQ5vU3\r\ncGmt1oiL5pnCAe8r/d4OT4k/0LIidvw7NDmXEOM6mknIFNyag4HPnDpczm0y\r\nbcBVHDGq7WMvysCjsgJOjxNb/CApOHx33z5qqbdmAKQLDVADjfQ/9gBPWOZ7\r\n5hSeSA30oyzZZR/vAlWKkK32hqIAuNq+/w+73s6flQB90Mccqia7F7Ahq43S\r\nioWBJ1frTjhlrPX0jmItGDDJLRRKS7kNm1TObWj5B4WWdJGPlWaNJi07VJkM\r\nJ5ubvfbPGH3zQZt9i5dK60UPBbB/Pm5ZtSUE5TugTuxM+hXTSxzJHgtrNzwU\r\nFFkxVaEBeFgfuUXiSnwiDI4bpCRmB/RByyMcIKT1LhO7V+3KKl2s1nd3+5V4\r\njoa6r1d33dG7eUacD+ijDrLzQzCj/2rwm9cgccsG2XdWjbudkaZBfgySQ+ka\r\niGsmYH1MfCpGzrKr+DgtQn0crIMh/0UdTmbh3NFdm24C2PeiskcfggYaj/Xn\r\nR2ROnLecGp3aTTn6W5PCRkeZW2GgCQDcR+Y=\r\n=ZOod\r\n-----END PGP SIGNATURE-----\r\n", "size": 20536}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_5.0.0_1681077250203_0.5777324393037977"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-09T21:54:10.390Z", "publish_time": 1681077250390}, "4.2.8": {"name": "minipass", "version": "4.2.8", "publishConfig": {"tag": "legacy-v4"}, "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "readmeFilename": "README.md", "gitHead": "ceb8d68b7658039349a2bfc92ae95ae4ca822fbe", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.8", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==", "shasum": "f0010f64393ecfc1d1ccb5f582bcaf45f48e1a3a", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-4.2.8.tgz", "fileCount": 6, "unpackedSize": 69429, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpE6oaNgiTQhK21Njy7Dq1bzo4GlD0kS/cIVykQ4zDUwIhALabUYU3enRuRmJ06G6fnsDjNqXWBj7aGjekgS7TXmoi"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNYQLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvgQ//V6X8coKNs46JL6BLV9xWSxsPego+U4HCpMt/UOq2JA/s0idq\r\ns4aXF7UpAjlgqaM+Tml08AVQUDa6anV75UOYR6OOxqOAWSjaOkQ4QINP5b/N\r\n9UuDA+jYYky2K/Ty40eSILoF/46wr1VijOjppf6f2IMOBhsOwt5RSIgC6wr4\r\naj+Swv64OVOnfk+oYuR82EagGM2ZbNkZzRppfX8B8wBpGOhjpgYgeR8iGdox\r\nDhT6BGBwLu5ZeSLZJWAARcrpS7d3Ubey0MqLBqhljojScFPIGxtHM6SweJAz\r\nHMGCIEEThPVHHS+20QWrQ4bcp0pb6x7lz9J/To9foJ9zjPEQ/5LhF75NOhU+\r\nmhDUEeU7iBPXlH5zTsDIm592Ms+5ftIqebtI0hXDdzxwFG+Hf3IzRNTvRL5C\r\nJDbCj8sleQ3Ac1LVLcFJHpDV2HWzw9W4fghSbJdRKycdZ1EMw3jYm57rdOye\r\ncnQExDG5fjyFP7QDtF/qXUFZGCDvUhdG2bSAgTtkk5qVaZHf04e9CDobyMB1\r\nzeuolaCcsrycSzMwTA+C3HZivQkYB5v0kFTmt2zNcJgU5SWsMR/NOaqpX5og\r\nPi6ZTvu04ZwqM4pHyUQeFVshy+x9EkBQrm+7VvsUMuYJMieXUJYMPhbNPqxC\r\nJa5gFdpzNyq2L2dwRT+Uodqun1RBrbRndG8=\r\n=cRYA\r\n-----END PGP SIGNATURE-----\r\n", "size": 20560}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.8_1681228810954_0.6015072400739621"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-04-11T16:00:11.170Z", "publish_time": 1681228811170}, "6.0.0": {"name": "minipass", "version": "6.0.0", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=16 || 14 >=14.17"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "c2aa0b2196a5d622fdf08032753dd424f53a101b", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@6.0.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-mvD5U4pUen1aWcjTxUgdoMg6PB98dcV0obc/OiPzls79++IpgNoO+MCbOHRlKfWIOvjIjmjUygjZmSStP7B0Og==", "shasum": "3b000c121dd32da5dc56156381dc322b4f2ffaa0", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-6.0.0.tgz", "fileCount": 6, "unpackedSize": 72054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBlc+AX/YsyxCraefscb5Zyo3yJ45JWMhjGa81amMxsCAiBecj+rCxbucN4yPHgCBu10jzz20rpqiAV5jckNlrVe/Q=="}], "size": 21371}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_6.0.0_1684125669158_0.33282480463221487"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-15T04:41:09.361Z", "publish_time": 1684125669361, "_source_registry_name": "default"}, "6.0.1": {"name": "minipass", "version": "6.0.1", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=16 || 14 >=14.17"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "6125ceeddb721eb3f69749d4e71953adb48ad37d", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@6.0.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-Tenl5QPpgozlOGBiveNYHg2f6y+VpxsXRoIHFUVJuSmTonXRAE6q9b8Mp/O46762/2AlW4ye4Nkyvx0fgWDKbw==", "shasum": "315417c259cb32a1b2fc530c0e7f55c901a60a6d", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-6.0.1.tgz", "fileCount": 6, "unpackedSize": 72294, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNruBUjX8fCLndo48IoRdCsdBpuk6i73yxW0hPNGROUgIhANso7bY70CzUm0WqgRSJ6F7UhPDPbUyL5N/u9LeCOk2+"}], "size": 21435}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_6.0.1_1684189575979_0.959831283268048"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-15T22:26:16.226Z", "publish_time": 1684189576226, "_source_registry_name": "default"}, "6.0.2": {"name": "minipass", "version": "6.0.2", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=16 || 14 >=14.17"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "15ab07809dab7a278f9c79027cf25a3a150b770a", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@6.0.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-MzWSV5nYVT7mVyWCwn2o7JH13w2TBRmmSqSRCKzTw+lmft9X4z+3wjvs06Tzijo5z4W/kahUCDpRXTF+ZrmF/w==", "shasum": "542844b6c4ce95b202c0995b0a471f1229de4c81", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-6.0.2.tgz", "fileCount": 6, "unpackedSize": 72303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICqqstVBSXdNnDS7yIs9sh8GRz/rsanUH1wWdrAFWQLqAiBDt4qJ26FPCCT0NIbAidBwo6o20h7lDsecxPX722wfog=="}], "size": 21443}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_6.0.2_1684358210139_0.12283922254492419"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-17T21:16:50.370Z", "publish_time": 1684358210370, "_source_registry_name": "default"}, "7.0.0": {"name": "minipass", "version": "7.0.0", "description": "minimal implementation of a PassThrough stream", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.1.2", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.6.2", "tap": "^16.3.0", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.3", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "sync-content": "^1.0.2", "through2": "^2.0.3"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "gitHead": "d63abffc8734d679177d2382ac1841caa82349f3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@7.0.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-QWQmFjKDHhfJdyAievi/KRg/S5oZ41a4u/A10YsJpfgXmEtdvKeJFsaLZr+gQas7hpKoCrUUdJ97iwoySrmqHQ==", "shasum": "164051d8c2881b7a47f21d9cb6661dcb8f4121f2", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-7.0.0.tgz", "fileCount": 13, "unpackedSize": 284315, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFm344OpKXsjq3f41vXTkaa67iV+BE4BVB7RAIj3ns98AiEAqX9j++GUzFQy74bCR5dZIsTmMQ8Jc1ByFwxKAMeAHsw="}], "size": 61779}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.0_1688775283609_0.8892298371891081"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-08T00:14:43.854Z", "publish_time": 1688775283854, "_source_registry_name": "default"}, "7.0.1": {"name": "minipass", "version": "7.0.1", "description": "minimal implementation of a PassThrough stream", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.1.2", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.6.2", "tap": "^16.3.0", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.3", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "sync-content": "^1.0.2", "through2": "^2.0.3"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "gitHead": "6baaade6726d1cac656426f89f15de631a56b3d1", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@7.0.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-NQ8MCKimInjVlaIqx51RKJJB7mINVkLTJbsZKmto4UAAOC/CWXES8PGaOgoBZyqoUsUA/U3DToGK7GJkkHbjJw==", "shasum": "dff63464407cd8b83d7f008c0f116fa8c9b77ebf", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-7.0.1.tgz", "fileCount": 13, "unpackedSize": 284357, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClYll+UqcZLUhaw1lE28RwLHQ2FKEDJL5bEIRB5NoytgIgb3QPb1oh9psaeoRSW1zrmbWK6Vga33Wrpv8rSOeo3q0="}], "size": 61851}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.1_1688775799416_0.7448468714465568"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-08T00:23:19.663Z", "publish_time": 1688775799663, "_source_registry_name": "default"}, "7.0.2": {"name": "minipass", "version": "7.0.2", "description": "minimal implementation of a PassThrough stream", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.1.2", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.6.2", "tap": "^16.3.0", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.3", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "sync-content": "^1.0.2", "through2": "^2.0.3"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "gitHead": "b220db67d918c9717911ac5a05d427d2da6074d3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@7.0.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-eL79dXrE1q9dBbDCLg7xfn/vl7MS4F1gvJAgjJrQli/jbQWdUttuVawphqpffoIYfRdq78LHx6GP4bU/EQ2ATA==", "shasum": "58a82b7d81c7010da5bd4b2c0c85ac4b4ec5131e", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-7.0.2.tgz", "fileCount": 13, "unpackedSize": 284773, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7WGcZqFyLsmrEI9b3laL7X+2hs5cFVpEZzNYLTtDPzQIhAP2KIJk5QYSNcLkw20nTWN27iZ5s680dgip2IdkVhwvh"}], "size": 61912}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.2_1689052650121_0.789215871620615"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-11T05:17:30.315Z", "publish_time": 1689052650315, "_source_registry_name": "default"}, "7.0.3": {"name": "minipass", "version": "7.0.3", "description": "minimal implementation of a PassThrough stream", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.1.2", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.6.2", "tap": "^16.3.0", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.3", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "sync-content": "^1.0.2", "through2": "^2.0.3"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "_id": "minipass@7.0.3", "gitHead": "8d95dcac2d3e769bbb8e66d721ce8359a1380d42", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "18.16.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-LhbbwCfz3vsb12j/WkWQPZfKTsgqIe1Nf/ti1pKjYESGLHIVjWU96G9/ljLH4F9mWNVhlQOm0VySdAWzf05dpg==", "shasum": "05ea638da44e475037ed94d1c7efcc76a25e1974", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-7.0.3.tgz", "fileCount": 13, "unpackedSize": 284648, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiYXjXuN2gRxpFynlAU0SXHTZdvKEs7mQvppOHJLx1UQIhAM43cPR9RG9QsHR55/Rii3ob1U8ICeg3BKIhA2kj6e48"}], "size": 61827}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.3_1691868603294_0.8818660773346114"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-12T19:30:03.611Z", "publish_time": 1691868603611, "_source_registry_name": "default"}, "7.0.4": {"name": "minipass", "version": "7.0.4", "description": "minimal implementation of a PassThrough stream", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/end-of-stream": "^1.4.2", "@types/node": "^20.1.2", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "sync-content": "^1.0.2", "tap": "^18.3.0", "through2": "^2.0.3", "tshy": "^1.2.2", "typedoc": "^0.25.1", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "tap": {"include": ["test/*.ts"]}, "_id": "minipass@7.0.4", "gitHead": "c776c8778b25c479c7ea76601197db5c2dfbae8a", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "20.7.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-jYofLM5Dam9279rdkWzqHozUo4ybjdZmCsDHePy5V/PbBcVMiSZR97gmAy45aqi8CK1lG2ECd356FU86avfwUQ==", "shasum": "dbce03740f50a4786ba994c1fb908844d27b038c", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-7.0.4.tgz", "fileCount": 13, "unpackedSize": 284660, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIASWXUFLvLCfZ3HHD76Khv1mUxAUTuge3FJlqEjrhBqxAiBAN/+U3Bp4Nn3HF17swNziVSiS2xfH1VI/BAy0eyE99g=="}], "size": 61792}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.4_1695945513386_0.8044391401477946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-09-28T23:58:33.597Z", "publish_time": 1695945513597, "_source_registry_name": "default"}, "7.1.0": {"name": "minipass", "version": "7.1.0", "description": "minimal implementation of a PassThrough stream", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/end-of-stream": "^1.4.2", "@types/node": "^20.1.2", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^18.3.0", "through2": "^2.0.3", "tshy": "^1.2.2", "typedoc": "^0.25.1", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "tap": {"typecheck": true, "include": ["test/*.ts"]}, "_id": "minipass@7.1.0", "gitHead": "1875e522c0ff22d0f5e51dbd7843423ca74b0c5c", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-oGZRv2OT1lO2UF1zUcwdTb3wqUwI0kBGTgt/T7OdSj6M6N5m3o5uPf0AIW6lVxGGoiWUR7e2AwTE+xiwK8WQig==", "shasum": "b545f84af94e567386770159302ca113469c80b8", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-7.1.0.tgz", "fileCount": 13, "unpackedSize": 284683, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGciFNerqlrToRzSKykz7TuE0HbnvdRIwx4+ejCMrZIWAiEAmCkDWY8SWPzrG6oPyUIk9Xa+fToEey4fGQ3bsMIaFMk="}], "size": 61978}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.1.0_1714788037968_0.17800660359126508"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-04T02:00:38.139Z", "publish_time": 1714788038139, "_source_registry_name": "default"}, "7.1.1": {"name": "minipass", "version": "7.1.1", "description": "minimal implementation of a PassThrough stream", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/end-of-stream": "^1.4.2", "@types/node": "^20.1.2", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^18.3.0", "through2": "^2.0.3", "tshy": "^1.2.2", "typedoc": "^0.25.1", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "tap": {"typecheck": true, "include": ["test/*.ts"]}, "_id": "minipass@7.1.1", "gitHead": "9410c3e3bb5bccb4f11c4f9080c5f4d695f72870", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-UZ7eQ+h8ywIRAW1hIEl2AqdwzJucU/Kp59+8kkZeSvafXhZjul247BvIJjEVFVeON6d7lM46XX1HXCduKAS8VA==", "shasum": "f7f85aff59aa22f110b20e27692465cf3bf89481", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-7.1.1.tgz", "fileCount": 13, "unpackedSize": 284808, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4QuLBeF4qIu67aOHEUzwpIc9W0PeGjmWrlfZSKQedzQIhAOYwmeFu1vsndqyaknodtTmPBk+q/iO2dgi3/+goerTi"}], "size": 62073}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.1.1_1715262566961_0.4945015346384156"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-09T13:49:27.137Z", "publish_time": 1715262567137, "_source_registry_name": "default"}, "7.1.2": {"name": "minipass", "version": "7.1.2", "description": "minimal implementation of a PassThrough stream", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "tshy": {"selfLink": false, "main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/end-of-stream": "^1.4.2", "@types/node": "^20.1.2", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^19.0.0", "through2": "^2.0.3", "tshy": "^1.14.0", "typedoc": "^0.25.1"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "tap": {"typecheck": true, "include": ["test/*.ts"]}, "_id": "minipass@7.1.2", "gitHead": "1fc7b914533c367ac00a982051849add2169f641", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==", "shasum": "93a9626ce5e5e66bd4db86849e7515e92340a707", "tarball": "https://registry.npmmirror.com/minipass/-/minipass-7.1.2.tgz", "fileCount": 13, "unpackedSize": 286202, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBkQJPfSAC23QGJC4cJMYAUXpqqVOq4t69yc/4rkRU3GAiEAl4BdS3v8U/I+g+Na71CrCfplx3P1Z2lvj7NBGdATUR8="}], "size": 62112}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.1.2_1716511340973_0.6336662935701123"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-05-24T00:42:21.149Z", "publish_time": 1716511341149, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "keywords": ["passthrough", "stream"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "_source_registry_name": "default"}