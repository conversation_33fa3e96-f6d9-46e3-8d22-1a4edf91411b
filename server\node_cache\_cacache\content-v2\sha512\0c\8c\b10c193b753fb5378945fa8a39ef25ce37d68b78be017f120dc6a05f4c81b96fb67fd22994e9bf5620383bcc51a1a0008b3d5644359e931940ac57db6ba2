{"_attachments": {}, "_id": "encoding", "_rev": "489-61f144a6b77ea98a748f57a3", "author": {"name": "<PERSON><PERSON>"}, "description": "Convert encodings, uses iconv-lite", "dist-tags": {"latest": "0.1.13"}, "license": "MIT", "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "name": "encoding", "readme": "# Encoding\n\n**encoding** is a simple wrapper around [iconv-lite](https://github.com/ashtuchkin/iconv-lite/) to convert strings from one encoding to another.\n\n[![Build Status](https://secure.travis-ci.org/andris9/encoding.svg)](http://travis-ci.org/andris9/Nodemailer)\n[![npm version](https://badge.fury.io/js/encoding.svg)](http://badge.fury.io/js/encoding)\n\nInitially _encoding_ was a wrapper around _node-iconv_ (main) and _iconv-lite_ (fallback) and was used as the encoding layer for Nodemailer/mailparser. Somehow it also ended up as a dependency for a bunch of other project, none of these actually using _node-iconv_. The loading mechanics caused issues for front-end projects and <PERSON><PERSON>mail<PERSON>/malparser had moved on, so _node-iconv_ was removed.\n\n## Install\n\nInstall through npm\n\n    npm install encoding\n\n## Usage\n\nRequire the module\n\n    var encoding = require(\"encoding\");\n\nConvert with encoding.convert()\n\n    var resultBuffer = encoding.convert(text, toCharset, fromCharset);\n\nWhere\n\n-   **text** is either a Buffer or a String to be converted\n-   **toCharset** is the characterset to convert the string\n-   **fromCharset** (_optional_, defaults to UTF-8) is the source charset\n\nOutput of the conversion is always a Buffer object.\n\nExample\n\n    var result = encoding.convert(\"ÕÄÖÜ\", \"Latin_1\");\n    console.log(result); //<Buffer d5 c4 d6 dc>\n\n## License\n\n**MIT**\n", "time": {"created": "2022-01-26T12:55:02.154Z", "modified": "2023-07-27T20:46:41.530Z", "0.1.13": "2020-07-10T07:02:11.638Z", "0.1.12": "2015-12-23T09:06:30.764Z", "0.1.11": "2014-11-08T19:41:18.146Z", "0.1.10": "2014-10-16T12:37:21.638Z", "0.1.9": "2014-10-13T06:44:03.588Z", "0.1.8": "2014-06-17T12:13:45.482Z", "0.1.7": "2013-09-03T10:45:22.834Z", "0.1.6": "2013-03-23T06:20:47.925Z", "0.1.5": "2012-11-07T09:24:38.001Z", "0.1.4": "2012-09-14T13:13:45.852Z", "0.1.3": "2012-09-04T09:56:33.049Z", "0.1.2": "2012-08-16T08:48:07.540Z", "0.1.1": "2012-08-16T08:36:05.770Z"}, "versions": {"0.1.13": {"name": "encoding", "version": "0.1.13", "description": "Convert encodings, uses iconv-lite", "main": "lib/encoding.js", "scripts": {"test": "nodeunit test"}, "repository": {"type": "git", "url": "git+https://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "^0.6.2"}, "devDependencies": {"nodeunit": "0.11.3"}, "gitHead": "a7554ca9083bab4847705d562d02f0924271cbaa", "bugs": {"url": "https://github.com/andris9/encoding/issues"}, "homepage": "https://github.com/andris9/encoding#readme", "_id": "encoding@0.1.13", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "dist": {"shasum": "56574afdd791f54a8e9b2785c0582a2d26210fa9", "size": 3040, "noattachment": false, "tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A=="}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "_npmUser": {"name": "andris", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/encoding_0.1.13_1594364531517_0.31350544316084505"}, "_hasShrinkwrap": false, "publish_time": 1594364531638, "_cnpm_publish_time": 1594364531638, "_cnpmcore_publish_time": "2021-12-13T12:43:36.803Z"}, "0.1.12": {"name": "encoding", "version": "0.1.12", "description": "Convert encodings, uses iconv by default and fallbacks to iconv-lite if needed", "main": "lib/encoding.js", "scripts": {"test": "nodeunit test"}, "repository": {"type": "git", "url": "git+https://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "~0.4.13"}, "devDependencies": {"iconv": "~2.1.11", "nodeunit": "~0.9.1"}, "gitHead": "91ae950aaa854a119122c27cdbabd8c5585106f7", "bugs": {"url": "https://github.com/andris9/encoding/issues"}, "homepage": "https://github.com/andris9/encoding#readme", "_id": "encoding@0.1.12", "_shasum": "538b66f3ee62cd1ab51ec323829d1f9480c74beb", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "andris", "email": "<EMAIL>"}, "dist": {"shasum": "538b66f3ee62cd1ab51ec323829d1f9480c74beb", "size": 3441, "noattachment": false, "tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.12.tgz", "integrity": "sha512-bl1LAgiQc4ZWr++pNYUdRe/alecaHFeHxIJ/pNciqGdKXghaTCOwKkbKp6ye7pKZGu/GcaSXFk8PBVhgs+dJdA=="}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1450861590764, "_hasShrinkwrap": false, "_cnpm_publish_time": 1450861590764, "_cnpmcore_publish_time": "2021-12-13T12:43:37.081Z"}, "0.1.11": {"name": "encoding", "version": "0.1.11", "description": "Convert encodings, uses iconv by default and fallbacks to iconv-lite if needed", "main": "lib/encoding.js", "scripts": {"test": "nodeunit test"}, "repository": {"type": "git", "url": "https://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "~0.4.4"}, "devDependencies": {"nodeunit": "~0.8.1"}, "gitHead": "b1f9ea063c33c70daa4b66662ef8374117306645", "bugs": {"url": "https://github.com/andris9/encoding/issues"}, "homepage": "https://github.com/andris9/encoding", "_id": "encoding@0.1.11", "_shasum": "52c65ac15aab467f1338451e2615f988eccc0258", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "andris", "email": "<EMAIL>"}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "dist": {"shasum": "52c65ac15aab467f1338451e2615f988eccc0258", "size": 2903, "noattachment": false, "tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.11.tgz", "integrity": "sha512-LIe27CSSmc7XIJ24RkxFKR0ILYbp6dZ/Um/fd9Gho0gGJVxGsSU44FsueFVl+pJV6Vzlmg0CKOLbhaskGjGr8w=="}, "directories": {}, "publish_time": 1415475678146, "_hasShrinkwrap": false, "_cnpm_publish_time": 1415475678146, "_cnpmcore_publish_time": "2021-12-13T12:43:37.375Z"}, "0.1.10": {"name": "encoding", "version": "0.1.10", "description": "Convert encodings, uses iconv by default and fallbacks to iconv-lite if needed", "main": "lib/encoding.js", "scripts": {"test": "nodeunit test"}, "repository": {"type": "git", "url": "https://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "~0.4.4"}, "devDependencies": {"nodeunit": "~0.8.1"}, "gitHead": "2a8f2139370e469435b0b71dc29583ba061376a5", "bugs": {"url": "https://github.com/andris9/encoding/issues"}, "homepage": "https://github.com/andris9/encoding", "_id": "encoding@0.1.10", "_shasum": "4463122033a7e3fdae4e81bf306f675dd8e4612c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "andris", "email": "<EMAIL>"}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "dist": {"shasum": "4463122033a7e3fdae4e81bf306f675dd8e4612c", "size": 2901, "noattachment": false, "tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.10.tgz", "integrity": "sha512-Mk5OWxUft6BQhaDreioXQEeVRkb6TbDpYmJuIRAY3EkSznK3i3u/k+k/UQD8PihDf7548AHV1xB180eZQ9i+Vw=="}, "directories": {}, "publish_time": 1413463041638, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413463041638, "_cnpmcore_publish_time": "2021-12-13T12:43:37.710Z"}, "0.1.9": {"name": "encoding", "version": "0.1.9", "description": "Convert encodings, uses iconv by default and fallbacks to iconv-lite if needed", "main": "lib/encoding.js", "scripts": {"test": "nodeunit test"}, "repository": {"type": "git", "url": "https://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "^0.4.4"}, "devDependencies": {"nodeunit": "~0.8.1"}, "gitHead": "f75db3780405bb979508cd57c318cf605a0580c8", "bugs": {"url": "https://github.com/andris9/encoding/issues"}, "homepage": "https://github.com/andris9/encoding", "_id": "encoding@0.1.9", "_shasum": "0e082880ac477b79714dce4a78c699efcdd99c18", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "andris", "email": "<EMAIL>"}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "dist": {"shasum": "0e082880ac477b79714dce4a78c699efcdd99c18", "size": 2902, "noattachment": false, "tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.9.tgz", "integrity": "sha512-9Skjslf0GXbMD2wJVpSXOezAiyIusW0UZIeyx39lMd+PlNrw5yc36GrfHG6L3QsiER5DsXRmUwXteprtuST8Mg=="}, "directories": {}, "publish_time": 1413182643588, "_hasShrinkwrap": false, "_cnpm_publish_time": 1413182643588, "_cnpmcore_publish_time": "2021-12-13T12:43:38.043Z"}, "0.1.8": {"name": "encoding", "version": "0.1.8", "description": "Convert encodings, uses iconv by default and fallbacks to iconv-lite if needed", "main": "lib/encoding.js", "scripts": {"test": "nodeunit test"}, "repository": {"type": "git", "url": "https://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "~0.4.3"}, "devDependencies": {"nodeunit": "~0.8.1"}, "bugs": {"url": "https://github.com/andris9/encoding/issues"}, "homepage": "https://github.com/andris9/encoding", "_id": "encoding@0.1.8", "dist": {"shasum": "3c48d355f6f4da0545de88c6f2673ccf70df11e7", "size": 2219, "noattachment": false, "tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.8.tgz", "integrity": "sha512-5XtPTUaD3sbLr8Czd5/Ji0Vv6xTQTuxvcLSUqd0NQ4xUyGsDvx9PZUb7f0K5A6Kg8Q2JyAF98qqq3javv9b6qg=="}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "andris", "email": "<EMAIL>"}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1403007225482, "_hasShrinkwrap": false, "_cnpm_publish_time": 1403007225482, "_cnpmcore_publish_time": "2021-12-13T12:43:38.393Z"}, "0.1.7": {"name": "encoding", "version": "0.1.7", "description": "Convert encodings, uses iconv by default and fallbacks to iconv-lite if needed", "main": "index.js", "scripts": {"test": "nodeunit test.js"}, "repository": {"type": "git", "url": "https://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "~0.2.11"}, "devDependencies": {"nodeunit": "~0.8.1"}, "readmeFilename": "README.md", "_id": "encoding@0.1.7", "dist": {"tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.7.tgz", "shasum": "25cc19b34e9225d120c2ea769f9136c91cecc908", "size": 2170, "noattachment": false, "integrity": "sha512-TiG6vj7ii7T/IcqPDdzKwbNMdvfZM4gzLvgvJuJQrRLOFnfYHzYjB2ixkjKJN6Wdoda1QGo6E06a6fbisv/wYg=="}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "andris", "email": "<EMAIL>"}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1378205122834, "_hasShrinkwrap": false, "_cnpm_publish_time": 1378205122834, "_cnpmcore_publish_time": "2021-12-13T12:43:38.722Z"}, "0.1.6": {"name": "encoding", "version": "0.1.6", "description": "Convert encodings, uses iconv by default and fallbacks to iconv-lite if needed", "main": "index.js", "scripts": {"test": "nodeunit test.js"}, "repository": {"type": "git", "url": "git://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "0.2.7"}, "devDependencies": {"nodeunit": "*"}, "readmeFilename": "README.md", "_id": "encoding@0.1.6", "dist": {"tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.6.tgz", "shasum": "fec66b6d1c6b8cc554aa78c05ece35bef11a913f", "size": 2168, "noattachment": false, "integrity": "sha512-t4QcGhz74Tlj1iiko3iVHH26WFj/UhZA+f+h3+5n4gvmNiq3N7fVslKKchHWlS0T6CEdqL858n+KPVJzQJTxNA=="}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "andris", "email": "<EMAIL>"}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1364019647925, "_hasShrinkwrap": false, "_cnpm_publish_time": 1364019647925, "_cnpmcore_publish_time": "2021-12-13T12:43:39.085Z"}, "0.1.5": {"name": "encoding", "version": "0.1.5", "description": "Convert encodings, uses iconv by default and fallbacks to iconv-lite if needed", "main": "index.js", "scripts": {"test": "nodeunit test.js"}, "repository": {"type": "git", "url": "git://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "*"}, "devDependencies": {"nodeunit": "*"}, "_id": "encoding@0.1.5", "dist": {"tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.5.tgz", "shasum": "3900ac48e4eb6b2885efe050fe74883287b7f33e", "size": 2157, "noattachment": false, "integrity": "sha512-zOHkgDbrmPgJbNilpwlh21dy+5qQs+qMp21KAJgnqD4yngHbvPQ9RTecdZH3v+OV3fR4bcofhEyMEf/+hxa5WQ=="}, "_npmVersion": "1.1.61", "_npmUser": {"name": "andris", "email": "<EMAIL>"}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1352280278001, "_hasShrinkwrap": false, "_cnpm_publish_time": 1352280278001, "_cnpmcore_publish_time": "2021-12-13T12:43:39.508Z"}, "0.1.4": {"name": "encoding", "version": "0.1.4", "description": "Remove accents from international characters", "main": "index.js", "scripts": {"test": "nodeunit test.js"}, "repository": {"type": "git", "url": "git://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "*", "iconv": "*"}, "optionalDependencies": {"iconv": "*"}, "devDependencies": {"nodeunit": "*"}, "_id": "encoding@0.1.4", "dist": {"tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.4.tgz", "shasum": "dcd860c75975259d4159c840153d6ccdf82b899e", "size": 1938, "noattachment": false, "integrity": "sha512-xmD95psWYCwGM+jL+Nmh02+ImFyuuq1cbPEk4mIY/R0w3UlKdrlTit+eDRanEclfdHIcC6RqVGkRfTpe++I8Dg=="}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1347628425852, "_hasShrinkwrap": false, "_cnpm_publish_time": 1347628425852, "_cnpmcore_publish_time": "2021-12-13T12:43:39.948Z"}, "0.1.3": {"name": "encoding", "version": "0.1.3", "description": "Remove accents from international characters", "main": "index.js", "scripts": {"test": "nodeunit test.js"}, "repository": {"type": "git", "url": "git://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "*", "iconv": "*"}, "optionalDependencies": {"iconv": "*"}, "devDependencies": {"nodeunit": "*"}, "_id": "encoding@0.1.3", "dist": {"tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.3.tgz", "shasum": "0fdde7af970a729afe948227f174f3f79f524cd7", "size": 1946, "noattachment": false, "integrity": "sha512-x9ziVneSUf0cUdNL/L1z5VJPmQDVS80cKy7yydxa/M0pyc2vUhr6IjhUgFkMVXQ0MUZdqs2bifnRHdIVPi44Ww=="}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1346752593049, "_hasShrinkwrap": false, "_cnpm_publish_time": 1346752593049, "_cnpmcore_publish_time": "2021-12-13T12:43:40.344Z"}, "0.1.2": {"name": "encoding", "version": "0.1.2", "description": "Remove accents from international characters", "main": "index.js", "scripts": {"test": "nodeunit test.js"}, "repository": {"type": "git", "url": "git://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "*", "iconv": "*"}, "optionalDependencies": {"iconv": "*"}, "devDependencies": {"nodeunit": "*"}, "_id": "encoding@0.1.2", "dist": {"tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.2.tgz", "shasum": "6827a60632997ed27001d805dcdd2b925de9a913", "size": 1898, "noattachment": false, "integrity": "sha512-0IwQRh3ur9eTp0H4EA932OO9+aqQxVHCGUcD/tD5G/sv7Z31ioc1MDFYZmjvVVlKY3OJk3D7XzLAg+sTNYMxNA=="}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1345106887540, "_hasShrinkwrap": false, "_cnpm_publish_time": 1345106887540, "_cnpmcore_publish_time": "2021-12-13T12:43:40.748Z"}, "0.1.1": {"name": "encoding", "version": "0.1.1", "description": "Remove accents from international characters", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/andris9/encoding.git"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"iconv-lite": "*", "iconv": "*"}, "optionalDependencies": {"iconv": "*"}, "_id": "encoding@0.1.1", "dist": {"tarball": "https://registry.npmmirror.com/encoding/-/encoding-0.1.1.tgz", "shasum": "f5dfa57009a0f81ce63c966b9cdb4f12a3f14e05", "size": 1492, "noattachment": false, "integrity": "sha512-qDBp7J0yvti519twBf5YT6XMufxg+5HiHq8mVkY3NB5K8fkzF6IeEDOJMe16NO4DZuQM8FGJJQtOBMKO1Xcpig=="}, "maintainers": [{"name": "andris", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1345106165770, "_hasShrinkwrap": false, "_cnpm_publish_time": 1345106165770, "_cnpmcore_publish_time": "2021-12-13T12:43:41.191Z"}}, "bugs": {"url": "https://github.com/andris9/encoding/issues"}, "homepage": "https://github.com/andris9/encoding#readme", "repository": {"type": "git", "url": "git+https://github.com/andris9/encoding.git"}, "_source_registry_name": "default"}