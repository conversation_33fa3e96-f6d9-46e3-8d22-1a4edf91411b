{"_attachments": {}, "_id": "lodash.includes", "_rev": "2926-61f14a52830fd08f52a2cfef", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "description": "The lodash method `_.includes` exported as a module.", "dist-tags": {"latest": "4.3.0"}, "license": "MIT", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "lodash.includes", "readme": "# lodash.includes v4.3.0\n\nThe [lodash](https://lodash.com/) method `_.includes` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.includes\n```\n\nIn Node.js:\n```js\nvar includes = require('lodash.includes');\n```\n\nSee the [documentation](https://lodash.com/docs#includes) or [package source](https://github.com/lodash/lodash/blob/4.3.0-npm-packages/lodash.includes) for more details.\n", "time": {"created": "2022-01-26T13:19:14.326Z", "modified": "2023-08-08T15:59:05.402Z", "4.3.0": "2016-08-13T17:39:40.778Z", "4.2.0": "2016-07-25T14:45:00.479Z", "4.1.3": "2016-05-12T14:10:51.468Z", "4.1.2": "2016-04-03T03:49:25.247Z", "4.1.1": "2016-03-02T05:30:04.435Z", "4.1.0": "2016-02-16T09:35:09.772Z", "4.0.1": "2016-02-03T07:27:47.101Z", "4.0.0": "2016-01-13T11:03:05.454Z", "3.1.3": "2015-06-30T15:19:44.292Z", "3.1.2": "2015-05-19T19:50:44.759Z", "3.1.1": "2015-04-16T16:30:56.911Z", "3.1.0": "2015-03-25T23:34:48.795Z", "3.0.0": "2015-01-26T15:29:09.047Z"}, "versions": {"4.3.0": {"name": "lodash.includes", "version": "4.3.0", "description": "The lodash method `_.includes` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "includes"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@4.3.0", "_shasum": "60bb98a87cb923c68ca1e51325483314849f553f", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "60bb98a87cb923c68ca1e51325483314849f553f", "size": 6191, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.3.0.tgz", "integrity": "sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.includes-4.3.0.tgz_1471109980530_0.7038842691108584"}, "directories": {}, "publish_time": 1471109980778, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471109980778, "_cnpmcore_publish_time": "2021-12-13T18:06:12.319Z"}, "4.2.0": {"name": "lodash.includes", "version": "4.2.0", "description": "The lodash method `_.includes` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "includes"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@4.2.0", "_shasum": "ace38b3d10fdb2b7f78151e445011b23996717fc", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ace38b3d10fdb2b7f78151e445011b23996717fc", "size": 6662, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.2.0.tgz", "integrity": "sha512-mu3mnnahBoh1HgYYsgI+FsVlifjyiwyyLtoHMxT8S1kC+HnDYuQLbTMDOw2gtg8Yz9KynONSwRcwU8U0PrBmLw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.includes-4.2.0.tgz_1469457900231_0.1818472722079605"}, "directories": {}, "publish_time": 1469457900479, "_hasShrinkwrap": false, "_cnpm_publish_time": 1469457900479, "_cnpmcore_publish_time": "2021-12-13T18:06:12.567Z"}, "4.1.3": {"name": "lodash.includes", "version": "4.1.3", "description": "The lodash method `_.includes` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "includes"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.keys": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@4.1.3", "_shasum": "949a5591d4e2afaf6902d1b57706dd932209a83e", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "949a5591d4e2afaf6902d1b57706dd932209a83e", "size": 5439, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.1.3.tgz", "integrity": "sha512-4jsKVLRI18PebfGuKisMsUf7KfQQOM9lDIrpxJ2tLHVx7gUmdX8HlBbpg8pINPvZO2sWQNAbKk6I372WgYZSlQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.includes-4.1.3.tgz_1463062250986_0.6493278921116143"}, "directories": {}, "publish_time": 1463062251468, "_hasShrinkwrap": false, "_cnpm_publish_time": 1463062251468, "_cnpmcore_publish_time": "2021-12-13T18:06:12.758Z"}, "4.1.2": {"name": "lodash.includes", "version": "4.1.2", "description": "The lodash method `_.includes` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "includes"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.keys": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@4.1.2", "_shasum": "44a5ec82fd7cbaac7bacfae796ac472ac74647a9", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "44a5ec82fd7cbaac7bacfae796ac472ac74647a9", "size": 5357, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.1.2.tgz", "integrity": "sha512-Azkk/MZkBQBWOjfG2g+XIbHtHmdGb5A62SAv66vEtqwZcIFdN9ArrlM3UGNH/Pjhu6ukMNP7+/aGoLIiTOJNqw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.includes-4.1.2.tgz_1459655364619_0.5097221513278782"}, "directories": {}, "publish_time": 1459655365247, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459655365247, "_cnpmcore_publish_time": "2021-12-13T18:06:12.973Z"}, "4.1.1": {"name": "lodash.includes", "version": "4.1.1", "description": "The lodash method `_.includes` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "includes"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.keys": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@4.1.1", "_shasum": "c728bbed1efa1fb5439109340abcf474028b3300", "_from": ".", "_npmVersion": "2.14.17", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c728bbed1efa1fb5439109340abcf474028b3300", "size": 4927, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.1.1.tgz", "integrity": "sha512-oF3eV+u/KI6pZIBJr/EtGOw7HgRuld+gjeS+1J4a2uZiEGNz/c8Ytfcl8CL6vJvfPkxiaYlhjRXFYZcXIZrNkw=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.includes-4.1.1.tgz_1456896603930_0.7035022689960897"}, "directories": {}, "publish_time": 1456896604435, "_hasShrinkwrap": false, "_cnpm_publish_time": 1456896604435, "_cnpmcore_publish_time": "2021-12-13T18:06:13.177Z"}, "4.1.0": {"name": "lodash.includes", "version": "4.1.0", "description": "The lodash method `_.includes` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "includes"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.keys": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@4.1.0", "_shasum": "2bad30f93979c9361d5893570b02caf66bdf6e87", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2bad30f93979c9361d5893570b02caf66bdf6e87", "size": 4928, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.1.0.tgz", "integrity": "sha512-2Nav5agG60v1JxzydA+9u1drRi6bC/y1zu3sFYVEywaNkaJbOLpNMByNlwseYwbC8xI0aJ1w1yJmM9b+jdGPnA=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/lodash.includes-4.1.0.tgz_1455615307741_0.5317947857547551"}, "directories": {}, "publish_time": 1455615309772, "_hasShrinkwrap": false, "_cnpm_publish_time": 1455615309772, "_cnpmcore_publish_time": "2021-12-13T18:06:13.392Z"}, "4.0.1": {"name": "lodash.includes", "version": "4.0.1", "description": "The lodash method `_.includes` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "includes"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraymap": "^3.0.0", "lodash.keys": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@4.0.1", "_shasum": "08e842aa4ceea1c2b9e3f7a6a13d4a074db30f78", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "08e842aa4ceea1c2b9e3f7a6a13d4a074db30f78", "size": 4818, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.0.1.tgz", "integrity": "sha512-3q0B4dbTQguUfq5+W//TbZXGjONhaZA+qNzmjmSiXeMa7n2bjjhowdOBpBTeqveLgFTODPOfzwLRxT25AYRZXg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/lodash.includes-4.0.1.tgz_1454484464682_0.9021453231107444"}, "directories": {}, "publish_time": 1454484467101, "_hasShrinkwrap": false, "_cnpm_publish_time": 1454484467101, "_cnpmcore_publish_time": "2021-12-13T18:06:13.584Z"}, "4.0.0": {"name": "lodash.includes", "version": "4.0.0", "description": "The lodash method `_.includes` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "includes"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._arraymap": "^3.0.0", "lodash.keys": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@4.0.0", "_shasum": "16e6e30a61698e263c811eb949c04f237f00e275", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "16e6e30a61698e263c811eb949c04f237f00e275", "size": 4893, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-4.0.0.tgz", "integrity": "sha512-2XV+lM/N7rwoiwe4WJKSuhyI8QSmD3ITiGHrzkF+uMJnhPxP5VfE6HNiH5BWYFtAXCyAnvNYpC4REe8qZifMCQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452682985454, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452682985454, "_cnpmcore_publish_time": "2021-12-13T18:06:13.861Z"}, "3.1.3": {"name": "lodash.includes", "version": "3.1.3", "description": "The modern build of lodash’s `_.includes` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseindexof": "^3.0.0", "lodash._basevalues": "^3.0.0", "lodash._isiterateecall": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isstring": "^3.0.0", "lodash.keys": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@3.1.3", "_shasum": "c322d049c27892b29a01b995936e595381ebbc17", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.12.5", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "c322d049c27892b29a01b995936e595381ebbc17", "size": 3226, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-3.1.3.tgz", "integrity": "sha512-D7ryPX0XfdfxwpcN2KFdDpUm9wxrQ6GiVefzN7xgaYlo7QfL69d7J2iXU78Q696H/GPTecKquesAFfptb8FKqA=="}, "directories": {}, "publish_time": 1435677584292, "_hasShrinkwrap": false, "_cnpm_publish_time": 1435677584292, "_cnpmcore_publish_time": "2021-12-13T18:06:14.088Z"}, "3.1.2": {"name": "lodash.includes", "version": "3.1.2", "description": "The modern build of lodash’s `_.includes` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseindexof": "^3.0.0", "lodash._basevalues": "^3.0.0", "lodash._isiterateecall": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isstring": "^3.0.0", "lodash.keys": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@3.1.2", "_shasum": "98bc0c7a958c5da198f2e6fcd331417b2c8a74b3", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "0.12.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "98bc0c7a958c5da198f2e6fcd331417b2c8a74b3", "size": 3229, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-3.1.2.tgz", "integrity": "sha512-yz7e8n3ytyF0AGU+SSjy1MqgJzBqiYXt2DppsucZp5Fa6O2/kvEtZ58Q1CjJOCLwYAxEZNw5+WP+FdbV4Hg2HQ=="}, "directories": {}, "publish_time": 1432065044759, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432065044759, "_cnpmcore_publish_time": "2021-12-13T18:06:14.303Z"}, "3.1.1": {"name": "lodash.includes", "version": "3.1.1", "description": "The modern build of lodash’s `_.includes` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseindexof": "^3.0.0", "lodash._basevalues": "^3.0.0", "lodash._isiterateecall": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isstring": "^3.0.0", "lodash.keys": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@3.1.1", "_shasum": "8237b1c52d5d328e2cc9afa7aa5fd0c0b9fcf386", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "8237b1c52d5d328e2cc9afa7aa5fd0c0b9fcf386", "size": 3254, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-3.1.1.tgz", "integrity": "sha512-bb2GlfD3KYvUJEBZeCwavGvLvSsAbeLZvEmyy41xcV9T0S36oRIVHFk4H4UOAdXWANRC7Ojlk6daeuIZ2oSSJQ=="}, "directories": {}, "publish_time": 1429201856911, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429201856911, "_cnpmcore_publish_time": "2021-12-13T18:06:14.508Z"}, "3.1.0": {"name": "lodash.includes", "version": "3.1.0", "description": "The modern build of lodash’s `_.includes` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseindexof": "^3.0.0", "lodash._basevalues": "^3.0.0", "lodash._isiterateecall": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isstring": "^3.0.0", "lodash.keys": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@3.1.0", "_shasum": "f4a7bf46ecf732121ed647aa2c67e67de5d64d36", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "f4a7bf46ecf732121ed647aa2c67e67de5d64d36", "size": 3041, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-3.1.0.tgz", "integrity": "sha512-6dhUWh7C3x1iUKUjki0WpWfpngje1kQ5QZ+jhBKDRS/SlWg7jxrijecD/NcNjsXYS7iVCzUQCdjcfTAYXqrI5w=="}, "directories": {}, "publish_time": 1427326488795, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427326488795, "_cnpmcore_publish_time": "2021-12-13T18:06:14.723Z"}, "3.0.0": {"name": "lodash.includes", "version": "3.0.0", "description": "The modern build of lodash’s `_.includes` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseindexof": "^3.0.0", "lodash._basevalues": "^3.0.0", "lodash.isarray": "^3.0.0", "lodash.isstring": "^3.0.0", "lodash.keys": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.includes@3.0.0", "_shasum": "bbfed63a974b31bfac67758daac8ec6f6c89aebe", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bbfed63a974b31bfac67758daac8ec6f6c89aebe", "size": 2929, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.includes/-/lodash.includes-3.0.0.tgz", "integrity": "sha512-zYjZUlV3OxANqvlNkDKkYzPtKy2N5Z6yscNDIOBhNBPaGfDLPwyoo8Qydob+Wet/ZCLqZRtaPu0peNWAcqBV2w=="}, "directories": {}, "publish_time": 1422286149047, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422286149047, "_cnpmcore_publish_time": "2021-12-13T18:06:14.952Z"}}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "includes"], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "_source_registry_name": "default"}