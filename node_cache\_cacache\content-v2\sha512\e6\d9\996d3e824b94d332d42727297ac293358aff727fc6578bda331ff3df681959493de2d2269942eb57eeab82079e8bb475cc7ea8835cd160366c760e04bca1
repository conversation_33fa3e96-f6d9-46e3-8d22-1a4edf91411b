{"_attachments": {}, "_id": "lodash.once", "_rev": "2938-61f14a57b77ea98a7490d34a", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "description": "The lodash method `_.once` exported as a module.", "dist-tags": {"latest": "4.1.1"}, "license": "MIT", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "lodash.once", "readme": "# lodash.once v4.1.1\n\nThe [lodash](https://lodash.com/) method `_.once` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.once\n```\n\nIn Node.js:\n```js\nvar once = require('lodash.once');\n```\n\nSee the [documentation](https://lodash.com/docs#once) or [package source](https://github.com/lodash/lodash/blob/4.1.1-npm-packages/lodash.once) for more details.\n", "time": {"created": "2022-01-26T13:19:19.203Z", "modified": "2023-07-28T14:53:54.311Z", "4.1.1": "2016-08-13T17:42:47.106Z", "4.1.0": "2016-07-25T14:48:28.075Z", "4.0.0": "2016-01-13T11:09:35.998Z", "3.0.1": "2015-04-16T16:33:07.443Z", "3.0.0": "2015-01-26T15:29:42.991Z", "2.4.1": "2013-12-03T17:15:59.090Z", "2.4.0": "2013-11-26T19:56:23.762Z", "2.3.0": "2013-11-11T16:48:27.567Z", "2.2.1": "2013-10-03T18:51:12.617Z", "2.2.0": "2013-09-29T22:10:29.121Z", "2.1.0": "2013-09-23T07:58:08.940Z", "2.0.0": "2013-09-23T07:39:45.509Z"}, "versions": {"4.1.1": {"name": "lodash.once", "version": "4.1.1", "description": "The lodash method `_.once` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "once"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.once@4.1.1", "_shasum": "0dd3971213c7c56df880977d504c88fb471a97ac", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0dd3971213c7c56df880977d504c88fb471a97ac", "size": 3903, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-4.1.1.tgz", "integrity": "sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.once-4.1.1.tgz_1471110166870_0.09848929662257433"}, "directories": {}, "publish_time": 1471110167106, "_hasShrinkwrap": false, "_cnpm_publish_time": 1471110167106, "_cnpmcore_publish_time": "2021-12-16T13:27:04.816Z"}, "4.1.0": {"name": "lodash.once", "version": "4.1.0", "description": "The lodash method `_.once` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "once"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.once@4.1.0", "_shasum": "bb238c1efcec22789d26a65953fc94af409dc3c4", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bb238c1efcec22789d26a65953fc94af409dc3c4", "size": 4091, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-4.1.0.tgz", "integrity": "sha512-0Ro0yTVv28PQxhmAcsD4Bf4e4bhDN0RfVY4D64FFccD3PqNbeszh4c/izOnx9AQTAeStySXduIisQHm5QGqtew=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.once-4.1.0.tgz_1469458104684_0.4345986496191472"}, "directories": {}, "publish_time": 1469458108075, "_hasShrinkwrap": false, "_cnpm_publish_time": 1469458108075, "_cnpmcore_publish_time": "2021-12-16T13:27:05.031Z"}, "4.0.0": {"name": "lodash.once", "version": "4.0.0", "description": "The lodash method `_.once` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "once"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.before": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.once@4.0.0", "_shasum": "7e3c85376107d6a0735f75aac2318b8ab6d908b3", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7e3c85376107d6a0735f75aac2318b8ab6d908b3", "size": 1914, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-4.0.0.tgz", "integrity": "sha512-LyvEe4OlydF6z6FSmvb1SkLW7m6l3bfZRCoqCfv2pwEklgpbuSQSZwLao9y8YDxft0vYNSYK45YI7hGf9nhVNQ=="}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1452683375998, "_hasShrinkwrap": false, "_cnpm_publish_time": 1452683375998, "_cnpmcore_publish_time": "2021-12-16T13:27:05.308Z"}, "3.0.1": {"name": "lodash.once", "version": "3.0.1", "description": "The modern build of lodash’s `_.once` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.before": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.once@3.0.1", "_shasum": "18137ec96fa3cce868226f3dabb9ea70d0bc128f", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "dist": {"shasum": "18137ec96fa3cce868226f3dabb9ea70d0bc128f", "size": 2015, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-3.0.1.tgz", "integrity": "sha512-xhERPeKvGhgEAsXktDRJsORVjG4dVGZscY0eNCsLRkuGx2fka970e5BnZobFW2eSy2y3QGpahvkKSgy7CQUtRw=="}, "directories": {}, "publish_time": 1429201987443, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429201987443, "_cnpmcore_publish_time": "2021-12-16T13:27:05.533Z"}, "3.0.0": {"name": "lodash.once", "version": "3.0.0", "description": "The modern build of lodash’s `_.once` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.before": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.once@3.0.0", "_shasum": "4181363d55682c0ceed3e1b56b31145d612a8ae6", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "4181363d55682c0ceed3e1b56b31145d612a8ae6", "size": 2018, "noattachment": false, "tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-3.0.0.tgz", "integrity": "sha512-BI8N7Np5l08XKGXxdBRdUaHtqzYY/ovIRRX0EmqY5SpGxxgm3WtQXhaiLZXRtLFvpRLCa9qI3go+/byaRSUWnQ=="}, "directories": {}, "publish_time": 1422286182991, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422286182991, "_cnpmcore_publish_time": "2021-12-16T13:27:05.954Z"}, "2.4.1": {"name": "lodash.once", "version": "2.4.1", "description": "The Lo-Dash function `_.once` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.4.1"}, "readmeFilename": "README.md", "_id": "lodash.once@2.4.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-2.4.1.tgz", "shasum": "480999f39fdb8e411a31760f9d5b0050be38736d", "size": 2246, "noattachment": false, "integrity": "sha512-cF4gtmSPEy6PPDn+sadmCYjgRz5g5k/WLG2JLb3iLpN6d3Q4UlE1eJNExcNtEFTMXgEKEeGoCjIDs74j1lWbsA=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1386090959090, "_hasShrinkwrap": false, "_cnpm_publish_time": 1386090959090, "_cnpmcore_publish_time": "2021-12-16T13:27:06.203Z"}, "2.4.0": {"name": "lodash.once", "version": "2.4.0", "description": "The Lo-Dash function `_.once` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.4.0"}, "readmeFilename": "README.md", "_id": "lodash.once@2.4.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-2.4.0.tgz", "shasum": "acd9c99f77cc02d4bb73f67f54c77e893688e893", "size": 2249, "noattachment": false, "integrity": "sha512-zZ33cQvEvKnsKtwcNeLL20HVE8a3/9oamRwHkpfMnStSv9Nf6KcS6SuSEM+TCkZKrp5skPyVEaVK80IglTuQyA=="}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1385495783762, "_hasShrinkwrap": false, "_cnpm_publish_time": 1385495783762, "_cnpmcore_publish_time": "2021-12-16T13:27:06.422Z"}, "2.3.0": {"name": "lodash.once", "version": "2.3.0", "description": "The Lo-Dash function `_.once` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.3.0"}, "readmeFilename": "README.md", "_id": "lodash.once@2.3.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-2.3.0.tgz", "shasum": "6198089a127a62e2d7ecb9933a968f70d8fb4f7f", "size": 2245, "noattachment": false, "integrity": "sha512-lDPlCTrY9muaFolzJyCbiN77jX5ZCuqHLEhjH8Jw6KvZGyPEH/wXkXG/8AbsDQ37pDPlZv9p0RoLHSerQWkWcw=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1384188507567, "_hasShrinkwrap": false, "_cnpm_publish_time": 1384188507567, "_cnpmcore_publish_time": "2021-12-16T13:27:06.632Z"}, "2.2.1": {"name": "lodash.once", "version": "2.2.1", "description": "The Lo-Dash function `_.once` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.2.1"}, "readmeFilename": "README.md", "_id": "lodash.once@2.2.1", "dist": {"tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-2.2.1.tgz", "shasum": "62636f46f0e24e450aee21d8a772247dda107a55", "size": 2245, "noattachment": false, "integrity": "sha512-AUDfxQMqe1x+roxPseBaakHbLEvqpOlcmswXl1ey80LgvXuh0iPsYxPtboon5KuEnllpcbu3nuV8M5vul0vaew=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380826272617, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380826272617, "_cnpmcore_publish_time": "2021-12-16T13:27:06.853Z"}, "2.2.0": {"name": "lodash.once", "version": "2.2.0", "description": "The Lo-Dash function `_.once` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "server", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.2.0"}, "readmeFilename": "README.md", "_id": "lodash.once@2.2.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-2.2.0.tgz", "shasum": "66099bf7cec4455fb1f5138867e6ad0671964751", "size": 2292, "noattachment": false, "integrity": "sha512-hwhRIHYEFUL1MQK2ODX2zaySTSPtqQSrH4BEBdxKeiKGlDwStKBdceewusrJ+HNBXJNdxIMxPmPzU4UshPKGUQ=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1380492629121, "_hasShrinkwrap": false, "_cnpm_publish_time": 1380492629121, "_cnpmcore_publish_time": "2021-12-16T13:27:07.069Z"}, "2.1.0": {"name": "lodash.once", "version": "2.1.0", "description": "The Lo-Dash function `_.once` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["functional", "lodash", "lodash-modularized", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.1.0"}, "readmeFilename": "README.md", "_id": "lodash.once@2.1.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-2.1.0.tgz", "shasum": "c5f32d62a841adf66ad8172c5c50eaecaa43ef62", "size": 2304, "noattachment": false, "integrity": "sha512-+EuEGmsJ1M5FDXrxZz1qnNIcIuuuvdFEWRLTY9vDED3yzEsHaNA7DRwlWID9GcTNT2h4/BFTUfh8JnMnKMgDMw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379923088940, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379923088940, "_cnpmcore_publish_time": "2021-12-16T13:27:07.277Z"}, "2.0.0": {"name": "lodash.once", "version": "2.0.0", "description": "The Lo-Dash function `_.once` as a Node.js module generated by lodash-cli.", "homepage": "http://lodash.com/custom-builds", "license": "MIT", "keywords": ["customize", "functional", "lodash", "performance", "speed", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/lodash/lodash-cli/issues"}, "repository": {"type": "git", "url": "https://github.com/lodash/lodash-cli.git"}, "dependencies": {"lodash.isfunction": "~2.0.0"}, "readmeFilename": "README.md", "_id": "lodash.once@2.0.0", "dist": {"tarball": "https://registry.npmmirror.com/lodash.once/-/lodash.once-2.0.0.tgz", "shasum": "c2254ee40b934a98ea1929e20098368a50614ede", "size": 2278, "noattachment": false, "integrity": "sha512-gEFMubYNovCBkwBdSqtNPOvgjcsgETGQqAlDPhp+9n3/jJXqmyhpYz2YALMkufj79bfA3qdq0LqMw1e4mhRkJw=="}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1379921985509, "_hasShrinkwrap": false, "_cnpm_publish_time": 1379921985509, "_cnpmcore_publish_time": "2021-12-16T13:27:07.504Z"}}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "once"], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "_source_registry_name": "default"}