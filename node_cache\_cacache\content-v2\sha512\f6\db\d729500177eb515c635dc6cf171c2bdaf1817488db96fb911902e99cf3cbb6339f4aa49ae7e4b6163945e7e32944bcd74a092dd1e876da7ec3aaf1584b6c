{"_attachments": {}, "_id": "proc-log", "_rev": "6878-61f15ca1a920628a7b723818", "author": {"name": "GitHub Inc."}, "description": "just emit 'log' events on the process object", "dist-tags": {"latest": "5.0.0"}, "license": "ISC", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "name": "proc-log", "readme": "# proc-log\n\nEmits events on the process object which a listener can consume and print to the terminal or log file.\n\nThis is used by various modules within the npm CLI stack in order to send log events that can be consumed by a listener on the process object.\n\nCurrently emits `log`, `output`, `input`, and `time` events.\n\n## API\n\n```js\nconst { log, output, input, time } = require('proc-log')\n```\n\n#### output\n* `output.standard(...args)` calls `process.emit('output', 'standard', ...args)`\n  \n  This is for general standard output.  Consumers will typically show this on stdout (after optionally formatting or filtering it).\n\n* `output.error(...args)` calls `process.emit('output', 'error', ...args)`\n  \n  This is for general error output.  Consumers will typically show this on stderr (after optionally formatting or filtering it).\n\n* `output.buffer(...args)` calls `process.emit('output', 'buffer', ...args)`\n  \n  This is for buffered output.  Consumers will typically buffer this until they are ready to display.\n\n* `output.flush(...args)` calls `process.emit('output', 'flush', ...args)`\n  \n  This is to indicate that the output buffer should be flushed.\n\n* `output.LEVELS` an array of strings of all output method names\n\n#### log\n* `log.error(...args)` calls `process.emit('log', 'error', ...args)`\n  \n  The highest log level.  For printing extremely serious errors that indicate something went wrong.\n\n* `log.warn(...args)` calls `process.emit('log', 'warn', ...args)`\n  \n  A fairly high log level.  Things that the user needs to be aware of, but which won't necessarily cause improper functioning of the system.\n\n* `log.notice(...args)` calls `process.emit('log', 'notice', ...args)`\n  \n  Notices which are important, but not necessarily dangerous or a cause for excess concern.\n\n* `log.info(...args)` calls `process.emit('log', 'info', ...args)`\n  \n  Informative messages that may benefit the user, but aren't particularly important.\n\n* `log.verbose(...args)` calls `process.emit('log', 'verbose', ...args)`\n  \n  Noisy output that is more detail that most users will care about.\n\n* `log.silly(...args)` calls `process.emit('log', 'silly', ...args)`\n  \n  Extremely noisy excessive logging messages that are typically only useful for debugging.\n\n* `log.http(...args)` calls `process.emit('log', 'http', ...args)`\n  \n  Information about HTTP requests made and/or completed.\n\n* `log.timing(...args)` calls `process.emit('log', 'timing', ...args)`\n  \n  Timing information.\n\n* `log.pause()` calls `process.emit('log', 'pause')`\n  \n  Used to tell the consumer to stop printing messages.\n\n* `log.resume()` calls `process.emit('log', 'resume')`\n  \n  Used to tell the consumer that it is ok to print messages again.\n\n* `log.LEVELS` an array of strings of all log method names\n\n#### input\n\n* `input.start(fn?)` calls `process.emit('input', 'start')`\n\n  Used to tell the consumer that the terminal is going to begin reading user input. Returns a function that will call `input.end()` for convenience.\n  \n  This also takes an optional callback which will run `input.end()` on its completion. If the callback returns a `Promise` then `input.end()` will be run during `finally()`.\n\n* `input.end()` calls `process.emit('input', 'end')`\n\n  Used to tell the consumer that the terminal has stopped reading user input.\n\n* `input.read(...args): Promise` calls `process.emit('input', 'read', resolve, reject, ...args)`\n\n  Used to tell the consumer that the terminal is reading user input and returns a `Promise` that the producer can `await` until the consumer has finished its async action.\n  \n  This emits `resolve` and `reject` functions (in addition to all passed in arguments) which the consumer must use to resolve the returned `Promise`.\n\n#### time\n\n* `time.start(timerName, fn?)` calls `process.emit('time', 'start', 'timerName')`\n\n  Used to start a timer with the specified name. Returns a function that will call `time.end()` for convenience.\n  \n  This also takes an optional callback which will run `time.end()` on its completion. If the callback returns a `Promise` then `time.end()` will be run during `finally()`.\n\n* `time.end(timerName)` calls `process.emit('time', 'end', timeName)`\n\n  Used to tell the consumer to stop a timer with the specified name.\n\n## Examples\n\n### log\n\nEvery `log` method calls `process.emit('log', level, ...otherArgs)` internally.  So in order to consume those events you need to do `process.on('log', fn)`.\n\n#### Colorize based on level\n\nHere's an example of how to consume `proc-log` log events and colorize them based on level:\n\n```js\nconst chalk = require('chalk')\n\nprocess.on('log', (level, ...args) => {\n  if (level === 'error') {\n    console.log(chalk.red(level), ...args)\n  } else {\n    console.log(chalk.blue(level), ...args)\n  }\n})\n```\n\n#### Pause and resume\n\n`log.pause` and `log.resume` are included so you have the ability to tell your consumer that you want to pause or resume your display of logs. In the npm CLI we use this to buffer all logs on init until we know the correct loglevel to display.  But we also setup a second handler that writes everything to a file even if paused.\n\n```js\nlet paused = true\nconst buffer = []\n\n// this handler will buffer and replay logs only after `procLog.resume()` is called\nprocess.on('log', (level, ...args) => {\n  if (level === 'resume') {\n    buffer.forEach((item) => console.log(...item))\n    paused = false\n    return\n  } \n\n  if (paused) {\n    buffer.push([level, ...args])\n  } else {\n    console.log(level, ...args)\n  }\n})\n\n// this handler will write everything to a file\nprocess.on('log', (...args) => {\n  fs.appendFileSync('debug.log', args.join(' '))\n})\n```\n\n### input\n\n### `start` and `end`\n\n**producer.js**\n```js\nconst { output, input } = require('proc-log')\nconst { readFromUserInput } = require('./my-read')\n\n// Using callback passed to `start`\ntry {\n  const res = await input.start(\n    readFromUserInput({ prompt: 'OK?', default: 'y' })\n  )\n  output.standard(`User said ${res}`)\n} catch (err) {\n  output.error(`User cancelled: ${err}`)\n}\n\n// Manually calling `start` and `end`\ntry {\n  input.start()\n  const res = await readFromUserInput({ prompt: 'OK?', default: 'y' })\n  output.standard(`User said ${res}`)\n} catch (err) {\n  output.error(`User cancelled: ${err}`)\n} finally {\n  input.end()\n}\n```\n\n**consumer.js**\n```js\nconst { read } = require('read')\n\nprocess.on('input', (level) => {\n  if (level === 'start') {\n    // Hide UI to make room for user input being read\n  } else if (level === 'end') {\n    // Restore UI now that reading is ended\n  }\n})\n```\n\n### Using `read` to call `read()`\n\n**producer.js**\n```js\nconst { output, input } = require('proc-log')\n\ntry {\n  const res = await input.read({ prompt: 'OK?', default: 'y' })\n  output.standard(`User said ${res}`)\n} catch (err) {\n  output.error(`User cancelled: ${err}`)\n}\n```\n\n**consumer.js**\n```js\nconst { read } = require('read')\n\nprocess.on('input', (level, ...args) => {\n  if (level === 'read') {\n    const [res, rej, opts] = args\n    read(opts).then(res).catch(rej)\n  }\n})\n```", "time": {"created": "2022-01-26T14:37:21.230Z", "modified": "2025-05-14T20:10:26.630Z", "1.0.0": "2021-04-15T19:43:28.370Z", "2.0.0": "2022-02-10T19:51:38.758Z", "2.0.1": "2022-03-28T20:50:20.548Z", "3.0.0": "2022-10-14T05:23:06.806Z", "4.0.0": "2024-04-12T16:56:41.595Z", "4.1.0": "2024-04-15T16:36:33.064Z", "4.2.0": "2024-04-16T20:49:53.093Z", "5.0.0": "2024-09-05T00:21:21.957Z"}, "versions": {"1.0.0": {"name": "proc-log", "version": "1.0.0", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me"}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "eslint index.js test/*.js", "postsnap": "eslint index.js test/*.js --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "devDependencies": {"eslint": "^7.9.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "tap": "^15.0.2"}, "gitHead": "a3ebf6f6588c1a89ba573b498bfdfa21b71b4aef", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "homepage": "https://github.com/npm/proc-log#readme", "_id": "proc-log@1.0.0", "_nodeVersion": "15.3.0", "_npmVersion": "7.9.0", "dist": {"shasum": "0d927307401f69ed79341e83a0b2c9a13395eb77", "size": 1812, "noattachment": false, "tarball": "https://registry.npmmirror.com/proc-log/-/proc-log-1.0.0.tgz", "integrity": "sha512-aCk8AO51s+4JyuYGg3Q/a6gnrlDO09NpVWePtjp7xwphcoQ04x5WAfCyugcsbLooWcMJ87CLkD4+604IckEdhg=="}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proc-log_1.0.0_1618515808246_0.33636671349896585"}, "_hasShrinkwrap": false, "publish_time": 1618515808370, "_cnpm_publish_time": 1618515808370, "_cnpmcore_publish_time": "2021-12-13T18:14:02.314Z"}, "2.0.0": {"name": "proc-log", "version": "2.0.0", "main": "lib/index.js", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "npm run lint", "postsnap": "eslint index.js test/*.js --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lint": "eslint '**/*.js'", "postlint": "npm-template-check", "template-copy": "npm-template-copy --force", "lintfix": "npm run lint -- --fix"}, "devDependencies": {"@npmcli/template-oss": "^2.7.1", "tap": "^15.1.6"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "templateOSS": {"version": "2.7.1"}, "gitHead": "adccecc2bf5e77427e3fefe826a8e5a1a57640d7", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "homepage": "https://github.com/npm/proc-log#readme", "_id": "proc-log@2.0.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.4.1", "dist": {"integrity": "sha512-I/35MfCX2H8jBUhKN8JB8nmqvQo/nKdrBodBY7L3RhDSPPyvOHwLYNmPuhwuJq7a7C3vgFKWGQM+ecPStcvOHA==", "shasum": "25f8cb346a5d08e27f2422b3ca6ba8379bcbf8ba", "tarball": "https://registry.npmmirror.com/proc-log/-/proc-log-2.0.0.tgz", "fileCount": 4, "unpackedSize": 5044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBWzKCRA9TVsSAnZWagAABNQP/2n7O2dda/91D8yi9H/n\n6ZteH6o1SHK2L9TLw8MN/ynojeq8yfUE9SIUyou0oRZerM7nVjd6kbw86iRz\nW021GlatUjeftWmr08JnTuXXq5vWoyXvXmDPPIe+HLYZHWQXLVPw7+zlJKij\nFe+aXqZvaUy2e+fp2UtsaAiiPRyEBppwWmAAUjwUh4OFf93qA03EG2Jw64fF\nelelb1+f9ArIZIpuZ/qcVEhZ5l0rI24OfhrQGMCMkEDLUQMzSXQvOwfN4MUg\niMczL5TKYfIYuryMQjrfVe7Vf33FxEBtaLEpOEcUO+fsdBogm9NBMmIwx2oz\nYiDZg9guqp/2faItvK+Z/0x5ySNZWMAwLOVmbn37oQBtfE3gFDthCjysT9Nq\nEx2gH9boNMs2Ti2+6GbdHLIQSA3wXGaqoFRHyWH8tUbD80We6cEUWB3TBaoQ\n45HvTeCaMIKlMZSR2twT515hwLpdUhey/yeVv/63B+F5GjdG8rCJWlBfaLjY\nhFKFKGkuIOUOeB2kqTCsetjEIoJM6ndWkZJRgLXhEuEds08kjaBwqqEC9Lqw\nr1ViUaFiuhe8JyxhfhtUFiYbJU8vdHtlEkQ0GyQ769pg7bMA8P1gNSrZdKMs\nA+P5h0mBjXVlonYny0rMCFBVOTProz00w0ck+nv2T0x2Ze5isgInYZ17SdnC\n/Ki8\r\n=1Nk3\r\n-----END PGP SIGNATURE-----\r\n", "size": 2422}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proc-log_2.0.0_1644522698587_0.5470729257783982"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-02-10T19:52:09.648Z"}, "2.0.1": {"name": "proc-log", "version": "2.0.1", "main": "lib/index.js", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "npm run lint", "postsnap": "eslint index.js test/*.js --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.2.0", "tap": "^16.0.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.2.0"}, "gitHead": "71042352b67eb0a81e8cfea9b5e51966b9a84ead", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "homepage": "https://github.com/npm/proc-log#readme", "_id": "proc-log@2.0.1", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-Kcmo2FhfDTXdcbfDH76N7uBYHINxc/8GW7UAVuVP9I+Va3uHSerrnKV6dLooga/gh7GlgzuCCr/eoldnL1muGw==", "shasum": "8f3f69a1f608de27878f91f5c688b225391cb685", "tarball": "https://registry.npmmirror.com/proc-log/-/proc-log-2.0.1.tgz", "fileCount": 4, "unpackedSize": 5251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQh+MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrW4xAAhOqyDdX42UkizrLSsVstrXA05GLHTAtMIuCI34ZshSlqdf5k\r\nz5NeF1Z90T52mtmqKlfWpq4zzGSDgdf0xSQdjZuM9zmyNShIQ0ILx1L4tPUb\r\nbWMYDKcjy+4s672ds7YRk10tlTuP3IKvMvUVM5MEt9m9KJCKZ7c6VuvaE3fj\r\nn6TWSel9PQfLltZbMFcPw+Gjhv5qOjJ69LWEUzoU5iL6vCp0OV/FKYRkqsjg\r\nRc8uw4xIuWF41fMG74eL7CrniOIBArXA8dduQ+RiOkFbXAY6lQhKm/+09YCq\r\nmPuyL7XPcS/Hm5B1JZidkuyo2k3vmDoLxHPf1DThr510uazvpRR4GdNyDANN\r\njjOgFpPrt4C+Vf2aqr6MNUX6DrkH125C/y2Sm6wRsUjOnrSwNceX+tHU2XJf\r\n8xxi++lLzfKJ52bjO2U/qktJpDaTmBgSeueI2CCrVa13dJM9XUiDf18cDGfH\r\nBQBVBq1ZnBYCdm1V01Gren1Uv70C4G3+5a8qG+SxQn2FXLoPVb05iom6oPZY\r\niuYMisS3g9D8QAyFHFOBx6AlC3D0lk3YUrnjiq+JFpH9ROpNO2gtJEuI2CJl\r\nM/Q/N5LDHRUetpDKlpSVGHlwJsoCy5YltCSSkL+z95MZ/r7URX7V9k0Zq32h\r\nVyPO/tq+MSPP2HBjd6ycsQe1C5xXzQtWcbw=\r\n=uaEm\r\n-----END PGP SIGNATURE-----\r\n", "size": 2502}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proc-log_2.0.1_1648500620397_0.38798423133177296"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-28T20:50:27.396Z"}, "3.0.0": {"name": "proc-log", "version": "3.0.0", "main": "lib/index.js", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "npm run lint", "postsnap": "eslint index.js test/*.js --fix", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "4.5.1", "tap": "^16.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.5.1"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "gitHead": "a3318aac6541572d897f404c1db7d905016c5cfe", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "homepage": "https://github.com/npm/proc-log#readme", "_id": "proc-log@3.0.0", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-++Vn7NS4Xf9NacaU9Xq3URUuqZETPsf8L4j5/ckhaRYsfPeRyzGw+iDjFhV/Jr3uNmTvvddEJFWh5R1gRgUH8A==", "shasum": "fb05ef83ccd64fd7b20bbe9c8c1070fc08338dd8", "tarball": "https://registry.npmmirror.com/proc-log/-/proc-log-3.0.0.tgz", "fileCount": 4, "unpackedSize": 5215, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHifbHgF6M3IpbNVu8XNpsbTUeNJM3qIs5XF1g0YLliHAiEAuptlCUzpR9akldhsbgMLMxeuPC7hOWXL8fkpJ1WgP7E="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPI6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjPQ//UPSrpMvLqry94iJkulfi5zOWvurCtHjX2ttnkkZ/s0H+SigN\r\nvOe8PQcIacbmrBqYSydLuM4XBf3vUErBZXjwegNzTY28q0kNL6JMZink/28e\r\nAaKu2EY50BfKG/yB+StonfnfXiv9XGqtIvdqoVF0yMKbwghjVUNfLb3mjx4+\r\n4D3S3obKZPksS1up5yLCMyMOiz3h5W30M05Ipyyp0IHTVc4NQB7zpVN/fVrt\r\nRBt/Z0aml3JPe+A2tKq8rtIFegVsuyp8oDT/xGugZRNI7cuK3bLTQxQXZvJW\r\neusL/Q/TzuyieN35bZuzrdzk0Ofs7Sx4LUEcbwv2AbBOhUdSZHM71gXe4GHq\r\nP+5qLsmZEoQ/dDbaQ/mYst/bYlvb36Avt0naN+SizCBrS7AuNErb+vW6Lud6\r\nnNF4q0iT0IBERbMAKqMUfAoriTidLInRwHL+YA7Zd4Plw9djUqWF2X3L7q1z\r\n/kwvinhV6tP04LpOlnKOlB5eUD8yyWF5WN0vJPRJu5TseUMOAdSEQ0/qlwRh\r\nA32FMskN+dBEHy6mDa5fJfMkXA8/lARwdyHKq/PvNbQCJCL5FU6WhPgk3heK\r\n94sOQbe4zwx6VHJ1InfFRElXW/MsmN+S4sQnUhbQVHOQp+04h38e54IqqcwF\r\nQbFZXz2zcDVctGitwZjS6dIuinKsLDuXivk=\r\n=QR6L\r\n-----END PGP SIGNATURE-----\r\n", "size": 2500}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proc-log_3.0.0_1665724986606_0.6562993514020836"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-10-14T06:52:48.616Z"}, "4.0.0": {"name": "proc-log", "version": "4.0.0", "main": "lib/index.js", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "npm run lint", "postsnap": "eslint index.js test/*.js --fix", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "tap": "^16.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "_id": "proc-log@4.0.0", "gitHead": "ad99316a9f70e3beced2f0a0709649b6fd7b3e52", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "homepage": "https://github.com/npm/proc-log#readme", "_nodeVersion": "20.12.1", "_npmVersion": "10.5.2", "dist": {"integrity": "sha512-v1lzmYxGDs2+OZnmYtYZK3DG8zogt+CbQ+o/iqqtTfpyCmGWulCTEQu5GIbivf7OjgIkH2Nr8SH8UxAGugZNbg==", "shasum": "b8aac7609599a5aea22c23a7544523bc1ff85eda", "tarball": "https://registry.npmmirror.com/proc-log/-/proc-log-4.0.0.tgz", "fileCount": 4, "unpackedSize": 7265, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/proc-log@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDOPLHGvLEa/XkGjJD3bD9V1Nz7QSQZXZOuJ49mtjWmMAiEAs0FYXQI5Y+YLkS+j4RQ+ZgguYidV3P8JFwRRD8bEzN8="}], "size": 2807}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proc-log_4.0.0_1712941001450_0.09906957453402687"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-12T16:56:41.595Z", "publish_time": 1712941001595, "_source_registry_name": "default"}, "4.1.0": {"name": "proc-log", "version": "4.1.0", "main": "lib/index.js", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "npm run lint", "postsnap": "eslint index.js test/*.js --fix", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "tap": "^16.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "_id": "proc-log@4.1.0", "gitHead": "3dbd032fc792e66ab4987265eb9c3c1bd7667386", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "homepage": "https://github.com/npm/proc-log#readme", "_nodeVersion": "20.12.1", "_npmVersion": "10.5.2", "dist": {"integrity": "sha512-dmQ2iPw2nJMi9/4dpaG1wd0m1GE+K5kW7RGbjy5hoEEGnhPIzsm+klBO5RGGdcoYbWsNtU2KSNAdEldts+icLg==", "shasum": "ab6c1552b454e9e467cb58119179e4557d109b34", "tarball": "https://registry.npmmirror.com/proc-log/-/proc-log-4.1.0.tgz", "fileCount": 4, "unpackedSize": 11937, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/proc-log@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFjto5R5AGTaZnse3SonjBcvLLaZFQ2e+mNOCO99xviAIgenDJJxSG2+oBTE9kd/Q4r8oOMM4wFsg0lY8jvPvduEE="}], "size": 3914}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proc-log_4.1.0_1713198992922_0.0027306484928157904"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-15T16:36:33.064Z", "publish_time": 1713198993064, "_source_registry_name": "default"}, "4.2.0": {"name": "proc-log", "version": "4.2.0", "main": "lib/index.js", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "npm run lint", "postsnap": "eslint index.js test/*.js --fix", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "tap": "^16.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "_id": "proc-log@4.2.0", "gitHead": "953e6035670f9afe2ec93f6286d76db2828854d6", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "homepage": "https://github.com/npm/proc-log#readme", "_nodeVersion": "20.12.1", "_npmVersion": "10.5.2", "dist": {"integrity": "sha512-g8+OnU/L2v+wyiVK+D5fA34J7EH8jZ8DDlvwhRCMxmMj7UCBvxiO1mGeN+36JXIKF4zevU4kRBd8lVgG9vLelA==", "shasum": "b6f461e4026e75fdfe228b265e9f7a00779d7034", "tarball": "https://registry.npmmirror.com/proc-log/-/proc-log-4.2.0.tgz", "fileCount": 4, "unpackedSize": 12261, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/proc-log@4.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNStNROA4yNJ9PelPPE2Zu/qXkWLycFqs2cUlUrvGcfQIgQloGakWeTeBxeHy8tAlNpXjvlDF+X8/oZk7PB1DfNrQ="}], "size": 3991}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proc-log_4.2.0_1713300592930_0.14959539526116528"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-16T20:49:53.093Z", "publish_time": 1713300593093, "_source_registry_name": "default"}, "5.0.0": {"name": "proc-log", "version": "5.0.0", "main": "lib/index.js", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "npm run lint", "postsnap": "eslint index.js test/*.js --fix", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.0.1"}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "_id": "proc-log@5.0.0", "gitHead": "8105dea8ca31296f5826e62683e63539401d6a8e", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "homepage": "https://github.com/npm/proc-log#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-Azwzvl90HaF0aCz1JrDdXQykFakSSNPaPoiZ9fm5qJIMHioDZEi7OAdRwSm6rSoPtY3Qutnm3L7ogmg3dc+wbQ==", "shasum": "e6c93cf37aef33f835c53485f314f50ea906a9d8", "tarball": "https://registry.npmmirror.com/proc-log/-/proc-log-5.0.0.tgz", "fileCount": 4, "unpackedSize": 12287, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/proc-log@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBDwbtbw4ZO3G0Ar3DKMzs/lxt6nkwOZJQqQjCNhmP+ZAiB/lBILWDPgnRuP5QJD+aNySwJjefjFDP8tVNyJPgOQKQ=="}], "size": 3989}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proc-log_5.0.0_1725495681824_0.5708126841081362"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-09-05T00:21:21.957Z", "publish_time": 1725495681957, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "homepage": "https://github.com/npm/proc-log#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/proc-log.git"}, "_source_registry_name": "default"}