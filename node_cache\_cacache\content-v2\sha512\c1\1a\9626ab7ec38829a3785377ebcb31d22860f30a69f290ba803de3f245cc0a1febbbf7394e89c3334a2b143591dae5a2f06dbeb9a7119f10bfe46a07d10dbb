{"_attachments": {}, "_id": "node-fetch", "_rev": "198-61f14424fbcaa28a759408ac", "author": {"name": "<PERSON>"}, "description": "A light-weight module that brings Fetch API to node.js", "dist-tags": {"beta": "4.0.0-beta.4", "cjs": "2.6.7", "latest": "3.3.2", "next": "3.0.0-beta.10", "release-2.x": "2.7.0"}, "license": "MIT", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "name": "node-fetch", "readme": "<div align=\"center\">\n\t<img src=\"docs/media/Banner.svg\" alt=\"Node Fetch\"/>\n\t<br>\n\t<p>A light-weight module that brings <a href=\"https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API\">Fetch API</a> to Node.js.</p>\n\t<a href=\"https://github.com/node-fetch/node-fetch/actions\"><img src=\"https://github.com/node-fetch/node-fetch/workflows/CI/badge.svg?branch=master\" alt=\"Build status\"></a>\n\t<a href=\"https://coveralls.io/github/node-fetch/node-fetch\"><img src=\"https://img.shields.io/coveralls/github/node-fetch/node-fetch\" alt=\"Coverage status\"></a>\n\t<a href=\"https://packagephobia.now.sh/result?p=node-fetch\"><img src=\"https://badgen.net/packagephobia/install/node-fetch\" alt=\"Current version\"></a>\n\t<a href=\"https://www.npmjs.com/package/node-fetch\"><img src=\"https://img.shields.io/npm/v/node-fetch\" alt=\"Install size\"></a>\n\t<a href=\"https://github.com/sindresorhus/awesome-nodejs\"><img src=\"https://awesome.re/mentioned-badge.svg\" alt=\"Mentioned in Awesome Node.js\"></a>\n\t<a href=\"https://discord.gg/Zxbndcm\"><img src=\"https://img.shields.io/discord/619915844268326952?color=%237289DA&label=Discord\" alt=\"Discord\"></a>\n\t<br>\n\t<br>\n\t<b>Consider supporting us on our Open Collective:</b>\n\t<br>\n\t<br>\n\t<a href=\"https://opencollective.com/node-fetch\"><img src=\"https://opencollective.com/node-fetch/donate/button.png?color=blue\" alt=\"Open Collective\"></a>\n</div>\n\n---\n\n**You might be looking for the [v2 docs](https://github.com/node-fetch/node-fetch/tree/2.x#readme)**\n\n<!-- TOC -->\n\n- [Motivation](#motivation)\n- [Features](#features)\n- [Difference from client-side fetch](#difference-from-client-side-fetch)\n- [Installation](#installation)\n- [Loading and configuring the module](#loading-and-configuring-the-module)\n- [Upgrading](#upgrading)\n- [Common Usage](#common-usage)\n\t- [Plain text or HTML](#plain-text-or-html)\n\t- [JSON](#json)\n\t- [Simple Post](#simple-post)\n\t- [Post with JSON](#post-with-json)\n\t- [Post with form parameters](#post-with-form-parameters)\n\t- [Handling exceptions](#handling-exceptions)\n\t- [Handling client and server errors](#handling-client-and-server-errors)\n\t- [Handling cookies](#handling-cookies)\n- [Advanced Usage](#advanced-usage)\n\t- [Streams](#streams)\n\t- [Accessing Headers and other Metadata](#accessing-headers-and-other-metadata)\n\t- [Extract Set-Cookie Header](#extract-set-cookie-header)\n\t- [Post data using a file](#post-data-using-a-file)\n\t- [Request cancellation with AbortSignal](#request-cancellation-with-abortsignal)\n- [API](#api)\n\t- [fetch(url[, options])](#fetchurl-options)\n\t- [Options](#options)\n\t\t- [Default Headers](#default-headers)\n\t\t- [Custom Agent](#custom-agent)\n\t\t- [Custom highWaterMark](#custom-highwatermark)\n\t\t- [Insecure HTTP Parser](#insecure-http-parser)\n\t- [Class: Request](#class-request)\n\t\t- [new Request(input[, options])](#new-requestinput-options)\n\t- [Class: Response](#class-response)\n\t\t- [new Response([body[, options]])](#new-responsebody-options)\n\t\t- [response.ok](#responseok)\n\t\t- [response.redirected](#responseredirected)\n\t\t- [response.type](#responsetype)\n\t- [Class: Headers](#class-headers)\n\t\t- [new Headers([init])](#new-headersinit)\n\t- [Interface: Body](#interface-body)\n\t\t- [body.body](#bodybody)\n\t\t- [body.bodyUsed](#bodybodyused)\n\t\t- [body.arrayBuffer()](#bodyarraybuffer)\n\t\t- [body.blob()](#bodyblob)\n\t\t- [body.formData()](#formdata)\n\t\t- [body.json()](#bodyjson)\n\t\t- [body.text()](#bodytext)\n\t- [Class: FetchError](#class-fetcherror)\n\t- [Class: AbortError](#class-aborterror)\n- [TypeScript](#typescript)\n- [Acknowledgement](#acknowledgement)\n- [Team](#team)\n\t\t\t\t- [Former](#former)\n- [License](#license)\n\n<!-- /TOC -->\n\n## Motivation\n\nInstead of implementing `XMLHttpRequest` in Node.js to run browser-specific [Fetch polyfill](https://github.com/github/fetch), why not go from native `http` to `fetch` API directly? Hence, `node-fetch`, minimal code for a `window.fetch` compatible API on Node.js runtime.\n\nSee Jason Miller's [isomorphic-unfetch](https://www.npmjs.com/package/isomorphic-unfetch) or Leonardo Quixada's [cross-fetch](https://github.com/lquixada/cross-fetch) for isomorphic usage (exports `node-fetch` for server-side, `whatwg-fetch` for client-side).\n\n## Features\n\n- Stay consistent with `window.fetch` API.\n- Make conscious trade-off when following [WHATWG fetch spec][whatwg-fetch] and [stream spec](https://streams.spec.whatwg.org/) implementation details, document known differences.\n- Use native promise and async functions.\n- Use native Node streams for body, on both request and response.\n- Decode content encoding (gzip/deflate/brotli) properly, and convert string output (such as `res.text()` and `res.json()`) to UTF-8 automatically.\n- Useful extensions such as redirect limit, response size limit, [explicit errors][error-handling.md] for troubleshooting.\n\n## Difference from client-side fetch\n\n- See known differences:\n\t- [As of v3.x](docs/v3-LIMITS.md)\n\t- [As of v2.x](docs/v2-LIMITS.md)\n- If you happen to use a missing feature that `window.fetch` offers, feel free to open an issue.\n- Pull requests are welcomed too!\n\n## Installation\n\nCurrent stable release (`3.x`) requires at least Node.js 12.20.0.\n\n```sh\nnpm install node-fetch\n```\n\n## Loading and configuring the module\n\n### ES Modules (ESM)\n\n```js\nimport fetch from 'node-fetch';\n```\n\n### CommonJS\n\n`node-fetch` from v3 is an ESM-only module - you are not able to import it with `require()`.\n\nIf you cannot switch to ESM, please use v2 which remains compatible with CommonJS. Critical bug fixes will continue to be published for v2.\n\n```sh\nnpm install node-fetch@2\n```\n\nAlternatively, you can use the async `import()` function from CommonJS to load `node-fetch` asynchronously:\n\n```js\n// mod.cjs\nconst fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));\n```\n\n### Providing global access\n\nTo use `fetch()` without importing it, you can patch the `global` object in node:\n\n```js\n// fetch-polyfill.js\nimport fetch, {\n  Blob,\n  blobFrom,\n  blobFromSync,\n  File,\n  fileFrom,\n  fileFromSync,\n  FormData,\n  Headers,\n  Request,\n  Response,\n} from 'node-fetch'\n\nif (!globalThis.fetch) {\n  globalThis.fetch = fetch\n  globalThis.Headers = Headers\n  globalThis.Request = Request\n  globalThis.Response = Response\n}\n\n// index.js\nimport './fetch-polyfill'\n\n// ...\n```\n\n## Upgrading\n\nUsing an old version of node-fetch? Check out the following files:\n\n- [2.x to 3.x upgrade guide](docs/v3-UPGRADE-GUIDE.md)\n- [1.x to 2.x upgrade guide](docs/v2-UPGRADE-GUIDE.md)\n- [Changelog](https://github.com/node-fetch/node-fetch/releases)\n\n## Common Usage\n\nNOTE: The documentation below is up-to-date with `3.x` releases, if you are using an older version, please check how to [upgrade](#upgrading).\n\n### Plain text or HTML\n\n```js\nimport fetch from 'node-fetch';\n\nconst response = await fetch('https://github.com/');\nconst body = await response.text();\n\nconsole.log(body);\n```\n\n### JSON\n\n```js\nimport fetch from 'node-fetch';\n\nconst response = await fetch('https://api.github.com/users/github');\nconst data = await response.json();\n\nconsole.log(data);\n```\n\n### Simple Post\n\n```js\nimport fetch from 'node-fetch';\n\nconst response = await fetch('https://httpbin.org/post', {method: 'POST', body: 'a=1'});\nconst data = await response.json();\n\nconsole.log(data);\n```\n\n### Post with JSON\n\n```js\nimport fetch from 'node-fetch';\n\nconst body = {a: 1};\n\nconst response = await fetch('https://httpbin.org/post', {\n\tmethod: 'post',\n\tbody: JSON.stringify(body),\n\theaders: {'Content-Type': 'application/json'}\n});\nconst data = await response.json();\n\nconsole.log(data);\n```\n\n### Post with form parameters\n\n`URLSearchParams` is available on the global object in Node.js as of v10.0.0. See [official documentation](https://nodejs.org/api/url.html#url_class_urlsearchparams) for more usage methods.\n\nNOTE: The `Content-Type` header is only set automatically to `x-www-form-urlencoded` when an instance of `URLSearchParams` is given as such:\n\n```js\nimport fetch from 'node-fetch';\n\nconst params = new URLSearchParams();\nparams.append('a', 1);\n\nconst response = await fetch('https://httpbin.org/post', {method: 'POST', body: params});\nconst data = await response.json();\n\nconsole.log(data);\n```\n\n### Handling exceptions\n\nNOTE: 3xx-5xx responses are _NOT_ exceptions, and should be handled in `then()`, see the next section.\n\nWrapping the fetch function into a `try/catch` block will catch _all_ exceptions, such as errors originating from node core libraries, like network errors, and operational errors which are instances of FetchError. See the [error handling document][error-handling.md] for more details.\n\n```js\nimport fetch from 'node-fetch';\n\ntry {\n\tawait fetch('https://domain.invalid/');\n} catch (error) {\n\tconsole.log(error);\n}\n```\n\n### Handling client and server errors\n\nIt is common to create a helper function to check that the response contains no client (4xx) or server (5xx) error responses:\n\n```js\nimport fetch from 'node-fetch';\n\nclass HTTPResponseError extends Error {\n\tconstructor(response) {\n\t\tsuper(`HTTP Error Response: ${response.status} ${response.statusText}`);\n\t\tthis.response = response;\n\t}\n}\n\nconst checkStatus = response => {\n\tif (response.ok) {\n\t\t// response.status >= 200 && response.status < 300\n\t\treturn response;\n\t} else {\n\t\tthrow new HTTPResponseError(response);\n\t}\n}\n\nconst response = await fetch('https://httpbin.org/status/400');\n\ntry {\n\tcheckStatus(response);\n} catch (error) {\n\tconsole.error(error);\n\n\tconst errorBody = await error.response.text();\n\tconsole.error(`Error body: ${errorBody}`);\n}\n```\n\n### Handling cookies\n\nCookies are not stored by default. However, cookies can be extracted and passed by manipulating request and response headers. See [Extract Set-Cookie Header](#extract-set-cookie-header) for details.\n\n## Advanced Usage\n\n### Streams\n\nThe \"Node.js way\" is to use streams when possible. You can pipe `res.body` to another stream. This example uses [stream.pipeline](https://nodejs.org/api/stream.html#stream_stream_pipeline_streams_callback) to attach stream error handlers and wait for the download to complete.\n\n```js\nimport {createWriteStream} from 'node:fs';\nimport {pipeline} from 'node:stream';\nimport {promisify} from 'node:util'\nimport fetch from 'node-fetch';\n\nconst streamPipeline = promisify(pipeline);\n\nconst response = await fetch('https://github.githubassets.com/images/modules/logos_page/Octocat.png');\n\nif (!response.ok) throw new Error(`unexpected response ${response.statusText}`);\n\nawait streamPipeline(response.body, createWriteStream('./octocat.png'));\n```\n\nIn Node.js 14 you can also use async iterators to read `body`; however, be careful to catch\nerrors -- the longer a response runs, the more likely it is to encounter an error.\n\n```js\nimport fetch from 'node-fetch';\n\nconst response = await fetch('https://httpbin.org/stream/3');\n\ntry {\n\tfor await (const chunk of response.body) {\n\t\tconsole.dir(JSON.parse(chunk.toString()));\n\t}\n} catch (err) {\n\tconsole.error(err.stack);\n}\n```\n\nIn Node.js 12 you can also use async iterators to read `body`; however, async iterators with streams\ndid not mature until Node.js 14, so you need to do some extra work to ensure you handle errors\ndirectly from the stream and wait on it response to fully close.\n\n```js\nimport fetch from 'node-fetch';\n\nconst read = async body => {\n\tlet error;\n\tbody.on('error', err => {\n\t\terror = err;\n\t});\n\n\tfor await (const chunk of body) {\n\t\tconsole.dir(JSON.parse(chunk.toString()));\n\t}\n\n\treturn new Promise((resolve, reject) => {\n\t\tbody.on('close', () => {\n\t\t\terror ? reject(error) : resolve();\n\t\t});\n\t});\n};\n\ntry {\n\tconst response = await fetch('https://httpbin.org/stream/3');\n\tawait read(response.body);\n} catch (err) {\n\tconsole.error(err.stack);\n}\n```\n\n### Accessing Headers and other Metadata\n\n```js\nimport fetch from 'node-fetch';\n\nconst response = await fetch('https://github.com/');\n\nconsole.log(response.ok);\nconsole.log(response.status);\nconsole.log(response.statusText);\nconsole.log(response.headers.raw());\nconsole.log(response.headers.get('content-type'));\n```\n\n### Extract Set-Cookie Header\n\nUnlike browsers, you can access raw `Set-Cookie` headers manually using `Headers.raw()`. This is a `node-fetch` only API.\n\n```js\nimport fetch from 'node-fetch';\n\nconst response = await fetch('https://example.com');\n\n// Returns an array of values, instead of a string of comma-separated values\nconsole.log(response.headers.raw()['set-cookie']);\n```\n\n### Post data using a file\n\n```js\nimport fetch, {\n  Blob,\n  blobFrom,\n  blobFromSync,\n  File,\n  fileFrom,\n  fileFromSync,\n} from 'node-fetch'\n\nconst mimetype = 'text/plain'\nconst blob = fileFromSync('./input.txt', mimetype)\nconst url = 'https://httpbin.org/post'\n\nconst response = await fetch(url, { method: 'POST', body: blob })\nconst data = await response.json()\n\nconsole.log(data)\n```\n\nnode-fetch comes with a spec-compliant [FormData] implementations for posting\nmultipart/form-data payloads\n\n```js\nimport fetch, { FormData, File, fileFrom } from 'node-fetch'\n\nconst httpbin = 'https://httpbin.org/post'\nconst formData = new FormData()\nconst binary = new Uint8Array([ 97, 98, 99 ])\nconst abc = new File([binary], 'abc.txt', { type: 'text/plain' })\n\nformData.set('greeting', 'Hello, world!')\nformData.set('file-upload', abc, 'new name.txt')\n\nconst response = await fetch(httpbin, { method: 'POST', body: formData })\nconst data = await response.json()\n\nconsole.log(data)\n```\n\nIf you for some reason need to post a stream coming from any arbitrary place,\nthen you can append a [Blob] or a [File] look-a-like item.\n\nThe minimum requirement is that it has:\n1. A `Symbol.toStringTag` getter or property that is either `Blob` or `File`\n2. A known size.\n3. And either a `stream()` method or a `arrayBuffer()` method that returns a ArrayBuffer.\n\nThe `stream()` must return any async iterable object as long as it yields Uint8Array (or Buffer)\nso Node.Readable streams and whatwg streams works just fine.\n\n```js\nformData.append('upload', {\n\t[Symbol.toStringTag]: 'Blob',\n\tsize: 3,\n  *stream() {\n    yield new Uint8Array([97, 98, 99])\n\t},\n\tarrayBuffer() {\n\t\treturn new Uint8Array([97, 98, 99]).buffer\n\t}\n}, 'abc.txt')\n```\n\n### Request cancellation with AbortSignal\n\nYou may cancel requests with `AbortController`. A suggested implementation is [`abort-controller`](https://www.npmjs.com/package/abort-controller).\n\nAn example of timing out a request after 150ms could be achieved as the following:\n\n```js\nimport fetch, { AbortError } from 'node-fetch';\n\n// AbortController was added in node v14.17.0 globally\nconst AbortController = globalThis.AbortController || await import('abort-controller')\n\nconst controller = new AbortController();\nconst timeout = setTimeout(() => {\n\tcontroller.abort();\n}, 150);\n\ntry {\n\tconst response = await fetch('https://example.com', {signal: controller.signal});\n\tconst data = await response.json();\n} catch (error) {\n\tif (error instanceof AbortError) {\n\t\tconsole.log('request was aborted');\n\t}\n} finally {\n\tclearTimeout(timeout);\n}\n```\n\nSee [test cases](https://github.com/node-fetch/node-fetch/blob/master/test/) for more examples.\n\n## API\n\n### fetch(url[, options])\n\n- `url` A string representing the URL for fetching\n- `options` [Options](#fetch-options) for the HTTP(S) request\n- Returns: <code>Promise&lt;[Response](#class-response)&gt;</code>\n\nPerform an HTTP(S) fetch.\n\n`url` should be an absolute URL, such as `https://example.com/`. A path-relative URL (`/file/under/root`) or protocol-relative URL (`//can-be-http-or-https.com/`) will result in a rejected `Promise`.\n\n<a id=\"fetch-options\"></a>\n\n### Options\n\nThe default values are shown after each option key.\n\n```js\n{\n\t// These properties are part of the Fetch Standard\n\tmethod: 'GET',\n\theaders: {},            // Request headers. format is the identical to that accepted by the Headers constructor (see below)\n\tbody: null,             // Request body. can be null, or a Node.js Readable stream\n\tredirect: 'follow',     // Set to `manual` to extract redirect headers, `error` to reject redirect\n\tsignal: null,           // Pass an instance of AbortSignal to optionally abort requests\n\n\t// The following properties are node-fetch extensions\n\tfollow: 20,             // maximum redirect count. 0 to not follow redirect\n\tcompress: true,         // support gzip/deflate content encoding. false to disable\n\tsize: 0,                // maximum response body size in bytes. 0 to disable\n\tagent: null,            // http(s).Agent instance or function that returns an instance (see below)\n\thighWaterMark: 16384,   // the maximum number of bytes to store in the internal buffer before ceasing to read from the underlying resource.\n\tinsecureHTTPParser: false\t// Use an insecure HTTP parser that accepts invalid HTTP headers when `true`.\n}\n```\n\n#### Default Headers\n\nIf no values are set, the following request headers will be sent automatically:\n\n| Header              | Value                                                  |\n| ------------------- | ------------------------------------------------------ |\n| `Accept-Encoding`   | `gzip, deflate, br` (when `options.compress === true`) |\n| `Accept`            | `*/*`                                                  |\n| `Content-Length`    | _(automatically calculated, if possible)_              |\n| `Host`              | _(host and port information from the target URI)_      |\n| `Transfer-Encoding` | `chunked` _(when `req.body` is a stream)_              |\n| `User-Agent`        | `node-fetch`                                           |\n\n\nNote: when `body` is a `Stream`, `Content-Length` is not set automatically.\n\n#### Custom Agent\n\nThe `agent` option allows you to specify networking related options which are out of the scope of Fetch, including and not limited to the following:\n\n- Support self-signed certificate\n- Use only IPv4 or IPv6\n- Custom DNS Lookup\n\nSee [`http.Agent`](https://nodejs.org/api/http.html#http_new_agent_options) for more information.\n\nIf no agent is specified, the default agent provided by Node.js is used. Note that [this changed in Node.js 19](https://github.com/nodejs/node/blob/4267b92604ad78584244488e7f7508a690cb80d0/lib/_http_agent.js#L564) to have `keepalive` true by default. If you wish to enable `keepalive` in an earlier version of Node.js, you can override the agent as per the following code sample. \n\nIn addition, the `agent` option accepts a function that returns `http`(s)`.Agent` instance given current [URL](https://nodejs.org/api/url.html), this is useful during a redirection chain across HTTP and HTTPS protocol.\n\n```js\nimport http from 'node:http';\nimport https from 'node:https';\n\nconst httpAgent = new http.Agent({\n\tkeepAlive: true\n});\nconst httpsAgent = new https.Agent({\n\tkeepAlive: true\n});\n\nconst options = {\n\tagent: function(_parsedURL) {\n\t\tif (_parsedURL.protocol == 'http:') {\n\t\t\treturn httpAgent;\n\t\t} else {\n\t\t\treturn httpsAgent;\n\t\t}\n\t}\n};\n```\n\n<a id=\"custom-highWaterMark\"></a>\n\n#### Custom highWaterMark\n\nStream on Node.js have a smaller internal buffer size (16kB, aka `highWaterMark`) from client-side browsers (>1MB, not consistent across browsers). Because of that, when you are writing an isomorphic app and using `res.clone()`, it will hang with large response in Node.\n\nThe recommended way to fix this problem is to resolve cloned response in parallel:\n\n```js\nimport fetch from 'node-fetch';\n\nconst response = await fetch('https://example.com');\nconst r1 = response.clone();\n\nconst results = await Promise.all([response.json(), r1.text()]);\n\nconsole.log(results[0]);\nconsole.log(results[1]);\n```\n\nIf for some reason you don't like the solution above, since `3.x` you are able to modify the `highWaterMark` option:\n\n```js\nimport fetch from 'node-fetch';\n\nconst response = await fetch('https://example.com', {\n\t// About 1MB\n\thighWaterMark: 1024 * 1024\n});\n\nconst result = await res.clone().arrayBuffer();\nconsole.dir(result);\n```\n\n#### Insecure HTTP Parser\n\nPassed through to the `insecureHTTPParser` option on http(s).request. See [`http.request`](https://nodejs.org/api/http.html#http_http_request_url_options_callback) for more information.\n\n#### Manual Redirect\n\nThe `redirect: 'manual'` option for node-fetch is different from the browser & specification, which\nresults in an [opaque-redirect filtered response](https://fetch.spec.whatwg.org/#concept-filtered-response-opaque-redirect).\nnode-fetch gives you the typical [basic filtered response](https://fetch.spec.whatwg.org/#concept-filtered-response-basic) instead.\n\n```js\nimport fetch from 'node-fetch';\n\nconst response = await fetch('https://httpbin.org/status/301', { redirect: 'manual' });\n\nif (response.status === 301 || response.status === 302) {\n\tconst locationURL = new URL(response.headers.get('location'), response.url);\n\tconst response2 = await fetch(locationURL, { redirect: 'manual' });\n\tconsole.dir(response2);\n}\n```\n\n<a id=\"class-request\"></a>\n\n### Class: Request\n\nAn HTTP(S) request containing information about URL, method, headers, and the body. This class implements the [Body](#iface-body) interface.\n\nDue to the nature of Node.js, the following properties are not implemented at this moment:\n\n- `type`\n- `destination`\n- `mode`\n- `credentials`\n- `cache`\n- `integrity`\n- `keepalive`\n\nThe following node-fetch extension properties are provided:\n\n- `follow`\n- `compress`\n- `counter`\n- `agent`\n- `highWaterMark`\n\nSee [options](#fetch-options) for exact meaning of these extensions.\n\n#### new Request(input[, options])\n\n<small>_(spec-compliant)_</small>\n\n- `input` A string representing a URL, or another `Request` (which will be cloned)\n- `options` [Options](#fetch-options) for the HTTP(S) request\n\nConstructs a new `Request` object. The constructor is identical to that in the [browser](https://developer.mozilla.org/en-US/docs/Web/API/Request/Request).\n\nIn most cases, directly `fetch(url, options)` is simpler than creating a `Request` object.\n\n<a id=\"class-response\"></a>\n\n### Class: Response\n\nAn HTTP(S) response. This class implements the [Body](#iface-body) interface.\n\nThe following properties are not implemented in node-fetch at this moment:\n\n- `trailer`\n\n#### new Response([body[, options]])\n\n<small>_(spec-compliant)_</small>\n\n- `body` A `String` or [`Readable` stream][node-readable]\n- `options` A [`ResponseInit`][response-init] options dictionary\n\nConstructs a new `Response` object. The constructor is identical to that in the [browser](https://developer.mozilla.org/en-US/docs/Web/API/Response/Response).\n\nBecause Node.js does not implement service workers (for which this class was designed), one rarely has to construct a `Response` directly.\n\n#### response.ok\n\n<small>_(spec-compliant)_</small>\n\nConvenience property representing if the request ended normally. Will evaluate to true if the response status was greater than or equal to 200 but smaller than 300.\n\n#### response.redirected\n\n<small>_(spec-compliant)_</small>\n\nConvenience property representing if the request has been redirected at least once. Will evaluate to true if the internal redirect counter is greater than 0.\n\n#### response.type\n\n<small>_(deviation from spec)_</small>\n\nConvenience property representing the response's type. node-fetch only supports `'default'` and `'error'` and does not make use of [filtered responses](https://fetch.spec.whatwg.org/#concept-filtered-response).\n\n<a id=\"class-headers\"></a>\n\n### Class: Headers\n\nThis class allows manipulating and iterating over a set of HTTP headers. All methods specified in the [Fetch Standard][whatwg-fetch] are implemented.\n\n#### new Headers([init])\n\n<small>_(spec-compliant)_</small>\n\n- `init` Optional argument to pre-fill the `Headers` object\n\nConstruct a new `Headers` object. `init` can be either `null`, a `Headers` object, an key-value map object or any iterable object.\n\n```js\n// Example adapted from https://fetch.spec.whatwg.org/#example-headers-class\nimport {Headers} from 'node-fetch';\n\nconst meta = {\n\t'Content-Type': 'text/xml'\n};\nconst headers = new Headers(meta);\n\n// The above is equivalent to\nconst meta = [['Content-Type', 'text/xml']];\nconst headers = new Headers(meta);\n\n// You can in fact use any iterable objects, like a Map or even another Headers\nconst meta = new Map();\nmeta.set('Content-Type', 'text/xml');\nconst headers = new Headers(meta);\nconst copyOfHeaders = new Headers(headers);\n```\n\n<a id=\"iface-body\"></a>\n\n### Interface: Body\n\n`Body` is an abstract interface with methods that are applicable to both `Request` and `Response` classes.\n\n#### body.body\n\n<small>_(deviation from spec)_</small>\n\n- Node.js [`Readable` stream][node-readable]\n\nData are encapsulated in the `Body` object. Note that while the [Fetch Standard][whatwg-fetch] requires the property to always be a WHATWG `ReadableStream`, in node-fetch it is a Node.js [`Readable` stream][node-readable].\n\n#### body.bodyUsed\n\n<small>_(spec-compliant)_</small>\n\n- `Boolean`\n\nA boolean property for if this body has been consumed. Per the specs, a consumed body cannot be used again.\n\n#### body.arrayBuffer()\n\n#### body.formData()\n\n#### body.blob()\n\n#### body.json()\n\n#### body.text()\n\n`fetch` comes with methods to parse `multipart/form-data` payloads as well as\n`x-www-form-urlencoded` bodies using `.formData()` this comes from the idea that\nService Worker can intercept such messages before it's sent to the server to\nalter them. This is useful for anybody building a server so you can use it to\nparse & consume payloads.\n\n<details>\n<summary>Code example</summary>\n\n```js\nimport http from 'node:http'\nimport { Response } from 'node-fetch'\n\nhttp.createServer(async function (req, res) {\n  const formData = await new Response(req, {\n    headers: req.headers // Pass along the boundary value\n  }).formData()\n  const allFields = [...formData]\n\n  const file = formData.get('uploaded-files')\n  const arrayBuffer = await file.arrayBuffer()\n  const text = await file.text()\n  const whatwgReadableStream = file.stream()\n\n  // other was to consume the request could be to do:\n  const json = await new Response(req).json()\n  const text = await new Response(req).text()\n  const arrayBuffer = await new Response(req).arrayBuffer()\n  const blob = await new Response(req, {\n    headers: req.headers // So that `type` inherits `Content-Type`\n  }.blob()\n})\n```\n\n</details>\n\n<a id=\"class-fetcherror\"></a>\n\n### Class: FetchError\n\n<small>_(node-fetch extension)_</small>\n\nAn operational error in the fetching process. See [ERROR-HANDLING.md][] for more info.\n\n<a id=\"class-aborterror\"></a>\n\n### Class: AbortError\n\n<small>_(node-fetch extension)_</small>\n\nAn Error thrown when the request is aborted in response to an `AbortSignal`'s `abort` event. It has a `name` property of `AbortError`. See [ERROR-HANDLING.MD][] for more info.\n\n## TypeScript\n\n**Since `3.x` types are bundled with `node-fetch`, so you don't need to install any additional packages.**\n\nFor older versions please use the type definitions from [DefinitelyTyped](https://github.com/DefinitelyTyped/DefinitelyTyped):\n\n```sh\nnpm install --save-dev @types/node-fetch@2.x\n```\n\n## Acknowledgement\n\nThanks to [github/fetch](https://github.com/github/fetch) for providing a solid implementation reference.\n\n## Team\n\n| [![David Frank](https://github.com/bitinn.png?size=100)](https://github.com/bitinn) | [![Jimmy Wärting](https://github.com/jimmywarting.png?size=100)](https://github.com/jimmywarting) | [![Antoni Kepinski](https://github.com/xxczaki.png?size=100)](https://github.com/xxczaki) | [![Richie Bendall](https://github.com/Richienb.png?size=100)](https://github.com/Richienb) | [![Gregor Martynus](https://github.com/gr2m.png?size=100)](https://github.com/gr2m) |\n| ----------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------- |\n| [David Frank](https://bitinn.net/)                                                  | [Jimmy Wärting](https://jimmy.warting.se/)                                                        | [Antoni Kepinski](https://kepinski.ch)                                                    | [Richie Bendall](https://www.richie-bendall.ml/)                                           | [Gregor Martynus](https://twitter.com/gr2m)                                         |\n\n###### Former\n\n- [Timothy Gu](https://github.com/timothygu)\n- [Jared Kantrowitz](https://github.com/jkantr)\n\n## License\n\n[MIT](LICENSE.md)\n\n[whatwg-fetch]: https://fetch.spec.whatwg.org/\n[response-init]: https://fetch.spec.whatwg.org/#responseinit\n[node-readable]: https://nodejs.org/api/stream.html#stream_readable_streams\n[mdn-headers]: https://developer.mozilla.org/en-US/docs/Web/API/Headers\n[error-handling.md]: https://github.com/node-fetch/node-fetch/blob/master/docs/ERROR-HANDLING.md\n[FormData]: https://developer.mozilla.org/en-US/docs/Web/API/FormData\n[Blob]: https://developer.mozilla.org/en-US/docs/Web/API/Blob\n[File]: https://developer.mozilla.org/en-US/docs/Web/API/File\n", "time": {"created": "2022-01-26T12:52:52.080Z", "modified": "2025-06-05T10:30:38.503Z", "4.0.0-beta.2": "2022-01-27T22:40:18.797Z", "4.0.0-beta.3": "2022-01-27T22:42:58.079Z", "4.0.0-beta.4": "2022-01-27T22:45:21.768Z", "4.0.0-beta.1": "2022-01-27T00:27:50.280Z", "3.1.0": "2021-11-08T18:16:57.641Z", "2.6.6": "2021-10-31T15:49:13.781Z", "2.6.5": "2021-09-22T12:53:43.259Z", "2.6.4": "2021-09-21T12:38:53.828Z", "2.6.3": "2021-09-20T21:52:54.550Z", "2.6.2": "2021-09-06T13:05:31.143Z", "3.0.0": "2021-08-31T16:22:24.563Z", "3.0.0-beta.10": "2021-07-19T08:41:01.602Z", "2.6.1": "2020-09-05T13:00:44.110Z", "3.0.0-beta.9": "2020-09-05T12:52:27.791Z", "3.0.0-beta.8": "2020-08-10T08:37:24.456Z", "3.0.0-beta.7": "2020-06-11T09:31:08.811Z", "3.0.0-beta.6-exportfix": "2020-05-25T17:30:35.066Z", "3.0.0-beta.6": "2020-05-25T15:21:06.740Z", "3.0.0-beta.5": "2020-04-22T09:42:44.001Z", "3.0.0-beta.4": "2020-03-14T05:58:10.748Z", "3.0.0-beta.3": "2020-03-13T19:10:06.375Z", "3.0.0-beta.2": "2020-03-13T19:01:52.964Z", "3.0.0-beta.1": "2020-03-13T15:13:38.280Z", "2.6.0": "2019-05-16T06:39:41.004Z", "2.5.0": "2019-05-01T05:08:07.621Z", "2.4.1": "2019-04-27T07:07:42.071Z", "2.4.0": "2019-04-26T17:24:20.869Z", "2.3.0": "2018-11-13T06:41:56.187Z", "2.2.1": "2018-11-05T09:44:41.523Z", "2.2.0": "2018-07-22T21:32:05.747Z", "2.1.2": "2018-03-25T20:55:00.498Z", "2.1.1": "2018-03-05T04:44:09.059Z", "2.1.0": "2018-03-05T04:41:12.676Z", "2.0.0": "2018-02-03T20:34:24.774Z", "2.0.0-alpha.9": "2017-09-24T05:47:43.171Z", "1.7.3": "2017-09-08T06:28:18.255Z", "1.7.2": "2017-08-08T11:23:01.450Z", "2.0.0-alpha.8": "2017-07-26T17:11:52.034Z", "2.0.0-alpha.7": "2017-07-25T04:11:01.517Z", "2.0.0-alpha.6": "2017-07-21T18:04:31.572Z", "2.0.0-alpha.5": "2017-06-03T13:05:26.072Z", "1.7.1": "2017-06-03T12:44:58.110Z", "1.7.0": "2017-05-23T08:24:31.812Z", "2.0.0-alpha.4": "2017-05-15T12:16:58.709Z", "2.0.0-alpha.3": "2017-01-29T18:36:09.254Z", "2.0.0-alpha.1": "2017-01-15T05:37:22.197Z", "1.6.3": "2016-09-26T06:20:12.535Z", "1.6.2": "2016-09-24T09:53:47.594Z", "1.6.1": "2016-09-11T15:38:02.062Z", "1.6.0": "2016-08-03T10:10:34.469Z", "1.5.3": "2016-05-25T18:54:43.665Z", "1.5.2": "2016-05-06T12:13:02.250Z", "1.5.1": "2016-04-12T18:58:56.981Z", "1.5.0": "2016-04-05T19:05:25.946Z", "1.4.1": "2016-03-23T07:33:18.873Z", "1.4.0": "2016-03-19T11:02:46.951Z", "1.3.3": "2015-09-28T15:15:17.294Z", "1.3.2": "2015-07-22T07:53:11.928Z", "1.3.1": "2015-07-11T11:45:04.398Z", "1.3.0": "2015-06-04T04:43:32.007Z", "1.2.1": "2015-05-04T04:08:19.210Z", "1.2.0": "2015-05-03T10:07:55.396Z", "1.1.2": "2015-04-29T04:18:18.100Z", "1.1.1": "2015-04-22T15:36:35.892Z", "1.1.0": "2015-04-17T05:33:19.652Z", "1.0.6": "2015-03-24T04:31:46.736Z", "1.0.5": "2015-03-19T17:02:57.089Z", "1.0.4": "2015-02-06T11:21:10.299Z", "1.0.3": "2015-01-28T15:43:47.362Z", "1.0.2": "2015-01-28T05:05:24.596Z", "1.0.1": "2015-01-27T18:07:00.334Z", "1.0.0": "2015-01-27T17:38:21.770Z", "0.1.0": "2015-01-27T13:17:06.232Z", "2.6.7": "2022-01-16T12:18:38.923Z", "3.1.1": "2022-01-16T12:28:10.545Z", "3.2.0": "2022-01-20T20:43:19.191Z", "3.2.1": "2022-03-01T09:54:58.523Z", "3.2.2": "2022-03-07T10:57:08.718Z", "3.2.3": "2022-03-11T21:49:52.954Z", "3.2.4": "2022-04-28T09:34:06.072Z", "3.2.5": "2022-06-01T19:44:31.838Z", "3.2.6": "2022-06-09T10:55:35.738Z", "3.2.7": "2022-07-11T22:46:01.774Z", "3.2.8": "2022-07-12T16:32:22.886Z", "3.2.9": "2022-07-18T15:21:10.989Z", "3.2.10": "2022-07-31T08:02:25.368Z", "3.3.0": "2022-11-10T21:47:30.429Z", "2.6.8": "2023-01-13T01:04:20.236Z", "2.6.9": "2023-01-30T22:00:06.583Z", "3.3.1": "2023-03-11T10:47:49.391Z", "2.6.10": "2023-05-08T16:20:45.981Z", "2.6.11": "2023-05-09T11:06:32.325Z", "2.6.12": "2023-06-29T19:16:33.256Z", "3.3.2": "2023-07-25T11:50:17.626Z", "2.6.13": "2023-08-18T20:24:16.578Z", "2.7.0": "2023-08-23T17:18:39.396Z"}, "versions": {"4.0.0-beta.2": {"name": "node-fetch", "version": "4.0.0-beta.2", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^14.17.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "1c775f16e50724ea6721e9f7482351341eb1b198", "_id": "node-fetch@4.0.0-beta.2", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-cst8faTrxwqXAPcXfaPvfL22hIq+F+2hYl2ORAgXWWN1kd8zdB2DvL3nlSUYdYVlP4RPVHyU2q4YehbZFAsY0g==", "shasum": "b00dac03861ad9948fc4c6bea9fbd3527cb5ab6d", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-4.0.0-beta.2.tgz", "fileCount": 17, "unpackedSize": 105340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8x9SCRA9TVsSAnZWagAAIV0P/iB15Dzj65JHB/gAXMqk\nmobzZEe1yOpjZ1R59975RLLQoPCFsMEzCPSVDs7sDmB1X6/wFo6QXUVifutm\n2Fo5SSlmgZNr+8ONy64R+LDJLlniPL/BFSnngExgDTVwuDnAY/zgHos9CeHf\ncCGdXnXk1bdV7ylzJHxDLruNvnpBIgIPbMKuPqG/XFyPILOwAxCEdMNWY0IY\numHcLNyb9so96xUsDrC46ZeAJmy83rLUkchxo9sRpDXOjbPdRhE9KTO2XBV6\n6L63Y0SmsixnxY8MUX9ouByORGAEJa2KAh/I/tglyNMJe0Qu+cX/EEQyTTEk\nhwoDkcU+NlwVbsYaGBi8HeQSE+otBANAZDyo+G34MCotor/ca12kCMVJ/fqM\n0ubT9j/G1TmhaJDVdnhbJIdPTMFecRoU7BoW8QldGxPXrPnS+9r4vZByO1Rf\nP+pJDNec891kBOsB6A5Tka/JBwUMqhsIqvl/zZX/BnbhgAxKTnWX1cO5z07x\nSCaNsNPi+dxuilFeSRbEunCgNIxhDLyoK2ZXw76XSzVJv8YkNdRWCMUhFAKH\nHtAn/K/WC102FTeR+rcKMkeNg7GsXoegLz5aqpTjcHUpROjt7jLqaQHboZct\nS6y64MN1yJM45bVPYsoIb9TizhgWuzflnnrz0FXW+BMg1TOsFgeFKD/wGfmS\nwoAU\r\n=XyFg\r\n-----END PGP SIGNATURE-----\r\n", "size": 31211}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_4.0.0-beta.2_1643323218614_0.1556713475747067"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-27T22:40:37.653Z"}, "4.0.0-beta.3": {"name": "node-fetch", "version": "4.0.0-beta.3", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^14.17.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "fcd8bdd6d93402df728f90a8b67adec1ce5ea1ab", "_id": "node-fetch@4.0.0-beta.3", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-+U+bLwL6hgMipn3joPtojndRbTBdi0yNCxPVgU6sS9N4GmX4ZPo1rI/7Rp/Cw3/bjv0ItVaCNgjkDM+3Hh9cBQ==", "shasum": "36920e2b17041d000f3b0c13ad9624c9f31b575d", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-4.0.0-beta.3.tgz", "fileCount": 17, "unpackedSize": 105259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8x/yCRA9TVsSAnZWagAANA8P/1VhEnRXkH3dxXXrzMvW\nUBPSgZe0VPTyj6eXiepdDolG8CQQ01Oc8Xi7c66S6gISmWxlAyuceLvpW/e+\ndTsJI5+KqqJK2mNZnAGLGGIxZglbFWDVGfFTv5d6kj9w4oi2BxhcLhlyVJeR\nCyyc1zQLuxIYO79pcIUymhhaIrVpnIj8hiYxJJFV8l4pY8htb3MspF250E09\ntUWURSvcIobPPbB62VsYPCSU+g1Bd088db4PeMeBF9c21OEDhG/Ae4aUiLXK\nqBHrnJhKP7JbOnR54hkeufSZMGLIT4vcdVj16LnE6V9yuM+Ggvs0VGTmYJxg\ntnFs9Z1OdPAVTLu2aC5qvcU02nvksvVe8zGr9c89948ujalJmbTH48TEaBY7\nHMut6gVZMl9saKe5hh7I3p+JNbItzHKjS9FBijBeu9fTwUIH5EeKddLEP+4H\n2yCPke0bM7SyGHtkWc8o1Z+O2NwWbwrXSoUi6kUeunJFnHD1HPBgM3QCPOEI\nGZfzfOAQezFXiXmizLYVA2UHtsaqw6N0dSXe/SSMh95gOhOWU8Q2u435x8We\njCDPeRj2ci86ZdyndvlaBY69kCyvPxyg+013LJzuDpv3SjqqOc+Cs6CWssrL\nvD6+Kuth7IDxBf1MRZa48z8Mrfma6Lw12G92Yk6Gt48i6eygxy9F0oL5gt2Y\naDEn\r\n=PbBF\r\n-----END PGP SIGNATURE-----\r\n", "size": 31204}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_4.0.0-beta.3_1643323377903_0.18754722639862065"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-27T22:44:33.446Z"}, "4.0.0-beta.4": {"name": "node-fetch", "version": "4.0.0-beta.4", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^14.17.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "bd3c4844dae343977d4f240aa9d0f7a3c0a0da12", "_id": "node-fetch@4.0.0-beta.4", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-R+axlY/crCwhwOKPw9d0SxHUhzA2jEXkasDdooL6Z1pJt2y22SrIsGKaO3ZtIF2uE+DruGU6tOZchgQRTQ8keQ==", "shasum": "edd784c33e8c795bf4dd2bf0b90e2d10e1e6c0ab", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-4.0.0-beta.4.tgz", "fileCount": 17, "unpackedSize": 104439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8yCBCRA9TVsSAnZWagAAMXkP+QGbk1BQVg2wpOhOFIHR\nYsy4rlwlbHiHmONEpG5JNlUYwOUG3lvogzAIf+V9RG5XaXpmjQb5n2DFSMbH\nUd75f54S0NCOxwDmt/FQRt+Edokn4oMBst0jshUfy6GNTX8qUt60sZ1ZxD20\nGG1vegWmEkw3zordQM5z8zjBNxLPeOUbMGf8l2WO0mt4h7a37OmzJGFChXrD\nN6caf8VHGm0TRng+lVPkXoID5+8AbYFnjBb6WT0tdhNr6XOFdrQAEX1OPAch\nwii5pyBmQb9z+qCmlp0xX0615BtSoxSnZpJAw3XXW30qEo8gjftvDgzXurz7\nJNDHpJuoOijOERXamLZY/uf4WlhSzscdn8XypGI86gw/eybviWU3Ga/wikkE\nbBu3shnm+glL8S4KNB8raNU3uj+tLXDMmpQT6Lm7eEKkSWkiohrdw1CGSh2+\nVX+RL7b6xGFsJKRHaqAKYSEzhXKMdTnFqecJZobu9hD6B5oNgWIBnHYnFPF7\nbwJw15jmGIiMEXeQO4tq9zRuzP0KmE3zqpJTX0BSeTPjpmPfVUE7yj77n8E4\nln6aUCy6JLaqBW+tOy75vNIA77YoW/3eJwdrkkdfOQLaur0BYLOWbwIX3wAX\nd360dm/dU2Uk/KNE9gmny+ie8bFUcNBGjq2vHYP3iX5nK8yz6RC4/o8sc3iW\nYXaV\r\n=3mIB\r\n-----END PGP SIGNATURE-----\r\n", "size": 30969}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_4.0.0-beta.4_1643323521623_0.48981533709184544"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-27T22:49:15.559Z"}, "4.0.0-beta.1": {"name": "node-fetch", "version": "4.0.0-beta.1", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^14.17.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "8c1fe4141604793be82120520afc677c1454ed1b", "_id": "node-fetch@4.0.0-beta.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-dK98OjQMiugaYsj6f5fkOR+z26mlGPidUVermaCeKu8nCIyMCjbgPcal1QbGKVoXKAGipSszgzhloiR0kfH50A==", "shasum": "aa9578870652667d38e42e93564d45ee898e26d4", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-4.0.0-beta.1.tgz", "fileCount": 17, "unpackedSize": 105618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8ecGCRA9TVsSAnZWagAA4fAP/R0SHdEgRNmSFVWCiZxk\nksvPAuzkmKNWqrFBVYlDj/7waXMfNxMWwP3Q9hR0HrWseY2dK8rVTJdFdxYp\nx8Zyi4/16vt4tJjjWnmb4Stx2+IQfaUsaSOWCvaq3+CvRgGW373DOvVodZpI\nJSn0wcV+//T92Jqc3DTrvcJmDQ3gxSThjgUFTcpIe20VZ1erlwU36r2rdKSg\n5XZ9p0s6PyvOmTL4S8/8OjY9Cldl62aQ/h2tFTZZtHpHYeeg4T8eoJREIFd7\nkKVRFvcUp+uk63h5bvBvWr4P1XJfyyCl5tqygmFuPJmdrwAMZlZLnxyuUNuc\nye56JJfRRQiLup2fIm/xyMNW+lfEWl7nKSr0AaCCgosj790NjkeV8I+txrsw\nsLQhMMWE1o9Vp5ise5iCTay2mFetTxvs+Qb1PwMJfB1h4kIdpDXyAOCXNHRG\nOgoSM+lR2dmJjVz4qF9WHVR03imEUymBU1Uf1Um+SiAxYXuM4lBnKkNCXESL\n9A6sCkb1sfYr4OZL0S0hPoJbqFlAWjdLbdz9jYUTXPtTLWxtDnNNY7F6T2k9\nJLT0r6CsrdcVcUtwoE0BYV+/25mV/i8rzSObvtYuQd72wUHbH6bxy2DqPfsF\nAl7YYDS2gXNtNpWKYzWG7g3hvQTJOXHZpAAcXoK1Sv7m1te+sToIJfLwP2sO\ndr/y\r\n=gtGW\r\n-----END PGP SIGNATURE-----\r\n", "size": 31287}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_4.0.0-beta.1_1643243270114_0.23131160202884615"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-27T00:27:58.752Z"}, "3.1.0": {"name": "node-fetch", "version": "3.1.0", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^0.3.1", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "delay": "^5.0.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "formdata-polyfill": "^4.0.10", "fetch-blob": "^3.1.2"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "gitHead": "109bd21313c277f043089f8c38b1a716c39ff86f", "_id": "node-fetch@3.1.0", "_nodeVersion": "17.0.1", "_npmVersion": "8.1.0", "dist": {"shasum": "714f4922dc270239487654eaeeab86b8206cb52e", "size": 29649, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.1.0.tgz", "integrity": "sha512-QU0WbIfMUjd5+MUzQOYhenAazakV7Irh1SGkWCsRzBwvm4fAhzEUaHMJ6QLP7gWT6WO9/oH2zhKMMGMuIrDyKw=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.1.0_1636395417407_0.8574820247956818"}, "_hasShrinkwrap": false, "publish_time": 1636395417641, "_cnpm_publish_time": 1636395417641, "_cnpmcore_publish_time": "2021-12-13T13:13:06.990Z"}, "2.6.6": {"name": "node-fetch", "version": "2.6.6", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "readmeFilename": "README.md", "gitHead": "f56b0c66d3dd2ef185436de1f2fd40f66bfea8f4", "_id": "node-fetch@2.6.6", "_nodeVersion": "17.0.1", "_npmVersion": "8.1.0", "dist": {"shasum": "1751a7c01834e8e1697758732e9efb6eeadfaf89", "size": 40891, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.6.tgz", "integrity": "sha512-Z8/6vRlTUChSdIgMa51jxQ4lrw/Jy5SOW10ObaA47/RElsAN2c5Pn8bTgFGWn/ibwzXTE8qwr1Yzx28vsecXEA=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.6_1635695353635_0.06752957373806523"}, "_hasShrinkwrap": false, "publish_time": 1635695353781, "_cnpm_publish_time": 1635695353781, "_cnpmcore_publish_time": "2021-12-13T13:13:07.389Z"}, "2.6.5": {"name": "node-fetch", "version": "2.6.5", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "readmeFilename": "README.md", "gitHead": "b5417aea6a3275932283a200214522e6ab53f1ea", "_id": "node-fetch@2.6.5", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "42735537d7f080a7e5f78b6c549b7146be1742fd", "size": 40882, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.5.tgz", "integrity": "sha512-mmlIVHJEu5rnIxgEgez6b9GgWXbkZj5YZ7fx+2r94a2E+Uirsp6HsPTPlomfdHtpt/B0cdKviwkoaM6pyvUOpQ=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.5_1632315223112_0.3620325715587276"}, "_hasShrinkwrap": false, "publish_time": 1632315223259, "_cnpm_publish_time": 1632315223259, "_cnpmcore_publish_time": "2021-12-13T13:13:07.760Z"}, "2.6.4": {"name": "node-fetch", "version": "2.6.4", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0", "whatwg-url": "^5.0.0"}, "gitHead": "152214ca2f6e2a5a17d71e4638114625d3be30c6", "_id": "node-fetch@2.6.2", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "986996818b73785e47b1965cc34eb093a1d464d0", "size": 40173, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.2.tgz", "integrity": "sha512-aLoxToI6RfZ+0NOjmWAgn9+LEd30YCkJKFSyWacNZdEKTit/ZMcKjGkTRo8uWEsnIb/hfKecNPEbln02PdWbcA=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.2_1630933530982_0.15843731399275662"}, "_hasShrinkwrap": false, "publish_time": 1630933531143, "_cnpm_publish_time": 1630933531143, "_cnpmcore_publish_time": "2021-12-13T13:13:08.847Z", "deprecated": "[WARNING] Use 2.6.2 instead of 2.6.4, reason: https://github.com/node-fetch/node-fetch/pull/1303"}, "2.6.3": {"name": "node-fetch", "version": "2.6.3", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "readmeFilename": "README.md", "gitHead": "ace7536c955556be742d9910566738630cc3c2a6", "_id": "node-fetch@2.6.3", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "57b29b34400e9c52346cbfb575cf3d10f7a49e92", "size": 41047, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.3.tgz", "integrity": "sha512-BXSmNTLLDHT0UjQDg5E23x+0n/hPDjySqc0ELE4NpCa2wE5qmmaEWFRP/+v8pfuocchR9l5vFLbSB7CPE2ahvQ=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.3_1632174774376_0.8151645552950857"}, "_hasShrinkwrap": false, "publish_time": 1632174774550, "deprecated": "v2.6.3 cause bug where query params where not included", "_cnpm_publish_time": 1632174774550, "_cnpmcore_publish_time": "2021-12-13T13:13:08.515Z"}, "2.6.2": {"name": "node-fetch", "version": "2.6.2", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0", "whatwg-url": "^5.0.0"}, "gitHead": "152214ca2f6e2a5a17d71e4638114625d3be30c6", "_id": "node-fetch@2.6.2", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "986996818b73785e47b1965cc34eb093a1d464d0", "size": 40173, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.2.tgz", "integrity": "sha512-aLoxToI6RfZ+0NOjmWAgn9+LEd30YCkJKFSyWacNZdEKTit/ZMcKjGkTRo8uWEsnIb/hfKecNPEbln02PdWbcA=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.2_1630933530982_0.15843731399275662"}, "_hasShrinkwrap": false, "publish_time": 1630933531143, "_cnpm_publish_time": 1630933531143, "_cnpmcore_publish_time": "2021-12-13T13:13:08.847Z"}, "3.0.0": {"name": "node-fetch", "version": "3.0.0", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^0.3.1", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "delay": "^5.0.0", "form-data": "^4.0.0", "formdata-node": "^3.5.4", "mocha": "^8.3.2", "p-timeout": "^5.0.0", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^3.0.1", "fetch-blob": "^3.1.2"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "gitHead": "d6e23a2a85f3364193c1bc85b80ef38c771b090b", "_id": "node-fetch@3.0.0", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"shasum": "79da7146a520036f2c5f644e4a26095f17e411ea", "size": 23132, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0.tgz", "integrity": "sha512-bKMI+C7/T/SPU1lKnbQbwxptpCrG9ashG+VkytmXCPZyuM9jB6VU+hY0oi4lC8LxTtAeWdckNCTa3nrGsAdA3Q=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0_1630426944433_0.49173978227800874"}, "_hasShrinkwrap": false, "publish_time": 1630426944563, "_cnpm_publish_time": 1630426944563, "_cnpmcore_publish_time": "2021-12-13T13:13:09.273Z"}, "3.0.0-beta.10": {"name": "node-fetch", "version": "3.0.0-beta.10", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^0.3.1", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "delay": "^5.0.0", "form-data": "^4.0.0", "formdata-node": "^3.5.4", "mocha": "^8.3.2", "p-timeout": "^5.0.0", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^3.0.1", "fetch-blob": "^3.1.2"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "gitHead": "b50fbc105755123cad34fb0bc9d5653ecc693b8a", "_id": "node-fetch@3.0.0-beta.10", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"shasum": "bd79bc429bf92c083d592c7be2c46d8ce96ca1f8", "size": 22775, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.10.tgz", "integrity": "sha512-gEHHLuUNaCHDv5TdC5+7lWlGPBgmSOWXsHvX6l64mnHH8C3V7ObLK5p5udbgc78Y19pPnCuKwwGE7o2yktGE1w=="}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.10_1626684061458_0.6017906281701693"}, "_hasShrinkwrap": false, "publish_time": 1626684061602, "_cnpm_publish_time": 1626684061602, "_cnpmcore_publish_time": "2021-12-13T13:13:09.659Z"}, "2.6.1": {"name": "node-fetch", "version": "2.6.1", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "^3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "whatwg-url": "^5.0.0"}, "dependencies": {}, "gitHead": "b5e2e41b2b50bf2997720d6125accaf0dd68c0ab", "_id": "node-fetch@2.6.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.7", "dist": {"shasum": "045bd323631f76ed2e2b55573394416b639a0052", "size": 43660, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.1.tgz", "integrity": "sha512-V4aYg89jEoVRxRb2fJdAg8FHvI7cEyYdVAh94HH0UIK8oJxUfkjlDQN9RbMx+bEjP7+ggMiFRprSti032Oipxw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.1_1599310844006_0.725487977237546"}, "_hasShrinkwrap": false, "publish_time": 1599310844110, "_cnpm_publish_time": 1599310844110, "_cnpmcore_publish_time": "2021-12-13T13:13:10.169Z"}, "3.0.0-beta.9": {"name": "node-fetch", "version": "3.0.0-beta.9", "description": "A light-weight module that brings Fetch API to node.js", "main": "./dist/index.cjs", "module": "./src/index.js", "sideEffects": false, "type": "module", "exports": {".": {"import": "./src/index.js", "require": "./dist/index.cjs"}, "./package.json": "./package.json"}, "types": "./@types/index.d.ts", "engines": {"node": "^10.17 || >=12.3"}, "scripts": {"build": "rollup -c", "test": "node --experimental-modules node_modules/c8/bin/c8 --reporter=html --reporter=lcov --reporter=text --check-coverage node --experimental-modules node_modules/mocha/bin/mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo", "prepublishOnly": "node ./test/commonjs/test-artifact.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.5.0", "busboy": "^0.3.1", "c8": "^7.3.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "delay": "^4.4.0", "form-data": "^3.0.0", "formdata-node": "^2.4.0", "mocha": "^8.1.3", "p-timeout": "^3.2.0", "rollup": "^2.26.10", "tsd": "^0.13.1", "xo": "^0.33.1"}, "dependencies": {"data-uri-to-buffer": "^3.0.1", "fetch-blob": "^2.1.1"}, "esm": {"sourceMap": true, "cjs": false}, "tsd": {"cwd": "@types", "compilerOptions": {"target": "esnext", "lib": ["es2018"], "allowSyntheticDefaultImports": false, "esModuleInterop": false}}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "unicorn/import-index": 0, "unicorn/no-reduce": 0, "capitalized-comments": 0}, "ignores": ["dist", "@types"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "runkitExampleFilename": "example.js", "readmeFilename": "README.md", "gitHead": "38839c53bd417cef440757265f75c8371d4c51e6", "_id": "node-fetch@3.0.0-beta.9", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.7", "dist": {"shasum": "0a7554cfb824380dd6812864389923c783c80d9b", "size": 47961, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.9.tgz", "integrity": "sha512-RdbZCEynH2tH46+tj0ua9caUHVWrd/RHnRfvly2EVdqGmI3ndS1Vn/xjm5KuGejDt2RNDQsVRLPNd2QPwcewVg=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.9_1599310347581_0.8595690156309526"}, "_hasShrinkwrap": false, "publish_time": 1599310347791, "_cnpm_publish_time": 1599310347791, "_cnpmcore_publish_time": "2021-12-13T13:13:10.623Z"}, "3.0.0-beta.8": {"name": "node-fetch", "version": "3.0.0-beta.8", "description": "A light-weight module that brings Fetch API to node.js", "main": "./dist/index.cjs", "module": "./src/index.js", "sideEffects": false, "type": "module", "exports": {".": {"import": "./src/index.js", "require": "./dist/index.cjs"}, "./package.json": "./package.json"}, "types": "./@types/index.d.ts", "engines": {"node": "^10.17 || >=12.3"}, "scripts": {"build": "rollup -c", "test": "node --experimental-modules node_modules/c8/bin/c8 --reporter=html --reporter=lcov --reporter=text --check-coverage node --experimental-modules node_modules/mocha/bin/mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo", "prepublishOnly": "node ./test/commonjs/test-artifact.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.4.0", "busboy": "^0.3.1", "c8": "^7.1.2", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "delay": "^4.3.0", "form-data": "^3.0.0", "formdata-node": "^2.2.0", "mocha": "^8.0.0", "p-timeout": "^3.2.0", "rollup": "^2.15.0", "tsd": "^0.13.1", "xo": "^0.32.0"}, "dependencies": {"data-uri-to-buffer": "^3.0.1", "fetch-blob": "^2.0.0"}, "esm": {"sourceMap": true, "cjs": false}, "tsd": {"cwd": "@types", "compilerOptions": {"target": "esnext", "lib": ["es2018"], "allowSyntheticDefaultImports": false, "esModuleInterop": false}}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "unicorn/import-index": 0, "unicorn/no-reduce": 0, "capitalized-comments": 0}, "ignores": ["dist", "@types"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "runkitExampleFilename": "example.js", "gitHead": "4083ce5cddbf901ea74a18b8afc630aa45197b83", "readmeFilename": "README.md", "_id": "node-fetch@3.0.0-beta.8", "_nodeVersion": "10.19.0", "_npmVersion": "6.14.7", "dist": {"shasum": "d8c4408a54b7727654b5823f8944550a72a10c50", "size": 47981, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.8.tgz", "integrity": "sha512-1zOowjOC39Wgo0Q9GDI1F24XKKkyQQuOL3UCWv8MBcqx/IHlj8A9ioxzbNlKogRS8+osL2bUjn/leT/XRmy/XA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.8_1597048644308_0.3498696917605659"}, "_hasShrinkwrap": false, "publish_time": 1597048644456, "_cnpm_publish_time": 1597048644456, "_cnpmcore_publish_time": "2021-12-13T13:13:11.350Z"}, "3.0.0-beta.7": {"name": "node-fetch", "version": "3.0.0-beta.7", "description": "A light-weight module that brings window.fetch to node.js", "main": "./dist/index.cjs", "module": "./src/index.js", "sideEffects": false, "type": "module", "exports": {"import": "./src/index.js", "require": "./dist/index.cjs"}, "types": "./@types/index.d.ts", "engines": {"node": ">=10.17"}, "scripts": {"build": "rollup -c", "test": "node --experimental-modules node_modules/c8/bin/c8 --reporter=html --reporter=lcov --reporter=text --check-coverage node --experimental-modules node_modules/mocha/bin/mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo", "prepublishOnly": "node ./test/commonjs/test-artifact.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.4.0", "busboy": "^0.3.1", "c8": "^7.1.2", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "delay": "^4.3.0", "form-data": "^3.0.0", "formdata-node": "^2.2.0", "mocha": "^8.0.0", "p-timeout": "^3.2.0", "parted": "^0.1.1", "rollup": "^2.15.0", "string-to-arraybuffer": "^1.0.2", "tsd": "^0.11.0", "xo": "^0.32.0"}, "dependencies": {"data-uri-to-buffer": "^3.0.1", "fetch-blob": "^2.0.0"}, "esm": {"sourceMap": true, "cjs": false}, "tsd": {"cwd": "@types", "compilerOptions": {"target": "esnext", "lib": ["es2018"], "allowSyntheticDefaultImports": false, "esModuleInterop": false}}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "unicorn/import-index": 0, "unicorn/no-reduce": 0, "capitalized-comments": 0, "node/no-unsupported-features/node-builtins": ["error", {"ignores": ["stream.Readable.from"]}]}, "ignores": ["dist", "@types"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "runkitExampleFilename": "example.js", "readmeFilename": "README.md", "gitHead": "2c005872ae21eff6b11c9536a55e7b8091b96273", "_id": "node-fetch@3.0.0-beta.7", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "28e122943aa8a3e4e7447d525fc138c207faa834", "size": 47992, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.7.tgz", "integrity": "sha512-UTmmxR2RCLiGL0q61p8DgMgw1UXd10+XVB77IHG55flJ/tHqQQXloNTm5dd/mB3RNXP3+CJPf++t0nb3whKNkw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.7_1591867868680_0.8094233683683905"}, "_hasShrinkwrap": false, "publish_time": 1591867868811, "_cnpm_publish_time": 1591867868811, "_cnpmcore_publish_time": "2021-12-13T13:13:11.788Z"}, "3.0.0-beta.6-exportfix": {"name": "node-fetch", "version": "3.0.0-beta.6-exportfix", "description": "A light-weight module that brings window.fetch to node.js", "main": "./dist/index.cjs", "module": "./src/index.js", "sideEffects": false, "type": "module", "exports": {"import": "./src/index.js", "require": "./dist/index.cjs"}, "types": "./@types/index.d.ts", "engines": {"node": ">=10.16"}, "scripts": {"build": "rollup -c", "test": "node --experimental-modules node_modules/c8/bin/c8 --reporter=html --reporter=lcov --reporter=text --check-coverage node --experimental-modules node_modules/mocha/bin/mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.4.0", "c8": "^7.1.2", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "delay": "^4.3.0", "form-data": "^3.0.0", "mocha": "^7.1.2", "p-timeout": "^3.2.0", "parted": "^0.1.1", "promise": "^8.1.0", "resumer": "0.0.0", "rollup": "^2.10.8", "string-to-arraybuffer": "^1.0.2", "tsc": "^1.20150623.0", "tsd": "^0.11.0", "xo": "^0.30.0"}, "dependencies": {"data-uri-to-buffer": "^3.0.0", "fetch-blob": "^1.0.6"}, "tsd": {"cwd": "@types", "compilerOptions": {"target": "esnext", "lib": ["es2018"], "allowSyntheticDefaultImports": true}}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "unicorn/import-index": 0, "capitalized-comments": 0}, "ignores": ["dist", "@types"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "runkitExampleFilename": "example.js", "readmeFilename": "README.md", "gitHead": "9b28c86d965c34bd4b412635c65819ef2a5339ca", "_id": "node-fetch@3.0.0-beta.6-exportfix", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "fc39091dd71487c48a4d02ebcd3474b5c578fdea", "size": 44013, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.6-exportfix.tgz", "integrity": "sha512-jhW2arLPAddi7q04JxlQtApqMQF8GxlGZV+wj5/WuDz3hpDTnAU9haJ5Fi2a0N3b4e728O4EIVACNq9nDCzLYw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.6-exportfix_1590427834943_0.2966373959565298"}, "_hasShrinkwrap": false, "publish_time": 1590427835066, "deprecated": "This release contains some TypeScript definition issues and we therefore recommend to update to the next version. Changelog: https://git.io/JfV1D", "_cnpm_publish_time": 1590427835066, "_cnpmcore_publish_time": "2021-12-13T13:13:12.231Z"}, "3.0.0-beta.6": {"name": "node-fetch", "version": "3.0.0-beta.6", "description": "A light-weight module that brings window.fetch to node.js", "main": "./dist/index.cjs", "module": "./src/index.js", "sideEffects": false, "type": "module", "exports": {"import": "./src/index.js", "require": "./dist/index.cjs"}, "types": "./@types/index.d.ts", "engines": {"node": ">=10.16"}, "scripts": {"build": "rollup -c", "test": "node --experimental-modules node_modules/c8/bin/c8 --reporter=html --reporter=lcov --reporter=text --check-coverage node --experimental-modules node_modules/mocha/bin/mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.4.0", "c8": "^7.1.2", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "delay": "^4.3.0", "form-data": "^3.0.0", "mocha": "^7.1.2", "p-timeout": "^3.2.0", "parted": "^0.1.1", "promise": "^8.1.0", "resumer": "0.0.0", "rollup": "^2.10.8", "string-to-arraybuffer": "^1.0.2", "tsc": "^1.20150623.0", "tsd": "^0.11.0", "xo": "^0.30.0"}, "dependencies": {"data-uri-to-buffer": "^3.0.0", "fetch-blob": "^1.0.6"}, "tsd": {"cwd": "@types", "compilerOptions": {"target": "esnext", "lib": ["es2018"], "allowSyntheticDefaultImports": true}}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "unicorn/import-index": 0, "capitalized-comments": 0}, "ignores": ["dist", "@types"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "runkitExampleFilename": "example.js", "readmeFilename": "README.md", "gitHead": "aab3c605eb8e1897dbcf105c43b00d31db754a4d", "_id": "node-fetch@3.0.0-beta.6", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"shasum": "eaec5c49371d7aaf0905e0433b596178a23174e4", "size": 45608, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.6.tgz", "integrity": "sha512-dnHS/MScUWGOQuCP77Wa5ej8s48VvWXybwpRpwFB4iRE+jTVRbhy7I5esClBfoBwh3Jj+smGVOxuLAHvnijiNQ=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.6_1590420066603_0.794872758899698"}, "_hasShrinkwrap": false, "publish_time": 1590420066740, "deprecated": "This version includes a broken  function export & declaration. Please use a next beta version.", "_cnpm_publish_time": 1590420066740, "_cnpmcore_publish_time": "2021-12-13T13:13:12.970Z"}, "3.0.0-beta.5": {"name": "node-fetch", "version": "3.0.0-beta.5", "description": "A light-weight module that brings window.fetch to node.js", "main": "./dist/index.js", "module": "./src/index.js", "sideEffects": false, "exports": {"import": "./src/index.js", "require": "./dist/index.js"}, "engines": {"node": ">=10"}, "scripts": {"build": "babel src --out-dir dist", "test": "nyc --reporter=html --reporter=text mocha --require @babel/register --throw-deprecation", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/register": "^7.9.0", "@istanbuljs/nyc-config-babel": "^3.0.0", "abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.4.0", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-istanbul": "^6.0.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.0.13", "form-data": "^3.0.0", "mocha": "^7.1.1", "nyc": "^15.0.1", "parted": "^0.1.1", "promise": "^8.1.0", "resumer": "0.0.0", "string-to-arraybuffer": "^1.0.2", "xo": "^0.29.1"}, "dependencies": {"data-uri-to-buffer": "^3.0.0", "fetch-blob": "^1.0.5"}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "promise/prefer-await-to-then": 0, "no-mixed-operators": 0, "no-negated-condition": 0, "unicorn/prevent-abbreviations": 0, "@typescript-eslint/prefer-readonly-parameter-types": 0}, "ignores": ["dist", "index.d.ts"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": true}}]], "plugins": ["add-module-exports", "istanbul"]}, "nyc": {"extends": "@istanbuljs/nyc-config-babel"}, "runkitExampleFilename": "example.js", "readmeFilename": "README.md", "gitHead": "4f73c8a766fb509fdb27f38bf7ecf3fef9944b4d", "_id": "node-fetch@3.0.0-beta.5", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "dist": {"shasum": "227ec9667a707172c39d7ab0384a21d4c36281d1", "size": 57193, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.5.tgz", "integrity": "sha512-PAQuLryEp/5tuLI3IBQk5Sn2NicJdpdNIdSF1vuTeT4pwYrv9Iwq70RK5WewDYeci2U2VEcAY8LXiMk+MUFdAw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.5_1587548563871_0.13264435111849293"}, "_hasShrinkwrap": false, "publish_time": 1587548564001, "deprecated": "This version has a bigger size, includes many bugs and broken TypeScript definition. Please update to the next beta version. Changelog: https://git.io/JfV1D", "_cnpm_publish_time": 1587548564001, "_cnpmcore_publish_time": "2021-12-13T13:13:13.597Z"}, "3.0.0-beta.4": {"name": "node-fetch", "version": "3.0.0-beta.4", "description": "A light-weight module that brings window.fetch to node.js", "main": "./dist/dist-src", "engines": {"node": ">=10.0.0"}, "scripts": {"build": "pika build --out dist/", "prepublishOnly": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require @babel/register --throw-deprecation test/*.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/*.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/*.js && codecov -f coverage/coverage-final.json", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"@babel/core": "^7.8.7", "@babel/preset-env": "^7.8.7", "@babel/register": "^7.8.6", "@pika/pack": "^0.5.0", "@pika/plugin-standard-pkg": "^0.9.2", "abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.4.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "codecov": "^3.6.5", "cross-env": "^7.0.2", "form-data": "^3.0.0", "mocha": "^7.1.0", "nyc": "^15.0.0", "parted": "^0.1.1", "promise": "^8.1.0", "resumer": "0.0.0", "string-to-arraybuffer": "^1.0.2", "xo": "^0.28.0"}, "dependencies": {"data-uri-to-buffer": "^3.0.0", "fetch-blob": "^1.0.5"}, "@pika/pack": {"pipeline": [["@pika/plugin-standard-pkg"]]}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "promise/prefer-await-to-then": 0, "no-mixed-operators": 0, "no-negated-condition": 0, "unicorn/prevent-abbreviations": 0}, "ignores": ["dist"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": true}}]]}, "nyc": {"require": ["@babel/register"], "sourceMap": false, "instrument": false}, "runkitExampleFilename": "example.js", "readmeFilename": "README.md", "gitHead": "d75bed0da1d79049426bb0c6fcb5710ae1034a2d", "_id": "node-fetch@3.0.0-beta.4", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "f5fcc70e506314c90df4f5f19e81cff55aa1ecea", "size": 41606, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.4.tgz", "integrity": "sha512-NxI746RCCuW5XmvGEOVPMFksmoMFEaWUEhNAGY5VLJ9qmit1mCEfRTHdomZuMe94FuY2QO2DLrWYe70HT7ubqw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.4_1584165490620_0.27668916199086957"}, "_hasShrinkwrap": false, "publish_time": 1584165490748, "deprecated": "This version includes a serious issue with url params, please upgrade to at least 3.0.0-beta.5", "_cnpm_publish_time": 1584165490748, "_cnpmcore_publish_time": "2021-12-13T13:13:14.181Z"}, "3.0.0-beta.3": {"name": "node-fetch", "version": "3.0.0-beta.3", "description": "A light-weight module that brings window.fetch to node.js", "engines": {"node": ">=10.0.0"}, "scripts": {"build": "pika-pack --out dist/", "prepare": "npm run build", "prepublishOnly": "npm run build", "publish": "pika publish", "test": "cross-env BABEL_ENV=test mocha --require @babel/register --throw-deprecation test/*.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/*.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/*.js && codecov -f coverage/coverage-final.json", "lint": "xo", "version": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"@babel/core": "^7.8.7", "@babel/preset-env": "^7.8.7", "@babel/register": "^7.8.6", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.2", "@pika/plugin-build-types": "^0.9.2", "@pika/plugin-copy-assets": "^0.9.2", "@pika/plugin-standard-pkg": "^0.9.2", "abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.4.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "codecov": "^3.6.5", "cross-env": "^7.0.2", "form-data": "^3.0.0", "mocha": "^7.1.0", "nyc": "^15.0.0", "parted": "^0.1.1", "promise": "^8.1.0", "resumer": "0.0.0", "string-to-arraybuffer": "^1.0.2", "xo": "^0.28.0"}, "dependencies": {"data-uri-to-buffer": "^3.0.0", "fetch-blob": "^1.0.5"}, "@pika/pack": {"pipeline": [["@pika/plugin-standard-pkg"], ["@pika/plugin-build-node"], ["@pika/plugin-build-types"], ["@pika/plugin-copy-assets", {"files": ["externals.d.ts"]}]]}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "promise/prefer-await-to-then": 0, "no-mixed-operators": 0, "no-negated-condition": 0, "unicorn/prevent-abbreviations": 0}, "ignores": ["dist"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": true}}]]}, "nyc": {"require": ["@babel/register"], "sourceMap": false, "instrument": false}, "runkitExampleFilename": "example.js", "readmeFilename": "README.md", "gitHead": "6f2a5efbb6b6177b3ae4a99573b517b4c64cf84a", "_id": "node-fetch@3.0.0-beta.3", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "46ce9df3c4d4134e6cf6a30a682f894e0ee812dc", "size": 50212, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.3.tgz", "integrity": "sha512-gp8E4VvERm0/b9MoPx3S8/WT1LyJVbffcQf04LsyTWnOrZUPHQUNTIo84n3T+HA3YNkrWTt2g4K9g/g1QXO63A=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.3_1584126606233_0.5859320705818469"}, "_hasShrinkwrap": false, "publish_time": 1584126606375, "deprecated": "this package is deprecated, it was published in the wrong release channel, 2.x is still the stable release", "_cnpm_publish_time": 1584126606375, "_cnpmcore_publish_time": "2021-12-13T13:13:14.818Z"}, "3.0.0-beta.2": {"name": "node-fetch", "version": "3.0.0-beta.2", "description": "A light-weight module that brings window.fetch to node.js", "engines": {"node": ">=10.0.0"}, "scripts": {"build": "pika-pack --out dist/", "prepare": "npm run build", "prepublishOnly": "npm run build", "publish": "pika publish", "test": "cross-env BABEL_ENV=test mocha --require @babel/register --throw-deprecation test/*.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/*.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/*.js && codecov -f coverage/coverage-final.json", "lint": "xo", "version": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"@babel/core": "^7.8.7", "@babel/preset-env": "^7.8.7", "@babel/register": "^7.8.6", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.2", "@pika/plugin-build-types": "^0.9.2", "@pika/plugin-copy-assets": "^0.9.2", "@pika/plugin-standard-pkg": "^0.9.2", "abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.4.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "codecov": "^3.6.5", "cross-env": "^7.0.2", "form-data": "^3.0.0", "mocha": "^7.1.0", "nyc": "^15.0.0", "parted": "^0.1.1", "promise": "^8.1.0", "resumer": "0.0.0", "string-to-arraybuffer": "^1.0.2", "xo": "^0.28.0"}, "dependencies": {"data-uri-to-buffer": "^3.0.0", "fetch-blob": "^1.0.5"}, "@pika/pack": {"pipeline": [["@pika/plugin-standard-pkg"], ["@pika/plugin-build-node"], ["@pika/plugin-build-types"], ["@pika/plugin-copy-assets", {"files": ["externals.d.ts"]}]]}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "promise/prefer-await-to-then": 0, "no-mixed-operators": 0, "no-negated-condition": 0, "unicorn/prevent-abbreviations": 0}, "ignores": ["dist"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": true}}]]}, "nyc": {"require": ["@babel/register"], "sourceMap": false, "instrument": false}, "runkitExampleFilename": "example.js", "gitHead": "6f2a5efbb6b6177b3ae4a99573b517b4c64cf84a", "_id": "node-fetch@3.0.0-beta.2", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "21dab0112c90d66d5d8b4f1e7a895cae5ac9d391", "size": 50212, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.2.tgz", "integrity": "sha512-B3Gu7oTNhaaK0kZTSdAewW1Wpxj07Oe77R0p7S7todxpolnJuwvRFfabnILVFO2CWcp1BRQOBtem5s2xBt6aGA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.2_1584126112803_0.24794439972808857"}, "_hasShrinkwrap": false, "publish_time": 1584126112964, "deprecated": "this package is deprecated, it was published in the wrong release channel, 2.x is still the stable release", "_cnpm_publish_time": 1584126112964, "_cnpmcore_publish_time": "2021-12-13T13:13:15.422Z"}, "3.0.0-beta.1": {"name": "node-fetch", "version": "3.0.0-beta.1", "description": "A light-weight module that brings window.fetch to node.js", "main": "dist/index.js", "module": "dist/index.mjs", "types": "types/index.d.ts", "engines": {"node": ">=10.0.0"}, "scripts": {"build": "pika-pack --out dist/", "prepare": "npm run build", "prepublishOnly": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require @babel/register --throw-deprecation test/*.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/*.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/*.js && codecov -f coverage/coverage-final.json", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"@babel/core": "^7.8.7", "@babel/preset-env": "^7.8.7", "@babel/register": "^7.8.6", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.2", "@pika/plugin-build-types": "^0.9.2", "@pika/plugin-copy-assets": "^0.9.2", "@pika/plugin-standard-pkg": "^0.9.2", "abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.4.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "codecov": "^3.6.5", "cross-env": "^7.0.2", "form-data": "^3.0.0", "mocha": "^7.1.0", "nyc": "^15.0.0", "parted": "^0.1.1", "promise": "^8.1.0", "resumer": "0.0.0", "string-to-arraybuffer": "^1.0.2", "xo": "^0.28.0"}, "dependencies": {"data-uri-to-buffer": "^3.0.0", "fetch-blob": "^1.0.5"}, "@pika/pack": {"pipeline": [["@pika/plugin-standard-pkg"], ["@pika/plugin-build-node"], ["@pika/plugin-build-types"], ["@pika/plugin-copy-assets", {"files": ["externals.d.ts"]}]]}, "xo": {"envs": ["node", "browser"], "rules": {"complexity": 0, "promise/prefer-await-to-then": 0, "no-mixed-operators": 0, "no-negated-condition": 0, "unicorn/prevent-abbreviations": 0}, "ignores": ["dist"], "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "new-cap": 0, "guard-for-in": 0}}, {"files": "example.js", "rules": {"import/no-extraneous-dependencies": 0}}]}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": true}}]]}, "nyc": {"require": ["@babel/register"], "sourceMap": false, "instrument": false}, "runkitExampleFilename": "example.js", "readmeFilename": "README.md", "gitHead": "0959ca9739850bbd24e0721cc1296e7a0aa5c2bd", "_id": "node-fetch@3.0.0-beta.1", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "7664c0c7031a1c8a296395ee964b613b2ff82485", "size": 50279, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.0.0-beta.1.tgz", "integrity": "sha512-92vRiuYD81o/xGXOtwFNatkbPdg7w8XdLu5pj+OJmHvbxI6Qj/bfJe/U1PqbPNu0P+p5bNlqbxoiSOeFGmNO4w=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.0.0-beta.1_1584112418028_0.6941549988015374"}, "_hasShrinkwrap": false, "publish_time": 1584112418280, "deprecated": "this package is deprecated, it was published in the wrong release channel, 2.x is still the stable release", "_cnpm_publish_time": 1584112418280, "_cnpmcore_publish_time": "2021-12-13T13:13:16.040Z"}, "2.6.0": {"name": "node-fetch", "version": "2.6.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "^3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "whatwg-url": "^5.0.0"}, "dependencies": {}, "gitHead": "95286f52bb866283bc69521a04efe1de37b26a33", "_id": "node-fetch@2.6.0", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "e633456386d4aa55863f676a7ab0daa8fdecb0fd", "size": 42792, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.0.tgz", "integrity": "sha512-8dG4H5ujfvFiqDmVu9fQ5bOHUC15JMjMY/Zumv26oOvvVJjM67KF8koCWIabKQ1GJIa9r2mMZscBq/TbdOcmNA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.0_1557988780767_0.1012615293164354"}, "_hasShrinkwrap": false, "publish_time": 1557988781004, "_cnpm_publish_time": 1557988781004, "_cnpmcore_publish_time": "2021-12-13T13:13:16.614Z"}, "2.5.0": {"name": "node-fetch", "version": "2.5.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "^3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "whatwg-url": "^5.0.0"}, "dependencies": {}, "gitHead": "0c2294ec48fa5b84519f8bdd60f4e2672ebd9b06", "_id": "node-fetch@2.5.0", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "8028c49fc1191bba56a07adc6e2a954644a48501", "size": 42444, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.5.0.tgz", "integrity": "sha512-YuZKluhWGJwCcUu4RlZstdAxr8bFfOVHakc1mplwHkk8J+tqM1Y5yraYvIUpeX8aY7+crCwiELJq7Vl0o0LWXw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.5.0_1556687287265_0.5189085772983091"}, "_hasShrinkwrap": false, "publish_time": 1556687287621, "_cnpm_publish_time": 1556687287621, "_cnpmcore_publish_time": "2021-12-13T13:13:17.192Z"}, "2.4.1": {"name": "node-fetch", "version": "2.4.1", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "^3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "url-search-params": "^1.0.2", "whatwg-url": "^5.0.0"}, "dependencies": {}, "gitHead": "b3ecba5e81016390eec57718636122459cc33a94", "_id": "node-fetch@2.4.1", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "b2e38f1117b8acbedbe0524f041fb3177188255d", "size": 41741, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.4.1.tgz", "integrity": "sha512-P9UbpFK87NyqBZzUuDBDz4f6Yiys8xm8j7ACDbi6usvFm6KItklQUKjeoqTrYS/S1k6I8oaOC2YLLDr/gg26Mw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.4.1_1556348861860_0.709170139396043"}, "_hasShrinkwrap": false, "publish_time": 1556348862071, "_cnpm_publish_time": 1556348862071, "_cnpmcore_publish_time": "2021-12-13T13:13:17.739Z"}, "2.4.0": {"name": "node-fetch", "version": "2.4.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "^3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "url-search-params": "^1.0.2", "whatwg-url": "^5.0.0"}, "dependencies": {}, "gitHead": "c9805a2868bb0896be126acefdc2c11c4c586bf9", "_id": "node-fetch@2.4.0", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"shasum": "125ae2b0c2a75cb3b2ed141bf73e106cfe1f0ca8", "size": 41706, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.4.0.tgz", "integrity": "sha512-1mt8bw5JQWWTcwUM1FGjFJLFo5lB/jz6zbm+qwdEh2iqYobKS4aHWgz1d+mvho5cqCaShFDF+hnpgraIi/5tqA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.4.0_1556299460716_0.9054885680028364"}, "_hasShrinkwrap": false, "publish_time": 1556299460869, "_cnpm_publish_time": 1556299460869, "_cnpmcore_publish_time": "2021-12-13T13:13:18.341Z"}, "2.3.0": {"name": "node-fetch", "version": "2.3.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"abort-controller": "^1.0.2", "abortcontroller-polyfill": "^1.1.9", "babel-core": "^6.26.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "^3.0.0", "cross-env": "^5.1.3", "form-data": "^2.3.1", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.1", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.3", "string-to-arraybuffer": "^1.0.0", "url-search-params": "^1.0.2", "whatwg-url": "^5.0.0"}, "dependencies": {}, "gitHead": "5367fe6a978e01745e4264384a91140dc99a4bf8", "_id": "node-fetch@2.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "1a1d940bbfb916a1d3e0219f037e89e71f8c5fa5", "size": 41549, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.3.0.tgz", "integrity": "sha512-MOd8pV3fxENbryESLgVIeaGKrdl+uaYhCSSVkjeOb/31/njTpcis5aWfdqgNlHIrKOLRbMnfPINPOML2CIFeXA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.3.0_1542091315995_0.7896717463160674"}, "_hasShrinkwrap": false, "publish_time": 1542091316187, "_cnpm_publish_time": 1542091316187, "_cnpmcore_publish_time": "2021-12-13T13:13:19.057Z"}, "2.2.1": {"name": "node-fetch", "version": "2.2.1", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-core": "^6.26.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "^3.0.0", "cross-env": "^5.1.3", "form-data": "^2.3.1", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.1", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.3", "string-to-arraybuffer": "^1.0.0", "url-search-params": "^1.0.2", "whatwg-url": "^5.0.0"}, "dependencies": {}, "gitHead": "ac07bf751c93fa86f87f01c924ddbd8c76b84966", "_id": "node-fetch@2.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "1fe551e0ded6c45b3b3b937d0fb46f76df718d1e", "size": 38979, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.2.1.tgz", "integrity": "sha512-ObXBpNCD3A/vYQiQtEWl7DuqjAXjfptYFuGHLdPl5U19/6kJuZV+8uMHLrkj3wJrJoyfg4nhgyFixZdaZoAiEQ=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.2.1_1541411081386_0.6507879255509943"}, "_hasShrinkwrap": false, "publish_time": 1541411081523, "_cnpm_publish_time": 1541411081523, "_cnpmcore_publish_time": "2021-12-13T13:13:19.807Z"}, "2.2.0": {"name": "node-fetch", "version": "2.2.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index", "browser": "./browser.js", "module": "lib/index.mjs", "files": ["lib/index.js", "lib/index.mjs", "lib/index.es.js", "browser.js"], "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-core": "^6.26.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^3.0.0", "cross-env": "^5.1.3", "form-data": "^2.3.1", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.1", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.3", "string-to-arraybuffer": "^1.0.0", "url-search-params": "^1.0.2", "whatwg-url": "^5.0.0"}, "dependencies": {}, "gitHead": "09ef40e8a883344a38f8ebddc58ff0ac129a415c", "_id": "node-fetch@2.2.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4ee79bde909262f9775f731e3656d0db55ced5b5", "size": 37676, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.2.0.tgz", "integrity": "sha512-OayFWziIxiHY8bCUyLX6sTpDH8Jsbp4FfYd1j1f7vZyfgkcOnAyM4oQR16f8a0s7Gl/viMGRey8eScYk4V4EZA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.2.0_1532295125636_0.31629715469618946"}, "_hasShrinkwrap": false, "publish_time": 1532295125747, "_cnpm_publish_time": 1532295125747, "_cnpmcore_publish_time": "2021-12-13T13:13:20.545Z"}, "2.1.2": {"name": "node-fetch", "version": "2.1.2", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js", "browser.js"], "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-core": "^6.26.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^3.0.0", "cross-env": "^5.1.3", "form-data": "^2.3.1", "mocha": "^5.0.0", "nyc": "^11.4.1", "parted": "^0.1.1", "promise": "^8.0.1", "resumer": "0.0.0", "rollup": "^0.55.1", "rollup-plugin-babel": "^3.0.3", "string-to-arraybuffer": "^1.0.0", "url-search-params": "^0.10.0", "whatwg-url": "^5.0.0"}, "dependencies": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 David <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n", "_id": "node-fetch@2.1.2", "dist": {"shasum": "ab884e8e7e57e38a944753cec706f788d1768bb5", "size": 28564, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.1.2.tgz", "integrity": "sha512-IHLHYskTc2arMYsHZH82PVX8CSKT5lzb7AXeyO06QnjGDKtkv+pv3mEki6S7reB/x1QPo+YPxQRNEVgR5V/w3Q=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.1.2_1522011300411_0.9473331736799258"}, "_hasShrinkwrap": false, "publish_time": 1522011300498, "_cnpm_publish_time": 1522011300498, "_cnpmcore_publish_time": "2021-12-13T13:13:21.244Z"}, "2.1.1": {"name": "node-fetch", "version": "2.1.1", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js", "browser.js"], "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-core": "^6.26.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^3.0.0", "cross-env": "^5.1.3", "form-data": "^2.3.1", "mocha": "^5.0.0", "nyc": "^11.4.1", "parted": "^0.1.1", "promise": "^8.0.1", "resumer": "0.0.0", "rollup": "^0.55.1", "rollup-plugin-babel": "^3.0.3", "string-to-arraybuffer": "^1.0.0", "url-search-params": "^0.10.0", "whatwg-url": "^5.0.0"}, "dependencies": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 David <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n", "_id": "node-fetch@2.1.1", "dist": {"shasum": "369ca70b82f50c86496104a6c776d274f4e4a2d4", "size": 27994, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.1.1.tgz", "integrity": "sha512-6AJlcbC/MCslTTI89F4rg7Q+jOl7YfmzJVEFVCKuc9zvBzeupObeT540hXVYAkx/Hy2o+JX0RUH4ref0sadTjg=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.1.1_1520225048881_0.16817806000341773"}, "_hasShrinkwrap": false, "publish_time": 1520225049059, "_cnpm_publish_time": 1520225049059, "_cnpmcore_publish_time": "2021-12-13T13:13:21.989Z"}, "2.1.0": {"name": "node-fetch", "version": "2.1.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js", "browser.js"], "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-core": "^6.26.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^3.0.0", "cross-env": "^5.1.3", "form-data": "^2.3.1", "mocha": "^5.0.0", "nyc": "^11.4.1", "parted": "^0.1.1", "promise": "^8.0.1", "resumer": "0.0.0", "rollup": "^0.55.1", "rollup-plugin-babel": "^3.0.3", "string-to-arraybuffer": "^1.0.0", "url-search-params": "^0.10.0", "whatwg-url": "^5.0.0"}, "dependencies": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 David <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n", "_id": "node-fetch@2.1.0", "dist": {"shasum": "9d96dde9fe1fcef6e4081226ba496b8d5950b647", "size": 27993, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.1.0.tgz", "integrity": "sha512-5Fsb1LJNJnE1GoZ5b5fhs91ul9pHbjadCKgQQeNolpDKmGhrGtzyaebq12lfx/oUaHYbkfJ3U5PM082BEJYyIA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.1.0_1520224872510_0.5133806259710896"}, "_hasShrinkwrap": false, "publish_time": 1520224872676, "_cnpm_publish_time": 1520224872676, "_cnpmcore_publish_time": "2021-12-13T13:13:22.743Z"}, "2.0.0": {"name": "node-fetch", "version": "2.0.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js", "browser.js"], "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-core": "^6.26.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^3.0.0", "cross-env": "^5.1.3", "form-data": "^2.3.1", "mocha": "^5.0.0", "nyc": "^11.4.1", "parted": "^0.1.1", "promise": "^8.0.1", "resumer": "0.0.0", "rollup": "^0.55.1", "rollup-plugin-babel": "^3.0.3", "url-search-params": "^0.10.0", "whatwg-url": "^5.0.0"}, "dependencies": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 David <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n", "_id": "node-fetch@2.0.0", "dist": {"shasum": "982bba43ecd4f2922a29cc186a6bbb0bb73fcba6", "size": 28488, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.0.0.tgz", "integrity": "sha512-bici2HCWFnAghTYMcy12WPxrEwJ5qK7GQJOTwTfyEZjyL99ECWxbYQfabZ2U1zrHMKkOBE97Z9iHIuKQfCMdzQ=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-2.0.0.tgz_1517690064671_0.487887607421726"}, "directories": {}, "publish_time": 1517690064774, "_hasShrinkwrap": false, "_cnpm_publish_time": 1517690064774, "_cnpmcore_publish_time": "2021-12-13T13:13:23.486Z"}, "2.0.0-alpha.9": {"name": "node-fetch", "version": "2.0.0-alpha.9", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js", "browser.js"], "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-plugin-istanbul": "^4.0.0", "babel-preset-env": "^1.1.10", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^2.2.0", "cross-env": "^5.0.1", "form-data": ">=1.0.0", "mocha": "^3.1.2", "nyc": "^11.1.0", "parted": "^0.1.1", "promise": "^8.0.1", "resumer": "0.0.0", "rollup": "^0.45.2", "rollup-plugin-babel": "^2.6.1", "url-search-params": "^0.9.0", "whatwg-url": "^5.0.0"}, "dependencies": {}, "gitHead": "d1a3b1ee34720b0d2674a21741264a888863cb3f", "_id": "node-fetch@2.0.0-alpha.9", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "990c0634f510f76449a0d6f6eaec96b22f273628", "size": 27251, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.0.0-alpha.9.tgz", "integrity": "sha512-I7wP1QkmBNX1mt4BS5zyLRTegl5Ii+MSalpfFefn+EZFrGVsdfCvLTKt9eHkNlU4phKgp3tqLWW8VXDcCm9m9w=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-2.0.0-alpha.9.tgz_1506232062978_0.9201480248011649"}, "directories": {}, "publish_time": 1506232063171, "_hasShrinkwrap": false, "_cnpm_publish_time": 1506232063171, "_cnpmcore_publish_time": "2021-12-13T13:13:24.308Z"}, "1.7.3": {"name": "node-fetch", "version": "1.7.3", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && codecov"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "codecov": "^1.0.1", "form-data": ">=1.0.0", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "16f33bf76f06afb58307a652ceb48f597e48a095", "_id": "node-fetch@1.7.3", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "980f6f72d85211a5347c6b2bc18c5b84c3eb47ef", "size": 27217, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.7.3.tgz", "integrity": "sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-1.7.3.tgz_1504852098188_0.7899843158666044"}, "directories": {}, "publish_time": 1504852098255, "_hasShrinkwrap": false, "_cnpm_publish_time": 1504852098255, "_cnpmcore_publish_time": "2021-12-13T13:13:24.973Z"}, "1.7.2": {"name": "node-fetch", "version": "1.7.2", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && codecov"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "codecov": "^1.0.1", "form-data": ">=1.0.0", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "3c9b64caf5c84f96052078f18d91c9e12bd5f5c6", "_id": "node-fetch@1.7.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "c54e9aac57e432875233525f3c891c4159ffefd7", "size": 27137, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.7.2.tgz", "integrity": "sha512-xZZUq2yDhKMIn/UgG5q//IZSNLJIwW2QxS14CNH5spuiXkITM2pUitjdq58yLSaU7m4M0wBNaM2Gh/ggY4YJig=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-1.7.2.tgz_1502191381219_0.664517188211903"}, "directories": {}, "publish_time": 1502191381450, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502191381450, "_cnpmcore_publish_time": "2021-12-13T13:13:25.812Z"}, "2.0.0-alpha.8": {"name": "node-fetch", "version": "2.0.0-alpha.8", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js"], "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-plugin-istanbul": "^4.0.0", "babel-preset-env": "^1.1.10", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^6.0.0", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^1.0.1", "cross-env": "^3.1.4", "form-data": ">=1.0.0", "mocha": "^3.1.2", "nyc": "^10.0.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0", "rollup": "^0.41.4", "rollup-plugin-babel": "^2.6.1", "url-search-params": "^0.9.0", "whatwg-url": "^4.0.0"}, "dependencies": {}, "gitHead": "863449de2e5b14c128503dd1c425d5e0fd24d267", "_id": "node-fetch@2.0.0-alpha.8", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "f586cf6730ce30431c7d4528ce561d81add8ba90", "size": 27005, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.0.0-alpha.8.tgz", "integrity": "sha512-cDokdqn6TD7LNpMqxgPsI1Iqu23sD4ihqbGELFn4YmMvJSiyJcAws2sm0qKo5hIPXwvc7vR2BDPK09UyyLSivw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-2.0.0-alpha.8.tgz_1501089111834_0.10333641874603927"}, "directories": {}, "publish_time": 1501089112034, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501089112034, "_cnpmcore_publish_time": "2021-12-13T13:13:26.632Z"}, "2.0.0-alpha.7": {"name": "node-fetch", "version": "2.0.0-alpha.7", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js"], "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-plugin-istanbul": "^4.0.0", "babel-preset-env": "^1.1.10", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^6.0.0", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^1.0.1", "cross-env": "^3.1.4", "form-data": ">=1.0.0", "is-builtin-module": "^1.0.0", "mocha": "^3.1.2", "nyc": "^10.0.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0", "rollup": "^0.41.4", "rollup-plugin-babel": "^2.6.1", "url-search-params": "^0.9.0", "whatwg-url": "^4.0.0"}, "dependencies": {}, "gitHead": "2edb4c026667e5c8e7c808a3973bd94d193b2d0f", "_id": "node-fetch@2.0.0-alpha.7", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "78f61fc78c55a86674f071237a3f73de5ba4138b", "size": 26934, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.0.0-alpha.7.tgz", "integrity": "sha512-jODLrTvE4jOCDkrxf6RDqhoShkMIfExOQ8XBaYZx+zo3u9UfzYxMWFlPONAlslPwWQNgM8WZ7Tpdo26VWDWS9A=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-2.0.0-alpha.7.tgz_1500955861406_0.3457566825672984"}, "directories": {}, "publish_time": 1500955861517, "_hasShrinkwrap": false, "_cnpm_publish_time": 1500955861517, "_cnpmcore_publish_time": "2021-12-13T13:13:27.375Z"}, "2.0.0-alpha.6": {"name": "node-fetch", "version": "2.0.0-alpha.6", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js"], "engines": {"node": ">=4"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-plugin-istanbul": "^4.0.0", "babel-preset-env": "^1.1.10", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^6.0.0", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^1.0.1", "cross-env": "^3.1.4", "form-data": ">=1.0.0", "is-builtin-module": "^1.0.0", "mocha": "^3.1.2", "nyc": "^10.0.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0", "rollup": "^0.41.4", "rollup-plugin-babel": "^2.6.1", "url-search-params": "^0.9.0", "whatwg-url": "^4.0.0"}, "dependencies": {}, "gitHead": "2b359c1ea161edf5778490b9303a40ee11fef7ea", "_id": "node-fetch@2.0.0-alpha.6", "_shasum": "2fafa63b4a248f8d6b67a239fc16299a35641161", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "2fafa63b4a248f8d6b67a239fc16299a35641161", "size": 26024, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.0.0-alpha.6.tgz", "integrity": "sha512-DLg33+NQ5sca8OZPnmKzTPOlbS27mT5Q4xoHktwt1kXDOcQSgN0lzoXHZW9ubT7kduRl8x75KPW7dvWr0jGqBA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-2.0.0-alpha.6.tgz_1500660271436_0.007863717619329691"}, "directories": {}, "publish_time": 1500660271572, "_hasShrinkwrap": false, "deprecated": "it accidentally includes an older copy of node-fetch v2, do not use", "_cnpm_publish_time": 1500660271572, "_cnpmcore_publish_time": "2021-12-13T13:13:28.190Z"}, "2.0.0-alpha.5": {"name": "node-fetch", "version": "2.0.0-alpha.5", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js"], "engines": {"node": ">=4"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-plugin-istanbul": "^4.0.0", "babel-preset-env": "^1.1.10", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^6.0.0", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^1.0.1", "cross-env": "^3.1.4", "form-data": ">=1.0.0", "is-builtin-module": "^1.0.0", "mocha": "^3.1.2", "nyc": "^10.0.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0", "rollup": "^0.41.4", "rollup-plugin-babel": "^2.6.1", "whatwg-url": "^4.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "fa58aa0ab152293066ea912d4abdaa877d72eb16", "_id": "node-fetch@2.0.0-alpha.5", "_npmVersion": "5.0.1", "_nodeVersion": "8.0.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "efe2816af7107bafd3d3cabd0f24deca4b408b64", "size": 25909, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.0.0-alpha.5.tgz", "integrity": "sha512-ricDvef3324E+SqaOFJWiVCrQVHbJqxDfeOBwn7LymV67MYxeljHkJXAYOCADlopy/Q9NpTjFxtwv3+xLubGbA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-2.0.0-alpha.5.tgz_1496495125929_0.37553365412168205"}, "directories": {}, "publish_time": 1496495126072, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496495126072, "_cnpmcore_publish_time": "2021-12-13T13:13:29.053Z"}, "1.7.1": {"name": "node-fetch", "version": "1.7.1", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && codecov"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "codecov": "^1.0.1", "form-data": ">=1.0.0", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "4bb15ce988bf614a2b1b72da1d6545b09ca6b4c6", "_id": "node-fetch@1.7.1", "_npmVersion": "5.0.1", "_nodeVersion": "8.0.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "899cb3d0a3c92f952c47f1b876f4c8aeabd400d5", "size": 26707, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.7.1.tgz", "integrity": "sha512-j8XsFGCLw79vWXkZtMSmmLaOk9z5SQ9bV/tkbZVCqvgwzrjAGq66igobLofHtF63NvMTp2WjytpsNTGKa+XRIQ=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-1.7.1.tgz_1496493897766_0.25435457238927484"}, "directories": {}, "publish_time": 1496493898110, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496493898110, "_cnpmcore_publish_time": "2021-12-13T13:13:29.837Z"}, "1.7.0": {"name": "node-fetch", "version": "1.7.0", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && codecov"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "codecov": "^1.0.1", "form-data": ">=1.0.0", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "cf71a1bc8badaebf0243748f66a719fe5e32cfdc", "_id": "node-fetch@1.7.0", "_shasum": "3ff6c56544f9b7fb00682338bb55ee6f54a8a0ef", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "3ff6c56544f9b7fb00682338bb55ee6f54a8a0ef", "size": 26648, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.7.0.tgz", "integrity": "sha512-x19qZ2Qcg7TWciOkYlqAZpbF5pGqe6e1DeNOalRwVkRHfkro572ZV9CzmkFLuMmy72DlBqmQ4Wq+V3qyAfsBjg=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch-1.7.0.tgz_1495527871673_0.4224355819169432"}, "directories": {}, "publish_time": 1495527871812, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495527871812, "_cnpmcore_publish_time": "2021-12-13T13:13:30.785Z"}, "2.0.0-alpha.4": {"name": "node-fetch", "version": "2.0.0-alpha.4", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js"], "engines": {"node": ">=4"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepublish": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-plugin-istanbul": "^4.0.0", "babel-preset-env": "^1.1.10", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^6.0.0", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^1.0.1", "cross-env": "^3.1.4", "form-data": ">=1.0.0", "is-builtin-module": "^1.0.0", "mocha": "^3.1.2", "nyc": "^10.0.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0", "rollup": "^0.41.4", "rollup-plugin-babel": "^2.6.1", "whatwg-url": "^4.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "9cea8719eb4c2de70ec1d423398ebc38d3f8b8ab", "_id": "node-fetch@2.0.0-alpha.4", "_shasum": "ae3fe092fef710a7e565e68d5fcccefabad674b6", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "dist": {"shasum": "ae3fe092fef710a7e565e68d5fcccefabad674b6", "size": 25709, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.0.0-alpha.4.tgz", "integrity": "sha512-7uX1Z7/mt/3XOUXLhziCPq3d3gnUk/owpwIWv7EroFsVtXH+4QKZwuTMoRUpH6vrelckkm1TvIalALrXU8p1tA=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-fetch-2.0.0-alpha.4.tgz_1494850616065_0.33211735682561994"}, "directories": {}, "publish_time": 1494850618709, "_hasShrinkwrap": false, "_cnpm_publish_time": 1494850618709, "_cnpmcore_publish_time": "2021-12-13T13:13:31.714Z"}, "2.0.0-alpha.3": {"name": "node-fetch", "version": "2.0.0-alpha.3", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js"], "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepublish": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-plugin-istanbul": "^3.0.0", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-env": "^1.1.8", "babel-register": "^6.16.3", "bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^6.0.0", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^1.0.1", "cross-env": "2.0.1", "form-data": ">=1.0.0", "is-builtin-module": "^1.0.0", "mocha": "^3.1.2", "nyc": "^10.0.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0", "rollup": "^0.37.0", "rollup-plugin-babel": "^2.6.1", "rollup-plugin-node-resolve": "^2.0.0", "whatwg-url": "^4.0.0"}, "dependencies": {"babel-runtime": "^6.11.6", "buffer-to-arraybuffer": "0.0.4", "encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "560d5d6a023f069c37497f633fe65955182fa3d5", "_id": "node-fetch@2.0.0-alpha.3", "_shasum": "8031bded671c806e4604c16b27ab1973b4fe88cc", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.4.0", "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8031bded671c806e4604c16b27ab1973b4fe88cc", "size": 24998, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.0.0-alpha.3.tgz", "integrity": "sha512-nqvTEOgRwzzRpJUgC828/mJo0mgPNkkQS9ahRukzxypMGu9Ltwsh7XVZ5vkM+sMhchRYuscYzx2mDrPdAOA0Gg=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-fetch-2.0.0-alpha.3.tgz_1485714967416_0.2058272489812225"}, "directories": {}, "publish_time": 1485714969254, "_hasShrinkwrap": false, "_cnpm_publish_time": 1485714969254, "_cnpmcore_publish_time": "2021-12-13T13:13:32.652Z"}, "2.0.0-alpha.1": {"name": "node-fetch", "version": "2.0.0-alpha.1", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "jsnext:main": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js"], "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepublish": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-plugin-istanbul": "^3.0.0", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-es2015": "^6.16.0", "babel-register": "^6.16.3", "bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^6.0.0", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^1.0.1", "cross-env": "2.0.1", "form-data": ">=1.0.0", "is-builtin-module": "^1.0.0", "mocha": "^3.1.2", "nyc": "^10.0.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0", "rollup": "^0.37.0", "rollup-plugin-babel": "^2.6.1", "rollup-plugin-node-resolve": "^2.0.0", "whatwg-url": "^4.0.0"}, "dependencies": {"babel-runtime": "^6.11.6", "buffer-to-arraybuffer": "0.0.4", "encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "908f661664e51fd847a5f69eb31724bbf2b3724b", "_id": "node-fetch@2.0.0-alpha.1", "_shasum": "28499a933f19adcf3ecf8b9fa39a6c79202d1072", "_from": ".", "_npmVersion": "4.1.1", "_nodeVersion": "7.3.0", "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "28499a933f19adcf3ecf8b9fa39a6c79202d1072", "size": 25515, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.0.0-alpha.1.tgz", "integrity": "sha512-USbhijhIEG5pvpFkyzNlzHx2HMVg/pUILNEJjl3oG9mWOzeq5/y+AIt66N9+IChlnKTbiE2rKSa8sA6hjFb7Lw=="}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-fetch-2.0.0-alpha.1.tgz_1484458641973_0.14498046622611582"}, "directories": {}, "publish_time": 1484458642197, "_hasShrinkwrap": false, "_cnpm_publish_time": 1484458642197, "_cnpmcore_publish_time": "2021-12-13T13:13:33.548Z"}, "1.6.3": {"name": "node-fetch", "version": "1.6.3", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": ">=1.0.0", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "3c053ce32760d2d5d6cb8712fb4115b44e4083d4", "_id": "node-fetch@1.6.3", "_shasum": "dc234edd6489982d58e8f0db4f695029abcd8c04", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dc234edd6489982d58e8f0db4f695029abcd8c04", "size": 18733, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.6.3.tgz", "integrity": "sha512-BDxbhLHXFFFvilHjh9xihcDyPkXQ+kjblxnl82zAX41xUYSNvuRpFRznmldR9+OKu+p+ULZ7hNoyunlLB5ecUA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-fetch-1.6.3.tgz_1474870810431_0.44125940511003137"}, "directories": {}, "publish_time": 1474870812535, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474870812535, "_cnpmcore_publish_time": "2021-12-13T13:13:34.439Z"}, "1.6.2": {"name": "node-fetch", "version": "1.6.2", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "b2e6cca648bb5da176b6f4ab39dca6a33e992567", "_id": "node-fetch@1.6.2", "_shasum": "6a605f52c6b77ce28ce24f131b97e73fe2ea9fa0", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6a605f52c6b77ce28ce24f131b97e73fe2ea9fa0", "size": 18165, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.6.2.tgz", "integrity": "sha512-17HXZdP+uNH8L5R/Fhjgx92Zo9GP4PelR9J/zWO5MaVe3TlKnnXqYgNoYLJzddyPQZC2CXqYwGOIivWtHpWEmw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-fetch-1.6.2.tgz_1474710825909_0.8582186447456479"}, "directories": {}, "publish_time": 1474710827594, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474710827594, "_cnpmcore_publish_time": "2021-12-13T13:13:35.282Z"}, "1.6.1": {"name": "node-fetch", "version": "1.6.1", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "3e53f0c2c86078808508aa2ca11094bb45570548", "_id": "node-fetch@1.6.1", "_shasum": "2ac748594cc82610da9a7a32c530777dcfcb51ff", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2ac748594cc82610da9a7a32c530777dcfcb51ff", "size": 17985, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.6.1.tgz", "integrity": "sha512-XNa9iNqDCayFnjUEV5HHB2vnipuBNImZeQxh0f2Kk93sbzDmtuHpcwVVBU5JI6fetNMbzWdXQHRH4pb5IHkfdQ=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-fetch-1.6.1.tgz_1473608280483_0.7950712053570896"}, "directories": {}, "publish_time": 1473608282062, "_hasShrinkwrap": false, "_cnpm_publish_time": 1473608282062, "_cnpmcore_publish_time": "2021-12-13T13:13:36.349Z"}, "1.6.0": {"name": "node-fetch", "version": "1.6.0", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "4e455a4185da81abf814ccdb9c9e789691f7a349", "_id": "node-fetch@1.6.0", "_shasum": "00ccd811c657aee9e2f0d79fd2256fbcffd733a2", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "00ccd811c657aee9e2f0d79fd2256fbcffd733a2", "size": 17381, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.6.0.tgz", "integrity": "sha512-ye/lrpJWLIatIXqoeZQrIsBkzEA8mxWNdlnb+vBwGI1CjqExEdUq18iV5hUHsmn/er5mkbW5QENEvL235wlPfg=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-fetch-1.6.0.tgz_1470219032038_0.3425547960214317"}, "directories": {}, "publish_time": 1470219034469, "_hasShrinkwrap": false, "_cnpm_publish_time": 1470219034469, "_cnpmcore_publish_time": "2021-12-13T13:13:37.395Z"}, "1.5.3": {"name": "node-fetch", "version": "1.5.3", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "b46c5ea9ee6692676a2824334e24f9d3b95d3b78", "_id": "node-fetch@1.5.3", "_shasum": "f28d8b95ca8d45b433745dd319361e36402baef0", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f28d8b95ca8d45b433745dd319361e36402baef0", "size": 16740, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.5.3.tgz", "integrity": "sha512-pRq8nO+X9UIokC2vZEx7HM6R0eDUQVuKT17zlM7uIL4BSQwMhsDXC1nNoYyNClW+9F03F7MYVNJNgh08tPBDrA=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-fetch-1.5.3.tgz_1464202483131_0.3381968201138079"}, "directories": {}, "publish_time": 1464202483665, "_hasShrinkwrap": false, "_cnpm_publish_time": 1464202483665, "_cnpmcore_publish_time": "2021-12-13T13:13:38.314Z"}, "1.5.2": {"name": "node-fetch", "version": "1.5.2", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "e1fcc2315acd9bcb211ca6c0b1e7218093289763", "_id": "node-fetch@1.5.2", "_shasum": "7dfd59492766cafb6af198b8e954468497a44727", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7dfd59492766cafb6af198b8e954468497a44727", "size": 16215, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.5.2.tgz", "integrity": "sha512-8hWExYfpmePeV0PMfNuoFRTXMCfGFVo3a6YkaFpsUtmWzKJjqqBaY/7Yv0HbjhawuVAAZabJajJe/IpYVMnsnA=="}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-fetch-1.5.2.tgz_1462536779216_0.9876336704473943"}, "directories": {}, "publish_time": 1462536782250, "_hasShrinkwrap": false, "_cnpm_publish_time": 1462536782250, "_cnpmcore_publish_time": "2021-12-13T13:13:39.331Z"}, "1.5.1": {"name": "node-fetch", "version": "1.5.1", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "3f24c09005fe5c7e42c64c19a0ff57a8d0c43710", "_id": "node-fetch@1.5.1", "_shasum": "edc64350cc0bca48a5f79e038c1f7c5ff0869fef", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "edc64350cc0bca48a5f79e038c1f7c5ff0869fef", "size": 16068, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.5.1.tgz", "integrity": "sha512-hWX3yO9vdIQU1M89P+QhxsEOS90wx5QooZlHPilUo4Ip1XErL/dG1G4qOmBah8cyZD6FBiu5GWMkXlXW7jhcHw=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-fetch-1.5.1.tgz_1460487536401_0.19198199780657887"}, "directories": {}, "publish_time": 1460487536981, "_hasShrinkwrap": false, "_cnpm_publish_time": 1460487536981, "_cnpmcore_publish_time": "2021-12-13T13:13:40.291Z"}, "1.5.0": {"name": "node-fetch", "version": "1.5.0", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "321d80075062a8bfaf917d9b562e748624325da0", "_id": "node-fetch@1.5.0", "_shasum": "c1b82cda9579aa57ee05b838278183c6e997f642", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c1b82cda9579aa57ee05b838278183c6e997f642", "size": 15909, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.5.0.tgz", "integrity": "sha512-DFgnQtGNp8Z654olTc8b4RWio5Tr/DGi8Iz1X371y7ALlAqIzSSKC2xoT1Npv4XL3xQQH8hD9Z+WEWbhj0YO+w=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-fetch-1.5.0.tgz_1459883125410_0.07301584328524768"}, "directories": {}, "publish_time": 1459883125946, "_hasShrinkwrap": false, "_cnpm_publish_time": 1459883125946, "_cnpmcore_publish_time": "2021-12-13T13:13:41.345Z"}, "1.4.1": {"name": "node-fetch", "version": "1.4.1", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "98b167c35d4465bdff1c5760ad9676142e3eefc6", "_id": "node-fetch@1.4.1", "_shasum": "f50a3a98a586c434e9a63984c97127eb33c09145", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f50a3a98a586c434e9a63984c97127eb33c09145", "size": 14907, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.4.1.tgz", "integrity": "sha512-ei4wNMHHEfBp3FA5+kWOB9FNGlJmdBsow5KeqX83/HYBlC8BgzULEJy/fPTpVfyn0L0K1G1NS2kLb8c9lhKR2Q=="}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-fetch-1.4.1.tgz_1458718398432_0.22000137832947075"}, "directories": {}, "publish_time": 1458718398873, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458718398873, "_cnpmcore_publish_time": "2021-12-13T13:13:42.336Z"}, "1.4.0": {"name": "node-fetch", "version": "1.4.0", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^3.3.4", "chai": "^3.5.0", "chai-as-promised": "^5.2.0", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.4.2", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^7.1.1", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}, "gitHead": "2dd4ee583867dabc0102b4391a7aa343fd366be2", "_id": "node-fetch@1.4.0", "_shasum": "5c9f574ee1239838d8eb888a8f38ec4590903357", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5c9f574ee1239838d8eb888a8f38ec4590903357", "size": 14677, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.4.0.tgz", "integrity": "sha512-g+ANk8RzDwluzHYRIaiQ+RKvy5OhHIpXpMCaErYiHkxjOrqIMIOQG+GQUlCHVh90dNwY0sd9GN8GcG+C+yN+/Q=="}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/node-fetch-1.4.0.tgz_1458385366564_0.6444221257697791"}, "directories": {}, "publish_time": 1458385366951, "_hasShrinkwrap": false, "_cnpm_publish_time": 1458385366951, "_cnpmcore_publish_time": "2021-12-13T13:13:43.441Z"}, "1.3.3": {"name": "node-fetch", "version": "1.3.3", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.3.5", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "fd3f89fcd94e5bcee31de4cbca4c15cd79999b07", "_id": "node-fetch@1.3.3", "_shasum": "880228be75fd207a8a9baa2042ead437e6edaf84", "_from": ".", "_npmVersion": "2.11.1", "_nodeVersion": "2.3.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "880228be75fd207a8a9baa2042ead437e6edaf84", "size": 12830, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.3.3.tgz", "integrity": "sha512-aru9GLSvU8scV9SMrOa0AiPF8Rst8mSMaN51GGxMlTN3yL7zybAYjHSVefVOmurcGj2pdF0F1Qy8Nekzqbk+/Q=="}, "directories": {}, "publish_time": 1443453317294, "_hasShrinkwrap": false, "_cnpm_publish_time": 1443453317294, "_cnpmcore_publish_time": "2021-12-13T13:13:44.450Z"}, "1.3.2": {"name": "node-fetch", "version": "1.3.2", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "form-data": "^1.0.0-rc1", "istanbul": "^0.3.5", "mocha": "^2.1.0", "parted": "^0.1.1", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "129d9338a9d2b89f211b2d014d1eb23b4e9d307d", "_id": "node-fetch@1.3.2", "_shasum": "c0ef9fc0f86809349269cc8051a459d07c091412", "_from": ".", "_npmVersion": "2.11.1", "_nodeVersion": "2.3.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c0ef9fc0f86809349269cc8051a459d07c091412", "size": 12013, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.3.2.tgz", "integrity": "sha512-A9z0nC+2rE2f1lbMv6K7DbFZAf79958a5Zs9NnYNb8j4O+CoLfGH1gntYNc8lBzYzCE6TMc7yz603Mo344UArA=="}, "directories": {}, "publish_time": 1437551591928, "_hasShrinkwrap": false, "_cnpm_publish_time": 1437551591928, "_cnpmcore_publish_time": "2021-12-13T13:13:45.604Z"}, "1.3.1": {"name": "node-fetch", "version": "1.3.1", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "b5579eb34d13cc3422dfb3c9d3a6d262f3200019", "_id": "node-fetch@1.3.1", "_shasum": "e95c3796272295061222ba67644577959bd4faab", "_from": ".", "_npmVersion": "2.11.1", "_nodeVersion": "2.3.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e95c3796272295061222ba67644577959bd4faab", "size": 11655, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.3.1.tgz", "integrity": "sha512-J7uxqBWA/5FtHDaiGCX/0yU1V2nsTJZSHZXLYGmR3Q1Ok6H06CwJmHQ+/zS0t4+ply+G/1Bw5+Ag/ztcOaoxJw=="}, "directories": {}, "publish_time": 1436615104398, "_hasShrinkwrap": false, "_cnpm_publish_time": 1436615104398, "_cnpmcore_publish_time": "2021-12-13T13:13:46.845Z"}, "1.3.0": {"name": "node-fetch", "version": "1.3.0", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "32b60634434a63865ea3f79edb33d17e40876c9f", "_id": "node-fetch@1.3.0", "_shasum": "655fd3a0c5d86bf452f63b5a959d15498678befe", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.8.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "655fd3a0c5d86bf452f63b5a959d15498678befe", "size": 11539, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.3.0.tgz", "integrity": "sha512-Qq1Pa0KL7MMThqbZXFS3GzSkKqs+O151BaWgfWqdAVOuH+gk3bcpYZCKGrcfRi/z/AaBBzmjFLtblyLuCkh8mA=="}, "directories": {}, "publish_time": 1433393012007, "_hasShrinkwrap": false, "_cnpm_publish_time": 1433393012007, "_cnpmcore_publish_time": "2021-12-13T13:13:47.859Z"}, "1.2.1": {"name": "node-fetch", "version": "1.2.1", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "177d42e00168c87e9ce830a2b796c81a4b8f6ac6", "_id": "node-fetch@1.2.1", "_shasum": "8404802c3346c1e8776e3699c0f32df9be13cbed", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.7.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8404802c3346c1e8776e3699c0f32df9be13cbed", "size": 11129, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.2.1.tgz", "integrity": "sha512-Rr5IC9KXNE+ORU3i7qzXaJrNPy1Wfb5JjcQ4q81lMRFZ61maE0EPue2Xm9gYQ3t6QWAdNiA6KYtQDqLJjoNGrA=="}, "directories": {}, "publish_time": 1430712499210, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430712499210, "_cnpmcore_publish_time": "2021-12-13T13:13:48.943Z"}, "1.2.0": {"name": "node-fetch", "version": "1.2.0", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "60f819f0a0fc4779a5f61a8a3db2faa5459e63f7", "_id": "node-fetch@1.2.0", "_shasum": "2051585c4f4ed0265805a0b783d15bb81634ca7c", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.7.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2051585c4f4ed0265805a0b783d15bb81634ca7c", "size": 11013, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.2.0.tgz", "integrity": "sha512-+pmuREE6qXhMdxJzG7rKxDGBOHHyLj3k/4E0qC8b4ZgenaFNFzesbNTKl6QKdjIGs6cj0rAXU3gYBiGS1l43WA=="}, "directories": {}, "publish_time": 1430647675396, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430647675396, "_cnpmcore_publish_time": "2021-12-13T13:13:50.027Z"}, "1.1.2": {"name": "node-fetch", "version": "1.1.2", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "377bce655fbd62f08ebcbc30c261ab9acccc52c9", "_id": "node-fetch@1.1.2", "_shasum": "cf369f6dd45415dcbebf9aaeb7d7e8ad18dec9ab", "_from": ".", "_npmVersion": "2.7.0", "_nodeVersion": "1.5.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "cf369f6dd45415dcbebf9aaeb7d7e8ad18dec9ab", "size": 10880, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.1.2.tgz", "integrity": "sha512-nkVioIz1DCMNIpOZI9RbkbSMCuZVIZXeq9omWRxYPKNsEsp8fAJIJfquLHl29ilyavGHD/rSDVdgxmGzAUKaFA=="}, "directories": {}, "publish_time": 1430281098100, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430281098100, "_cnpmcore_publish_time": "2021-12-13T13:13:51.064Z"}, "1.1.1": {"name": "node-fetch", "version": "1.1.1", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "c02cb4229d46ff653e33f9ae75cc450c1c608c2c", "_id": "node-fetch@1.1.1", "_shasum": "0f7ffe76f12bb386757ccd566ced04b7d6c3cc06", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.7.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0f7ffe76f12bb386757ccd566ced04b7d6c3cc06", "size": 10728, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.1.1.tgz", "integrity": "sha512-sWucRD35oEoPD+lS7XaDz1B528tJ0Xm2F+B1074Rbb3ofUjV4vj5z7JEAi8tyOav7Qf+31BNMfTLNfdGMVg7ew=="}, "directories": {}, "publish_time": 1429716995892, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429716995892, "_cnpmcore_publish_time": "2021-12-13T13:13:52.028Z"}, "1.1.0": {"name": "node-fetch", "version": "1.1.0", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "c363782b0a88af99963c4b4cc58d472f11bc4375", "_id": "node-fetch@1.1.0", "_shasum": "92a8f0407e5d153494f45f8d05a0c1688f8986ab", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.7.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "92a8f0407e5d153494f45f8d05a0c1688f8986ab", "size": 10533, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.1.0.tgz", "integrity": "sha512-uZrWMd8b6ji4wmNEeR4zsScziMh/B9hsmatzhyAzKPDPo9cPGhT2iNDA7oyUcPRsIJ0Qimqw6Twx5Nd9Q3/rUg=="}, "directories": {}, "publish_time": 1429248799652, "_hasShrinkwrap": false, "_cnpm_publish_time": 1429248799652, "_cnpmcore_publish_time": "2021-12-13T13:13:52.936Z"}, "1.0.6": {"name": "node-fetch", "version": "1.0.6", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "f04609055c0d3c4543ba743abe6d94902c8a9c7f", "_id": "node-fetch@1.0.6", "_shasum": "2299d61ffd5ba0abc4440cd782ea9125ea96fd11", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.5.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2299d61ffd5ba0abc4440cd782ea9125ea96fd11", "size": 10224, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.0.6.tgz", "integrity": "sha512-P3VezxjUSmavUWSdUYkdCbormWXs21XZI35ICXwZThduOMzXxK+LtwDyj19yaBQO7G6wVMICrsa3VJNtZTaQrw=="}, "directories": {}, "publish_time": 1427171506736, "_hasShrinkwrap": false, "_cnpm_publish_time": 1427171506736, "_cnpmcore_publish_time": "2021-12-13T13:13:54.399Z"}, "1.0.5": {"name": "node-fetch", "version": "1.0.5", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "220506757b4d1bd91f0724b5c781430867736e65", "_id": "node-fetch@1.0.5", "_shasum": "320ca8a3e584267df3bd22bd10489b3868e4c8d8", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.5.1", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "320ca8a3e584267df3bd22bd10489b3868e4c8d8", "size": 10071, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.0.5.tgz", "integrity": "sha512-6YkfD5EV8o7BVqlxWvQ7/1IpljkDgG2ompk+Yq/7PqxSlss7qdJXd2IunyQiZ7BIP+YA0Wn4+zP67OvGtwc2aw=="}, "directories": {}, "publish_time": 1426784577089, "_hasShrinkwrap": false, "_cnpm_publish_time": 1426784577089, "_cnpmcore_publish_time": "2021-12-13T13:13:55.759Z"}, "1.0.4": {"name": "node-fetch", "version": "1.0.4", "description": "A light-weight module that brings window.fetch to node.js and io.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "6172067d1ef9704b9af97de2554d912572801360", "_id": "node-fetch@1.0.4", "_shasum": "e324813be355fb832bdfce68b959f8d7707dbc9b", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e324813be355fb832bdfce68b959f8d7707dbc9b", "size": 10085, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.0.4.tgz", "integrity": "sha512-RKN860AQePbmY51DqWYknadsmHt103tGMBEedytDNfdfiEHnQb31mDm8+uttlDZ4j+2aJ9S2nrAMZG+jBoB7rg=="}, "directories": {}, "publish_time": 1423221670299, "_hasShrinkwrap": false, "_cnpm_publish_time": 1423221670299, "_cnpmcore_publish_time": "2021-12-13T13:13:56.987Z"}, "1.0.3": {"name": "node-fetch", "version": "1.0.3", "description": "A light-weight module that brings window.fetch to node.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "6705b3cdaf3e0b472623e7d271ca686a97e61276", "_id": "node-fetch@1.0.3", "_shasum": "7c3bcc966ebaa5fbf9c650423f2bbf204abf0c52", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7c3bcc966ebaa5fbf9c650423f2bbf204abf0c52", "size": 10039, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.0.3.tgz", "integrity": "sha512-IPDUAjL6SaM1BumA4HvVqiwh93rNT2/CxKPrp3CNwlILjOT2gbSmgKJqzAUSdHXPyIL2Sk8S+3sUkuKlJcNTsQ=="}, "directories": {}, "publish_time": 1422459827362, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422459827362, "_cnpmcore_publish_time": "2021-12-13T13:13:58.051Z"}, "1.0.2": {"name": "node-fetch", "version": "1.0.2", "description": "A light-weight module that brings window.fetch to node.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "f0bca7ee663879e5d50814048d9a4cec333d271d", "_id": "node-fetch@1.0.2", "_shasum": "ead209298a4dbb0a0ea151745ff641db67747953", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ead209298a4dbb0a0ea151745ff641db67747953", "size": 9796, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.0.2.tgz", "integrity": "sha512-aiLsT7OVX09FrzghnNIFnCfml0Xx+s4HSaYutBGmlwVgjiaLFD8TjFKVkkHBu7WWLQzWvZxBusWvsy2ZBsGRww=="}, "directories": {}, "publish_time": 1422421524596, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422421524596, "_cnpmcore_publish_time": "2021-12-13T13:13:59.273Z"}, "1.0.1": {"name": "node-fetch", "version": "1.0.1", "description": "A light-weight module that brings window.fetch to node.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "4532ad699ecf290202a2c2bdab9c6c6cc8570d16", "_id": "node-fetch@1.0.1", "_shasum": "ecbccd80d52708f9c0bb6a900ba60672c743b243", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ecbccd80d52708f9c0bb6a900ba60672c743b243", "size": 9394, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.0.1.tgz", "integrity": "sha512-veuM6UtMkVOxDVy9m/1cI3UHHt7bdRpkY/188u8M28w8E6wB7NL6T2AGgNjU2PY1nlN8XZMrvSitoLMYPWI4pg=="}, "directories": {}, "publish_time": 1422382020334, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422382020334, "_cnpmcore_publish_time": "2021-12-13T13:14:00.632Z"}, "1.0.0": {"name": "node-fetch", "version": "1.0.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "index.js", "scripts": {"test": "mocha test/test.js", "report": "istanbul cover _mocha -- -R spec test/test.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec test/test.js && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0", "promise": "^6.1.0", "resumer": "0.0.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "95ad88e527455bdef40c69d936e4bf1da2fae307", "_id": "node-fetch@1.0.0", "_shasum": "342cc3ff90d200d6710ffcba132af37f385df2d8", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "342cc3ff90d200d6710ffcba132af37f385df2d8", "size": 8952, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.0.0.tgz", "integrity": "sha512-B3bapv16pfofkmAiYfoMZMnEzXKDjpMjf+tFWrAy9QB5unqoykArShcjT7sL4WZl4FKa6lKMFVoWL9sWtRPcGg=="}, "directories": {}, "publish_time": 1422380301770, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422380301770, "_cnpmcore_publish_time": "2021-12-13T13:14:01.937Z"}, "0.1.0": {"name": "node-fetch", "version": "0.1.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "index.js", "scripts": {"test": "mocha test/test.js"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"bluebird": "^2.9.1", "chai": "^1.10.0", "chai-as-promised": "^4.1.1", "mocha": "^2.1.0", "promise": "^6.1.0"}, "dependencies": {"encoding": "^0.1.11"}, "gitHead": "5cb7365d5cbf1dabffed31f6dfe6f996cbbfb7c2", "_id": "node-fetch@0.1.0", "_shasum": "26c6afcd56ff31a8de2643a4cba6438dfa844bf9", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}, {"name": "endless", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "26c6afcd56ff31a8de2643a4cba6438dfa844bf9", "size": 6859, "noattachment": false, "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-0.1.0.tgz", "integrity": "sha512-W7ONlZ5CkVlMUCsvU23n8TeisAFXZszkmxfjD2jOqrmjgSkBlAIFlw5WTmo/raflxaxQtgjB5JbLmsKJjMMTDg=="}, "directories": {}, "publish_time": 1422364626232, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422364626232, "_cnpmcore_publish_time": "2021-12-13T13:14:03.270Z"}, "2.6.7": {"name": "node-fetch", "version": "2.6.7", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "readmeFilename": "README.md", "gitHead": "1ef4b560a17e644a02a3bfdea7631ffeee578b35", "_id": "node-fetch@2.6.7", "_nodeVersion": "17.3.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==", "shasum": "24de9fba827e3b4ae44dc8b20256a379160052ad", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.7.tgz", "fileCount": 7, "unpackedSize": 152240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5A0eCRA9TVsSAnZWagAAnpgP/0S16heeJ8NdMa66tYpU\n3kIrMJ1Q0M/suGyz8u7naCWxJ9kBKVIhzoE9wVJ1sXaBNFZ2jbXh8xUl1xY3\n8zO0zUrZQFGfBk5PlbfYL1BjBFidQ4YGOvpu1bRT2KWt0VRpPDqdsTcsSluP\nIfSWc07IgdkrzbztFZ71rhqgP1ZKdnLamZUbRR22xbbxVixIPK+cnHzZbAxf\nsvFPda6Dj49rzTi7vLQ7Y/ePVzvVW9jJAgtaTKiwI7UT/DcdfgH9VOHs+KaX\nCIOL6vtWMOXQ1wJL4fpRyp8E+JfYWRcl9+0eCbmmI4WXjXegF+feBnpn1HvZ\nuHM3hVVywxzRKE8koEBrBpw3Cg4eB2L/3DqwG3pqBxwqR7rteOJ41TqQPJpR\nR4R7Z+wdOZIXXz+cVA3SJM19NWOh84UKA9pN92LuEk9n1ceyv985YkuvhAdr\n7G70zzap8/Yq8RuCW+J7m5XeknJf+Haj3/YSFv+km5ko8tSpaZ83+3AzNsKp\nW3IV1nEDgFKJ9Xz535/PPADwiWE3fA694+QG7yk+XvnZz8XxdDcokQTw1zce\nXraFjMR8IQFZwr/f7O3qOK2SLVPN3YWAiiHWhPdlPW1E5wXsq7PQreHadFH/\nDW7RzxLKY8355V9KimR0MjLJYMrZ8K5HJKE7G2wuBQgu1FHzyJC5mqyFc1yx\nrahn\r\n=46Wm\r\n-----END PGP SIGNATURE-----\r\n", "size": 41745}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.7_1642335518665_0.3533929588013822"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-16T12:21:16.643Z"}, "3.1.1": {"name": "node-fetch", "version": "3.1.1", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^0.3.1", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "delay": "^5.0.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.3", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "gitHead": "36e47e8a6406185921e4985dcbeff140d73eaa10", "_id": "node-fetch@3.1.1", "_nodeVersion": "17.3.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-SMk+vKgU77PYotRdWzqZGTZeuFKlsJ0hu4KPviQKkfY+N3vn2MIzr0rvpnYpR8MtB3IEuhlEcuOLbGvLRlA+yg==", "shasum": "d0d9607e455b3087e3092b821b5b1f1ebf4c2147", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.1.1.tgz", "fileCount": 17, "unpackedSize": 102809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5A9aCRA9TVsSAnZWagAAO9QQAKQsYyp4RUAxLqw98RYg\njYofPzNKlvBx3dzTmULadRsdu5+G0yt9VWiI1mKadYVYQnkg8wsWycMVJexU\n7xRy2Izv7gwOzqlCGXbLKYcS//4NbFMLgAmg+ufk8nywV8trCnfZLrgrVkit\nBgVN7yMohvMVRft8P7Uxcbpy0IiSsLFzJV/qwYGTjMKD5m2OMDoebwtrC5Ig\nbWiGqLu15ZBMR4nLkasyFTHLZEbGj4/RweuJ6Arfrs594oq3xLsrW6d1zgji\n2q9FwLNBDDd9BgIixfvs9tlMCGjVL5aLmA40Iqtqw5wl9NZySn+V0jt6HAHd\nTxCclLuh980kMySmENdiKwiCxafKC8HsLOeh0bsiIssb7LcVdkoD3lrcbtPl\nID7R8dLtnzIYGJczs4IM6Nz/Xl25iSjwhe+/eALLbsR72yuPAcbJU1IXIAHH\nULk+XAKLJxB56eysFKtrMwjdrxju25ZOKRP9FFmed4ejlWMSNAooNblBQice\nVCb7r9gP9quxShpInbxqkgbTSOIne2nJ5S6ufKtLCuZLWC7/A+nvEVsBe13O\nHX8iaDlZeSYwtMt/5xW6NVNJRO0i22O8aaYw/yR0sW+iqPMf/J0xqgd25MTK\nFcZbhhMttiGgF55kbQv+MOvspFDP05dfV0HuHoH6qUPaZz3T+Ji39mgyZovH\n+KLY\r\n=Wbv0\r\n-----END PGP SIGNATURE-----\r\n", "size": 30514}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.1.1_1642336090375_0.17474613684700424"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-16T12:28:51.898Z"}, "3.2.0": {"name": "node-fetch", "version": "3.2.0", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "81b1378bb3bda555d3d2114e7d3dfddbd91f210c", "_id": "node-fetch@3.2.0", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-8xeimMwMItMw8hRrOl3C9/xzU49HV/yE6ORew/l+dxWimO5A4Ra8ld2rerlJvc/O7et5Z1zrWsPX43v1QBjCxw==", "shasum": "59390db4e489184fa35d4b74caf5510e8dfbaf3b", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.0.tgz", "fileCount": 17, "unpackedSize": 103830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6clnCRA9TVsSAnZWagAAPxwP/2IR/peRyoSO6iOXq9vs\nl8+EYCrfGIwcjk2Q5Xu99toXQ6F9XwwioJ0FH/E4h9MPauZnx/lsiwuVbEO+\nyTirqrLCY6SauD0GZikfq4G9auabSECsnf6hK6mfUIuEAKTPoRA3xZb2s+8I\nHj518YYIocWqX5tuyWs/RIMciNGjJOQao81qTlOAE67XXz2KIlte3rZ74y6L\nrlrKrToKp0qavRvcvaRxKPNk0w96thhakIxNnDKv4+S6SzpjnefA4rB8QRmO\n2q9cSuaFI5Z7eKPKWFuS+0w2dSQW4ZCyfF8mf/6aJEidH2CmeEvl4hehsvsU\ngwBnWUdcPeUbYkNwiPDFF1xVj7UlZvKv4MuuYaD/74gOCa1nrxXk3s+d1CI6\nOqFGdVKcyLkjsmhdAgv0qwt0kox7gi8RjmlvkP6zIvvVrjjkpwCbKWwDUnbG\n7U9JhfAfG6C/1X++ZpceGJJA5ZBFqZne+oBEBhPxm0h8PBVGieEMU+zbG5Io\nwIKGFjls42PDQihUT/fKaANpidCtp50V/qhm1vYpOzQgIT7EOji+N5PYdjDk\nsLwQ7Iwq31xn9VNCa8cMq8cYWPrfHvfHSVF176liukVG4Br6cSOIaIDf0kpd\nd2CVxUcmNPYXJF2oAYprF+5XzZOtT1cYHV6KyDtJWrC1IMbHcxfQVqmQczA/\nG1ft\r\n=LvgQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 30727}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.0_1642711398986_0.1632718206841841"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-01-20T20:49:25.627Z"}, "3.2.1": {"name": "node-fetch", "version": "3.2.1", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "61b3b5a06384003d332581080af6522bec19417f", "_id": "node-fetch@3.2.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-Ef3SPFtRWFCDyhvcwCSvacLpkwmYZcD57mmZzAsMiks9TpHpIghe32U9H06tMICnr+X7YCpzH7WvUlUoml2urA==", "shasum": "002177382810cfb77858857f69a3621a86c45f26", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.1.tgz", "fileCount": 17, "unpackedSize": 105756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHe1yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+8hAAkpzTWNP8VHkHA3YcsqkkW5lAT+VQNjcbMVbnRYikxBkl25t9\r\n7M2UF5aGN7Qr7LScDmoeMH0z7nq73lHO/VnmNwwBvTfFgCIMLjOtyjWA6ye0\r\nnBNuQ+kxJnD8N6sM3PLiLYRIJWLDFekRl4C5jvxqyYmpph0MNaozFNzpTDeD\r\nHXEeg0f369CRZOwxK3ZLpFFcm9DDSvU+4Nbg9roc4XiOmbMt7Ii5phl8l4rt\r\nokiq87wEg1E4BEDSQCpCExnkyMXsDUPqDUxUYB7t0bubGc0AomrOJp6NllYw\r\n+pG125HiP85t0VRQbFe6cVN+BujBaXwMfywfXHfxc8IocweQgzLSuGZ5w1kJ\r\nQ7EabNZxiIVgIhOmwKbJw/CPVC5Lv7zByL9E6kVuNqAkEqm8eSncsqsf11yH\r\nZRDCKlF2b0pQwX1fQZbMZz/suEeFP2Kz+skQSudqY297xggNvW5gbzlnxGvg\r\nnrXwZ2+8kt2rqqyUNiqQmmEQyYQpv0z9GrFxLzfqGs/hcqH1zjC0LNnXP3IS\r\nU5sY8QrsrvezmzaWCxdmh+10Mcie7hAo0Ue1O0YQMhPvFkj8jh/NTo2+TbPz\r\nSdX+oo9XZ62U/RV87aA3f3NIrB3+OltyZDjw0BNtLXrHkw1nZng5d5EJ9quD\r\n6AtrEC05PEsqRUelpS2f8RelhLMLY/y5ICs=\r\n=lOSf\r\n-----END PGP SIGNATURE-----\r\n", "size": 31325}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.1_1646128498397_0.038169238426281504"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-01T09:55:57.897Z"}, "3.2.2": {"name": "node-fetch", "version": "3.2.2", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "a4ea5f9308f942400695cce261291d0a80cd1b02", "_id": "node-fetch@3.2.2", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-Cwhq1JFIoon15wcIkFzubVNFE5GvXGV82pKf4knXXjvGmn7RJKcypeuqcVNZMGDZsAFWyIRya/anwAJr7TWJ7w==", "shasum": "16d33fbe32ca7c6ca1ca8ba5dfea1dd885c59f04", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.2.tgz", "fileCount": 17, "unpackedSize": 105822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJeUEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrbA/5ARhrTzQxQSdhto4wAZY0MSluvNKSCwqd4is8qBW6InmYZ8U5\r\nhvl2AYIaoQoUYZMw+jM81VlcfRHaOksqH49MlK5zdRZPUe+YZ0u/OONkI3WD\r\nU0frQGkSEmoba3T13B8psgWvu9AwKsugKLcif0RIDvMeVLdXRqW6smhkiPHo\r\na7NbgDn8mLtGVLyqYaRZOG0E6UMz0ajbq7PCPlotgGdF7nnzEEQA80Umti0E\r\nMoNcOI/2GyRUq6cQVgVRbZZ3OvZPScrND2hlMEQ7rCgLpHaIcX32lK2caMiP\r\nuURBB1Z2PdZ3s5YX3+vXJX31lPDwZLMw/qt8nz4Ol9PqIfaiafWSeUP+b5wL\r\nO4RryZYIQDGSC6c/sGWEscx3vbog2xiTsCdPBAOWden6mxBZlUtgh7PqHVoi\r\nTl4PZvs9D6gFeVqhkNpiJkWGpf1WdQ7/gGzgBMB9A3jjm+tQSGsxyDPjBqDq\r\nu5DqbAkpU52C/RL4K/zLkHJpW2+XtYp9KlO9r9eOXhqVZKIemP5OhfQEVGzD\r\nvkCqymJwfL6a6eeRfGe7fLamClV2x6Ux2HKcOlyCzIjQyYdi76izh98tEQEX\r\n7m7h9xs/UREMLRT8RPoz0/rPwu0sB3E7bDQXZeS4Cr+up5F3IZ+Hvsjz1lLj\r\n2dX/W673wxZLKChxNdVWLzQywn4nZTlrKrU=\r\n=wfuQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 31351}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.2_1646650628521_0.4891031133718875"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-07T10:58:09.560Z"}, "3.2.3": {"name": "node-fetch", "version": "3.2.3", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "6425e2021a7def096e13dbabcac2f10e6da83d11", "_id": "node-fetch@3.2.3", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-AXP18u4pidSZ1xYXRDPY/8jdv3RAozIt/WLNR/MBGZAz+xjtlr90RvCnsvHQRiXyWliZF/CpytExp32UU67/SA==", "shasum": "a03c9cc2044d21d1a021566bd52f080f333719a6", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.3.tgz", "fileCount": 17, "unpackedSize": 105816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiK8QAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrang//bDM3eZ35OcyHYfJcDl4Pnq8s933AOMguJkLbor0Iq876pVus\r\nLN2GKu0f/bwvv2LfZf0MNeV4wXdvcQV0kgDLTnP4hSeP8qm+yDEpLGj2WV0V\r\nBy3Hfg0kmIDuXd7ahdoZ5Eix0crUQCE0h/tER4qUFTcSBYbrJwO1ujHWRWQe\r\nHmOHVfZHk56ti+CCTDlVPXxfL0R/3u2wmaeK2vjyUbyhjYS0Ln6udnCRuaSK\r\nh+gdh7EhWWX66C0Jyz9NiY/3SGVmf0YG6+u7/ELkdqmoqxslAxd3cb5OGhFI\r\n9HTHvlVkBbzH1v4dh0JmIB+gqUDVgMruEEbs6y+hq8typJGohTA4hWkmkWtj\r\naNTVGrxiW+Ml+ldmB6iaJp/m2hUpY5t8gFjxF2RlyzILaaAQpN/ap7YdKcLB\r\nfiX+9RYr88H3zk3l/PjaK1zVJ7z8sC40m0w7KghT59F7LVkazJOY0cT9sOdS\r\n17tDyHzRCvGC/7DVTKBQ4Z6+Uwv4lQ0RNFAyOfjAXkof9JXh/1/l/n/ZyIhB\r\nkhdJLgPR2YyMoOWhpj0HRLcfDnn8a24gUqh4pP4jaExltV6o3VHJ2RMEPRGd\r\nnCGqRCuSORFofEDuyrjUPr9QjI6xHqYqB4HLFHR0mlJfpYq+dxs7gFoZ7FpA\r\npQxV0A5nLWpkc05g5xbmacKmZuzrES5GeR8=\r\n=huaC\r\n-----END PGP SIGNATURE-----\r\n", "size": 31370}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.3_1647035392795_0.1019260827976034"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-03-11T21:49:58.320Z"}, "3.2.4": {"name": "node-fetch", "version": "3.2.4", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "004b3ac8324e6cdbfb5d04b8bbdc6664ea48fbcf", "_id": "node-fetch@3.2.4", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-WvYJRN7mMyOLurFR2YpysQGuwYrJN+qrrpHjJDuKMcSPdfFccRUla/kng2mz6HWSBxJcqPbvatS6Gb4RhOzCJw==", "shasum": "3fbca2d8838111048232de54cb532bd3cf134947", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.4.tgz", "fileCount": 17, "unpackedSize": 105883, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/ZVjzJxpCvqH35LktaWs9xnKZec332Hiiambg2Q60PQIhAO46EoFeVPmDpP71oFbF20sLwWRcCND1/eabfgT+eHzu"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJial+OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpE4w/9GrGyU1qQlbSPUZOnSIWgMsO1NbtLPZxBTOa2ABKGv061Tsqu\r\nWqqWvw4mktuu4rcb+H38zFzc/QeMiUMi3IfD2uqofwoe6cUvJMEs63IKXQY9\r\nnOz13t2F7fD5UzVNBIpdS2W+q/KDEdmlFJu+eiEEkm6smYr9fbtbDfJ9Cqkd\r\nXkEkCeFA4+eewOWNlHSb6ULPdmAr7I2aL0BfhZybmDDvzhAU/Mz7im19/8vg\r\n9UZvWMXQLaBxenbU7El8q6LLk8StovC6Bwo3jRErDZjaSek72jM5XQ/WR0j/\r\nIt6aw/MCX3GWyA0kLiGLLMrk1Kth70Qsw4XoR7LamjPcGG37UZNjQgCeKgVL\r\nPmkB+AEDFOuX1GvdCwCRfbUWKDbRgPMJSS2+TKxUuGHt/49TY/gleyCA40C7\r\nYJySyW6+D8hZWj0+mzjwxVFHDpXpqKxADZwqT9h2YA09KRThwexa0KVRGBXb\r\noX3/xugFoglEcadOyvXi+VYW+rjy+eXyJkV8rYDDMNi1GTaZaPBGRiNrg5+H\r\nXwpiQ9D4/Nu1K1cIf2yF/La1/gzv05qOGhirvUjn/734pve5Qd1+OEn4sNdu\r\nM5ktfZkXg0zsoyvq2YdjEFwv7jECeuUm/XPIFIOIP1rQhTLymwOWAOA5qeRY\r\nr1f7I7h4ZYIl+BiXEMN7OMbfeJT0R11r9q4=\r\n=LNVb\r\n-----END PGP SIGNATURE-----\r\n", "size": 31388}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.4_1651138445892_0.47448392096352143"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-04-28T09:43:49.093Z"}, "3.2.5": {"name": "node-fetch", "version": "3.2.5", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "a92b5d5cf4457c2da95d8404b08cfd06a426a2fa", "_id": "node-fetch@3.2.5", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-u7zCHdJp8JXBwF09mMfo2CL6kp37TslDl1KP3hRGTlCInBtag+UO3LGVy+NF0VzvnL3PVMpA2hXh1EtECFnyhQ==", "shasum": "7d31da657804db5185540ddac7ddd516a9a2bd26", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.5.tgz", "fileCount": 17, "unpackedSize": 105945, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHYOn6fvVBM+dDCd1SI1sRRKEt5hzhOZ24T55yk3PXobAiEAvagtWCO6E5PHuAGrHXaJDTqIL0SzBshhuJq6CiaJHP8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil8GfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopeA//ZMF2s27un0TkFQoK9XXW1gUYH/tnYV0ojSy8j0P6AIxl8Tum\r\nLsbOwjlqVZK5yCZtq8GvkHaadTDjWQTTonsochw2zdmhDJt3sk9Xf2UvV9EH\r\nZPiG1gS4OMTFJkND5gAqeONUmsnHfEVs9ir/uS/MK4vlNVZ0ChZOwq+VdEwV\r\ncozOMtpfKistrFNQBdyCpUtwiusu+tZQfSG3Me9iJJaEgDLPHyF7xxq1Fh4X\r\nuIw/Rtzrwp+h8sk88V5N4DJuMCY8m9v3F65w/NQpnIqGB0GJ3ggaB0kChKPG\r\noef9W2eUoyB1w20T3edwavJl46eeEb8pR/GNaSWMn8bymH0nIu6f3NMlaKIn\r\n0u/FiCPIva0IcYItP70FIu9ipH03d/EG8Btljjx5IJEIhK+A44BnSFygmDns\r\ngvpf7zXASNgeBt+OazloEgNotRik+zDNr/Epq5P5Gbf8ntdvkP1+t1yzFwxv\r\nj93hygu4JMCLyhv4I8sry4xYGb5JMmI4NnluGDp0I6y98tZj0liDo4/RkcfE\r\nYnQuw69sZpZNix7BBXKH2EaHSq200NbmnyxMHwYq6Eqow9u4luTVEsIfw6+b\r\nK/I9H1dMcnBSA1Ifh1YEAZA0tdg9DKm6FJ3O2LI1kzb3pzKCmeFmrhmKkNJN\r\nolWshn4PsyRr2WpKDBah7psApN0UL9h6aBs=\r\n=rXlQ\r\n-----END PGP SIGNATURE-----\r\n", "size": 31397}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.5_1654112671648_0.833397215874595"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-01T19:46:07.639Z"}, "3.2.6": {"name": "node-fetch", "version": "3.2.6", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "1c5ed6b981e6c5dd28bd50f5ab5418e5bd262b99", "_id": "node-fetch@3.2.6", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-LAy/HZnLADOVkVPubaxHDft29booGglPFDr2Hw0J1AercRh01UiVFm++KMDnJeH9sHgNB4hsXPii7Sgym/sTbw==", "shasum": "6d4627181697a9d9674aae0d61548e0d629b31b9", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.6.tgz", "fileCount": 17, "unpackedSize": 105987, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkeiN6nlZq195KWQgIiP2Hk686q8TKhF9rIWFwXmBkAgIgcZMqAP99szJqGHWo8dv0mohLEeIBVJz+kdYlNkRpmxE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiodGnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJyQ/+L3c1pj/5ZNwpJJmAMlbR/hPT6dZclPILE4aQNIB2ktOt+5YS\r\noLIo7y3llP0A3GPv8brC3hi3evBD21oOmObzj9Vn8c5vsIwhnMdyAzquv+4p\r\n2sVEbHu9w5Z7tFKS4Rpd68D21x0dY5EYenzD48gA4AS/Sv+w/0ivcaLxSdKh\r\n991VTduJq5Dgoe4Ef6Ju90VZ1P8UO5zXzk/hnlrfGpqOH7hRUUw3yavxKwNi\r\nKO86mUNEmQKLgBjCFWOlYsPYwgSVxkWEgMMAxys3ZKhZP6DuuocQTep92BA8\r\nF4tB+oZYdXng9PKv+54vlwp2jL4ucrsnpbc2mLQFXJ0wHhX3kMfJukm5zZWU\r\nNIyAOvdYyLPrb84coW5lsxDTiZ6sGu7kyBHAQ+sP48MA5snM4Gir/dhrnxtc\r\n5ZhlcUdPV/XmoVyFRu8BbdcV+0NTsIFtcoVMAK11i5cpIk4oKUG8KfaZYwyf\r\nvS7cTUsLT44rlnMVJygMvDSw177mSdNVXlrkJFnDbQQRPGUUPxcLlpkBWtxt\r\nDUIATQB0Gts66iP0NJntjJNL9kgc0VDxlxtq8v7OEyWhsNHMQQDjiFD5YJdT\r\nLXwQy34gzKNIF78nER2dM0EcbuorVuISHMuJpROSqPE6cluZRK3o4W+DatWT\r\nbQhombXeG+7rTp7B3YSGz+MQ2MLAp5d1XE0=\r\n=BqGd\r\n-----END PGP SIGNATURE-----\r\n", "size": 31405}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.6_1654772135599_0.6798685100898683"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-06-09T11:19:21.575Z"}, "3.2.7": {"name": "node-fetch", "version": "3.2.7", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "4f43c9ed63da98f4b5167f0a8e447cd0f0133cd3", "_id": "node-fetch@3.2.7", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-xRYIBUEoMvzvLt0FT2mAKAGXps1Wm0GpswR63pPJC4Hp5EubeigGYJJge34f3mPApap4+2B3sTKyUygsZrkmyg==", "shasum": "30739f7becd068ef8a741b8a9509409f0f835cf4", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.7.tgz", "fileCount": 17, "unpackedSize": 106007, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVSd1ZtrlTh/kN115RQa7naIn2Hl8Hh6qyLkkKrQHo2AIgH3qKyqMpXJAHiGLWdKhB5UW7MXNVU5YbJq8CC1Zk5n0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizKgpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrV1g//fDT8m2e0GtY9aGNlN/xLA+ns3uiEvpaZN9Wx8gKOVeWy1hrK\r\nyQmaeu+ye8EB6G+ygwETfU5g/4u/K6EeC9F1mVbfddXn8pwRupVLQk5UohEo\r\nSd06Uf5P2Sqen47EXwRyWxyf+/sC5sEEOCPLkjre3xbsiJi7AiAiti3VtJUm\r\nIj/X2nEw5QVeLjElfAh82FjzEs7zNXMgzpIuyK/xcLvctNdtwe/3WCLCPh2z\r\nOVU2hykx1kkWpou0mloDS8AxKShMlhXj5bAw1UQEshGHKCKavybHw6D0GFjc\r\nky2f2S9z/s9nNq+lQInTyImeFYHHqC9bWrX+vh7ocOpf+0+nuWZvxRl1Z/cB\r\n28q167qEAdZUJ8fEU0eLRtlV3DGG1f6RqnXTHmxmXtXyikhlwmkXO1RsN8jS\r\ncFnrgFzWl14+tnGBcLJ+g012jKqJlaExhJMFJl9MVPLw6uiA7eCPv0EfWvrh\r\nUyKP1GObn73+2+h7/d295fWMZJvv6F0teH+ewSyDvIwGMnjXXHzM+rXD6naO\r\noz2urcxHjqU9pNI/MRzo6INoqt/YWyxwrJcC5/YXZGISi5N0vNzOGbNVz25O\r\nsks+q2h4KDczUH7HFj/cNS6RIvgzMtLo4tzu3HFb382f8gkIIrtFQFzPBXme\r\nB4T0Rm2+u7QBCRKXG4Llb7XGwvrqlT1pCt0=\r\n=PtaE\r\n-----END PGP SIGNATURE-----\r\n", "size": 31421}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.7_1657579561494_0.653545767003173"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-11T22:46:08.361Z"}, "3.2.8": {"name": "node-fetch", "version": "3.2.8", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "11b703361134340a8361f591d6e3a0bcf6a261fa", "_id": "node-fetch@3.2.8", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-KtpD1YhGszhntMpBDyp5lyagk8KIMopC1LEb7cQUAh7zcosaX5uK8HnbNb2i3NTQK3sIawCItS0uFC3QzcLHdg==", "shasum": "0d9516dcf43a758d78d6dbe538adf0b1f6a4944e", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.8.tgz", "fileCount": 17, "unpackedSize": 106007, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGtFOOgge83wwYnRTA1Drjh3qaJJnjVdHHDrKeBDNNjVAiEAuxnubKrLQbvKAG1QIO7N6Blgc/ZJY9PsIbpZwaF3d5Y="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizaIWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpufQ/+LYX5MWI7DiD32O2yeA4rQ236AG3gI92tJTuK3lWHhkrr7h2r\r\nJD8WRCsNrpVpyHB8TayT1rXMXlBhCKrLTINktyrxmfMeWwdqAB74W/KIx9KC\r\n+VLM2LmL5YNDL+fA39PNFA/Mmvloe/hMx2TKRb3UlE7HQNFhruBbCbpbVGOq\r\nmmWRXaubwAzsWVP19dhn0u9gri1EYlbZUNE85WBqUvDcd0gd2vzatbt1AIP5\r\na5pb+Y3ODwMWMl4Wv8YOo0g8+ckUuQ6/b3YzXxAl3TUufSDyAow9EkcB4SP/\r\n0I0y+wqT+ESdWLyQspity3sHQ0tLvOpvAuJcTHd8wQMr6qfqQNkvnEeVZXJy\r\nDLEw8GHydxdVGhdGkwcyQzLvnjN4PV8foFBYS6POhd24Ze/ioGvOwcELtxDb\r\nv9oV+0+Y3P6Xsii+kKmbW2RqGltBC3SSI05PNrxg0jFQcBvBvzbNPnTDdF6X\r\nyY5ca62Km9Ic44G8fgVNr++JKWUwfW8Afb8MCIjn1DNmOPkCGGUeWemlz/o7\r\nkBiFR2zUKASq2xLPXO5ttGLnXx90RcmrVO+I15hNscCCfyEWZeVAaWENZ81v\r\n8Clh7DiTb0xhT/1uThRLf5BlBLuyuaTFmWYHjs+9452ogaS7xRLUKKBKY9Nq\r\nWyDZXbYTzqnN9B3B0diPNeMbuAb1YCoxSiA=\r\n=8ALq\r\n-----END PGP SIGNATURE-----\r\n", "size": 31421}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.8_1657643542724_0.45322373448868514"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-12T17:24:05.175Z"}, "3.2.9": {"name": "node-fetch", "version": "3.2.9", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "e87b093fd678a9ea39c5b17b2a1bdfc4691eedc7", "_id": "node-fetch@3.2.9", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-/2lI+DBecVvVm9tDhjziTVjo2wmTsSxSk58saUYP0P/fRJ3xxtfMDY24+CKTkfm0Dlhyn3CSXNL0SoRiCZ8Rzg==", "shasum": "3f6070bf854de20f21b9fe8479f823462e615d7d", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.9.tgz", "fileCount": 17, "unpackedSize": 106651, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6oEVBFQvCDSS51PwroXpnYvkAHylZuM3pRA2XgEikHAIhALzApIL+uCruOvrz95UVp2bLD7S+fbe6QCa24nWb3xZc"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XpnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZsg/8CIkvn7SHxiFwkScyP1zWDeTi2d69+ek8KhYfbDRYYCILGglO\r\nxBhF2U1WLEMoxxdkEPmBA5P/nZWTTv5jIxKXvkzesa0E2hD7sQE+dMmXXX9F\r\nbovRR43UtpG3ULrxy/3/62ULIT38kHfpOJ2RywRqCvMfFeVpKmjXWJb7joo6\r\n+QROqHVwFdLTL8FWdLj6uBo0D3j6mO6R8NP1P+5n8032+GlmoKQZtImgdXlS\r\n0WF391nnMMnMkSZPFoSANppfR64z67V3Sd6B89ai8gzfzNMvzezw1VZcJfuB\r\n2ljIAvqotBdpPUmU91Ha/cXiQj5A58Ahs3dbyN7RknwxAURpImjhDJ3WWPz4\r\nCYmjsLxYiuZXvXD9G6VRWBJbhz3te9aDzL7gIT2Fjw/9vviruMvUjbCoidJO\r\n0Ef6CQD31YN8/IEwR4lMmaapShJ0QZ8zsEqMIRwUN85n3YFPPhNTZzwOWHL2\r\nRZnu2+PUZ6jEi+wNetp6eLEoNDSTm9Xn+t0sB/Rmhr+Zm5HUsaZyJD3MFDvC\r\n64pW3wtWldda8+IRArt/dN6a2qDat5J3qXhUdvymP7pCuMxUJp4AgYrkb2I8\r\n7nFnhUkqNVBa7lFlV18C+h98Nzb7dkCkPwF4XLIfmQZTFO7xAr/u8D/4z4Jv\r\nbNvpcFe7gBGer8uSi4EoN8IImvpIi/lm18w=\r\n=YANg\r\n-----END PGP SIGNATURE-----\r\n", "size": 31522}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.9_1658157670835_0.48376806435424236"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-18T15:27:38.547Z"}, "3.2.10": {"name": "node-fetch", "version": "3.2.10", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "28802387292baee467e042e168d92597b5bbbe3d", "_id": "node-fetch@3.2.10", "_nodeVersion": "16.16.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-MhuzNwdURnZ1Cp4XTazr69K0BTizsBroX7Zx3UgDSVcZYKF/6p0CBe4EUb/hLqmzVhl0UpYfgRljQ4yxE+iCxA==", "shasum": "e8347f94b54ae18b57c9c049ef641cef398a85c8", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.2.10.tgz", "fileCount": 17, "unpackedSize": 106676, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBoVT2fdra0bWratUFddLPiPdSR2eys2toDgY/3X4riWAiEAgjYUCoxFj7y8rcKiVKVOmzh8zcshgNWi75xduH0HcKc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi5jcRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpm+w/8CqYDh1gEULIa+iTWwtWq9e1IToCN4udtJprMa7E7kj7zyeZg\r\nftWeWfB4Fjl+fZA31cWyJiEjyWNAuGQS/hLbOw/lZACAfBYKFmdZtlA3jdFa\r\n+4dd0kDa8fQkb5SG+57NZk3XfMlKT8OLVwYNt9laUw+uu6G40hXSpCEHoSi/\r\n0zIGFQ7L0Ku9PZPXnlEhvI2SsofauzTP5JQNybB3jEKBQFM2DZUKZuTvgCeP\r\nm3qVCSP6TnfqFX4m+S3oUwHlUdAO5FmrSN8LB+FdH1qTKlC9k2gNtRf9rO9e\r\niITv0j3y5Y0TKn6l+gQsm9z+ywBTgPaJZ1g1+SkXmqO0QoVshYoU3ALc7gDi\r\nAYWRjdsb+HtWn/EcczvrldMDNdTwvFLkhWdAoZdnntLWQ4TC+ZNfDCm36WHO\r\n2OXDGWewyQ8vethhh90XaPa0WbsyvdVLGgZGMRITk/P0ibS/Ue3uWMMs4Bm/\r\najuVfvMsY1zwSN0cCBOWVh0B3Pv0ys6ZBJxl93IAsmwmMljkOBahkWR1KdKj\r\nBMyXVMNRXDZb43abiusCqj28aEhYnUZ3h3VIGw+SZVK5WQrBcBVPJg4DKxyQ\r\nXgF9y+DeQ7Fz/d7EqWmhqpnYcSjpIRhoyFMw6a94771d4MDTGXoMx+ejnQpM\r\nvvh9RHfveSGdzuI5mhDPGeGB1dO5IeBxIjk=\r\n=lHdj\r\n-----END PGP SIGNATURE-----\r\n", "size": 31528}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.2.10_1659254545184_0.7336737707371426"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-31T08:02:35.854Z"}, "3.3.0": {"name": "node-fetch", "version": "3.3.0", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "55a4870ae5f805d8ff9a890ea2c652c9977e048e", "_id": "node-fetch@3.3.0", "_nodeVersion": "16.18.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-BKwRP/O0UvoMKp7GNdwPlObhYGB5DQqwhEDQlNKuoqwVYSxkSZCSbHjnFFmUEtwSKRPU4kNK8PbDYYitwaE3QA==", "shasum": "37e71db4ecc257057af828d523a7243d651d91e4", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.3.0.tgz", "fileCount": 17, "unpackedSize": 107098, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1HQNPG/fkiNeJpSMqrRtkOd0qSba2wtkOP18Ltia2agIhALBjSyX0cwvYRxOYNi9QKlaSMsYG4dOXQa7QJOrqte19"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbXFyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8dA/9GQhkzw735Ih/sMbH0LxfK+f9HMmIKVTD9FyH5tPOK799msIX\r\ngwVRll1+HCKjL8YkKHpLj9Zx4ZV4agAWv8v/FKEAB+jnpXWzg9MwaQlABqqB\r\n6hL6m3VE78eEjKBSxZaXjUVAOhM6Dikk1Db1ICWApN61SAtl6NHPO+wViKK5\r\nYgiz3T2LN/I1O5COANdxOuzWFc8SsrSR1Ip2PWZ5VAsNaK5x4pMDFLYUZYyb\r\nNLs0HDHS8rHmI3aclRP4e9iSlM5X3phl0tMw+J9c<PERSON>wai+QVM8MjJk6LiGyIH\r\naHbMoPGJAcFe031Rlt7Xf3ph3DOzE+MKgcqCsb81NZX/3CXIhIShWORTplLU\r\nyMu1b+GTdMpR3Vo+hDg3hiBkbWzB+AVo14WdWbYh3NL5p8pROOY7pcH65umY\r\nXwbA/kPz/rbyzoifLoPcmGkSFmSdDj056j1wYpBmpmVustiubJi7Urpz8UEE\r\nPcM73e5VJPgS5viAhdh6FX4kB/yd3iAIlpEiFVqK/UZ6VDnOyFfG+e2DqPWO\r\nc3m1q5sa6kv24OGCTyKkywkv7f+PBCxXNhlf7qJSrwdJYXrGLdNg98BdB/+D\r\npZgqfZx9VSlq0Mh6P1kiNWgFIsj0k+sEUXVAFYQe1A2AXIW1cQj/ylUy57Lx\r\nlm2sFcVJdl2eeUeBFxhnNA6PSReANo8vCF0=\r\n=q64t\r\n-----END PGP SIGNATURE-----\r\n", "size": 31622}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.3.0_1668116850272_0.45059815705997797"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-11-10T21:48:57.849Z"}, "2.6.8": {"name": "node-fetch", "version": "2.6.8", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-polyfill": "^6.26.0", "babel-preset-env": "1.4.0", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "6e9464d7e34dc323edf4dabad7615dd94ab847bd", "_id": "node-fetch@2.6.8", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-RZ6dBYuj8dRSfxpUSu+NsdF1dpPpluJxwOp+6IoDp/sH2QNDSvurYsAa+F1WxY2RjA1iP93xhcsUoYbF2XBqVg==", "shasum": "a68d30b162bc1d8fd71a367e81b997e1f4d4937e", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.8.tgz", "fileCount": 7, "unpackedSize": 161490, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTSl8IQ1mJgP/J8zwEZwHgkdwRF0B8mfAuquJWRQzjVgIgLY8yVxn+8SnTrW5mnSAaqZ+OQTNdvwQydRrStlFFL+U="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwK4UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6+g//Qfdm1aCIwQT6TtZRnGWdSmqZ67rsSGZmWaJhQRGm6bwMvShl\r\nfAsfrJYZQTHvnt83aAUXwFl7J64AUUVGoJ05Sens/JCS/icOMisOaPjNTrKT\r\nRunRzeNAiEE39g1qsDV2ksEezhHbVJShnBqPJ77+iMVDtzPGLH2TCOiWCo2/\r\n/oa/5ltULJitdd6UslLD7grQtk0pFAOFebbnTp0wq4jNXaWlPJ344NhfQfHo\r\nuxkX4diq+uIqwDfs2/eHE6kheTzcTVrFEz776tmdZ+0QaA3lVj8UcskYHkbg\r\nJ7SphbckmMdVdFEZpqsz1Qbo/HA+5jjsz4tFMheRjxu4yAGhLExTLwFxWoJ/\r\nIZOGIWsbMwarFn/3MbIw6NCQ6ZxeoL+prqpqKEUF3vZ1w7CxeqMPdWD6gZTT\r\nblK/irVi+lvZ1DH2bmjF40YJvo3gc1tKjnNSqyQD6uI12EqAIYAN3yNRJid1\r\nZLmATl/3+s4PWeh5RTEPZ6IsmwruKc97s/ymDZKfSAPwsKiPaAh5chluqXxH\r\nFaND6momQHhVVHVjLzN3C7yNAJFbBuqJ/o74M7oro8ZQQme7KN0PJKzAC7DA\r\nRmfK4VDYs08oT9mLDkVvtBdgWKu/+cjkrpZ3kSHwAqbGm3CVAV2svtVzAkXh\r\n3iAlFKT2mInnu39apkHlRMSIwCxxIJ799k8=\r\n=FHSG\r\n-----END PGP SIGNATURE-----\r\n", "size": 44325}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.8_1673571859977_0.9829541842798033"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-13T01:04:20.236Z", "publish_time": 1673571860236}, "2.6.9": {"name": "node-fetch", "version": "2.6.9", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-polyfill": "^6.26.0", "babel-preset-env": "1.4.0", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "70f592d9d2da959df1cebc2dd2314286a4bcf345", "_id": "node-fetch@2.6.9", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-DJm/CJkZkRjKKj4Zi4BsKVZh3ValV5IR5s7LVZnW+6YMh0W1BfNA8XSs6DLMGYlId5F3KnA70uu2qepcR08Qqg==", "shasum": "7c7f744b5cc6eb5fd404e0c7a9fec630a55657e6", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.9.tgz", "fileCount": 7, "unpackedSize": 161622, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC94e/JEjjG1WTkpY3EjjUZ1o/VmVBV1qZNs3cfcKm25wIgaWxnWrFGrNpcTZr8eLqrjJ+jWyzlAxCQMUq3NsAimeM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2D3mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqocg/+Iz2e6rhemn6lCnkJxhVB9i4M9Cwbeu63snORixJj65U0h1qe\r\n6pEl4OIJkoUwhHuzsaCZCqiyRxq/hSIK6+fqi9i2xpyuTD8oJccu2ZjTPv1U\r\nN6cjPF0nHI9HyCP+cYzDGzeoKWPHtd61OGGZf8Lk6SQzTg/yGhmFgOiXm12W\r\nFY1yBA/eCtDM0yby2Om4xmCFYcNX5eycEX7S1SaHipXQcVYZpVpyP4EOoxPR\r\nJxhoq7JkK330dMvls49QlLXHbeq3qFFzR8jYKRb/R5iB0rt1zqswLymI4SRY\r\n8+1tA30U/Bq/MxcRJ7BzUF1vq1K9hWYHVj8lQNDSDns3Dx3cA5F5BZe7iWIE\r\nf7SwpqWBeblJunHAj/tULpWgIdFuX+KBlw2lh7G3MCSj/vzVxjI4+r0upf/f\r\nCCECAwSqf9mLCc1VRbAN1tgOBvqrrI649mB4ZYyD3ATZ+OUr/47xG48V8+Tw\r\noiEVSaGKERR+naOpmb99CQC5kD7rrfDEu7RsxuqdejdF+kJ8ZDy1GwJzaiwt\r\n2/9Hkpi07wy7XDaWEiVw0WePmV4pK3u4hEmoC4hFu+P6WzdhbLdrRshCMRfI\r\n8pBE+xFDrbNi7ZqL+xZcQWASLjucpIenya0JUXlEXtt9j+0jt6ZdcLG0ODXZ\r\nvY9l4keyUjy4NdfkrVgQblwKOLbWXvz+GBk=\r\n=JVso\r\n-----END PGP SIGNATURE-----\r\n", "size": 44330}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.9_1675116006388_0.41650960637529844"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-01-30T22:00:06.583Z", "publish_time": 1675116006583}, "3.3.1": {"name": "node-fetch", "version": "3.3.1", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "7b86e946b02dfdd28f4f8fca3d73a022cbb5ca1e", "_id": "node-fetch@3.3.1", "_nodeVersion": "18.14.2", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-cRVc/kyto/7E5shrWca1Wsea4y6tL9iYJE5FBCius3JQfb/4P4I295PfhgbJQBLTx6lATE4z+wK0rPM4VS2uow==", "shasum": "b3eea7b54b3a48020e46f4f88b9c5a7430d20b2e", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.3.1.tgz", "fileCount": 17, "unpackedSize": 107105, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEbf++qZ1YcpNxHVVef53b3HIDGUkJj4x9oMYgfTR2LvAiB6FpJYU/mtF4cWV1u/mRMYtneRwf/ZgVoXjYjk5prRww=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDFxVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrzag//VkdDRFzvmQwyI3+s2RFWljj+SKDo3XxlV5F1kcbwvf/gZRqS\r\noh6veDRLNXa7ZCdOdbYnj299086oiKTJqaFsc/N6zz1jhJnWOFYqkuAiaMAo\r\nz7u2lPukm1oifGY0RieTihQIajCnlNccbCp33wENJnAf16UJVZNlRnmMz9NJ\r\neRn3/cT59fXfbMWzCBD9jdScPZdzK5HJUECB19cfZyoNv3ivn05idVnIgeZI\r\n6ee5P2wNyZk0ClTIFoM6dwID7UUJV/sT/N4dogxNsbDTi9g31TJ5vDIjksTu\r\nzWp3L21BctEfp8EnIUD6ek194p+Q9bhPhwoYTxdqc9ipkZsNid80GmjS7JP/\r\nBfuXoUqJN5W+pCIrNIrulpXDNOUMtneptLf8nFbPgBIeada0Xa6gYGzFQemX\r\nrQu9QMaqJ/i5mF3DIFCK4EvskmcoBsnvrcnDa1DqmPg4CHzdJQdaCc9RFWpp\r\nkvsJoUmOSG7m4TtJ9WsclnExWidKWxtpy2n8mqLzBebVJHuNQHhIGAS1bCTI\r\nwKXv+ve1sBL/QOHYRFnJ7l94atJ7b3JMg7UyTXG5mboonhPLAP1ie2RUw1h5\r\n1cAbLhlTQdwWxPJzKRISuLzLuf1GwLuo1UgPVCGCv/DlOOv3iZ9iFlbxaRBa\r\no2k21tCi+beLUlo6rMBTF8OUSN2OD2Bz9f0=\r\n=huE7\r\n-----END PGP SIGNATURE-----\r\n", "size": 31615}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.3.1_1678531669225_0.2815289104736358"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-03-11T10:47:49.391Z", "publish_time": 1678531669391}, "2.6.10": {"name": "node-fetch", "version": "2.6.10", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-polyfill": "^6.26.0", "babel-preset-env": "1.4.0", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "29909d75c62d51e0d1c23758e526dba74bfd463d", "_id": "node-fetch@2.6.10", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-5YytjUVbwzjE/BX4N62vnPPkGNxlJPwdA9/ArUc4pcM6cYS4Hinuv4VazzwjMGgnWuiQqcemOanib/5PpcsGug==", "shasum": "4a52b637a6802092aa11a3bf73d19aac789fdce1", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.10.tgz", "fileCount": 7, "unpackedSize": 161590, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEfduTm+Xc/yhxw7rvD2IRGs2E7opKid9mTbAuUuJs+hAiEA0sFCQCaFnCc8j6AqWdd+/6OP4lWjSORz7GOYWFJ73fo="}], "size": 44385}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.10_1683562845752_0.7054914892922528"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-08T16:20:45.981Z", "publish_time": 1683562845981, "_source_registry_name": "default"}, "2.6.11": {"name": "node-fetch", "version": "2.6.11", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-polyfill": "^6.26.0", "babel-preset-env": "1.4.0", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "afb36f6c178342488d71947dfc87e7ddd19fab9e", "_id": "node-fetch@2.6.11", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-4I6pdBY1EthSqDmJkiNk3JIT8cswwR9nfeW/cPdUagJYEQG7R95WRH74wpz7ma8Gh/9dI9FP+OU+0E4FvtA55w==", "shasum": "cde7fc71deef3131ef80a738919f999e6edfff25", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.11.tgz", "fileCount": 7, "unpackedSize": 161623, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDo6q3bf9NzG2hEClP0n2J4E0EDsSdt5baTvmjsEj38KwIgM3ilbPg3hB9lZDuh0ZoeAT8i7hTFTIZniqs4+R68jEU="}], "size": 44329}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.11_1683630392145_0.8144385934811957"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-05-09T11:06:32.325Z", "publish_time": 1683630392325, "_source_registry_name": "default"}, "2.6.12": {"name": "node-fetch", "version": "2.6.12", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-polyfill": "^6.26.0", "babel-preset-env": "1.4.0", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "8bc3a7c85f67fb81bb3d71c8254e68f3b88e9169", "_id": "node-fetch@2.6.12", "_nodeVersion": "18.16.1", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-C/fGU2E8ToujUivIO0H+tpQ6HWo4eEmchoPIoXtxCrVghxdKq+QOHqEZW7tuP3KlV3bC8FRMO5nMCC7Zm1VP6g==", "shasum": "02eb8e22074018e3d5a83016649d04df0e348fba", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.12.tgz", "fileCount": 7, "unpackedSize": 162145, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGmUFr/nCcj0qT72N+XjnpZFq6abbVtnE/hPuFT1+fDDAiEA0SChf3IHK2jkVn/uOHwA0MhNkVlsE4NKDGGI7oLwkbI="}], "size": 44549}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.12_1688066193040_0.01772708545934565"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-06-29T19:16:33.256Z", "publish_time": 1688066193256, "_source_registry_name": "default"}, "3.3.2": {"name": "node-fetch", "version": "3.3.2", "description": "A light-weight module that brings Fetch API to node.js", "main": "./src/index.js", "sideEffects": false, "type": "module", "types": "./@types/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "mocha", "coverage": "c8 report --reporter=text-lcov | coveralls", "test-types": "tsd", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}, "devDependencies": {"abort-controller": "^3.0.0", "abortcontroller-polyfill": "^1.7.1", "busboy": "^1.4.0", "c8": "^7.7.2", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "coveralls": "^3.1.0", "form-data": "^4.0.0", "formdata-node": "^4.2.4", "mocha": "^9.1.3", "p-timeout": "^5.0.0", "stream-consumers": "^1.0.1", "tsd": "^0.14.0", "xo": "^0.39.1"}, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "tsd": {"cwd": "@types", "compilerOptions": {"esModuleInterop": true}}, "xo": {"envs": ["node", "browser"], "ignores": ["example.js"], "rules": {"complexity": 0, "import/extensions": 0, "import/no-useless-path-segments": 0, "import/no-anonymous-default-export": 0, "import/no-named-as-default": 0, "unicorn/import-index": 0, "unicorn/no-array-reduce": 0, "unicorn/prefer-node-protocol": 0, "unicorn/numeric-separators-style": 0, "unicorn/explicit-length-check": 0, "capitalized-comments": 0, "node/no-unsupported-features/es-syntax": 0, "@typescript-eslint/member-ordering": 0}, "overrides": [{"files": "test/**/*.js", "envs": ["node", "mocha"], "rules": {"max-nested-callbacks": 0, "no-unused-expressions": 0, "no-warning-comments": 0, "new-cap": 0, "guard-for-in": 0, "unicorn/no-array-for-each": 0, "unicorn/prevent-abbreviations": 0, "promise/prefer-await-to-then": 0, "ava/no-import-test-files": 0}}]}, "runkitExampleFilename": "example.js", "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "gitHead": "8b3320d2a7c07bce4afc6b2bf6c3bbddda85b01f", "_id": "node-fetch@3.3.2", "_nodeVersion": "18.17.0", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==", "shasum": "d1e889bacdf733b4ff3b2b243eb7a12866a0b78b", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-3.3.2.tgz", "fileCount": 17, "unpackedSize": 107319, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeCI5UYCrCLsYGRqKa5YEd1SJacs0NKcfKkELQEH+9NwIgQMs7zE7jzKw9XmcscpzfwehKFydK1dLcfOfDsxeRJmw="}], "size": 31722}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_3.3.2_1690285817409_0.8635909020297543"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-07-25T11:50:17.626Z", "publish_time": 1690285817626, "_source_registry_name": "default"}, "2.6.13": {"name": "node-fetch", "version": "2.6.13", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-polyfill": "^6.26.0", "babel-preset-env": "1.4.0", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "65ae25a1da2834b046c218685f2085a06f679492", "_id": "node-fetch@2.6.13", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-StxNAxh15zr77QvvkmveSQ8uCQ4+v5FkvNTj0OESmiHu+VRi/gXArXtkWMElOsOUNLtUEvI4yS+rdtOHZTwlQA==", "shasum": "a20acbbec73c2e09f9007de5cda17104122e0010", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.13.tgz", "fileCount": 7, "unpackedSize": 162197, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAqTb+wiuHTHdMY3B6gMiAsPrAuUj0rqHcAtnxQOa19JAiB0RdA+ypmwYh/09xv7xRsFS3umfFpiTojhq20xyZ1rzg=="}], "size": 44636}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.6.13_1692390256315_0.19942359870947257"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-18T20:24:16.578Z", "publish_time": 1692390256578, "_source_registry_name": "default"}, "2.7.0": {"name": "node-fetch", "version": "2.7.0", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.mjs", "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --require babel-register --throw-deprecation test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "dependencies": {"whatwg-url": "^5.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}, "devDependencies": {"@ungap/url-search-params": "^0.1.2", "abort-controller": "^1.1.0", "abortcontroller-polyfill": "^1.3.0", "babel-core": "^6.26.3", "babel-plugin-istanbul": "^4.1.6", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-polyfill": "^6.26.0", "babel-preset-env": "1.4.0", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "~1.3.0", "codecov": "3.3.0", "cross-env": "^5.2.0", "form-data": "^2.3.3", "is-builtin-module": "^1.0.0", "mocha": "^5.0.0", "nyc": "11.9.0", "parted": "^0.1.1", "promise": "^8.0.3", "resumer": "0.0.0", "rollup": "^0.63.4", "rollup-plugin-babel": "^3.0.7", "string-to-arraybuffer": "^1.0.2", "teeny-request": "3.7.0"}, "release": {"branches": ["+([0-9]).x", "main", "next", {"name": "beta", "prerelease": true}]}, "readmeFilename": "README.md", "gitHead": "9b9d45881e5ca68757077726b3c0ecf8fdca1f29", "_id": "node-fetch@2.7.0", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==", "shasum": "d0f0fa6e3e2dc1d27efcd8ad99d550bda94d187d", "tarball": "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.7.0.tgz", "fileCount": 7, "unpackedSize": 162253, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvDvShxFTRTMIi/rvuJJKxj9h+zzCO578D4oWZtYf7OwIgNpCFtM1iLbze3dy/JMImRtEy6v/FqdJla22ieuU2Ot4="}], "size": 44644}, "_npmUser": {"name": "node-fetch-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "node-fetch-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-fetch_2.7.0_1692811119186_0.3901977301325943"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-23T17:18:39.396Z", "publish_time": 1692811119396, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/node-fetch/node-fetch/issues"}, "homepage": "https://github.com/node-fetch/node-fetch", "keywords": ["fetch", "http", "promise", "request", "curl", "wget", "xhr", "whatwg"], "repository": {"type": "git", "url": "git+https://github.com/node-fetch/node-fetch.git"}, "_source_registry_name": "default"}