
7ebb1559cb8e09b8f2ae31ecef99a8d962247952	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/supabase","integrity":"sha512-i7wlqr6aUvcyVPjfXPFow6l+Tuta/CVUFXzyipvCP47LMbnsTy1VP7ebkQUIfIx30YkfA05H/rk9Mz5wjPBB/Q==","time":1749120191453,"size":2125685,"metadata":{"time":1749120191312,"url":"https://registry.npmmirror.com/supabase","reqHeaders":{"accept":"application/json"},"resHeaders":{"cache-control":"public, max-age=300","content-encoding":"gzip","content-type":"application/json; charset=utf-8","date":"Thu, 05 Jun 2025 10:43:11 GMT","etag":"W/\"42227e08912aecba4f25b665ca62d8fa0fe00bcb\"","vary":"Origin, Accept, Accept-Encoding"},"options":{"compress":true}}}
02e7927ca492d03808e524a04ffe2b9b81a0e894	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/supabase","integrity":"sha512-zz1SGSF1Z0fGJvC9b3Dt1tuJojM1Jz2lhO2FmnanRwJ74Q8LWnsUCtgm2TMoGZKc5LuS6HayID8o88DvJ+NTtw==","time":1749121439178,"size":1102643,"metadata":{"time":1749121439177,"url":"https://registry.npmmirror.com/supabase","reqHeaders":{"accept":"application/vnd.npm.install-v1+json; q=1.0, application/json; q=0.8, */*"},"resHeaders":{"cache-control":"public, max-age=300","date":"Thu, 05 Jun 2025 11:03:59 GMT","etag":"W/\"3e5b84391da3675af855cfc21feacc9c3e115497\"","vary":"Origin, Accept, Accept-Encoding","content-encoding":"gzip","content-type":"application/json; charset=utf-8"},"options":{"compress":true}}}
a56c28008be360c161b57951901ffb32d081b5fc	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/supabase","integrity":"sha512-i7wlqr6aUvcyVPjfXPFow6l+Tuta/CVUFXzyipvCP47LMbnsTy1VP7ebkQUIfIx30YkfA05H/rk9Mz5wjPBB/Q==","time":1749121520819,"size":2125685,"metadata":{"time":1749121520819,"url":"https://registry.npmmirror.com/supabase","reqHeaders":{"accept":"application/json"},"resHeaders":{"cache-control":"public, max-age=300","date":"Thu, 05 Jun 2025 11:05:20 GMT","etag":"W/\"42227e08912aecba4f25b665ca62d8fa0fe00bcb\"","vary":"Origin, Accept, Accept-Encoding","content-encoding":"gzip","content-type":"application/json; charset=utf-8"},"options":{"compress":true}}}