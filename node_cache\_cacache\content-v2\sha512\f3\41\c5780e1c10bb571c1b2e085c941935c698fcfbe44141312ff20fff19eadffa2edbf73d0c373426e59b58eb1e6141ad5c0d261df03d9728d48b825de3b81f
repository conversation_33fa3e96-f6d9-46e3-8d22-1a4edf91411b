{"_attachments": {}, "_id": "minizlib", "_rev": "2631-61f149d1b77ea98a7490af17", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "dist-tags": {"latest": "3.0.2"}, "license": "MIT", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "minizlib", "readme": "# minizlib\n\nA fast zlib stream built on [minipass](http://npm.im/minipass) and\nNode.js's zlib binding.\n\nThis module was created to serve the needs of\n[node-tar](http://npm.im/tar) and\n[minipass-fetch](http://npm.im/minipass-fetch).\n\nBrotli is supported in versions of node with a Brotli binding.\n\n## How does this differ from the streams in `'node:zlib'`?\n\nFirst, there are no convenience methods to compress or decompress a\nbuffer.  If you want those, use the built-in `zlib` module.  This is\nonly streams.  That being said, Minipass streams to make it fairly easy to\nuse as one-liners: `new zlib.Deflate().end(data).read()` will return the\ndeflate compressed result.\n\nThis module compresses and decompresses the data as fast as you feed\nit in.  It is synchronous, and runs on the main process thread.  Zlib\nand Brotli operations can be high CPU, but they're very fast, and doing it\nthis way means much less bookkeeping and artificial deferral.\n\nNode's built in zlib streams are built on top of `stream.Transform`.\nThey do the maximally safe thing with respect to consistent\nasynchrony, buffering, and backpressure.\n\nSee [Minipass](http://npm.im/minipass) for more on the differences between\nNode.js core streams and Minipass streams, and the convenience methods\nprovided by that class.\n\n## Classes\n\n- Deflate\n- Inflate\n- Gzip\n- Gunzip\n- DeflateRaw\n- InflateRaw\n- Unzip\n- BrotliCompress (Node v10 and higher)\n- BrotliDecompress (Node v10 and higher)\n\n## USAGE\n\n```js\nimport { BrotliDecompress } from 'minizlib'\n// or: const BrotliDecompress = require('minizlib')\n\nconst input = sourceOfCompressedData()\nconst decode = new BrotliDecompress()\nconst output = whereToWriteTheDecodedData()\ninput.pipe(decode).pipe(output)\n```\n\n## REPRODUCIBLE BUILDS\n\nTo create reproducible gzip compressed files across different operating\nsystems, set `portable: true` in the options.  This causes minizlib to set\nthe `OS` indicator in byte 9 of the extended gzip header to `0xFF` for\n'unknown'.\n", "time": {"created": "2022-01-26T13:17:05.453Z", "modified": "2025-05-13T17:00:30.798Z", "2.1.2": "2020-08-14T22:42:53.986Z", "2.1.1-testing-publish-with-beta-v7-npm.0": "2020-08-14T21:26:10.820Z", "2.1.1": "2020-08-14T21:21:51.605Z", "2.1.0": "2019-10-05T06:07:03.772Z", "2.0.0": "2019-09-30T20:18:03.436Z", "1.3.3": "2019-09-30T20:09:45.317Z", "1.3.2": "2019-09-26T15:42:32.412Z", "1.3.1": "2019-09-26T06:04:35.808Z", "1.3.0": "2019-09-26T05:46:14.498Z", "1.2.2": "2019-09-12T20:27:19.882Z", "1.2.1": "2018-12-07T18:00:58.925Z", "1.2.0": "2018-12-06T23:25:14.139Z", "1.1.1": "2018-10-10T17:52:20.823Z", "1.1.0": "2017-12-21T04:02:54.335Z", "1.0.4": "2017-10-18T07:35:11.315Z", "1.0.3": "2017-05-04T08:01:06.591Z", "1.0.2": "2017-03-29T08:12:55.022Z", "1.0.1": "2017-03-29T08:11:52.460Z", "1.0.0": "2017-03-29T08:11:06.728Z", "0.0.1": "2017-03-28T07:40:22.993Z", "3.0.0": "2024-03-19T03:00:19.304Z", "3.0.1": "2024-04-06T23:44:45.539Z", "3.0.2": "2025-03-31T16:39:49.530Z"}, "versions": {"2.1.2": {"name": "minizlib", "version": "2.1.2", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^14.6.9"}, "engines": {"node": ">= 8"}, "gitHead": "433c0caa0a3ba92a31623025c4ac386836649b09", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@2.1.2", "_nodeVersion": "14.8.0", "_npmVersion": "7.0.0-beta.4", "dist": {"shasum": "e90d3466ba209b932451508a11ce3d3632145931", "size": 6154, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-2.1.2.tgz", "integrity": "sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_2.1.2_1597444973743_0.32588819043211026"}, "_hasShrinkwrap": false, "publish_time": 1597444973986, "_cnpm_publish_time": 1597444973986, "_cnpmcore_publish_time": "2021-12-13T16:53:16.042Z"}, "2.1.1-testing-publish-with-beta-v7-npm.0": {"name": "minizlib", "version": "2.1.1-testing-publish-with-beta-v7-npm.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^14.6.9"}, "engines": {"node": ">= 8"}, "gitHead": "ec3483b05b67753a89f6466494076cf8609e6e17", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@2.1.1-testing-publish-with-beta-v7-npm.0", "_nodeVersion": "14.8.0", "_npmVersion": "7.0.0-beta.4", "dist": {"shasum": "6a8253c329f8e12bda15c24fab29cc0a0142b629", "size": 6169, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-2.1.1-testing-publish-with-beta-v7-npm.0.tgz", "integrity": "sha512-YHdsiBOw5jDd2+Sav0y9V1x+UeKMoP23b8wXjk3yOGPpcdHfwjkR/yUK5qP+bTuaPZB2bDTw0+GtOOsJfzhH/A=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_2.1.1-testing-publish-with-beta-v7-npm.0_1597440370683_0.035692628993942765"}, "_hasShrinkwrap": false, "publish_time": 1597440370820, "_cnpm_publish_time": 1597440370820, "_cnpmcore_publish_time": "2021-12-13T16:53:16.266Z"}, "2.1.1": {"name": "minizlib", "version": "2.1.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^14.6.9"}, "engines": {"node": ">= 8"}, "gitHead": "ec3483b05b67753a89f6466494076cf8609e6e17", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@2.1.1", "_nodeVersion": "14.8.0", "_npmVersion": "6.14.7", "dist": {"shasum": "e7c4598f8caf81f4958d759b3fda20de130ae390", "size": 6149, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-2.1.1.tgz", "integrity": "sha512-JDuq+CU5UgBMfeExTb0iQpxCiH3EsPf4ypyLBQaBqY1EMwwp39mapJM2kwTQC0tf+dp5Cs3Mwy/N4CrrdvZzQw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_2.1.1_1597440111476_0.4159523791162707"}, "_hasShrinkwrap": false, "publish_time": 1597440111605, "_cnpm_publish_time": 1597440111605, "_cnpmcore_publish_time": "2021-12-13T16:53:16.467Z"}, "2.1.0": {"name": "minizlib", "version": "2.1.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^14.6.9"}, "engines": {"node": ">= 8"}, "gitHead": "7972a022540d40f62ba8fd3e8038c057773e981c", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@2.1.0", "_nodeVersion": "12.11.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "fd52c645301ef09a63a2c209697c294c6ce02cf3", "size": 6062, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-2.1.0.tgz", "integrity": "sha512-EzTZN/fjSvifSX0SlqUERCN39o6T40AMarPbv0MrarSFtIITCBh7bi+dU8nxGFHuqs9jdIAeoYoKuQAAASsPPA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_2.1.0_1570255623624_0.5196261615937616"}, "_hasShrinkwrap": false, "publish_time": 1570255623772, "_cnpm_publish_time": 1570255623772, "_cnpmcore_publish_time": "2021-12-13T16:53:16.639Z"}, "2.0.0": {"name": "minizlib", "version": "2.0.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^12.0.1"}, "engines": {"node": ">= 8"}, "gitHead": "51e62f213076a941d5bebbecc7cab702c6b5aaf7", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@2.0.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "1c26c23055feaaaf8f8a86b166819d8ad906e697", "size": 5790, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-2.0.0.tgz", "integrity": "sha512-8f6fgftVu7S2bYKe4Ks9jS5ViU/3As9kEmCg6p9T5OCDAzxVooUmhhSqGar19ZR07xh0rknj9ZmGme0bv0d+Nw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_2.0.0_1569874683321_0.8665600631616883"}, "_hasShrinkwrap": false, "publish_time": 1569874683436, "_cnpm_publish_time": 1569874683436, "_cnpmcore_publish_time": "2021-12-13T16:53:16.847Z"}, "1.3.3": {"name": "minizlib", "version": "1.3.3", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.9.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^12.0.1"}, "gitHead": "5e2825db6fb4ee20b63dc08ab12f4415b8ed71d4", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.3.3", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"shasum": "2290de96818a34c29551c8a8d301216bd65a861d", "size": 5771, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.3.3.tgz", "integrity": "sha512-6ZYMOEnmVsdCeTJVE0W9ZD+pVnE8h9Hma/iOwwRDsdQoePpoX56/8B6z3P9VNwppJuBKNRuFDRNRqRWexT9G9Q=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_1.3.3_1569874185075_0.18396379860852585"}, "_hasShrinkwrap": false, "publish_time": 1569874185317, "_cnpm_publish_time": 1569874185317, "_cnpmcore_publish_time": "2021-12-13T16:53:17.018Z"}, "1.3.2": {"name": "minizlib", "version": "1.3.2", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.9.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^12.0.1"}, "gitHead": "e11b0571bb5a3fcf69f5099f908c1d6e228ee2fd", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.3.2", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "5d24764998f98112586f7e566bd4c0999769dad4", "size": 5666, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.3.2.tgz", "integrity": "sha512-lsNFqSHdJ21EwKzCp12HHJGxSMtHkCW1EMA9cceG3MkMNARjuWotZnMe3NKNshAvFXpm4loZqmYsCmRwhS2JMw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_1.3.2_1569512552274_0.7311787344216469"}, "_hasShrinkwrap": false, "publish_time": 1569512552412, "_cnpm_publish_time": 1569512552412, "_cnpmcore_publish_time": "2021-12-13T16:53:17.197Z"}, "1.3.1": {"name": "minizlib", "version": "1.3.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.2.1"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^12.0.1"}, "gitHead": "199410f31c7537f85712bf82d31746090d313d1c", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.2.2", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "6f0ccc82fa53e1bf2ff145f220d2da9fa6e3a166", "size": 5247, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.2.2.tgz", "integrity": "sha512-hR3At21uSrsjjDTWrbu0IMLTpnkpv8IIMFDFaoz43Tmu4LkmAXfH44vNNzpTnf+OAQQCHrb91y/wc2J4x5XgSQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_1.2.2_1568320039595_0.05086099863204718"}, "_hasShrinkwrap": false, "publish_time": 1568320039882, "_cnpm_publish_time": 1568320039882, "_cnpmcore_publish_time": "2021-12-13T16:53:17.699Z", "deprecated": "[WARNING] Use 1.2.2 instead of 1.3.1, reason: https://github.com/isaacs/minizlib/issues/9"}, "1.3.0": {"name": "minizlib", "version": "1.3.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.2.1"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^12.0.1"}, "gitHead": "199410f31c7537f85712bf82d31746090d313d1c", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.2.2", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "6f0ccc82fa53e1bf2ff145f220d2da9fa6e3a166", "size": 5247, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.2.2.tgz", "integrity": "sha512-hR3At21uSrsjjDTWrbu0IMLTpnkpv8IIMFDFaoz43Tmu4LkmAXfH44vNNzpTnf+OAQQCHrb91y/wc2J4x5XgSQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_1.2.2_1568320039595_0.05086099863204718"}, "_hasShrinkwrap": false, "publish_time": 1568320039882, "_cnpm_publish_time": 1568320039882, "_cnpmcore_publish_time": "2021-12-13T16:53:17.699Z", "deprecated": "[WARNING] Use 1.2.2 instead of 1.3.0, reason: https://github.com/isaacs/minizlib/issues/9"}, "1.2.2": {"name": "minizlib", "version": "1.2.2", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.2.1"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^12.0.1"}, "gitHead": "199410f31c7537f85712bf82d31746090d313d1c", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.2.2", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"shasum": "6f0ccc82fa53e1bf2ff145f220d2da9fa6e3a166", "size": 5247, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.2.2.tgz", "integrity": "sha512-hR3At21uSrsjjDTWrbu0IMLTpnkpv8IIMFDFaoz43Tmu4LkmAXfH44vNNzpTnf+OAQQCHrb91y/wc2J4x5XgSQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_1.2.2_1568320039595_0.05086099863204718"}, "_hasShrinkwrap": false, "publish_time": 1568320039882, "_cnpm_publish_time": 1568320039882, "_cnpmcore_publish_time": "2021-12-13T16:53:17.699Z"}, "1.2.1": {"name": "minizlib", "version": "1.2.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.2.1"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^12.0.1"}, "gitHead": "ad2e969851c3573eff84a5ddfe906344c814581b", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "dd27ea6136243c7c880684e8672bb3a45fd9b614", "size": 5191, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.2.1.tgz", "integrity": "sha512-7+4oTUOWKg7AuL3vloEWekXY2/D20cevzsrNT2kGWm+39J9hGTCBv8VI5Pm5lXZ/o3/mdR4f8rflAPhnQb8mPA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_1.2.1_1544205658697_0.07448144618627572"}, "_hasShrinkwrap": false, "publish_time": 1544205658925, "_cnpm_publish_time": 1544205658925, "_cnpmcore_publish_time": "2021-12-13T16:53:17.909Z"}, "1.2.0": {"name": "minizlib", "version": "1.2.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.2.1"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^12.0.1"}, "gitHead": "94087dacbcc5e49e4b55ccd3ee4901d64a0a37e1", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "59517387478fd98d8017ed0299c6cb16cbd12da3", "size": 5152, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.2.0.tgz", "integrity": "sha512-vQhkoouK/oKRVuFJynustmW3wrqZEXOrfbVVirvOVeglH4TNvIkcqiyojlIbbZYYDJZSbEKEXmDudg+tyRkm6g=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_1.2.0_1544138714049_0.5809128408831707"}, "_hasShrinkwrap": false, "publish_time": 1544138714139, "_cnpm_publish_time": 1544138714139, "_cnpmcore_publish_time": "2021-12-13T16:53:18.133Z"}, "1.1.1": {"name": "minizlib", "version": "1.1.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.2.1"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^10.7.2"}, "gitHead": "814af72cf7ee5011814dbc6226b5961051ea5bc3", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "6734acc045a46e61d596a43bb9d9cd326e19cc42", "size": 5190, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.1.1.tgz", "integrity": "sha512-TrfjCjk4jLhcJyGMYymBH6oTXcWjYbUAXTHDbtnWHjZC25h0cdajHuPE1zxb4DVmu8crfh+HwH/WMuyLG0nHBg=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_1.1.1_1539193940689_0.34679390777921637"}, "_hasShrinkwrap": false, "publish_time": 1539193940823, "_cnpm_publish_time": 1539193940823, "_cnpmcore_publish_time": "2021-12-13T16:53:18.323Z"}, "1.1.0": {"name": "minizlib", "version": "1.1.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.2.1"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^10.7.2"}, "files": ["index.js", "constants.js"], "gitHead": "e10e489a9d7a41b52dccacb39bb22e078835ebed", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "11e13658ce46bc3a70a267aac58359d1e0c29ceb", "size": 5230, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.1.0.tgz", "integrity": "sha512-4T6Ur/GctZ27nHfpt9THOdRZNgyJ9FZchYO1ceg5S8Q3DNLCKYy44nCZzgCJgcvx2UM8czmqak5BCxJMrq37lA=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib-1.1.0.tgz_1513828974124_0.5935359639115632"}, "directories": {}, "publish_time": 1513828974335, "_hasShrinkwrap": false, "_cnpm_publish_time": 1513828974335, "_cnpmcore_publish_time": "2021-12-13T16:53:18.531Z"}, "1.0.4": {"name": "minizlib", "version": "1.0.4", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.2.1"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^10.7.2"}, "files": ["index.js", "constants.js"], "gitHead": "98bb59595f49abb12246e9bf91a1b96d9809768d", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.0.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.7.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "8ebb51dd8bbe40b0126b5633dbb36b284a2f523c", "size": 5187, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.0.4.tgz", "integrity": "sha512-sN4U9tIJtBRwKbwgFh9qJfrPIQ/GGTRr1MGqkgOeMTLy8/lM0FcWU//FqlnZ3Vb7gJ+Mxh3FOg1EklibdajbaQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib-1.0.4.tgz_1508312111191_0.25975665473379195"}, "directories": {}, "publish_time": 1508312111315, "_hasShrinkwrap": false, "_cnpm_publish_time": 1508312111315, "_cnpmcore_publish_time": "2021-12-13T16:53:18.757Z"}, "1.0.3": {"name": "minizlib", "version": "1.0.3", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^2.0.0"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^10.3.0"}, "files": ["index.js", "constants.js"], "gitHead": "18adfc6e208468eae276617b34bb4dfaeec1f532", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.0.3", "_shasum": "d5c1abf77be154619952e253336eccab9b2a32f5", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "d5c1abf77be154619952e253336eccab9b2a32f5", "size": 5011, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.0.3.tgz", "integrity": "sha512-o3Z2Gf+6pyplapNa19Hsmf8vJvawkWYx5SsgmGKjp+HpOEWEAr4mSHsrr1yONVf6eW87Ug1lFZPmsh1vWjK8sw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minizlib-1.0.3.tgz_1493884866315_0.**********094559"}, "directories": {}, "publish_time": 1493884866591, "_hasShrinkwrap": false, "_cnpm_publish_time": 1493884866591, "_cnpmcore_publish_time": "2021-12-13T16:53:18.996Z"}, "1.0.2": {"name": "minizlib", "version": "1.0.2", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^1.1.6"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^10.3.0"}, "files": ["index.js", "constants.js"], "gitHead": "666b33cf8158c12111046d94449eed53c9c9fab2", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.0.2", "_shasum": "6c4a4c624bee69905b0044b166ea1adfcd6581fe", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "6c4a4c624bee69905b0044b166ea1adfcd6581fe", "size": 5001, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.0.2.tgz", "integrity": "sha512-Nn1NlBrwT/H19jZOSzAkiK5J+0kefOBBClisNA3bF9073iVgykoDQ5GNLdht92eGQzmyqlnucN3UeC8az9/Fyw=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minizlib-1.0.2.tgz_1490775174783_0.**********003821"}, "directories": {}, "publish_time": 1490775175022, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490775175022, "_cnpmcore_publish_time": "2021-12-13T16:53:19.251Z"}, "1.0.1": {"name": "minizlib", "version": "1.0.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^1.1.6"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^10.3.0"}, "files": ["index.js"], "gitHead": "89ef737a875a1176a60a6824b405c44d78eb9d5e", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.0.1", "_shasum": "038b4fc0dd85290fb4c94eb8a9c8254047985218", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "038b4fc0dd85290fb4c94eb8a9c8254047985218", "size": 4692, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.0.1.tgz", "integrity": "sha512-zDkCQomfcSRkBBNAF3Lsxy00b4mFXTd+kST1bxHvTf5FhsK8xlHKP5oc3VuqAo7ZtMJLbOPsdqYA+g+XPCM6XQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minizlib-1.0.1.tgz_1490775112216_0.8146075017284602"}, "directories": {}, "publish_time": 1490775112460, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490775112460, "_cnpmcore_publish_time": "2021-12-13T16:53:19.459Z"}, "1.0.0": {"name": "minizlib", "version": "1.0.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^1.1.6"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^10.3.0"}, "files": ["index.js"], "gitHead": "356f7243ac9a988b172b0a58074d05aac8351529", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@1.0.0", "_shasum": "a5b3994a671ad46c769af9905b7dc1cfe4aa1fb6", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "a5b3994a671ad46c769af9905b7dc1cfe4aa1fb6", "size": 4688, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-1.0.0.tgz", "integrity": "sha512-ND9e2gVqtoqk2sBSsq5vbY1axx2n4m5sHAUFXGk1SPWc5GFa4xNObFaKeHSzyV5VAQt9qLBpIjqVcb2hmA4zKQ=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minizlib-1.0.0.tgz_1490775065011_0.09464420657604933"}, "directories": {}, "publish_time": 1490775066728, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490775066728, "_cnpmcore_publish_time": "2021-12-13T16:53:19.661Z"}, "0.0.1": {"name": "minizlib", "version": "0.0.1", "description": "A smaller, faster, zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "index.js", "dependencies": {"minipass": "^1.1.1"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"tap": "^10.3.0"}, "files": ["index.js"], "gitHead": "eeb244f223858fb95742540fcdff8020c4e13e03", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_id": "minizlib@0.0.1", "_shasum": "86c1f23d56bd35c2e1fcf045548e11c8ed6c231a", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "86c1f23d56bd35c2e1fcf045548e11c8ed6c231a", "size": 4673, "noattachment": false, "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-0.0.1.tgz", "integrity": "sha512-KROQh8o6KxUkoYOy+cAr8uEGpLEN0rvcJ9yhoUerstRZ3mirQxs3T4GJA+TeHnIGJzyn6firaLqZDFxuEGK2ig=="}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minizlib-0.0.1.tgz_1490686822685_0.5977697225753218"}, "directories": {}, "publish_time": 1490686822993, "_hasShrinkwrap": false, "_cnpm_publish_time": 1490686822993, "_cnpmcore_publish_time": "2021-12-13T16:53:19.881Z"}, "3.0.0": {"name": "minizlib", "version": "3.0.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "./dist/commonjs/index.js", "dependencies": {"minipass": "^7.0.4", "rimraf": "^5.0.5"}, "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"@types/node": "^20.11.29", "mkdirp": "^3.0.1", "tap": "^18.7.1", "tshy": "^1.12.0", "typedoc": "^0.25.12"}, "engines": {"node": ">= 18"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "_id": "minizlib@3.0.0", "gitHead": "9642ae1edd3c1613b1e6c96452d97507d884cbae", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-LOM/D1a1wbYVnC3eNpqAKlOh++jpJFOMtV7nXx/xaG6l5V43wQglxW3f0a6RVLnSHI+FDnlFEwm+IDB6t4IP0A==", "shasum": "021909a23d6ed855d1ca192192930b412648940e", "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-3.0.0.tgz", "fileCount": 21, "unpackedSize": 107964, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIApJfW5PrADYVxAN40DOVx34m1pvTYVdb28Nxyys8tmmAiEAjaiIEQHl8WJqWfxQUzdBjYpqAD9tu9Oyak7ynXA2ljw="}], "size": 17330}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_3.0.0_1710817219131_0.2935235277815398"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-03-19T03:00:19.304Z", "publish_time": 1710817219304, "_source_registry_name": "default"}, "3.0.1": {"name": "minizlib", "version": "3.0.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "./dist/commonjs/index.js", "dependencies": {"minipass": "^7.0.4", "rimraf": "^5.0.5"}, "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"@types/node": "^20.11.29", "mkdirp": "^3.0.1", "tap": "^18.7.1", "tshy": "^1.12.0", "typedoc": "^0.25.12"}, "engines": {"node": ">= 18"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "_id": "minizlib@3.0.1", "gitHead": "3412623e9470bb72827c8a61e685140b5ee7b8a0", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-umcy022ILvb5/3Djuu8LWeqUa8D68JaBzlttKeMWen48SjabqS3iY5w/vzeMzMUNhLDifyhbOwKDSznB1vvrwg==", "shasum": "46d5329d1eb3c83924eff1d3b858ca0a31581012", "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-3.0.1.tgz", "fileCount": 21, "unpackedSize": 107996, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH3P2K7ntuct62g5BC7WNoJ44dBppfMWuGpgPdhz/lykAiEA8DXD3Flj/Sa7A+ZWWeqOdBfFn/lofmUhPqzEVxU/jok="}], "size": 17331}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minizlib_3.0.1_1712447085360_0.583390192639742"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2024-04-06T23:44:45.539Z", "publish_time": 1712447085539, "_source_registry_name": "default"}, "3.0.2": {"name": "minizlib", "version": "3.0.2", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "./dist/commonjs/index.js", "dependencies": {"minipass": "^7.1.2"}, "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"@types/node": "^22.13.14", "tap": "^21.1.0", "tshy": "^3.0.2", "typedoc": "^0.28.1"}, "engines": {"node": ">= 18"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "module": "./dist/esm/index.js", "_id": "minizlib@3.0.2", "gitHead": "c77e92c29633b2a5bd19c9912c4c72929523540b", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_nodeVersion": "22.14.0", "_npmVersion": "11.2.0", "dist": {"integrity": "sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==", "shasum": "f33d638eb279f664439aa38dc5f91607468cb574", "tarball": "https://registry.npmmirror.com/minizlib/-/minizlib-3.0.2.tgz", "fileCount": 21, "unpackedSize": 110996, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQC0qybrna9EFhvLvutAT5dhLmQROWo0+xZ0ePwczoHLwgIgA0lkOja3EwfW0D4bullHB9mixjtNPlUMeMDiLyYXlIY="}], "size": 17982}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/minizlib_3.0.2_1743439189362_0.970927312976229"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-31T16:39:49.530Z", "publish_time": 1743439189530, "_source_registry_name": "default"}}, "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "_source_registry_name": "default"}