{"_attachments": {}, "_id": "<PERSON><PERSON>", "_rev": "275-61f1443fa920628a7b6dff39", "author": {"name": "Invertase", "email": "<EMAIL>", "url": "http://github.com/invertase/"}, "description": "The fastest javascript implementation of a double-ended queue. Used by the official Redis, MongoDB, MariaDB & MySQL libraries for Node.js and many other libraries. Maintains compatability with deque.", "dist-tags": {"latest": "2.1.0"}, "license": "Apache-2.0", "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "name": "<PERSON><PERSON>", "readme": "<p align=\"center\">\n  <h1 align=\"center\">Denque</h1>\n</p>\n\n<p align=\"center\">\n  <a href=\"https://www.npmjs.com/package/denque\"><img src=\"https://img.shields.io/npm/dm/denque.svg?style=flat-square\" alt=\"NPM downloads\"></a>\n  <a href=\"https://www.npmjs.com/package/denque\"><img src=\"https://img.shields.io/npm/v/denque.svg?style=flat-square\" alt=\"NPM version\"></a>\n  <a href=\"https://github.com/invertase/denque/actions/workflows/testing.yam\"><img src=\"https://github.com/invertase/denque/actions/workflows/testing.yaml/badge.svg\" alt=\"Tests status\"></a>\n  <a href=\"https://codecov.io/gh/invertase/denque\"><img src=\"https://codecov.io/gh/invertase/denque/branch/master/graph/badge.svg?token=rn91iI4bSe\" alt=\"Coverage\"></a>\n  <a href=\"/LICENSE\"><img src=\"https://img.shields.io/npm/l/denque.svg?style=flat-square\" alt=\"License\"></a>\n  <a href=\"https://twitter.com/invertaseio\"><img src=\"https://img.shields.io/twitter/follow/invertaseio.svg?style=social&label=Follow\" alt=\"Follow on Twitter\"></a>\n</p>\n\nDenque is a well tested, extremely fast and lightweight [double-ended queue](http://en.wikipedia.org/wiki/Double-ended_queue)\nimplementation with zero dependencies and includes TypeScript types.\n\nDouble-ended queues can also be used as a:\n\n- [Stack](http://en.wikipedia.org/wiki/Stack_\\(abstract_data_type\\))\n- [Queue](http://en.wikipedia.org/wiki/Queue_\\(data_structure\\))\n\nThis implementation is currently the fastest available, even faster than `double-ended-queue`, see the [benchmarks](https://docs.page/invertase/denque/benchmarks).\n\nEvery queue operation is done at a constant `O(1)` - including random access from `.peekAt(index)`.\n\n**Works on all node versions >= v0.10**\n\n## Quick Start\n\nInstall the package:\n\n```bash\nnpm install denque\n```\n\nCreate and consume a queue:\n\n```js\nconst Denque = require(\"denque\");\n\nconst denque = new Denque([1,2,3,4]);\ndenque.shift(); // 1\ndenque.pop(); // 4\n```\n\n\nSee the [API reference documentation](https://docs.page/invertase/denque/api) for more examples.\n\n---\n\n## Who's using it?\n\n- [Kafka Node.js client](https://www.npmjs.com/package/kafka-node)\n- [MariaDB Node.js client](https://www.npmjs.com/package/mariadb)\n- [MongoDB Node.js client](https://www.npmjs.com/package/mongodb)\n- [MySQL Node.js client](https://www.npmjs.com/package/mysql2)\n- [Redis Node.js clients](https://www.npmjs.com/package/redis)\n\n... and [many more](https://www.npmjs.com/browse/depended/denque).\n\n\n---\n\n## License\n\n- See [LICENSE](/LICENSE)\n\n---\n\n<p align=\"center\">\n  <a href=\"https://invertase.io/?utm_source=readme&utm_medium=footer&utm_campaign=denque\">\n    <img width=\"75px\" src=\"https://static.invertase.io/assets/invertase/invertase-rounded-avatar.png\">\n  </a>\n  <p align=\"center\">\n    Built and maintained by <a href=\"https://invertase.io/?utm_source=readme&utm_medium=footer&utm_campaign=denque\">Invertase</a>.\n  </p>\n</p>\n", "time": {"created": "2022-01-26T12:53:19.841Z", "modified": "2023-07-31T15:50:02.983Z", "2.0.1": "2021-08-31T11:38:30.469Z", "2.0.0": "2021-08-18T18:05:07.917Z", "1.5.1": "2021-08-18T16:04:02.889Z", "1.5.0": "2021-01-04T17:33:01.685Z", "1.4.1": "2019-04-03T00:21:25.475Z", "1.4.0": "2018-11-10T17:08:32.582Z", "1.3.0": "2018-06-03T16:09:17.707Z", "1.2.6": "2018-05-24T23:45:10.431Z", "1.2.5": "2018-05-24T23:29:17.176Z", "1.2.4": "2018-05-24T23:24:26.823Z", "1.2.3": "2018-02-09T22:21:12.527Z", "1.2.2": "2017-08-15T21:03:36.952Z", "1.2.1": "2017-07-28T17:42:40.088Z", "1.2.0": "2017-07-28T17:16:06.761Z", "1.1.1": "2017-01-26T12:07:29.295Z", "1.1.0": "2016-10-17T12:41:50.755Z", "1.0.6": "2016-10-17T12:20:44.860Z", "1.0.5": "2016-10-17T12:00:09.537Z", "1.0.4": "2016-09-16T12:19:37.116Z", "1.0.3": "2016-09-01T09:31:33.251Z", "1.0.2": "2016-08-26T18:35:46.495Z", "1.0.1": "2016-07-02T04:56:46.833Z", "1.0.0": "2016-07-02T04:38:44.723Z", "2.1.0": "2022-07-18T10:14:29.117Z"}, "versions": {"2.0.1": {"name": "<PERSON><PERSON>", "version": "2.0.1", "description": "The fastest javascript implementation of a double-ended queue. Used by the official Redis, MongoDB, MariaDB & MySQL libraries for Node.js and many other libraries. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha && npm run typescript", "coveralls": "cat ./coverage/lcov.info | coveralls", "typescript": "tsc --project ./test/type/tsconfig.json", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/invertase/denque.git"}, "license": "Apache-2.0", "author": {"name": "Invertase", "email": "<EMAIL>", "url": "http://github.com/invertase/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "<PERSON><PERSON><PERSON>"}], "bugs": {"url": "https://github.com/invertase/denque/issues"}, "homepage": "https://docs.page/invertase/denque", "devDependencies": {"benchmark": "^2.1.4", "codecov": "^3.8.3", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.5", "mocha": "^3.5.3", "typescript": "^3.4.1"}, "gitHead": "a299325e871dfa8f58c326b67e9bb30ba595a6ed", "_id": "denque@2.0.1", "_nodeVersion": "14.17.5", "_npmVersion": "6.14.14", "dist": {"shasum": "bcef4c1b80dc32efe97515744f21a4229ab8934a", "size": 8828, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-2.0.1.tgz", "integrity": "sha512-tfiWc6BQLXNLpNiR5iGd0Ocu3P3VpxfzFiqubLgMfhfOw9WyvgJBd46CClNn9k3qfbjvT//0cf7AlYRX/OslMQ=="}, "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_2.0.1_1630409910299_0.4344720072647983"}, "_hasShrinkwrap": false, "publish_time": 1630409910469, "_cnpm_publish_time": 1630409910469, "_cnpmcore_publish_time": "2021-12-13T12:30:08.624Z"}, "2.0.0": {"name": "<PERSON><PERSON>", "version": "2.0.0", "description": "The fastest javascript implementation of a double-ended queue. Used by the official Redis, MongoDB, MariaDB & MySQL libraries for Node.js and many other libraries. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha && npm run typescript", "coveralls": "cat ./coverage/lcov.info | coveralls", "typescript": "tsc --project ./test/type/tsconfig.json", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/invertase/denque.git"}, "license": "Apache-2.0", "author": {"name": "Invertase", "email": "<EMAIL>", "url": "http://github.com/invertase/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "<PERSON><PERSON><PERSON>"}], "bugs": {"url": "https://github.com/invertase/denque/issues"}, "homepage": "https://docs.page/invertase/denque", "devDependencies": {"benchmark": "^2.1.4", "codecov": "^3.8.3", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.5", "mocha": "^3.5.3", "typescript": "^3.4.1"}, "gitHead": "784ad35f1b0e71ce55ad7f1476ce39d3e1e57e2d", "_id": "denque@2.0.0", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"shasum": "3e86e7dad83a10a6ace995f845cf3401730953f7", "size": 5258, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-2.0.0.tgz", "integrity": "sha512-k+cSndflXI4GWYZfMloaQYGddyQ7HzkZhWCg2+bBiLFJFlosjeT6THUFfsaTooDCxtgEeQxfVEpSPoqKkLjsXA=="}, "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_2.0.0_1629309907724_0.5991984376032258"}, "_hasShrinkwrap": false, "publish_time": 1629309907917, "_cnpm_publish_time": 1629309907917, "_cnpmcore_publish_time": "2021-12-13T12:30:08.876Z"}, "1.5.1": {"name": "<PERSON><PERSON>", "version": "1.5.1", "description": "The fastest javascript implementation of a double-ended queue. Used by the official Redis, MongoDB, MariaDB & MySQL libraries for Node.js and many other libraries. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha && npm run typescript", "coveralls": "cat ./coverage/lcov.info | coveralls", "typescript": "tsc --project ./test/type/tsconfig.json", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/invertase/denque.git"}, "license": "Apache-2.0", "author": {"name": "Invertase", "email": "<EMAIL>", "url": "http://github.com/invertase/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "<PERSON><PERSON><PERSON>"}], "bugs": {"url": "https://github.com/invertase/denque/issues"}, "homepage": "https://docs.page/invertase/denque", "devDependencies": {"benchmark": "^2.1.4", "codecov": "^3.8.3", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.5", "mocha": "^3.5.3", "typescript": "^3.4.1"}, "gitHead": "ba9b98d35213bdfe223cf60265908b64bd43dfde", "_id": "denque@1.5.1", "_nodeVersion": "14.17.1", "_npmVersion": "6.14.13", "dist": {"shasum": "07f670e29c9a78f8faecb2566a1e2c11929c5cbf", "size": 4999, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.5.1.tgz", "integrity": "sha512-XwE+iZ4D6ZUB7mfYRMb5wByE8L74HCn30FBN7sWnXksWc1LO1bPDl67pBR9o/kC4z/xSNAwkMYcGgqDV3BE3Hw=="}, "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_1.5.1_1629302642682_0.4251875983045814"}, "_hasShrinkwrap": false, "publish_time": 1629302642889, "_cnpm_publish_time": 1629302642889, "_cnpmcore_publish_time": "2021-12-13T12:30:09.189Z"}, "1.5.0": {"name": "<PERSON><PERSON>", "version": "1.5.0", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha && npm run typescript", "coveralls": "cat ./coverage/lcov.info | coveralls", "typescript": "tsc --project ./test/type/tsconfig.json", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/invertase/denque.git"}, "license": "Apache-2.0", "author": {"name": "Invertase", "email": "<EMAIL>", "url": "http://github.com/invertase/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "<PERSON><PERSON><PERSON>"}], "bugs": {"url": "https://github.com/invertase/denque/issues"}, "homepage": "https://github.com/invertase/denque#readme", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.3", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.5", "mocha": "^3.5.3", "typescript": "^3.4.1"}, "gitHead": "0420632a878b271e2d7483c30468a60b4afc9456", "_id": "denque@1.5.0", "_nodeVersion": "15.2.0", "_npmVersion": "7.0.8", "dist": {"shasum": "773de0686ff2d8ec2ff92914316a47b73b1c73de", "size": 6484, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.5.0.tgz", "integrity": "sha512-CYiCSgIF1p6EUByQPlGkKnP1M9g0ZV3qMIrqMqZqdwazygIA/YP2vrbcyl1h/WppKJTdl1F85cXIle+394iDAQ=="}, "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_1.5.0_1609781581555_0.6172315755242443"}, "_hasShrinkwrap": false, "publish_time": 1609781581685, "_cnpm_publish_time": 1609781581685, "_cnpmcore_publish_time": "2021-12-13T12:30:09.559Z"}, "1.4.1": {"name": "<PERSON><PERSON>", "version": "1.4.1", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha && npm run typescript", "coveralls": "cat ./coverage/lcov.info | coveralls", "typescript": "tsc --project ./test/type/tsconfig.json", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/invertase/denque.git"}, "license": "Apache-2.0", "author": {"name": "Invertase", "email": "<EMAIL>", "url": "http://github.com/invertase/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "<PERSON><PERSON><PERSON>"}], "bugs": {"url": "https://github.com/invertase/denque/issues"}, "homepage": "https://github.com/invertase/denque#readme", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.3", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.5", "mocha": "^3.5.3", "typescript": "^3.4.1"}, "gitHead": "05c0264385cce864bf5ac2811fad3e85a9f7fe37", "_id": "denque@1.4.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "6744ff7641c148c3f8a69c307e51235c1f4a37cf", "size": 6316, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.4.1.tgz", "integrity": "sha512-OfzPuSZKGcgr96rf1oODnfjqBFmr1DVoc/TrItj3Ohe0Ah1C5WX5Baquw/9U9KovnQ88EqmJbD66rKYUQYN1tQ=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_1.4.1_1554250885281_0.0009501203863016006"}, "_hasShrinkwrap": false, "publish_time": 1554250885475, "_cnpm_publish_time": 1554250885475, "_cnpmcore_publish_time": "2021-12-13T12:30:10.157Z"}, "1.4.0": {"name": "<PERSON><PERSON>", "version": "1.4.0", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha && npm run typescript", "coveralls": "cat ./coverage/lcov.info | coveralls", "typescript": "tsc --project ./test/type/tsconfig.json", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.13.1", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^3.0.0", "typescript": "^2.9.1"}, "gitHead": "062ed401b1c0f5ea914f006a1863e038b26ff732", "_id": "denque@1.4.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "79e2f0490195502107f24d9553f374837dabc916", "size": 5995, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.4.0.tgz", "integrity": "sha512-gh513ac7aiKrAgjiIBWZG0EASyDF9p4JMWwKA8YU5s9figrL5SRNEMT6FDynsegakuhWd1wVqTvqvqAoDxw7wQ=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_1.4.0_1541869712459_0.9327888940350866"}, "_hasShrinkwrap": false, "publish_time": 1541869712582, "_cnpm_publish_time": 1541869712582, "_cnpmcore_publish_time": "2021-12-13T12:30:10.475Z"}, "1.3.0": {"name": "<PERSON><PERSON>", "version": "1.3.0", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha && npm run typescript", "coveralls": "cat ./coverage/lcov.info | coveralls", "typescript": "tsc --project ./test/type/tsconfig.json", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "APACHE-2.0", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.13.1", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^3.0.0", "typescript": "^2.9.1"}, "gitHead": "19b6a6993bd77cfb88a40340af60340a25b96a4f", "_id": "den<PERSON>@1.3.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "681092ef44a630246d3f6edb2a199230eae8e76b", "size": 5987, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.3.0.tgz", "integrity": "sha512-4SRaSj+PqmrS1soW5/Avd7eJIM2JJIqLLmwhRqIGleZM/8KwZq80njbSS2Iqas+6oARkSkLDHEk4mm78q3JlIg=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_1.3.0_1528042157643_0.010421773715948879"}, "_hasShrinkwrap": false, "publish_time": 1528042157707, "_cnpm_publish_time": 1528042157707, "_cnpmcore_publish_time": "2021-12-13T12:30:10.920Z"}, "1.2.6": {"name": "<PERSON><PERSON>", "version": "1.2.6", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "APACHE-2.0", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "aeaf142269e916cfe877b33036af57da3829b1b0", "_id": "denque@1.2.6", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "8d3d7a1bb8dc96286368d41a9a522db6581ab00d", "size": 5776, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.2.6.tgz", "integrity": "sha512-In0vlL6vOSzrwFX1IfLEWj0F4DCl6yzc/SGSwkPlZhlb8hvD4qyOFvAk0/s8ec2MeNvL5qKFtj+w4igd5ZRWpw=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_1.2.6_1527205510345_0.4377134290234923"}, "_hasShrinkwrap": false, "publish_time": 1527205510431, "_cnpm_publish_time": 1527205510431, "_cnpmcore_publish_time": "2021-12-13T12:30:11.262Z"}, "1.2.5": {"name": "<PERSON><PERSON>", "version": "1.2.5", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "APACHE-2.0", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "59da62e1ed570e011ac2381e899063c0fe009e07", "_id": "den<PERSON>@1.2.5", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "406e3079debfb6f44a89b8b6d5773a0b65e71b1c", "size": 5697, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.2.5.tgz", "integrity": "sha512-9Gn9Q+fxH64fjN9e+thKF36sKAD3an90sGoDGEdzgHWEp0HsMk6dW1BvDTUOdfu54X2uRvWZoMACU2i4meDYpQ=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_1.2.5_1527204557098_0.8808916116298631"}, "_hasShrinkwrap": false, "publish_time": 1527204557176, "_cnpm_publish_time": 1527204557176, "_cnpmcore_publish_time": "2021-12-13T12:30:11.680Z"}, "1.2.4": {"name": "<PERSON><PERSON>", "version": "1.2.4", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "APACHE-2.0", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "3ecd05949ca85d60e9c80abe1a375b21ac50fac6", "_id": "denque@1.2.4", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "1f0af7ad8d1a3def86c57335b45cbdfec3b62567", "size": 5695, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.2.4.tgz", "integrity": "sha512-/VU7mVDHTVsLt2WbCwKJjz1EElceWJ9nyfnLpe/dWb3oIiRqJsd/Ae14m0dl17YdSOxSFGeyPpLBxMWFs7rc9g=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_1.2.4_1527204266756_0.2344016504619597"}, "_hasShrinkwrap": false, "publish_time": 1527204266823, "_cnpm_publish_time": 1527204266823, "_cnpmcore_publish_time": "2021-12-13T12:30:12.110Z"}, "1.2.3": {"name": "<PERSON><PERSON>", "version": "1.2.3", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "1c8f28a765f07bdd45634e8c094198add0da0051", "_id": "den<PERSON>@1.2.3", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "98c50c8dd8cdfae318cc5859cc8ee3da0f9b0cc2", "size": 8718, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.2.3.tgz", "integrity": "sha512-BOjyD1zPf7gqgXlXBCnCsz84cbRNfqpQNvWOUiw3Onu9s7a2afW2LyHzctoie/2KELfUoZkNHTnW02C3hCU20w=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_1.2.3_1518214871825_0.8494753695548045"}, "_hasShrinkwrap": false, "publish_time": 1518214872527, "_cnpm_publish_time": 1518214872527, "_cnpmcore_publish_time": "2021-12-13T12:30:12.553Z"}, "1.2.2": {"name": "<PERSON><PERSON>", "version": "1.2.2", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "7a6a38ac9eb99c747beb98fe60acec5ec88da84a", "_id": "denque@1.2.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.1.2", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "e06cf7cf0da8badc88cbdaabf8fc0a70d659f1d4", "size": 9803, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.2.2.tgz", "integrity": "sha512-x92Ql74lcTbGylXILO9Xf9S0cMpEPP04zVp2bB9e2C7G/n/Q1SgLl78RaSYEPSgpDX9uLgQXCEGAS5BI5dP3yA=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque-1.2.2.tgz_1502831015866_0.7025779865216464"}, "directories": {}, "publish_time": 1502831016952, "_hasShrinkwrap": false, "_cnpm_publish_time": 1502831016952, "_cnpmcore_publish_time": "2021-12-13T12:30:13.677Z"}, "1.2.1": {"name": "<PERSON><PERSON>", "version": "1.2.1", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "fbfa391c733cf66cb52505969716b178825a1bd3", "_id": "denque@1.2.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.1.2", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "3f7abaa87ad0439e03e5bab68c40ff86bad9bea2", "size": 9788, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.2.1.tgz", "integrity": "sha512-Ak/DUA1K1wMpamAfz3BYXHdeN6Bmbw6CC48QCMbn8DL8idfxEGIdVNjCwpkdTcT34uRY16/+faA6RzwXh9t6mw=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque-1.2.1.tgz_1501263759048_0.11474525299854577"}, "directories": {}, "publish_time": 1501263760088, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501263760088, "_cnpmcore_publish_time": "2021-12-13T12:30:14.146Z"}, "1.2.0": {"name": "<PERSON><PERSON>", "version": "1.2.0", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "85f8b399884c3f7e4fadc48b0245fe5271ad15cb", "_id": "denque@1.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.1.2", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "6816a0afe19bbd80a9cf5b4f310fce0a9795c786", "size": 9528, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.2.0.tgz", "integrity": "sha512-/I7pAEpN8s1DWIV5fGZ+jhApW5E7bT7ZBmCHMmRjGuQJaz9CDQOmDqcIb+aUt3pMyoQX1GASjMpm70vRXC0rnA=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque-1.2.0.tgz_1501262165666_0.9589826653245836"}, "directories": {}, "publish_time": 1501262166761, "_hasShrinkwrap": false, "_cnpm_publish_time": 1501262166761, "_cnpmcore_publish_time": "2021-12-13T12:30:14.688Z"}, "1.1.1": {"name": "<PERSON><PERSON>", "version": "1.1.1", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=4.0"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_two_million": "node benchmark/two_million"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "51ff1aefe182491ddfde394262edaea6889ffcf2", "_id": "denque@1.1.1", "_shasum": "10229c2b88eec1bd15ff82c5fde356e7beb6db9e", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "6.9.1", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "10229c2b88eec1bd15ff82c5fde356e7beb6db9e", "size": 7350, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.1.1.tgz", "integrity": "sha512-rXxp9BHw06GH9aBcMw9Q5jnPq7VEqVR17i+VA05qsC/fxbotJKuBCEP9y3+mT0qORJFbWsQNh+mg5TB/WSab4g=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/denque-1.1.1.tgz_1485432447235_0.5495167723856866"}, "directories": {}, "publish_time": 1485432449295, "_hasShrinkwrap": false, "_cnpm_publish_time": 1485432449295, "_cnpmcore_publish_time": "2021-12-13T12:30:15.197Z"}, "1.1.0": {"name": "<PERSON><PERSON>", "version": "1.1.0", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=4.0"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_two_million": "node benchmark/two_million"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "309c324bfdacd179a57564b5499d94f5c9c1dd28", "_id": "denque@1.1.0", "_shasum": "cc9892dd674bed833e74544964de1b3c7e847d90", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.8.1", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "cc9892dd674bed833e74544964de1b3c7e847d90", "size": 7362, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.1.0.tgz", "integrity": "sha512-5yQliXSrgwKX6ZDFKoSdnv6nyY9RyedgTiDj3AYbnLjWM1GXLVCeQ+2JlXSZHiBDj1MNjnYHDdrMoCasZxolxQ=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/denque-1.1.0.tgz_1476708108975_0.3665269056800753"}, "directories": {}, "publish_time": 1476708110755, "_hasShrinkwrap": false, "_cnpm_publish_time": 1476708110755, "_cnpmcore_publish_time": "2021-12-13T12:30:15.704Z"}, "1.0.6": {"name": "<PERSON><PERSON>", "version": "1.0.6", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=4.0"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_two_million": "node benchmark/two_million"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "c5f441cfa89e188ca0c8007e605ba0230df2bba9", "_id": "denque@1.0.6", "_shasum": "e397a2e28d3a3b43d7cd79d4064af5b894d32758", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.8.0", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "e397a2e28d3a3b43d7cd79d4064af5b894d32758", "size": 8352, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.0.6.tgz", "integrity": "sha512-KmPwDcej932kdis3h6H+y+9D7OQZ4E7qZBEzA2ROfvPThWFSQpEapOoKsFOybHtpLkiuE+fMRURYBzN4oOHCUg=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/denque-1.0.6.tgz_1476706842816_0.8751837986055762"}, "directories": {}, "publish_time": 1476706844860, "_hasShrinkwrap": false, "_cnpm_publish_time": 1476706844860, "_cnpmcore_publish_time": "2021-12-13T12:30:16.190Z"}, "1.0.5": {"name": "<PERSON><PERSON>", "version": "1.0.5", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=4.0"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_two_million": "node benchmark/two_million"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "a8ac023d8e52409b035cc7c356c62fd8b3cb02ea", "_id": "denque@1.0.5", "_shasum": "4657be0beb54c17c2a54d2c20690c3f6612b3763", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.8.0", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "4657be0beb54c17c2a54d2c20690c3f6612b3763", "size": 8342, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.0.5.tgz", "integrity": "sha512-I+Ph0HblWH2TdmSr3Ab+hLFPaBgvIQ+9RDuU7B3vF29MdZ+okpXWuQBc30SA0JvBJQMk2FcAFFAwDXm5iKrGZQ=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/denque-1.0.5.tgz_1476705607732_0.32143798912875354"}, "directories": {}, "publish_time": 1476705609537, "_hasShrinkwrap": false, "_cnpm_publish_time": 1476705609537, "_cnpmcore_publish_time": "2021-12-13T12:30:16.781Z"}, "1.0.4": {"name": "<PERSON><PERSON>", "version": "1.0.4", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=4.0"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_two_million": "node benchmark/two_million"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "15bba8bd027f5944640543546c9f7b850b1ba33a", "_id": "denque@1.0.4", "_shasum": "ae6e01df000ab2fc7b38f550f37588ca5e3d6237", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "ae6e01df000ab2fc7b38f550f37588ca5e3d6237", "size": 7314, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.0.4.tgz", "integrity": "sha512-wf0M40mJ8+87E9bP9bCKgKRBO5mtvItyqyWAAeL6HHz5BYGV/PG4t1Y0bWiPOTo25+d5oQ9c6xPCpq9C1D4kzA=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/denque-1.0.4.tgz_1474028375287_0.20370881911367178"}, "directories": {}, "publish_time": 1474028377116, "_hasShrinkwrap": false, "_cnpm_publish_time": 1474028377116, "_cnpmcore_publish_time": "2021-12-13T12:30:17.481Z"}, "1.0.3": {"name": "<PERSON><PERSON>", "version": "1.0.3", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=4.0"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_two_million": "node benchmark/two_million"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "2a9cb9220885ec24914d72dfc7e5323380fb3cd4", "_id": "den<PERSON>@1.0.3", "_shasum": "eef023d58a8c78840044773c5804ba220d60bbad", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "eef023d58a8c78840044773c5804ba220d60bbad", "size": 7296, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.0.3.tgz", "integrity": "sha512-avZ7/GaaL+S7KdSi919A79XHM/ysH1NCeYRzMEL+prwMn45sJt9wJzbCHe1hIZJX17cc7jXmurXDIy+MH7O4cQ=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/denque-1.0.3.tgz_1472722291688_0.3600267304573208"}, "directories": {}, "publish_time": 1472722293251, "_hasShrinkwrap": false, "_cnpm_publish_time": 1472722293251, "_cnpmcore_publish_time": "2021-12-13T12:30:18.005Z"}, "1.0.2": {"name": "<PERSON><PERSON>", "version": "1.0.2", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=4.0"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_two_million": "node benchmark/two_million"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "51aadd3ac7ca919cee7fb914850f5577b7400f0f", "_id": "denque@1.0.2", "_shasum": "95f25a4d5a90438420cf104bbe4cebc5e1f774eb", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "95f25a4d5a90438420cf104bbe4cebc5e1f774eb", "size": 7315, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.0.2.tgz", "integrity": "sha512-dUtei9Y7OWdloImVbFNM5icA72bxYE9epRXfYRF4I8Vq3+SmiR8YhasgizmMoqJJBRCV9B2agSUwd422DJAP/A=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/denque-1.0.2.tgz_1472236544654_0.17858526087366045"}, "directories": {}, "publish_time": 1472236546495, "_hasShrinkwrap": false, "_cnpm_publish_time": 1472236546495, "_cnpmcore_publish_time": "2021-12-13T12:30:18.511Z"}, "1.0.1": {"name": "<PERSON><PERSON>", "version": "1.0.1", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=4.0"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_two_million": "node benchmark/two_million"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "cd84e69bc4a351ca4f3e613351dcd305c290f0ea", "_id": "denque@1.0.1", "_shasum": "bfdafc8a9e7fcb505dec508e5aa1d1764a3bb204", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "bfdafc8a9e7fcb505dec508e5aa1d1764a3bb204", "size": 7194, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.0.1.tgz", "integrity": "sha512-B63nCR11Amc1O7N772SUPYbyApI3Kf7ELHIuR0tmAOyxmiGN8xC+b223jDi90pCy6yb5HmJVPm3W6uZuAsrdGw=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/denque-1.0.1.tgz_1467435404703_0.9011395557317883"}, "directories": {}, "publish_time": 1467435406833, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467435406833, "_cnpmcore_publish_time": "2021-12-13T12:30:19.127Z"}, "1.0.0": {"name": "<PERSON><PERSON>", "version": "1.0.0", "description": "The fastest javascript implementation of a double-ended queue. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=4.0"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls", "benchmark_thousand": "node benchmark/thousand", "benchmark_two_million": "node benchmark/two_million"}, "repository": {"type": "git", "url": "git+https://github.com/Salakar/denque.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/Salakar/"}, "license": "MIT", "bugs": {"url": "https://github.com/Salakar/denque/issues"}, "homepage": "https://github.com/Salakar/denque#readme", "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.4", "mocha": "^2.5.3"}, "gitHead": "c5d0958e1fe0e55296a889a5a2a0b05768fb677a", "_id": "denque@1.0.0", "_shasum": "0b9d32f10404f431ef81cdf197ff4fe48e5f5872", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "dist": {"shasum": "0b9d32f10404f431ef81cdf197ff4fe48e5f5872", "size": 7253, "noattachment": false, "tarball": "https://registry.npmmirror.com/denque/-/denque-1.0.0.tgz", "integrity": "sha512-1v+C3Vb1FUwU1EHJSUzHGC3UK2nkDX6GdWk4QY2IkPDUJP9f2k8f4qCotnmh5jg/4sZKTtvTP2q1PxQiUAcDLA=="}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/denque-1.0.0.tgz_1467434322710_0.5775562883354723"}, "directories": {}, "publish_time": 1467434324723, "_hasShrinkwrap": false, "_cnpm_publish_time": 1467434324723, "_cnpmcore_publish_time": "2021-12-13T12:30:19.734Z"}, "2.1.0": {"name": "<PERSON><PERSON>", "version": "2.1.0", "description": "The fastest javascript implementation of a double-ended queue. Used by the official Redis, MongoDB, MariaDB & MySQL libraries for Node.js and many other libraries. Maintains compatability with deque.", "main": "index.js", "engines": {"node": ">=0.10"}, "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "scripts": {"test": "istanbul cover --report lcov _mocha && npm run typescript", "coveralls": "cat ./coverage/lcov.info | coveralls", "typescript": "tsc --project ./test/type/tsconfig.json", "benchmark_thousand": "node benchmark/thousand", "benchmark_2mil": "node benchmark/two_million", "benchmark_splice": "node benchmark/splice", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne", "benchmark_growth": "node benchmark/growth", "benchmark_toArray": "node benchmark/toArray", "benchmark_fromArray": "node benchmark/fromArray"}, "repository": {"type": "git", "url": "git+https://github.com/invertase/denque.git"}, "license": "Apache-2.0", "author": {"name": "Invertase", "email": "<EMAIL>", "url": "http://github.com/invertase/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "<PERSON><PERSON><PERSON>"}], "bugs": {"url": "https://github.com/invertase/denque/issues"}, "homepage": "https://docs.page/invertase/denque", "devDependencies": {"benchmark": "^2.1.4", "codecov": "^3.8.3", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.5", "mocha": "^3.5.3", "typescript": "^3.4.1"}, "types": "./index.d.ts", "gitHead": "539105bb57854e997dd469221cdc52a0ad80e0a2", "_id": "denque@2.1.0", "_nodeVersion": "18.3.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-HVQE3AAb/pxF8fQAoiqpvg9i3evqug3hoiwakOyZAwJm+6vZehbkYXZ0l4JxS+I3QxM97v5aaRNhj8v5oBhekw==", "shasum": "e93e1a6569fb5e66f16a3c2a2964617d349d6ab1", "tarball": "https://registry.npmmirror.com/denque/-/denque-2.1.0.tgz", "fileCount": 6, "unpackedSize": 30361, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJLtIWDGdPVi66lvzFzHTvAxbrUcE/6nZCxIMHUa6ibwIgesl6s1DKxubgME0qTuvsYXrg6Exbd0es4WCS1ZAB+xA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1TKFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpzaw/9FdIxUf3Yk5IEH0yYhOjtUa824wvwPHMvgnv0lfxriJ1vFr5C\r\nb2mNBi9/9b+aAcoCbsVrMxAMZ+TmCdlWkVfC34FBIA62qQvfkNJLfa2M5xXu\r\nWwuJiPQMXHxA5tk+9dBjndervrmB4pnANlSxP7SH6WaCqGcoMUpErMkStOgh\r\n1npvbNFm4nBC+RWVPa6pYdx/MJRjBFhfJ+17eEm6/Br1mms7fA6OVrivSymv\r\nKWGaB46eC9CGGa+y8ZvtCKdsZyI0NdwBgbiVOjPzIOdVS0aRPDk7JqFEfVdU\r\nWVbTHdsQcX7oD8SP7Is72lBPu39kt8Irosq0amcTUYMmBE2IncSv2WHvGfqw\r\nvOJ6LkuVrrMIhll/2AOSabIFvr6plOoSGriDLqw665mrQQ2rYKWW312sNDKV\r\nRdnnngTV/AZzQW/L4GFJkWovRuYoRbgnEuVGFRRe80Wifq21TF5sVwRskAW+\r\nY+R3nml/radt1YTXn0Ciasl/I/LP20JBzUej7FkYl2iG2HypxvRVyCU6SuFW\r\nQXq3g8p+Gnw8jb7umfAS6aR4nwFC5om5S9iRR19SZ1Sj/Wku3APUGIt4CYj1\r\nRhJyJUrYFeHiPNIeNDMqy9KeyYLmp/6AG/XAyLf97RucLAUzWyUg59JIyTdW\r\nyt5RwPT1YuWN8ebx5udOwyR/5q3ZLyztK/I=\r\n=gX5c\r\n-----END PGP SIGNATURE-----\r\n", "size": 9230}, "_npmUser": {"name": "salakar", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "salakar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/denque_2.1.0_1658139268951_0.7196133466662422"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2022-07-18T10:24:01.593Z"}}, "bugs": {"url": "https://github.com/invertase/denque/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "<PERSON><PERSON><PERSON>"}], "homepage": "https://docs.page/invertase/denque", "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "repository": {"type": "git", "url": "git+https://github.com/invertase/denque.git"}, "_source_registry_name": "default"}