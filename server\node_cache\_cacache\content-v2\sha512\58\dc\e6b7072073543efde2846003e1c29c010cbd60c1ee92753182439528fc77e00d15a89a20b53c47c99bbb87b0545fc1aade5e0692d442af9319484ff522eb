{"_attachments": {}, "_id": "once", "_rev": "687-61f14512fbcaa28a7594593b", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "description": "Run a function exactly one time", "dist-tags": {"latest": "1.4.0"}, "license": "ISC", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "name": "once", "readme": "# once\n\nOnly call a function once.\n\n## usage\n\n```javascript\nvar once = require('once')\n\nfunction load (file, cb) {\n  cb = once(cb)\n  loader.load('file')\n  loader.once('load', cb)\n  loader.once('error', cb)\n}\n```\n\nOr add to the Function.prototype in a responsible way:\n\n```javascript\n// only has to be done once\nrequire('once').proto()\n\nfunction load (file, cb) {\n  cb = cb.once()\n  loader.load('file')\n  loader.once('load', cb)\n  loader.once('error', cb)\n}\n```\n\nIronically, the prototype feature makes this module twice as\ncomplicated as necessary.\n\nTo check whether you function has been called, use `fn.called`. Once the\nfunction is called for the first time the return value of the original\nfunction is saved in `fn.value` and subsequent calls will continue to\nreturn this value.\n\n```javascript\nvar once = require('once')\n\nfunction load (cb) {\n  cb = once(cb)\n  var stream = createStream()\n  stream.once('data', cb)\n  stream.once('end', function () {\n    if (!cb.called) cb(new Error('not found'))\n  })\n}\n```\n\n## `once.strict(func)`\n\nThrow an error if the function is called twice.\n\nSome functions are expected to be called only once. Using `once` for them would\npotentially hide logical errors.\n\nIn the example below, the `greet` function has to call the callback only once:\n\n```javascript\nfunction greet (name, cb) {\n  // return is missing from the if statement\n  // when no name is passed, the callback is called twice\n  if (!name) cb('Hello anonymous')\n  cb('Hello ' + name)\n}\n\nfunction log (msg) {\n  console.log(msg)\n}\n\n// this will print 'Hello anonymous' but the logical error will be missed\ngreet(null, once(msg))\n\n// once.strict will print 'Hello anonymous' and throw an error when the callback will be called the second time\ngreet(null, once.strict(msg))\n```\n", "time": {"created": "2022-01-26T12:56:50.253Z", "modified": "2023-07-28T01:54:24.432Z", "1.4.0": "2016-09-06T21:11:09.367Z", "1.3.3": "2015-11-20T21:45:14.765Z", "1.3.2": "2015-05-04T23:09:54.026Z", "1.3.1": "2014-09-18T23:05:04.887Z", "1.3.0": "2013-10-24T06:27:26.638Z", "1.2.0": "2013-08-12T02:55:21.962Z", "1.1.1": "2012-08-14T07:25:58.262Z"}, "versions": {"1.4.0": {"name": "once", "version": "1.4.0", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "^7.0.1"}, "scripts": {"test": "tap test/*.js"}, "files": ["once.js"], "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "0e614d9f5a7e6f0305c625f6b581f6d80b33b8a6", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "homepage": "https://github.com/isaacs/once#readme", "_id": "once@1.4.0", "_shasum": "583b1aa775961d4b113ac17d9c50baef9dd76bd1", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "583b1aa775961d4b113ac17d9c50baef9dd76bd1", "size": 1979, "noattachment": false, "tarball": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/once-1.4.0.tgz_1473196269128_0.537820661207661"}, "publish_time": 1473196269367, "_hasShrinkwrap": false, "_cnpm_publish_time": 1473196269367, "_cnpmcore_publish_time": "2021-12-13T06:43:26.463Z"}, "1.3.3": {"name": "once", "version": "1.3.3", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "^1.2.0"}, "scripts": {"test": "tap test/*.js"}, "files": ["once.js"], "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "2ad558657e17fafd24803217ba854762842e4178", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "homepage": "https://github.com/isaacs/once#readme", "_id": "once@1.3.3", "_shasum": "b2e261557ce4c314ec8304f3fa82663e4297ca20", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b2e261557ce4c314ec8304f3fa82663e4297ca20", "size": 1573, "noattachment": false, "tarball": "https://registry.npmmirror.com/once/-/once-1.3.3.tgz", "integrity": "sha512-6vaNInhu+CHxtONf3zw3vq4SP2DOQhjBvIa3rNcG0+P7eKWlYH6Peu7rHizSloRU2EwMz6GraLieis9Ac9+p1w=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1448055914765, "_hasShrinkwrap": false, "_cnpm_publish_time": 1448055914765, "_cnpmcore_publish_time": "2021-12-13T06:43:26.816Z"}, "1.3.2": {"name": "once", "version": "1.3.2", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "e35eed5a7867574e2bf2260a1ba23970958b22f2", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "homepage": "https://github.com/isaacs/once#readme", "_id": "once@1.3.2", "_shasum": "d8feeca93b039ec1dcdee7741c92bdac5e28081b", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "2.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "d8feeca93b039ec1dcdee7741c92bdac5e28081b", "size": 1776, "noattachment": false, "tarball": "https://registry.npmmirror.com/once/-/once-1.3.2.tgz", "integrity": "sha512-tPQxpk4nBjTgu+eHijWhgX2d+tE6HQyMPVnzY5b1qenTUFsxBaKlzEFUF+XVfbToFuVFm8hX+PzV9u3PewDZ4Q=="}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1430780994026, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430780994026, "_cnpmcore_publish_time": "2021-12-13T06:43:27.095Z"}, "1.3.1": {"name": "once", "version": "1.3.1", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "gitHead": "c90ac02a74f433ce47f6938869e68dd6196ffc2c", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "homepage": "https://github.com/isaacs/once", "_id": "once@1.3.1", "_shasum": "f3f3e4da5b7d27b5c732969ee3e67e729457b31f", "_from": ".", "_npmVersion": "2.0.0", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "f3f3e4da5b7d27b5c732969ee3e67e729457b31f", "size": 2000, "noattachment": false, "tarball": "https://registry.npmmirror.com/once/-/once-1.3.1.tgz", "integrity": "sha512-NzfbaaoQvz2JC/D/Yj3GZi0FJG1w9i3K9Bp99Ws3p0xriPynC/YfRcpo2zoVuIduvH4b8+6up4ogGxnqajSKhA=="}, "publish_time": 1411081504887, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411081504887, "_cnpmcore_publish_time": "2021-12-13T06:43:27.429Z"}, "1.3.0": {"name": "once", "version": "1.3.0", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "_id": "once@1.3.0", "dist": {"tarball": "https://registry.npmmirror.com/once/-/once-1.3.0.tgz", "shasum": "151af86bfc1f08c4b9f07d06ab250ffcbeb56581", "size": 1940, "noattachment": false, "integrity": "sha512-A31oqbdEQnnhkjIXJ6QKcgO9eN8Xe+dVAQqlFLAmri0Y5s11pUadCihT2popU2WLd5CbbnD2ZVkbEJsR/8JHvA=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1382596046638, "_hasShrinkwrap": false, "_cnpm_publish_time": 1382596046638, "_cnpmcore_publish_time": "2021-12-13T06:43:27.765Z"}, "1.2.0": {"name": "once", "version": "1.2.0", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "_id": "once@1.2.0", "dist": {"tarball": "https://registry.npmmirror.com/once/-/once-1.2.0.tgz", "shasum": "de1905c636af874a8fba862d9aabddd1f920461c", "size": 1864, "noattachment": false, "integrity": "sha512-WBd9yDi3JRrEsysh0s4px+jinLuW/DGRydS+ZGPTHVKu4JrIBmKj3uDC9LfnwEbXHFVLieUuZvunY74wln6arg=="}, "_from": ".", "_npmVersion": "1.3.7", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1376276121962, "_hasShrinkwrap": false, "_cnpm_publish_time": 1376276121962, "_cnpmcore_publish_time": "2021-12-13T06:43:28.076Z"}, "1.1.1": {"name": "once", "version": "1.1.1", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "_id": "once@1.1.1", "dist": {"tarball": "https://registry.npmmirror.com/once/-/once-1.1.1.tgz", "shasum": "9db574933ccb08c3a7614d154032c09ea6f339e7", "size": 1752, "noattachment": false, "integrity": "sha512-frdJr++QKEg4+JylTX+NNLgSoO6M2pDNYOOXe4WGIYKKBADBI9nU3oa06y4D4FpAJ3obAsjExeBOnscYJB9Blw=="}, "_npmVersion": "1.1.48", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "publish_time": 1344929158262, "_hasShrinkwrap": false, "_cnpm_publish_time": 1344929158262, "_cnpmcore_publish_time": "2021-12-13T06:43:28.454Z"}}, "bugs": {"url": "https://github.com/isaacs/once/issues"}, "homepage": "https://github.com/isaacs/once#readme", "keywords": ["once", "function", "one", "single"], "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "_source_registry_name": "default"}